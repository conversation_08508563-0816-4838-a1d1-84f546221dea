<?php

namespace App\Http\Controllers;

use DB;
use App;
use PDF;
use Gate;
use Alert;
use App\Label;
use App\Module;
use Carbon\Carbon;
use App\SiteSetting;
use Milon\Barcode\DNS2D;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\Master\QrcodeDataTable;
use App\DocNote;
use App\Qrcode;
use App\Services\GeneralService;
use App\View\TparmView;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class QrcodeController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:hasQrcode');
    }

    private $page = 'labellist';
    public static $qr_separator = "{RS}";
    public static $feild_separator = "||";
    public static $placeholders = [
        "Inventory Label" => [

            'Item Num' => "%item_num%",
            // 'Item Desc' => "%item_desc%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

            // 'Doc Num' => "%document_num%",
        ],
        "CO Shipping Label" => [

            'Item Num' => "%item_num%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'CO Num' => "%co_num%",
            'CO Line' => "%co_line%",
            'Customer Num' => "%cust_num%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

            // 'Cust Name' => "%cust_name%",

        ],

        "Customer Return Label" => [

            'Item Num' => "%item_num%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'Return Num' => "%return_num%",
            'Return Line' => "%return_line%",
            'Customer Num' => "%cust_num%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

            // 'Cust Name' => "%cust_name%",

        ],
        "PO Receipt Label" => [
            'Item Num' => "%item_num%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'PO Num' => "%po_num%",
            'PO Line' => "%po_line%",
            'Vend Num' => "%vend_name%",
            'Vend Lot' => "%vend_lot%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

        ],
        "Job Material Issue Label" => [
            'Item Num' => "%item_num%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'Job Num' => "%job_num%",
            'Suffix' => "%suffix%",
            'Operation' => "%oper_num%",
            'Sequence' => "%seq_num%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

        ],
        "Job Receipt Label" => [
            'Item Num' => "%item_num%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'Job Num' => "%job_num%",
            'Suffix' => "%suffix%",
            'Operation' => "%oper_num%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

        ],
        "TO Shipping Label" => [
            'Item Num' => "%item_num%",
            'Loc Num' => "%loc_num%",
            'Lot Num' => "%lot_num%",
            'UOM' => "%uom%",
            'TO Num' => "%trn_num%",
            'TO Line' => "%trn_line%",
            'From Whse' => "%from_whse%",
            'TO Whse' => "%to_whse%",
            'Qty in box' => "%qtyinbox%",
        ],

        "Pallet Label" => [
            // 'Company' => "%company_name%",
            'LPN'     => "%lpn_num%",
            'Whse' => "%whse_num%",
            'Qty in box' => "%qtyinbox%",

            // 'Creation Date' => "%creation_date%",
            // 'Box Num' => "%boxnum%",
            // 'Total Box' => "%totalbox%",

        ]


    ];
    public static $types = [
        "Inventory Label" => 'Inventory Label',
        "CO Shipping Label" => 'CO Shipping Label',
        'Customer Return Label' => "Customer Return Label",
        "PO Receipt Label" => 'PO Receipt Label',
        "Job Material Issue Label" => 'Job Material Issue Label',
        "TO Shipping Label" => 'TO Shipping Label',
        'Job Receipt Label' => "Job Receipt Label",
        'Pallet Label' => "Pallet Label",
       
    ];
    public static $shortcodes = array(
        "qrcode" => "generateQrCode"
    );

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(QrcodeDataTable $datatable)
    {
        // $shortcodes = array(
        //     "qrcode" => "generateQrCode"
        // );
        // $content = "[qrcode id=1]";
        // $inputs = [];
        // $return =  self::handleQrCode($content, $inputs);
        // dd($return);
        if (!Gate::allows('hasQrcode')) {
            return view('errors.404v2')->with('page', 'error');
        }



        // return view('admin.label.index')->with('page',$this->page);
        return $datatable->render('admin.qrcode.table');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $types = self::$types;
        // To control starter plan drop down
        $plan = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->first();
        $planId = customDecrypt($plan->plan_id);
        //hide for starter plans
        if ($planId == 1 || $planId == 4) {
            if (isset($types['Job Material Issue Label'])) {
                unset($types['Job Material Issue Label']);
            }
            if (isset($types['Job Material Label'])) {
                unset($types['Job Material Label']);
            }
            unset($types['TO Shipping Label']);
            unset($types['Job Receipt Label']);
        }
        if ($planId == 7) {
            unset($types['TO Shipping Label']);
        }

        if (!config('icapt.special_modules.enable_pallet') || $planId == 1 || $planId == 4 || $planId == 7) {
            unset($types['Pallet Label']);
        }
        return view('admin.qrcode.add')->with(['types' => $types, 'page', $this->page]);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {


        $data = $this->validate(request(), [
            'title' => 'required',
            'type' => 'required',


        ]);
        $data = $request->except(['_token']);
        // dd();
        $qrcodeAlready = Qrcode::where('title', $request->title)->where('site_id', auth()->user()->site_id)->first();

        if ($qrcodeAlready)
            throw ValidationException::withMessages([__('error.admin.exists', ['resource' => "QR Code Title", 'name' => $request->title])]);

        $saved = Qrcode::create($data);

        //if >1 selected and successful
        if ($saved) {
            return redirect(route('qrcodes.edit', $saved))->with('successmsg', 'QR Code [' . $request->title . '] has been added successfully.');
        }
    }

    public function edit(Request $request, $id)
    {
        $qrcode = new Qrcode();
        $qrcode = $qrcode->findOrFail($id);
        $types = self::$types;
        $modulelist = Module::all();
        // dd($modulelist);
        $placeholders = self::$placeholders[$qrcode->type];

        return view('admin.qrcode.edit')
            ->with('types', $types)
            ->with('modulelist', $modulelist)
            ->with('placeholders', $placeholders)
            ->with('qrcode', $qrcode)
            ->with('page', $this->page);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $labelId
     * @return Response
     * @throws ValidationException
     */
    public function update(Request $request, int $id)
    {

        $qrcode = Qrcode::where('id', $id)->first();

        if (!$qrcode) {
            return redirect(route('qrcodes.index'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.qr_code'), 'resource1' => __('admin.button.update'), 'resource2' => $id]));
        }
        $qrcode = Qrcode::findOrFail($id);
        $inputs = $request->except('type');
        $this->validate(request(), [
            'title' => 'required',

            // 'type' => 'required',
            'qr_placeholders' => 'required',

        ], [
            'qr_placeholders.required' => __('error.admin.atleastoneresource', ['resource' => 'QR value'])
        ]);

        $qrcodeAlready = Qrcode::where('title', $request->title)->where('id', "!=", $id)->where('site_id', auth()->user()->site_id)->first();

        if ($qrcodeAlready)
            throw ValidationException::withMessages([__('error.admin.exists', ['resource' => "QR Code Title", 'name' => $request->title])]);
        $placeholders_raw = implode(self::$qr_separator, $request->qr_placeholders);
        $placeholders_raw = self::$qr_separator . "" . $placeholders_raw . "" . self::$qr_separator;

        $placeholders_json = json_encode($request->qr_placeholders);
        // dd($request->qr_placeholders, $placeholders_json);
        $inputs['placeholders_raw'] = $placeholders_raw;

        $inputs['placeholders_json'] = $placeholders_json;

        $qrcode->update($inputs);
        // dd($inputs, $qrcode);
        return redirect(route('qrcodes.index'))->with('successmsg', __(
            'success.updated',
            ['resource' => __('Qrcode'), 'name' => $qrcode->title]
        ));
    }


    //delete label
    public function ap_delete(Request $request)
    {
        $label = new Qrcode();
        $myString = $request->id;
        $myArray = explode(',', $myString);
        // dd($myString);
        Qrcode::destroy($myArray);
        //if no row selected or request is null
        if ($myString == "null" || $myString == "") {
            return redirect(route('qrcodes.index'))->with('errormsg', __('error.admin.selectone'))
                ->with('page', $this->page);
        }
        //if >1 selected and successful
        else {
            return redirect(route('qrcodes.index'))->with('successmsg', __('success.deleted', ['resource' => __('Qrcode')]))
                ->with('page', $this->page);
        }
    }



    public function checkLabelName($label_name = "")
    {
        $label = Label::where('label_name', $label_name)->where('site_id', auth()->user()->site_id)->first();

        if ($label != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }
    public function getJson(Request $request)
    {
        $type = $request->type;
        // $qrcode = Qrcode::where('title', $id)->where('site_id', auth()->user()->site_id)->first();

        $qrcodes = Qrcode::where('type', $type)->where('site_id', auth()->user()->site_id)->get();
        // dd($qrcodes);
        $data = [];
        foreach ($qrcodes as $qrcode) {
            $data[$qrcode->id] = $qrcode->title;
        }
        return response()->json($data);
    }
    function handleShortcodes($content, $shortcodes, $inputs = [])
    {
        //Loop through all shortcodes
        foreach ($shortcodes as $key => $fnName) {
            $dat = array();
            preg_match_all("/\[" . $key . " (.+?)\]/", $content, $dat);
            if (count($dat) > 0 && $dat[0] != array() && isset($dat[1])) {
                $i = 0;
                $actual_string = $dat[0];
                foreach ($dat[1] as $temp) {
                    $temp = explode(" ", $temp);
                    $params = array();
                    foreach ($temp as $d) {
                        list($opt, $val) = explode("=", $d);
                        $params[$opt] = trim($val, '"');
                    }
                    $params['inputs'] = $inputs;
                    // dd($params);
                    // dd($fnName, method_exists($this, $fnName));


                    $qrcodeString = method_exists($this, $fnName) ? call_user_func_array(array($this, $fnName), $params) : "$qrcodeString";
                    $content = str_replace($actual_string[$i], $qrcodeString, $content);
                    $i++;
                }
            }
        }
        return $content;
    }
    public static function handleQrCodeImage($content, $inputs = [], $inputKey = false)
    {
        //Loop through all shortcodes

        $dat = array();
        preg_match_all("/\<img (.+?)\/>/", $content, $dat);

        // preg_match_all("/\[qr (.+?)\]/", $content, $dat);
        // dd($dat);
        if (count($dat) > 0 && $dat[0] != array() && isset($dat[1])) {
            $i = 0;
            $actual_string = $dat[0];
            // dd($dat[1]);
            foreach ($dat[1] as $temp) {
                // dd($temp);
                $temp = trim($temp);

                $temp = explode('" ', $temp);

                $params = array();
                foreach ($temp as $d) {

                    list($opt, $val) = explode("=", $d);
                    $params[$opt] = trim($val, '"');
                }
                // dd($params);
                // $params['inputs'] = $inputs;
                // dd($params);
                // dd($fnName, method_exists($this, $fnName));

                $src = isset($params['src']) ? $params['src'] : "";


                if ($src == "../../../images/qrcode_placeholder.png") {
                    $qrcodeString = self::generateQrCodeImage($params, $inputs, $inputKey);
                    // dd($actual_string[$i],$qrcodeString);
                    // $qrcodeString = method_exists($this, $fnName) ? call_user_func_array(array($this, $fnName), $params) : "$qrcodeString";
                    // if ($qrcodeString != "")
                    $content = str_replace($actual_string[$i], $qrcodeString, $content);
                }

                // dd($content);
                $i++;
            }
        }

        return $content;
    }
    public static function handlPrimaryImage($content, $inputs = [], $inputKey = false)
    {
        //Loop through all shortcodes

        // dd($content, $inputKey);
        $dat = array();
        preg_match_all("/\<img (.+?)\/>/", $content, $dat);

        // preg_match_all("/\[qr (.+?)\]/", $content, $dat);
        // dd($dat);
        if (count($dat) > 0 && $dat[0] != array() && isset($dat[1])) {
            $i = 0;
            $actual_string = $dat[0];
            // dd($dat[1]);
            foreach ($dat[1] as $temp) {
                // dd($temp);
                $temp = trim($temp);

                $temp = explode('" ', $temp);

                $params = array();
                foreach ($temp as $d) {

                    list($opt, $val) = explode("=", $d);
                    $params[$opt] = trim($val, '"');
                }

                $src = isset($params['src']) ? $params['src'] : "";
                // dd($src);

                if ($src == "../../../images/item_image_placeholder.png") {
                    $qrcodeString = self::generatePrimaryImage($params, $inputs, $inputKey);
                    // dd($actual_string[$i],$qrcodeString);
                    // $qrcodeString = method_exists($this, $fnName) ? call_user_func_array(array($this, $fnName), $params) : "$qrcodeString";
                    // if ($qrcodeString != "")
                    $content = str_replace($actual_string[$i], $qrcodeString, $content);
                }

                // dd($content);
                $i++;
            }
        }

        return $content;
    }
    public static function replacePlaceholders($content, $inputs = [], $key = false)
    {
        $dat = array();
        // preg_match_all("/\<img (.+?)\/>/", $content, $dat);
        // dd($inputs);
        foreach ($inputs as $k => $inp) {
            if (is_array($inp))
                $val = $inp[$key];
            else
                $val = $inp;
            // dd($k, $val);

            $content = str_ireplace("%$k%", $val, $content);
        }
        // preg_match_all("/%(.+?)%/", $content, $dat);
        // // dd($dat);
        // if (count($dat) > 0 && $dat[0] != array() && isset($dat[1])) {
        //     foreach ($dat[1] as $temp) {
        //         if ($key) {
        //             $val = isset($inputs[$temp][$key]) ? $inputs[$temp][$key] : "";
        //         } else {
        //             $val = isset($inputs[$temp]) ? $inputs[$temp] : "";
        //         }
        //        $content = str_ireplace("%$temp%", $val, $content);
        //     }
        // }
        return $content;
    }
    public static function handleQrCode($content, $inputs = [], $inputKey = false)
    {
        //Loop through all shortcodes

        $dat = array();
        // preg_match_all("/\<img (.+?)\/>/", $content, $dat);

        preg_match_all("/\[qr (.+?)\]/", $content, $dat);
        // dd($dat);
        if (count($dat) > 0 && $dat[0] != array() && isset($dat[1])) {
            $i = 0;
            $actual_string = $dat[0];
            // dd($dat[1]);
            foreach ($dat[1] as $temp) {
                // dd($temp);
                $temp = trim($temp);
                $temp = explode(" ", $temp);

                $params = array();
                foreach ($temp as $d) {

                    list($opt, $val) = explode("=", $d);
                    $params[$opt] = trim($val, '"');
                }
                // dd($params);
                // $params['inputs'] = $inputs;
                // dd($params);
                // dd($fnName, method_exists($this, $fnName));

                $qrcodeString = self::generateQrCode($params, $inputs, $inputKey);

                // $qrcodeString = method_exists($this, $fnName) ? call_user_func_array(array($this, $fnName), $params) : "$qrcodeString";
                $content = str_replace($actual_string[$i], $qrcodeString, $content);
                // echo $content;
                // exit;
                $i++;
            }
        }

        return $content;
    }
    public static function generateQrCodeImage($params, $inputs, $inputKey = false)
    {
        // dd($params,$inputs);
        $src = isset($params['src']) ? $params['src'] : "";


        if ($src == "../../../images/qrcode_placeholder.png") {
            // dd($id);
            $id = "" . $params['id'] . "";
            $id = htmlspecialchars_decode($id);

            if (stripos($id, "T_") !== false ) {
                //dd('ppp',$id);
                $qrcode = Qrcode::where('title', $id)->where('site_id', "LIKE", "axa_test")->orWhere('site_id', "LIKE", "axa")->first();
            }
             else {
                //dd('www');
                $qrcode = Qrcode::where('title', "like", '' . $id . '')->where('site_id', auth()->user()->site_id)->first();
                if($qrcode==null)
                {
                    $qrcode = Qrcode::where('title', $id)->where('site_id', "LIKE", "axa_test")->orWhere('site_id', "LIKE", "axa")->first();
                }
            }
           //dd($id,$params);
            if ($qrcode) {

                $qrcode_img = "";
                $stringArr = [];
                $placeholders = json_decode($qrcode->placeholders_json, true);
                // dd($placeholders);
                if (!$placeholders)
                    return "";
                // dd($placeholders);
                foreach ($placeholders as $placeholder) {
                    $key = str_replace("%", "", $placeholder);
                    if ($inputKey) {
                        if (isset($inputs[$key][$inputKey])) {
                            $stringArr[] = "$key" . self::$feild_separator . $inputs[$key][$inputKey];
                        }
                    } else {
                        if (isset($inputs[$key])) {
                            $stringArr[] = "$key" . self::$feild_separator . $inputs[$key];
                        }
                    }
                }
                $final = implode(self::$qr_separator, $stringArr);
                // dd($final);
                $final = self::$qr_separator . "" . $final . "" . self::$qr_separator;
                if ($final != "") {
                    $dnsd = new DNS2D();
                    $width = $qrcode->width ? $qrcode->width : 100;
                    $height  = $qrcode->height ? $qrcode->height : 100;
                    // dd($width,$height);

                    // $qrcodegen = '<img src="data:image/svg+xml;base64,' . base64_encode($qrcodegen) . '" width="' . $imgeWidth . '" />';
                    $img_w = isset($params['width']) ? $params['width'] : $width;
                    $img_h = isset($params['height']) ? $params['height'] : $height;


                    $qrcodegen = $dnsd->getBarcodeSVG($final, "QRCODE", $img_w, $img_h, "black", true);
                    $qrcode_src = 'data:image/svg+xml;base64,' . base64_encode($qrcodegen);
                    $qrcode_img = "<img src='$qrcode_src' width='" . $img_w . "' height='" . $img_h . "' />";
                    // dd($qrcode_img);
                }
                // // dd($qrcode_img);
                // echo $qrcode_img;
                // exit;
                return $qrcode_img;
                // dd($qrcode_img);
            }
        }
        return "N/A";
    }
    public static function generatePrimaryImage($params, $inputs, $inputKey = false)
    {
        // dd($params,$inputs);
        $src = isset($params['src']) ? $params['src'] : "";
       

        if ($src == "../../../images/item_image_placeholder.png") {
            // dd($id);
            // $id = "" . $params['id'] . "";
            // $id = htmlspecialchars_decode($id);
            $qrcode_img = "";
            // $primary_image = DocNote::where('ref_type', "Item")->where('ref_num', $inputs['item_num'])->where('is_primary', 1)->first();
            // dd($primary_image);
            if (isset($inputs['item_image']) && $inputs['item_image'] != "") {
                // $src = GeneralService::showStorageMedia($inputs['item_image']);
                if ($inputKey) {

                    $src = $inputs['item_image'][$inputKey];
                } else {
                    $src = $inputs['item_image'];
                }

                $width =  100;
                $height  = 100;

                $img_w = isset($params['width']) ? $params['width'] : $width;
                $img_h = isset($params['height']) ? $params['height'] : $height;

                // dd($src, $img_w, $img_h);
                if ($src)
                    $qrcode_img = "<img src='$src' width='" . $img_w . "' height='" . $img_h . "' />";
                // dd($qrcode_img);
            }
            return $qrcode_img;
            // dd($qrcode_img);

        }
        return "";
    }
    public static function generateQrCode($params, $inputs, $inputkey = false)
    {
        // dd($params,$inputs);

        $id = $params['id'];

        if (stripos($id, "T_") == 0) {

            $qrcode = Qrcode::where('title', $id)->where('site_id', "LIKE", "axa_test")->orWhere('site_id', "LIKE", "axa")->first();
        } else {
            $qrcode = Qrcode::where('title', $id)->where('site_id', auth()->user()->site_id)->first();
        }

        // dd($id);


        $qrcodegen = "";
        $stringArr = [];
        if ($qrcode) {
            $width = $qrcode->width ? $qrcode->width : 100;
            $height  = $qrcode->height ? $qrcode->height : 100;
            // dd($width, $height);
            $imgeWidth = isset($params['w']) && $params['w'] != "" ? $params['w'] : $width;
            // dd($imgeWidth);
            $placeholders = json_decode($qrcode->placeholders_json, true);
            // dd($placeholders);
            foreach ($placeholders as $placeholder) {
                $key = str_replace("%", "", $placeholder);
                if ($inputkey) {
                    if (isset($inputs[$key][$inputkey])) {
                        $stringArr[] = "$key" . self::$feild_separator . $inputs[$key][$inputkey];

                        // $stringArr[] = "$key:" . $inputs[$key];
                    }
                } else {
                    if (isset($inputs[$key])) {
                        $stringArr[] = "$key" . self::$feild_separator . $inputs[$key];

                        // $stringArr[] = "$key:" . $inputs[$key];
                    }
                }
            }
            $final = implode(self::$qr_separator, $stringArr);
            $final = self::$qr_separator . "" . $final . "" . self::$qr_separator;

            if ($final != "") {
                $dnsd = new DNS2D();

                $qrcodegen = $dnsd->getBarcodeSVG($final, "QRCODE", $width, $height, "black", true);
                $qrcodegen = '<img src="data:image/svg+xml;base64,' . base64_encode($qrcodegen) . '" width="' . $imgeWidth . '" />';
            }
        }

        // dd($qrcodegen);
        return $qrcodegen;
    }
    public static function generateQrCodeHtml($type, $width, $height, $param, $enable_multi_scan)
    {

        $qrcode = $qrcodegen = "";
        $tparm = new TparmView();
        switch ($type) {
            case 'job':
                $qrcode = self::generateJobQrCode($param, $enable_multi_scan);
                break;
            case 'inventory':
                $qrcode = self::generateQrCodeParam($param, $enable_multi_scan);
                break;
            default:
                # code...
                break;
        }

        if ($qrcode != "") {
            $dnsd = new DNS2D();

            $qrcodegen = $dnsd->getBarcodeSVG($qrcode, "QRCODE", $width, $height, "black", true);
            $qrcodegen = '<img src="data:image/svg+xml;base64,' . base64_encode($qrcodegen) . '" width="' . $width . '" />';
        }
        return $qrcodegen;
    }
    public static function generateJobQrCode($param, $enable_multi_scan)
    {
        $sub_type = $param['qr_type'];

        $qrParams = [];
        if ($sub_type == "job_num") {
            if ($enable_multi_scan) {
                $qrParams['whse_num'] = $param['whse_num'];
                $qrParams['job_num'] = $param['job_num'];
                $qrParams['suffix'] = $param['suffix'];
            } else {
                $qrParams = $param['job_num'];
            }
        }
        if ($sub_type == "suffix") {
            if (!$enable_multi_scan) {
                $qrParams = $param['suffix'];
            }
        }

        if ($sub_type == "oper_num") {
            if ($enable_multi_scan) {
                $qrParams['whse_num'] = $param['whse_num'];
                $qrParams['job_num'] = $param['job_num'];
                $qrParams['suffix'] = $param['suffix'];
                $qrParams['oper_num'] = $param['oper_num'];
            } else {
                $qrParams = $param['oper_num'];
            }
        }
        return self::paramsToQrString($qrParams);
    }

    public static function generateQrCodeParam($param, $enable_multi_scan)
    {
        $qrParams = [];
        if ($enable_multi_scan) {
            $qrParams = $param;
        } else {
            $qrParams = $param[array_key_first($param)];
        }

        return self::paramsToQrString($qrParams);
    }

    public static function paramsToQrString($qrParams)
    {
        $final = "";


        if (is_array($qrParams)) {
            if (count($qrParams) > 1) {
                $stringArr = [];
                foreach ($qrParams as $key => $qrParam) {
                    $stringArr[] = "$key" . self::$feild_separator . $qrParam;
                    // $stringArr[] = "$key:" . $inputs[$key];
                }
                $final = implode(self::$qr_separator, $stringArr);
                $final = self::$qr_separator . "" . $final . "" . self::$qr_separator;
            } else {
                $final = "";
            }
        } else {
            $final = $qrParams;
        }


        return $final;
    }
}
