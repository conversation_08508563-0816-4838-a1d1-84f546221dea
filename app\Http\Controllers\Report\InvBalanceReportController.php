<?php

namespace App\Http\Controllers\Report;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use PDF;
use Alert;
use DataTables;
use Illuminate\Support\Facades\DB;
use App\SiteSetting;
use App\View\TparmView;
use App\DataTables\InvBalRepDataTable;
use Camroncade\Timezone\Facades\Timezone;

class InvBalanceReportController extends Controller
{

    public function index(){

        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error');
        }
        return view('report.inv.invbalance.index')->with('total_quantity_format',$total_quantity_format)->with('unit_quantity_format',$unit_quantity_format)->with('report_module_name', 'Inventory Balance');

    }

    public function getQuery(Request $request) {

        $items = DB::query()->from('items')->where('items.site_id', auth()->user()->site_id);

        $query = DB::query()->from('matl_trans')
            ->select('matl_trans.item_num', 'items.item_desc', 'matl_trans.whse_num', 'matl_trans.loc_num', 'matl_trans.lot_num', DB::raw('SUM(matl_trans.qty) as qty_on_hand'), 'matl_trans.uom as uom', DB::raw('null as loc_tot'), DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'items.lot_tracked')
            ->leftjoinSub($items, "items", function ($q) {
                $q->on('items.item_num','=', 'matl_trans.item_num');
                $q->on('items.site_id','=', 'matl_trans.site_id');
            })
            ->where('matl_trans.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id)
            ->groupBy('item_num', 'whse_num', 'loc_num')
            ->having('qty_on_hand', '!=', '0.0')
        ;
        if (config('icapt.client_prefix') == "OceanCash")
        {
        //    $query->whereNotIn('lot_num', ['B1081063','B1081064','B1081243']);
            $query->where(function($q) {
                $q->whereNotIn('lot_num', ['B1081063','B1081064','B1081243'])
                ->orWhereNull('lot_num');
            });
        }


        if(request('from_item_num') && request('to_item_num')){
            $query->whereBetween('items.item_num', [request('from_item_num'), request('to_item_num')]);
            // $loc_total->whereBetween('query.item_num', [request('from_item_num'), request('to_item_num')]);
            // $whse_total->whereBetween('query.item_num', [request('from_item_num'), request('to_item_num')]);
            // $item_total->whereBetween('query.item_num', [request('from_item_num'), request('to_item_num')]);
        }
        else if(request('from_item_num')){
            $query->where('items.item_num', '>=', request('from_item_num'));
            // $loc_total->where('items.item_num', '>=', request('from_item_num'));
            // $whse_total->where('items.item_num', '>=', request('from_item_num'));
            // $item_total->where('items.item_num', '>=', request('from_item_num'));
        }
        else if(request('to_item_num')){
            $query->where('items.item_num', '<=', request('to_item_num'));
            // $loc_total->where('items.item_num', '<=', request('to_item_num'));
            // $whse_total->where('items.item_num', '<=', request('to_item_num'));
            // $item_total->where('items.item_num', '<=', request('to_item_num'));
        }

        if(request('from_product_code') && request('to_product_code')){
            $query->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
            // $loc_total->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
            // $whse_total->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
            // $item_total->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
        }
        else if(request('from_product_code')){
            $query->where('items.product_code', '>=', request('from_product_code'));
            // $loc_total->where('items.product_code', '>=', request('from_product_code'));
            // $whse_total->where('items.product_code', '>=', request('from_product_code'));
            // $item_total->where('items.product_code', '>=', request('from_product_code'));
        }
        else if(request('to_product_code')){
            $query->where('items.product_code', '<=', request('to_product_code'));
            // $loc_total->where('items.product_code', '<=', request('to_product_code'));
            // $whse_total->where('items.product_code', '<=', request('to_product_code'));
            // $item_total->where('items.product_code', '<=', request('to_product_code'));
        }

        if(request('from_whse_num') && request('to_whse_num')){
            $query->whereBetween('matl_trans.whse_num', [request('from_whse_num'), request('to_whse_num')]);
            // $loc_total->whereBetween('matl_trans.whse_num', [request('from_whse_num'), request('to_whse_num')]);
            // $whse_total->whereBetween('matl_trans.whse_num', [request('from_whse_num'), request('to_whse_num')]);
            // $item_total->whereBetween('matl_trans.whse_num', [request('from_whse_num'), request('to_whse_num')]);
        }
        else if(request('from_whse_num')){
            $query->where('matl_trans.whse_num', '>=', request('from_whse_num'));
            // $loc_total->where('matl_trans.whse_num', '>=', request('from_whse_num'));
            // $whse_total->where('matl_trans.whse_num', '>=', request('from_whse_num'));
            // $item_total->where('matl_trans.whse_num', '>=', request('from_whse_num'));
        }
        else if(request('to_whse_num')){
            $query->where('matl_trans.whse_num', '<=', request('to_whse_num'));
            // $loc_total->where('matl_trans.whse_num', '<=', request('to_whse_num'));
            // $whse_total->where('matl_trans.whse_num', '<=', request('to_whse_num'));
            // $item_total->where('matl_trans.whse_num', '<=', request('to_whse_num'));
        }

        if(request('cut_off_date')){
            $cut_off_date = Carbon::createFromFormat(SiteSetting::getInputDateFormat(), request('cut_off_date'))->format('Y-m-d');
            $query->where('matl_trans.modified_date', '<=', $cut_off_date.' 23:59:59');
            // $loc_total->where('matl_trans.modified_date', '<=', $cut_off_date.' 23:59:59');
            // $whse_total->where('matl_trans.modified_date', '<=', $cut_off_date.' 23:59:59');
            // $item_total->where('matl_trans.modified_date', '<=', $cut_off_date.' 23:59:59');
        }


        $loc_total = DB::query()->fromSub($query, "query")
            ->select('query.item_num', DB::raw('null as item_desc'), 'query.whse_num', 'query.loc_num', DB::raw('null as lot_num'), DB::raw('null as qty_on_hand'), 'query.uom', DB::raw('SUM(query.qty_on_hand) as loc_tot'), DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'query.lot_tracked')
            ->groupBy('loc_num', 'whse_num', 'item_num')
        ;

        $whse_total = DB::query()->fromSub($query, "query")
            ->select('query.item_num', DB::raw('null as item_desc'), 'query.whse_num', DB::raw('null as loc_num'), DB::raw('null as lot_num'), DB::raw('null as qty_on_hand'), 'query.uom', DB::raw('null as loc_tot'), DB::raw('SUM(query.qty_on_hand) as whse_tot'), DB::raw('null as item_tot'), 'query.lot_tracked')
            ->groupBy('whse_num', 'item_num')
        ;

        $item_total = DB::query()->fromSub($query, "query")
            ->select('query.item_num', DB::raw('null as item_desc'), DB::raw('null as whse_num'), DB::raw('null as loc_num'), DB::raw('null as lot_num'), DB::raw('null as qty_on_hand'), 'query.uom', DB::raw('null as loc_tot'), DB::raw('null as whse_tot'), DB::raw('SUM(query.qty_on_hand) as item_tot'), 'query.lot_tracked')
            ->groupBy('item_num')
        ;


        if(request('by_lot') == 'on') {
            $query->groupBy('lot_num');

            $query->union($loc_total);
            $query->union($whse_total);
            $query->union($item_total);

            $query->orderByRaw('ISNULL(item_num), item_num asc');
            $query->orderByRaw('ISNULL(whse_num), whse_num asc');
            $query->orderByRaw('ISNULL(loc_num), loc_num asc');
            $query->orderByRaw('ISNULL(lot_num), lot_num asc');
            $query->orderBy('loc_tot', 'asc');
            $query->orderBy('whse_tot', 'asc');
            $query->orderBy('item_tot', 'asc');
        }
        else {
            $query->union($whse_total);
            $query->union($item_total);

            $query->orderByRaw('ISNULL(item_num), item_num asc');
            $query->orderByRaw('ISNULL(whse_num), whse_num asc');
            $query->orderByRaw('ISNULL(loc_num), loc_num asc');
            $query->orderBy('whse_tot', 'asc');
            $query->orderBy('item_tot', 'asc');
        }

        // $dataTable = Datatables::of($query);
        // $response  = $dataTable->make(true);
        return $query;
    }

    public function data(Request $request) {

        $query = $this->getQuery($request);

        $dataTable = Datatables::of($query);
        $response  = $dataTable->make(true);

        return $response;
    }

    public function print(Request $request) {

        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        $query = $this->getQuery($request);
        $query1 = $query->get();

        // Site
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        if($site->logo != "") {
            $url = $site->getFileFullPath();
            $image = file_get_contents($url);
            $site->logoazure = "data:image/png+jpeg+jpg;base64,".base64_encode($image);
        }
        else
            $site->logoazure = "";

        if($site->company_info) {
            $sitedecode = json_decode($site->company_info);
            $site->company_name = strtoupper($sitedecode->company_name);
        }

        foreach($query1 as $q) {
            $q->qty_on_hand = numberFormatPrecision($q->qty_on_hand, $total_quantity_format);
            if ($request->by_lot == 'on') {
                if ($q->loc_tot != null) {
                    $q->loc_tot = numberFormatPrecision($q->loc_tot, $total_quantity_format);
                }
                else if ($q->whse_tot != null) {
                    $q->whse_tot = numberFormatPrecision($q->whse_tot, $total_quantity_format);
                }
                else if ($q->item_tot != null) {
                    $q->item_tot = numberFormatPrecision($q->item_tot, $total_quantity_format);
                }
            }
            else {
                if ($q->loc_tot != null) {
                    $q->loc_tot = numberFormatPrecision($q->loc_tot, $total_quantity_format);
                }
                else if ($q->whse_tot != null) {
                    $q->whse_tot = numberFormatPrecision($q->whse_tot, $total_quantity_format);
                }
                else if ($q->item_tot != null) {
                    $q->item_tot = numberFormatPrecision($q->item_tot, $total_quantity_format);
                }
            }
        }

        $prev_data = [
            'item_num' => '',
            'lot_num' => '',
            'loc_num' => '',
            'whse_num' => '',
        ];

        $now = Carbon::now()->toDateTimeString();
        $date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, auth()->user()->timezone, 'H:i:s');

        if(request('cut_off_date')==null)
        {
            $cut_off_date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        }
        else{
            $cut_off_date = Carbon::createFromFormat(SiteSetting::getInputDateFormat(), request('cut_off_date'))->format('Y-m-d');
        }

        $headerHtml = view()->make('report.trans.transactionReport')->with('siteLogo', $site->logo)->with('siteLogoAzure', $site->logoazure)->with('companyName', $site->company_name)->render();
        $footerHtml = view()->make('report.trans.footer')->with('date',$date)->with('time',$time)->render();

        $pdf = PDF::loadView('report.inv.invbalance.print', compact('query1', 'request', 'prev_data', 'site'))
            ->setPaper('A4', 'portrait')
            ->setOption('header-html', $headerHtml)
            ->setOption('footer-html', $footerHtml)
            ->setOption('margin-bottom', 10)
            ->setOption('margin-top', 5)
            ->setOption('margin-right', 10)
            ->setOption('margin-left', 10)
            ->setOption("footer-right", "Page [page] of [topage]")
            ->setOption("footer-font-size", 8);

        return $pdf->stream('Inventory Balance as of '.$cut_off_date.'.pdf');
    }
}
