<table class="table table-bordered table-xs display nowrap" style="width:200%" id="JobOperHourReport">
    {{-- Get Cookies --}}
    <?php
        $username = auth()->user()->id."JobOperHourReport";
        $value = Cookie::get($username);
        $JobOperHourReportArr = json_decode($value);
        
        if(is_array($JobOperHourReportArr)){

        }else{
            $JobOperHourReportArr= ['job_num','suffix', 'job_status', 'item_num','item_desc','qty_released','qty_completed',
            'qty_scrapped','uom','setup_time','run_time','machine_time','tot','ave_labour_run_time_per_piece',
            'ave_machine_run_time_per_piece','ave_total_run_per_piece','plan_labour_per_piece','plan_machine_per_piece'];
        }
    ?>
    <thead>
        {{-- Display Dynamic header --}}
        <tr>
            @foreach($JobOperHourReportArr as $key=>$title)
                <input type="hidden" value={{$name="admin.label.".$title}}>
                @if($title == "job_status")
                    <th>{{__('admin.label.status')}}</th>
                @elseif($title == "item_desc")
                    <th>{{__('admin.label.item_descpt')}}</th>
                @elseif($title == "job_num" || $title == "suffix" || $title == "job_status" || $title == "item_num" || $title == "item_desc" || $title == "uom")
                    <th>{{__($name)}}</th>
                @else
                    <th class="dt-right">{{__($name)}}</th>
                @endif
            @endforeach
        </tr>
        <tr class="search-filter">
            <th><input style="width:75%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <!-- <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th> -->
            <th>
                <select style="width: 70%" class="form-control input-sm">
                    <option value=""></option>
                    <option value="O">Open</option>
                    <option value="C">Completed</option>
                    <option value="R">Released</option>
                </select>
            </th>
            <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:70%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:85%" type="text" class="form-control input-sm" placeholder="Search.."></th>
        </tr>
    </thead>
</table>

