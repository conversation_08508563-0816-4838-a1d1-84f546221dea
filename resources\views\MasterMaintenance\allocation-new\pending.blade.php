@extends('layout.app')

@section('head')
<style>
    .dt-right {
        text-align: right;
    }
</style>
@endsection

@section('content')
<div class="content-header row">
    <div class="content-body">
        <section id="basic-form-layouts">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title" id="basic-layout-colored-form-control">{{__('admin.title.added_order_lines')}}</h4>
                        <a class="heading-elements-toggle"><i class="icon-ellipsis font-medium-3"></i></a>
                        <div class="heading-elements">
                            <ul class="list-inline mb-0">
                                <li><a data-action="collapse"><i class="icon-minus4"></i></a></li>
                                <li><a data-action="expand"><i class="icon-expand2"></i></a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="card-body collapse in">
                        <div class="card-block">
                            <form action="{{route('allocation.allocate')}}" method="POST">
                                @csrf
                                <input hidden name="pending" value="true">
                                <div class="form-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <table id="pending" class="table table-bordered table-hover nowrap">
                                                <thead>
                                                    <tr>
                                                        <th>{{__('admin.label.object_type')}}</th>
                                                        <th>{{__('admin.label.whse_num')}}</th>
                                                        <th>{{__('admin.label.order_num')}}</th>
                                                        <th>{{__('admin.label.line_or_oper')}}</th>
                                                        <th>{{__('admin.label.release')}}</th>
                                                        <th>{{__('admin.label.item_num')}}</th>
                                                        <th>{{__('admin.label.item_desc')}}</th>
                                                        <th>{{__('admin.label.qty_required')}}</th>
                                                        <th>{{__('admin.label.qty_available')}}</th>
                                                        <th>{{__('admin.label.uom')}}</th>
                                                        <th>{{__('admin.label.due_date')}}</th>
                                                        <th>{{__('admin.label.product_code')}}</th>
                                                        <th>{{__('admin.label.cust_num')}}</th>
                                                        <th>{{__('admin.label.action')}}</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div style="text-align:center">
                                                <a href="{{route('allocation.add')}}">
                                                    <button type="button" class="btn btn-warning mr-1">
                                                        <i class="icon-arrow2"></i> {{__('admin.button.back')}}
                                                    </button>
                                                </a>
                                                <button type="button" id="deleteAll" class="btn btn-danger mr-1">
                                                    <i class="icon-trash"></i> {{__('admin.button.delete_all')}}
                                                </button>
                                                <button type="submit" class="btn btn-info mr-1" id="generate_allocation">
                                                    <i class="icon-check2"></i> {{__('admin.button.generate_allocation')}}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<script type="text/javascript">
    $(document).ready(function() {

        $("form").on('submit',function() {
            // Return error if no data is available
            if ($('#pending').DataTable().data().count() == 0) {
                Alert.warning("{{__('error.admin.no_data_available')}}");
                return false;
            }
        });

        // not used (might use later)
        // $("#generate_allocation").on('click',function() {
        //     // Loading icon            
        //     $(".pageloader").css('display','block');

        //     // Return error if no data is available
        //     if ($('#pending').DataTable().data().count() == 0) {
        //         Alert.warning("{{__('error.admin.no_data_available')}}");
        //         $(".pageloader").css('display','none');
        //         return false;
        //     }

        //     // Allocate
        //     $.ajax({
        //         url: "{{route('allocation.allocate')}}",
        //         type: 'POST',
        //         data: {
        //             pending: true,
        //             _token: "{{csrf_token()}}"
        //         },
        //         success: function(data) {
        //             if (data == 'true') {
        //                 $(".pageloader").css('display','none');
                        
        //                 // Reload DataTable
        //                 $('#pending').DataTable().draw();

        //                 // Alert Success
        //                 Swal.fire({
        //                     icon: 'success',
        //                     title: "{{__('success.Success') }}",
        //                     text: "{{__('success.processed', ['process' => __('admin.label.allocation')])}}",
        //                     showConfirmButton: true,
        //                     showCloseButton: false,
        //                     timer: 1500
        //                 });
        //             }
        //         },
        //         error: function(data) {
        //             $(".pageloader").css('display','none');
        //             Alert.warning(data.responseJSON.errors[0][0]);
        //         },
        //     });

        // });

        // Delete all pending data
        $('#deleteAll').on('click',function() {
            Swal.fire({
                title: 'Warning',
                text: "{{__('admin.message.sure')}}",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes'
            }).then((result) => {
                if (result.value) {
                    $.ajax({
                        url: "{{route('allocation.destroy')}}",
                        type: 'DELETE',
                        data: {
                            _token: "{{csrf_token()}}",
                        },
                        success: function(data) {
                            if (data == 'true') {
                                // Reload DataTable
                                $('#pending').DataTable().draw();
                            }
                        }
                    });
                }
            });
        });

        // Delete single pending data
        $('#pending').on('click','.delete',function() {
            $row_data = $('#pending').DataTable().row($(this).parents('tr')).data();
            $.ajax({
                url: "{{route('allocation.destroy')}}",
                type: 'DELETE',
                data: {
                    _token: '{{csrf_token()}}',
                    id: $row_data['id'],
                },
                success: function(data) {
                    if (data == 'true') {
                        // Reload DataTable
                        $('#pending').DataTable().draw(false); // set false to stay at current page; do this for serverside datatable
                        // $('#pending').DataTable().ajax.reload( null, false ); // do this for for clientside datatable
                    }
                },
            });
        });

        // Show pending datatable
        $('#pending').DataTable({
            scrollX: true, // enable scroll horizontal
            scrollY: 300,  // enable scroll vertical
            scrollCollapse: true,  // enable scroll vertical to match the table's height if it is smaller than scrollY
            processing: true, // loading bar
            serverSide: true,
            responsive: true,
            searching: false,
            ajax: {
                url: "{{route('allocation.pending')}}",
                dataSrc: function (json) {
                    var result = new Array();
                    for (i = 0; i < json.data.length; i++) {
                        result.push({
                            'id'  : json.data[i].id,
                            'order_type'  : json.data[i].order_type,
                            'whse_num' : json.data[i].whse_num,
                            'ref_id' : json.data[i].ref_id,
                            'ref_num' : json.data[i].ref_num,
                            'ref_line' : json.data[i].ref_line,
                            'ref_release' : json.data[i].ref_release,
                            'item_num' : json.data[i].item_num,
                            'item_desc' : json.data[i].item.item_desc,
                            'qty_required' : json.data[i].qty_required,
                            'qty_available' : json.data[i].qty_available,
                            'uom' : json.data[i].uom,
                            'due_date' : json.data[i].due_date,
                            'product_code' : json.data[i].item.product_code,
                            'cust_num' : json.data[i].cust_num,
                            'action' : '<button type="button" class="btn btn-danger delete" id="'+json.data[i].id+'"><i class="icon-trash"></i></button>',
                        });
                    }
                    return result;
                }
            },
            columns: [
                { data: 'order_type', name: 'order_type', width:"100px", orderable: false, },
                { data: 'whse_num', name: 'whse_num', width:"15%", orderable: false, },
                { data: 'ref_num', name: 'ref_num', width:"15%", orderable: false, },
                { data: 'ref_line', name: 'ref_line', width:"15%", orderable: false, },
                { data: 'ref_release', name: 'ref_release', width:"15%", orderable: false, },
                { data: 'item_num', name: 'item_num', width:"15%", orderable: false, },
                { data: 'item_desc', name: 'item_desc', width:"20%", orderable: false, },
                { data: 'qty_required', name: 'qty_required', width:"15%", orderable: false, className: 'dt-right'},
                { data: 'qty_available', name: 'qty_available', width:"15%", orderable: false, className: 'dt-right'},
                { data: 'uom', name: 'uom', width:"15%", orderable: false, },
                { data: 'due_date', name: 'due_date', width:"15%", orderable: false, },
                { data: 'product_code', name: 'product_code', width:"15%", orderable: false, },
                { data: 'cust_num', name: 'cust_num', width:"15%", orderable: false, },
                { data: 'action', name: 'action', width:"15%", orderable: false, },
            ],
        });
    });
</script>
@endsection
