<?php

namespace Tests\Feature\Incoming;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use App\Http\Controllers\Incoming\TransferOrderReceiptController;
use App\TransferLine;
use App\Item;
use App\TransferOrder;
use App\User;
use Illuminate\Support\Facades\Gate;
use Mockery;

class TransferOrderReceiptCatchWeightTest extends TestCase
{
    use RefreshDatabase;

    protected $controller;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new TransferOrderReceiptController();
        
        // Create a test user
        $this->user = User::factory()->create([
            'site_id' => 1,
            'name' => 'Test User'
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_shows_catch_weight_view_for_catch_weight_items()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasToReceipt')
            ->andReturn(true);

        // Create test data
        $item = Item::factory()->create([
            'item_num' => 'CW001',
            'item_desc' => 'Catch Weight Item',
            'catch_weight' => 1,
            'catch_weight_tolerance' => 5.0,
            'lot_tracked' => 1,
            'site_id' => 1
        ]);

        $transferOrder = TransferOrder::factory()->create([
            'trn_num' => 'TO001',
            'site_id' => 1
        ]);

        $transferLine = TransferLine::factory()->create([
            'trn_num' => 'TO001',
            'trn_line' => '1',
            'item_num' => 'CW001',
            'from_whse' => 'WH001',
            'to_whse' => 'WH002',
            'qty_shipped' => 100,
            'qty_received' => 0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Mock global functions
        $this->mockGlobalFunctions();

        $request = new Request([
            'trn_num' => 'TO001',
            'trn_line' => '1'
        ]);

        $response = $this->controller->receiveTransferOrder($request);

        // Assert that the catch weight view is returned
        $this->assertEquals('Receiving.toreceipt.process_cw', $response->getName());
        
        // Assert that required data is passed to the view
        $viewData = $response->getData();
        $this->assertArrayHasKey('to_details', $viewData);
        $this->assertArrayHasKey('batch_id', $viewData);
        $this->assertArrayHasKey('allow_over_receive', $viewData);
        $this->assertArrayHasKey('printLabel', $viewData);
        $this->assertArrayHasKey('printerOptions', $viewData);
        $this->assertArrayHasKey('unitQuantityFormat', $viewData);
    }

    /** @test */
    public function it_shows_regular_view_for_non_catch_weight_items()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasToReceipt')
            ->andReturn(true);

        // Create test data for non-catch weight item
        $item = Item::factory()->create([
            'item_num' => 'REG001',
            'item_desc' => 'Regular Item',
            'catch_weight' => 0,
            'lot_tracked' => 0,
            'site_id' => 1
        ]);

        $transferOrder = TransferOrder::factory()->create([
            'trn_num' => 'TO002',
            'site_id' => 1
        ]);

        $transferLine = TransferLine::factory()->create([
            'trn_num' => 'TO002',
            'trn_line' => '1',
            'item_num' => 'REG001',
            'from_whse' => 'WH001',
            'to_whse' => 'WH002',
            'qty_shipped' => 50,
            'qty_received' => 0,
            'uom' => 'EA',
            'site_id' => 1
        ]);

        // Mock global functions
        $this->mockGlobalFunctions();

        $request = new Request([
            'trn_num' => 'TO002',
            'trn_line' => '1'
        ]);

        $response = $this->controller->receiveTransferOrder($request);

        // Assert that the regular view is returned
        $this->assertEquals('Receiving.toreceipt.process', $response->getName());
    }

    /** @test */
    public function it_returns_404_when_user_lacks_permission()
    {
        // Mock Gate to deny permission
        Gate::shouldReceive('allows')
            ->with('hasToReceipt')
            ->andReturn(false);

        $request = new Request([
            'trn_num' => 'TO001',
            'trn_line' => '1'
        ]);

        $response = $this->controller->receiveTransferOrder($request);

        $this->assertEquals('errors.404', $response->getName());
        $this->assertEquals('error', $response->getData()['page']);
    }

    protected function mockGlobalFunctions()
    {
        // Mock global functions that are used in the controller
        if (!function_exists('generateBatchId')) {
            function generateBatchId($type) {
                return 'BATCH_' . time();
            }
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
