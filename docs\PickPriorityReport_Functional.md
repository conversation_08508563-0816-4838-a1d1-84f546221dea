# Pick Priority Report – Functional Specification

_Last updated: 08-Jul-2025_

## 1. Purpose
The **Pick Priority Report** provides warehouse supervisors with a real-time, prioritised list of customer-order lines awaiting picking.  It blends order urgency, stock availability and warehouse zone information so supervisors can efficiently assign pickers and create wave-pick batches.

## 2. Scope
* Module: Outbound → Reports → Pick Priority.
* Users: Picker Supervisor, Warehouse Manager (read-only).
* Environments: Desktop browser (Chrome/Edge), mobile tablet (Safari/Chrome).  Responsive design required.

## 3. Stakeholders & Roles
| Role | Responsibility |
|------|----------------|
| Picker Supervisor | Review report, assign pick tasks, export data. |
| Warehouse Manager | Monitor workload & stock, export data. |
| IT/Support | Maintain query performance, backups, access rights. |

## 4. Business Rules
1. Include **open** customer-order releases (`rel_status = 'O'`).
2. Only lines **still outstanding**: `qty_required = qty_released − qty_shipped > 0`.
3. Calculate **Shortage** = `qty_required − qty_available` (negative value not displayed; treat as 0).
4. Priority ordering:
   1. Earliest `due_date` (oldest first).
   2. Warehouse `zone_num` (null zones last).
   3. Lowest `shortage` (items in stock first).
5. Display default **location** & **zone** from `item_locs` → `locs` → `zones` (left joins; rows still appear if data missing).
6. **Authorisation**: Report is gated by `hasReport` (policy).  Guests denied.

## 5. Data Sources & Logic
```
coitems        (CustomerOrderItem)   => base lines
item_warehouses                       => qty_available by whse
item_locs                             => default item → location mapping
locs                                  => location metadata, zone link
zones                                 => logical picking zones
```

```sql
SELECT ci.*, IFNULL(iw.qty_available,0)   AS qty_available,
       (ci.qty_required - IFNULL(iw.qty_available,0)) AS shortage,
       il.loc_num, z.zone_num
FROM (
   SELECT id, co_num, co_line, co_rel, item_num, item_desc, whse_num,
          cust_num, cust_name, due_date,
          (qty_released - qty_shipped) AS qty_required
   FROM   coitems
   WHERE  rel_status = 'O'
   AND    (qty_released - qty_shipped) > 0
) ci
LEFT JOIN item_warehouses iw ON iw.item_num = ci.item_num AND iw.whse_num = ci.whse_num
LEFT JOIN item_locs       il ON il.item_num = ci.item_num AND il.whse_num = ci.whse_num
LEFT JOIN locs             l ON l.loc_num  = il.loc_num  AND l.whse_num  = il.whse_num
LEFT JOIN zones            z ON z.id       = l.zone_id
ORDER BY ci.due_date, z.zone_num IS NULL, z.zone_num, shortage;
```

## 6. UI / UX Requirements
| Column           | Header Text  | Format / Notes                 |
|------------------|--------------|--------------------------------|
| co_num           | CO Num       | Link to order (future)         |
| co_line          | Line         | numeric                        |
| co_rel           | Rel          | integer                        |
| item_num         | Item         | link to item master (future)   |
| item_desc        | Description  | 35-char truncate               |
| cust_name        | Customer     |                                |
| zone_num         | Zone         | blank if null                  |
| loc_num          | Location     |                                |
| shortage         | Shortage     | 2-dp, negative → 0             |
| due              | Due Date     | dd/mm/yyyy                     |
| qty_required     | Qty Required | 2-dp                           |
| qty_available    | Qty Avail.   | 2-dp                           |
| whse_num         | Warehouse    | 3-char                         |

Additional UI specifications:
* **Table wrapper**: Bootstrap card (`bg-info` header).
* **DataTables** settings: processing, serverSide, fixedHeader, lengthMenu [25, 50, 100, All].
* **Export buttons**: Copy, CSV, Excel, PDF.
* **Filters** (phase-2): Warehouse dropdown, Zone dropdown, date-range picker.
* **Responsive**: Column priority set on `item_desc` (low) and `cust_name` (low) for mobile collapse.

## 7. Non-Functional Requirements
| NFR | Requirement |
|-----|-------------|
| Performance | Server response < 2 s for 10 000 open lines.  Indexes on `coitems.rel_status`, `coitems.due_date`, `item_warehouses(item_num,whse_num)`. |
| Availability | 99.5 % (24/7). |
| Security | HTTPS enforced in production; access via Laravel policies. |
| Audit | Laravel log channel stores query & export events. |

## 8. Error Handling & Edge Cases
* Missing `item_warehouses` row → `qty_available = 0`.
* Missing `zones` link → Zone column blank; still displayed.
* Concurrent pick allocation: supervisors refresh page to get latest shortage status.

## 9. Future Enhancements
1. Action column to _Allocate Picker_ directly.
2. Coloured row highlights: red (over-due), amber (due today), green (due ≥ tomorrow + 2 days).
3. REST API endpoint for mobile scanner apps.
4. KPI widget: total lines per zone, per due-date bucket.

---
**Author:** Dev Team – ICAPT SaaS WMS
