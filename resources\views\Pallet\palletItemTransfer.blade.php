@extends('layout.mobile.app')
@section('content')
@section('title', __('Pallet Transfer'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="postpallettransfer" name="postpallettransfer"
            action="{{ route('PalletTransferProcess') }}" method="POST">
             <input type="hidden" name="batch_id" value="{{ $batch_id }}">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                <div class="form-group row">
                    <label
                        class="col-xs-3 col-md-2 col-lg-3 label-control required ">{{ __('mobile.label.lpn') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="from_lpn" maxlength="30" id="from_lpn"
                                class="form-control border-primary" value=""
                                placeholder="{{ __('mobile.placeholder.lpn') }}" required>
                            <span id="checkLpnNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" name="{{ __('mobile.list.lpn') }}"
                            onClick="selection('/getLPNListNotTransit','whse_num,from_lpn','lpn_num','from_lpn');modalheader(this.id,this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                        for="whse_num">{{ __('mobile.label.whse_num') }}</label>
                    {{-- @if ($tparm == 1)
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" readonly name="whse_num" id="whse_num" value="" class="form-control border-primary" placeholder="{{__('admin.label.whse_num')}}">
                            </div>
                        </div> --}}
                    {{-- <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{__('admin.label.whse_num')}}" onClick="selection('/getWhse','whse_num','whse_num','whse_num');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </div> --}}
                    {{-- @else --}}
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="whse_num" id="whse_num" value=""
                                class="form-control border-primary" readonly>
                        </div>
                    </div>
                    {{-- @endif --}}
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                        for="location">{{ __('mobile.label.loc_num') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="from_loc" id="from_loc" value=""
                                class="form-control border-primary" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group row" id="item_num_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" maxlength="30" name="item_num" id="item_num" value=""
                                class="form-control border-primary" placeholder="{{ __('mobile.label.item_num') }}"
                                required>
                            <span id="checkItemNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" name="{{ __('mobile.list.item_num') }}"
                            onClick="selection('/getPalletItemList','from_lpn','item_num','item_num');modalheader(this.id,this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="item_desc_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control" for="item_desc"></label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <textarea id="item_desc" tabindex="5" class="form-control border-primary" rows="2" name="item_desc" readonly></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row" id="lot_num_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" maxlength=50 name="lot_num" id="lot_num" value=""
                                class="form-control border-primary" placeholder="{{ __('mobile.label.lot_num') }}"
                                required>
                            <span id="checkLotNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="Lot" id="lotbtn"
                            onclick="selectionMultiLineInput('/getLotNumPallet','whse_num,from_loc,item_num,from_lpn,sortField,sortBy','lot_num','lot_num');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="qty_contained_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                        for="qty_contained">{{ __('mobile.label.qty_contained') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="qty_contained" id="qty_contained"
                                class="form-control border-primary" value=""
                                placeholder="{{ __('mobile.label.qty_contained') }}" readonly />
                            <span class="input-group-btn" style="width:0px;"></span>
                            <input type="text" name="base_uom" id="base_uom" class="form-control border-primary"
                                value="" placeholder="{{ __('mobile.label.uom') }}" readonly />
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label
                        class="col-xs-3 col-md-2 col-lg-3 label-control required ">{{ __('mobile.label.to_lpn') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="lpn_num" maxlength="30" id="to_lpn"
                                class="form-control border-primary" value=""
                                placeholder="{{ __('mobile.label.to_lpn') }}" required>
                            <span id="checkToLpnNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" name="{{ __('mobile.label.to_lpn') }}"
                            onClick="selection('/getLPNwithLocList','from_lpn,from_loc','lpn_num','to_lpn');modalheader(this.id,this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                        for="to_loc">{{ __('mobile.label.to_loc') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="loc_num" id="to_loc" value=""
                                class="form-control border-primary" placeholder="{{ __('mobile.label.to_loc') }}"
                                readonly>
                        </div>
                    </div>
                </div>





                <div class="form-group row" id="qty_transfer">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="qty_transfer">{{ __('mobile.label.qty_transfer') }}</label>
                    <div class="col-xs-5 col-md-5 col-lg-5">
                        <div class="input-group">
                            <input type="text" inputmode="numeric" name="qty_input" id="qty_to_transfer"
                                class="form-control border-primary number-format" value=""
                                placeholder="{{ __('mobile.label.qty_transfer') }}" required />
                            <input type="hidden" name="max_qty_input" id="max_qty_input" value="">
                            <input type="hidden" name="conv_factor" id="conv_factor" value="1">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" maxlength="30" required name="uom" id="uom"
                            class="form-control border-primary" value=""
                            placeholder="{{ __('mobile.label.uom') }}" required />
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="UOMs"
                            onclick="selection('/getItemUOMConv','item_num','uom','uom');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>













                <div class="form-actions center">
                    <button type="submit" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.process') }}
                    </button>

                </div>
            </div>
        </form>
        @include('errors.maxchar')
    </div>
</div>

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

@include('util.validate_uom')

<script>
    $(document).ready(function() {
        $("#lot_num_field").attr('hidden', true);

        var qtycontaind = $('#qty_contained').val();
        $("#qty_to_transfer").attr("disabled", true);



        $("#whse_num").on("change", function() {
            if ($("#whse_num").val() != '') {
                ajaxurl = "{{ route('validateWhse', 'whse_num') }}";
                url = ajaxurl.replace('whse_num', btoa($("#whse_num").val()));

                $.get(url, function(data) {
                    if (data == 'not exist') {
                        $("#whse_num").val('');
                        $("#from_loc").val('');
                        $("#from_lpn").val('');
                        Alert.notexist('{{ __('admin.label.whse_num') }}', $("#whse_num")
                            .val());
                    } else {
                        $("#from_loc").val('');
                        $("#from_lpn").val('');
                    }
                });
            }
        });

        $("#lot_num").on("change", function() {

            if ($("#lot_num").val() != "") {
                // Send error if manually type object that is not exist or inactive

                ajaxurl = "{{ route('checkLotNumLPN', ['item_num', 'lpn_num', 'lot_num']) }}";

                url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
                url = url.replace('lpn_num', btoa($("#from_lpn").val()));
                url = url.replace('lot_num', btoa($("#lot_num").val()));

                $.get(url, function(data) {
                    console.log(data);
                    if (data == 'not exist') {
                        $("#checkLotNo").html(
                            '<span style="color:red;"> {{ __('error.mobile.lot_notexists') }}</span>'
                        );
                        // Alert.notexist('{{ __('admin.label.lot_num') }}', $("#lot_num").val());
                        $("#qty_contained").val('');
                        $("#base_uom").val('');
                        $("#uom").val('');
                        $("#checkLot").html('');
                        $("#lot_num").val('');
                        $("#qty_to_transfer").attr("disabled", true);
                        $("#qty_to_transfer").val('');
                        $("#max_qty_input").val('');

                    } else {
                        $("#qty_contained").val(data.qty_contained);
                        $("#max_qty_input").val(data.qty_contained);
                        $("#base_uom").val(data['uom']);
                        $("#uom").val(data['uom']);
                        $("#qty_to_transfer").attr("disabled", false);
                        $("#checkLotNo").html('');
                        console.log(data);
                    }
                });




                /*$.ajax({
                    url: '{{ route('checkLotNumLPN') }}',
                    type: "GET",
                    data: {
                       // whse_num: $("#whse_num").val(),
                        loc_num: btoa($("#from_loc").val()),
                        item_num:btoa($("#item_num").val()),
                        //lot_num: $("#lot_num").val(),
                        lpn_num: btoa($("#lpn_num").val()),
                    },
                    success: function(data){
                        console.log(data)
                        // console.log(data,'hfwiuf',$("#whse_num").val(),$("#from_loc").val(),$("#item_num").val(),$("#lot_num").val());
                        if (data == "not exist") {
                            Alert.notexist('{{ __('admin.label.lot_num') }}', $("#lot_num").val());
                            //$("#checkLot").html('<span style="color:red;"> {{ __('error.mobile.lot_notexists') }}</span>');
                            $("#lot_num").val('');
                            $("#qty_contained").val("");
                            $("#base_uom").val("");
                            $("#uom").val("");
                        }
                        else
                        {
                            $("#qty_contained").val(data['qty_contained']);
                            $("#base_uom").val(data['uom']);
                            $("#uom").val(data['uom']);
                            $("#checkLot").html('');
                        }

                    }
                });*/
            } else {
                $("#qty_contained").val("");
                $("#base_uom").val("");
                $("#uom").val("");
                $("#checkLot").html('');
            }

        });

        $("#loc_num").on("change", function() {
            if ($("#loc_num").val() != '') {
                ajaxurl = "{{ route('checkLocLpn', ['whse_num', 'loc_num']) }}";
                url = ajaxurl.replace('whse_num', $("#whse_num").val());
                url = url.replace('loc_num', $("#loc_num").val());

                $.get(url, function(data) {
                    if (data == 'not exist') {
                        Alert.notexist('{{ __('admin.label.loc_num') }}', $("#loc_num").val());
                        $("#loc_num").val('');
                        $("#lpn_num").val('');
                        $("#item_count").val('');
                    }
                });
            }
        });

        // $("#qty_to_transfer").on("keyup", function(){
        //     calculateQtyLimit($("#base_uom").val(), $("#qty_contained").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#to_loc").val(), $("#lot_num").val(), "", "", "", "");
        // });

        $("#uom").on("change", function() {
            // ajaxurl ="{{ route('getItemUOMConv', 'item_num') }}";
            // url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
            var selectuom = $("#uom").val();
            // var uomConv = [];
            if (selectuom != "") {

                let validate = validateConvUOM($('#item_num').val(), null, null, null, $('#base_uom')
                    .val(), selectuom);

                validate.then(function(resp) {
                        // true
                    },
                    function(err) {
                        // false
                        $("#qty_input").val('');
                        $("#uom").val($("#base_uom").val());
                    }).finally(function() {
                        $("#qty_to_transfer").val('');
                        $("#qty_to_transfer-error").hide();
                    calculateQtyLimit($("#base_uom").val(), $("#qty_contained").val(), $("#uom")
                        .val(), $("#item_num").val(), $("#whse_num").val(), $("#to_loc")
                        .val(), $("#lot_num").val(), "", "", "", "", "");
                });

                // $.get(url, function(data){
                //     data.forEach(element => {
                //         uomConv.push(element['uom']);
                //     });

                //     if(jQuery.inArray(selectuom, uomConv) != -1){
                //         calculateQtyLimit($("#base_uom").val(), $("#qty_contained").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#to_loc").val(), $("#lot_num").val(), "", "", "", "", "");
                //     }
                //     else{
                //         $("#qty_input").val('');
                //         $("#uom").val($("#base_uom").val());
                //         Alert.notexist('{{ __('admin.label.uom') }}', $("#uom").val());
                //     }
                // });
            }
        });

        // $("#qty_to_transfer").on("change", function(){
        //     if($("#qty_to_transfer").val() != ''){
        //         var qty_contained =  $("#qty_contained").val();
        //         var qty_to_transfer =  $("#qty_to_transfer").val();
        //         console.log(qty_contained + ' -- ' + qty_to_transfer);
        //         if(qty_to_transfer > qty_contained )
        //         {
        //             Alert.warning('Qty to transfer can not more than Qty Contained');
        //             $("#qty_to_transfer").val('');
        //         }
        //     }
        // });


        $("#from_lpn").on("change", function() {
            if ($("#from_lpn").val() != '') {
                var whse_num = $("#whse_num").val();
                ajaxurl = "{{ route('getPalletDetails', ['lpn_num', 'whse_num']) }}";
                url = ajaxurl.replace('lpn_num', btoa($("#from_lpn").val()));
                url = url.replace('whse_num', whse_num);

                $.get(url, function(data) {
                    console.log("Received data from the server:", data);
                    if (data == 'not exist') {
                        $("#checkLpnNo").html(
                            '<span style="color:red;"> {{ __('error.mobile.lpn_notexists') }}</span>'
                        );
                        // Alert.notexist('{{ __('LPN') }}', $("#from_lpn").val());
                        $("#from_lpn").val('');
                    } else if (data == 'picked') {
                        $("#checkLpnNo").html(
                            '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>'
                        );
                        // Alert.warning('Pallet status is not Open.');
                        $("#lpn_num").val('');
                    } else if (data == 'transfer') {
                        $("#checkLpnNo").html(
                            '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>'
                        );
                        // Alert.warning('Pallet status is not Open.');
                        $("#lpn_num").val('');
                    } else {
                        $("#whse_num").val(data['whse_num']);
                        $("#from_loc").val(data['loc_num']);
                        $("#checkLpnNo").html('');
                    }
                });
            } else {
                $("#from_lpn").val('');
            }
        });

        $("#to_lpn").on("change", function() {
            if ($("#to_lpn").val() != '') {
                if ($("#to_lpn").val() != $("#from_lpn").val()) {
                    ajaxurl =
                        "{{ route('getCheckLPNwithLocList', ['lpn_num', 'loc_num', 'item_num']) }}";
                    url = ajaxurl.replace('lpn_num', btoa($("#to_lpn").val()));
                    url = url.replace('loc_num', btoa($("#from_loc").val()));
                    url = url.replace('item_num', btoa($("#item_num").val()));
                    //alert(url);
                    var fromlpn = $("#from_loc").val();
                    $.get(url, function(data) {
                        console.log(data);
                        if (data == 'not exist' || fromlpn == "") {
                            $("#checkToLpnNo").html(
                                '<span style="color:red;"> {{ __('error.mobile.To_lpn_notexists') }}</span>'
                            );
                            // Alert.notexist('{{ __('LPN') }}', $("#to_lpn").val());
                            $("#to_lpn").val('');
                            $("#to_loc").val('');
                        } else if (data == 'single item') {
                            $("#checkToLpnNo").html(
                                '<span style="color:red;"> {{ __('mobile.message.lpn_restricted_single_item') }}</span>'
                            );
                            // Alert.warning('To LPN is restricted to single item only. Move a different item in this Pallet is not allowed.');
                            $("#to_lpn").val('');
                            $("#to_loc").val('');
                        } else if (data == 'picked') {
                            $("#checkToLpnNo").html(
                                '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>'
                            );
                            // Alert.warning('Pallet status is not Open.');
                            $("#to_lpn").val('');
                        } else if (data == 'transfer') {
                            $("#checkToLpnNo").html(
                                '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>'
                            );
                            // Alert.warning('Pallet status is not Open.');
                            $("#to_lpn").val('');
                        } else {
                            $("#to_loc").val($("#from_loc").val());
                            $("#checkToLpnNo").html('');
                        }
                    });
                } else {
                    $("#checkToLpnNo").html(
                        '<span style="color:red;"> {{ __('mobile.message.to_lpn_same_error') }}</span>'
                    );
                    // Alert.warning('Can not transfer to the same LPN');
                    $("#to_lpn").val('');
                    $("#to_loc").val('');
                }
            } else {
                $("#to_lpn").val('');
                $("#to_loc").val('');
            }
        });

        $("#item_num").on("change", function() {
            if ($("#item_num").val() != '') {
                ajaxurl = "{{ route('getPalletItemDetails', ['lpn_num', 'item_num']) }}";
                url = ajaxurl.replace('lpn_num', btoa($("#from_lpn").val()));
                url = url.replace('item_num', btoa($("#item_num").val()));

                $.get(url, function(data) {
                    console.log("Received data from the server:", data);
                    if (data == 'not exist') {
                        $("#checkItemNo").html(
                            '<span style="color:red;"> {{ __('error.mobile.itemnotexist') }}</span>'
                        );
                        // Alert.notexist('{{ __('Item') }}', $("#item_num").val());
                        $("#item_num").val('');
                        $("#item_desc").val('');
                        $("#qty_contained").val('');
                        $("#base_uom").val('');
                        $("#uom").val('');
                    } else if (data == 'inactive') {
                        $("#checkItemNo").html(
                            '<span style="color:red;"> {{ __('error.admin.error_inactive_item') }}</span>'
                        );
                        // Alert.inactive('{{ __('admin.label.item') }}',$("#item_num").val());
                        $("#item_num").val('');
                    } else {
                        $("#checkItemNo").html('');
                        display('/displayItemDesc', 'item_num', 'item_desc');
                        if (data['lot_tracked'] == 0) {
                            $("#qty_contained").val(data['qty_contained']);
                            $("#base_uom").val(data['uom']);
                            $("#uom").val(data['uom']);
                            $("#max_qty_input").val(data['qty_contained']);

                            $("#lot_num_field").attr('hidden', true);
                            $("#qty_to_transfer").attr("disabled", false);
                            $("#qty_to_transfer").val('');
                        } else {
                            // Check Lot {whse}/{loc}/{item_num}
                            ajaxurlchecklot =
                                "{{ route('getLotNum', ['whse', 'loc_num', 'item_num']) }}";
                            urlcheckLot = ajaxurlchecklot.replace('whse', btoa($("#whse_num")
                                .val()));
                            urlcheckLot = urlcheckLot.replace('loc_num', btoa($("#from_loc")
                                .val()));
                            urlcheckLot = urlcheckLot.replace('item_num', btoa($("#item_num")
                                .val()));
                            $.get(urlcheckLot, function(data) {
                                if (data[0]['lot_num']) {
                                    $("#lot_num_field").attr('hidden', false);
                                    //$("#qty_to_transfer").attr("disabled", false);
                                    // $("#qty_to_transfer").val('');
                                }
                            });
                        }
                    }
                });
            } else {
                $("#qty_contained").val("");
                $("#base_uom").val("");
                document.getElementById('item_num').value = "";
                document.getElementById('item_desc').value = "";
                document.getElementById('lot_num').value = "";
                document.getElementById('qty_to_letdown').value = "";
                document.getElementById('qty_contained').value = "";
                document.getElementById('base_uom').value = "";
                document.getElementById('uom').value = "";
            }
        });

    });

    function showNewLot() {
        $("#loc_info").html('');
        $("#expiry_date_info").html("");
        $("#expiry_date").prop('readonly', false);

        if ($("#lot_num").val()) {

            ajaxurl = "{{ route('lotitemv', ['lot_num', 'item_num', 'whse_num']) }}";
            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
            url = url.replace('item_num', btoa($("#item_num").val()));
            url = url.replace('whse_num', btoa($("#whse_num").val()));

            $.get(url, function(data) {
                if (data == 'not exist') {
                    $("#loc_info").html(
                        '<i class="icon-info"></i> <small>{{ __('mobile.message.new_lot_location') }}</small>'
                    );
                }
            });

            $.ajax({
                url: '{{ route('lot_item_expiry_date_exist') }}',
                type: "GET",
                data: {
                    lot_num: $("#lot_num").val(),
                    item_num: $("#item_num").val(),
                },
                success: function(data) {
                    if (data) {
                        $("#expiry_date").val(data);
                        $("#expiry_date").prop('readonly', true);
                    } else {
                        $("#expiry_date").prop('readonly', false);
                        $("#expiry_date").val($("#expiry_date").data('date-default-shelf-life'));
                    }
                }
            });
        }
        $("#lot_num").focus();
    }


    function palletValidation() {
        ajaxurl = "{{ route('getPalletDetails', ['lpn_num']) }}";
        url = ajaxurl.replace('lpn_num', btoa($("#from_lpn").val()));
        url = url.replace('whse_num', whse_num);

        return $.get(url, function(data) {
            if (data == 'not exist') {
                $("#checkLpnNo").html(
                    '<span style="color:red;"> {{ __('error.mobile.lpn_notexists') }}</span>');
                $("#from_lpn").val('');
            } else if (data == 'picked') {
                $("#checkLpnNo").html(
                    '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>');
                $("#lpn_num").val('');
            } else if (data == 'transfer') {
                $("#checkLpnNo").html(
                    '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>');
                $("#lpn_num").val('');
            } else {
                $("#whse_num").val(data['whse_num']);
                $("#from_loc").val(data['loc_num']);
            }
        });
    }

    function checkLPNLocList() {
        ajaxurl = "{{ route('getCheckLPNwithLocList', ['lpn_num', 'loc_num', 'item_num']) }}";
        url = ajaxurl.replace('lpn_num', btoa($("#to_lpn").val()));
        url = url.replace('loc_num', btoa($("#from_loc").val()));
        url = url.replace('item_num', btoa($("#item_num").val()));

        var fromlpn = $("#from_loc").val();

        return $.get(url, function(data) {
            if (data == 'not exist' || fromlpn == "") {
                $("#checkToLpnNo").html(
                    '<span style="color:red;"> {{ __('error.mobile.To_lpn_notexists') }}</span>');
                $("#to_lpn").val('');
                $("#to_loc").val('');
            } else if (data == 'single item') {
                $("#checkToLpnNo").html(
                    '<span style="color:red;"> {{ __('mobile.message.lpn_restricted_single_item') }}</span>'
                );
                $("#to_lpn").val('');
                $("#to_loc").val('');
            } else if (data == 'picked') {
                $("#checkToLpnNo").html(
                    '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>');
                $("#to_lpn").val('');
            } else if (data == 'transfer') {
                $("#checkToLpnNo").html(
                    '<span style="color:red;"> {{ __('mobile.message.lpn_not_open') }}</span>');
                $("#to_lpn").val('');
            } else {
                $("#to_loc").val($("#from_loc").val());
            }
        })
    }

    function checkLotNumLpn(on_submit = false) {
        ajaxurl = "{{ route('checkLotNumLPN', ['item_num', 'lpn_num', 'lot_num']) }}";
        url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
        url = url.replace('lpn_num', btoa($("#from_lpn").val()));
        url = url.replace('lot_num', btoa($("#lot_num").val()));

        return $.get(url, function(data) {
            if (data == 'not exist') {
                $("#checkLotNo").html(
                    '<span style="color:red;"> {{ __('error.mobile.lot_notexists') }}</span>');
                // Alert.notexist('{{ __('admin.label.lot_num') }}', $("#lot_num").val());
                if (!on_submit) {
                    $("#qty_contained").val('');
                    $("#base_uom").val('');
                    $("#uom").val('');
                    $("#checkLot").html('');
                    $("#lot_num").val('');
                    $("#qty_to_transfer").attr("disabled", true);
                    $("#qty_to_transfer").val('');
                    $("#max_qty_input").val('');
                }


            } else {
                $("#qty_contained").val(data.qty_contained);
                $("#max_qty_input").val(data.qty_contained);
                $("#base_uom").val(data['uom']);
                $("#uom").val(data['uom']);
                $("#qty_to_transfer").attr("disabled", false);
                console.log(data);
            }
        })
    }

    function getPalletItemDetails(on_submit = false) {
        ajaxurl = "{{ route('getPalletItemDetails', ['lpn_num', 'item_num']) }}";
        url = ajaxurl.replace('lpn_num', btoa($("#from_lpn").val()));
        url = url.replace('item_num', btoa($("#item_num").val()));

        return $.get(url, function(data) {
            console.log("Received data from the server:", data);
            if (data == 'not exist') {
                $("#checkItemNo").html(
                    '<span style="color:red;"> {{ __('error.mobile.itemnotexist') }}</span>');
                // Alert.notexist('{{ __('Item') }}', $("#item_num").val());
                $("#item_num").val('');
                $("#item_desc").val('');
                $("#qty_contained").val('');
                $("#base_uom").val('');
                $("#uom").val('');
            } else if (data == 'inactive') {
                $("#checkItemNo").html(
                    '<span style="color:red;"> {{ __('error.admin.error_inactive_item') }}</span>');
                // Alert.inactive('{{ __('admin.label.item') }}',$("#item_num").val());
                $("#item_num").val('');
            } else {
                // $("#checkItemNo").html('');
                if (!on_submit) {
                    display('/displayItemDesc', 'item_num', 'item_desc');
                    if (data['lot_tracked'] == 0) {
                        $("#qty_contained").val(data['qty_contained']);
                        $("#base_uom").val(data['uom']);
                        $("#uom").val(data['uom']);
                        $("#max_qty_input").val(data['qty_contained']);

                        $("#lot_num_field").attr('hidden', true);
                        $("#qty_to_transfer").attr("disabled", false);
                        $("#qty_to_transfer").val('');
                    } else {
                        // Check Lot {whse}/{loc}/{item_num}
                        ajaxurlchecklot = "{{ route('getLotNum', ['whse', 'loc', 'item_num']) }}";
                        urlcheckLot = ajaxurlchecklot.replace('whse', btoa($("#whse_num").val()));
                        urlcheckLot = urlcheckLot.replace('loc', btoa($("#from_loc").val()));
                        urlcheckLot = urlcheckLot.replace('item_num', btoa($("#item_num").val()));
                        $.get(urlcheckLot, function(data) {
                            // console.log(data[0]['lot_num']);
                            if (data[0]['lot_num']) {
                                $("#lot_num_field").attr('hidden', false);
                                //$("#qty_to_transfer").attr("disabled", false);
                                // $("#qty_to_transfer").val('');
                            }
                        });
                    }
                }

            }
        })
    }

    $("#postpallettransfer").on("invalid-form.validate", function(event, validator) {
        var errors = validator.numberOfInvalids();
        if (errors) {
            $(".submitloader").attr('disabled', false);
        }
    });

    $("#postpallettransfer").validate({
        onchange: true,
        submitHandler: function(form) {
            $(".submitloader").attr('disabled',true);

            let promise = [];

            promise.push(palletValidation());
            promise.push(checkLPNLocList());
            if ($("#lot_num").val() != "") {
                promise.push(checkLotNumLpn(true));
            }

            promise.push(getPalletItemDetails(true));

            Promise.all(promise).then(function() {
                $(".pageloader").css("display", "block");
                $(".submitloader").attr("disabled", true);
                setTimeout( function () {
                    form.submit();
                }, 300);
            });
        },
        rules: {

            item_num: {
                required: true,
                // remote:{
                //     url: "{{ route('WarehouseItemValidation') }}",
                //     type: "post",
                //     data: {
                //         _token : $('input[name="_token"]').val(),
                //         whse_num:
                //             function() {
                //                 return $("#whse_num").val();
                //             },
                //     }
                // }
            },
            uom: {
                required: true,
                uom_validation: function() {
                    return [$('#item_num').val(), null, null, null, $('#base_uom').val()];
                }
                // remote:{
                //         url: "{{ route('MiscValidation') }}",
                //         type: "post",
                //         data: { _token : $('input[name="_token"]').val() }
                //     }
            },
            qty_input: {
                required: true,
                number: true,
                number_size: true,
                min_value: 0.1,
                max_value: function() {
                    var qty_receivable = parseFloat($("#max_qty_input").val().replace(/,/g, ""));
                    return Math.max(0, qty_receivable);
                }
            },

        },
        messages: {

            // item_num:{
            //     remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.item_num')]) }}"
            // },
            uom: {
                remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
            },
            qty_input:{
                required: "{{ __('error.mobile.required', ['resource' => __('mobile.label.qty_transfer') ]) }}",
                number_size: "{{__('error.mobile.max_characters')}}",
                min_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty_transfer') ]) }} {0} ",
                max_value: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.label.qty_transfer') ]) }} {0} ",
            },
        }
    });
</script>



@include('Pallet.palletMobileValidation')
@include('util.selection')
@include('util.convert_alternade_barcode_to_item')

@endsection
