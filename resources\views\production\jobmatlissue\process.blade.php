@extends('layout.mobile.app')
@section('content')
@section('title', __('Job Material Issue Details'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 6px;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="jobmatlissueform" name="jobmatlissueform"
            action="{{ route('jobMatlProcess') }}" method="post">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                <div class="form-group row">
                    <input type="hidden" value="{{ $joborder->job->whse_num }}" name="whse_num" id="whse_num">
                    <input type="hidden" value="{{ $tparm_issue_location }}" name="tparm_issue_location"
                        id="tparm_issue_location">
                    <input type="hidden" value="JobMaterialIssue" name="form_name" id="form_name">

                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                        for="job_num">{{ __('mobile.label.job_num') }}</label>
                    <div class="col-xs-5 col-md-4 col-lg-4">
                        <div class="input-group">
                            <input type="text" name="ref_num" id="job" class="form-control border-primary"
                                value="{{ $joborder->job_num }}" readonly>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" name="ref_line" id="oper_num" class="form-control border-primary"
                            value="{{ $joborder->oper_num }}" readonly>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-1" style=" padding:0px;">
                        <input type="text" name="ref_release" id="sequence" class="form-control border-primary"
                            value="{{ $joborder->sequence }}" readonly>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="suffix">{{ __('mobile.label.suffix') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="suffix" id="suffix" class="form-control border-primary"
                                value="{{ $joborder->suffix }}" placeholder="Suffix" readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="material">{{ __('mobile.label.material') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="item_num" id="item_num"
                                class="equalfrom form-control border-primary" value="{{ $joborder->matl_item }}"
                                readonly>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="item_desc"> &nbsp;</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <textarea class="form-control border-primary" name="description" id="description" cols="4" rows="2"
                                readonly>{{ $joborder->matl_desc }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_open">{{ __('mobile.label.qty_open') }}</label>
                    <div class="col-xs-7 col-md-5 col-lg-5">
                        <div class="input-group">
                            <input type="text" name="qty_balance" id="qty_balance" style="text-align:right"
                                value="{{ numberFormatPrecision($joborder->qty_balance, $unit_quantity_format) }}"
                                class="form-control border-primary" readonly>
                            <input type="hidden" name="qty_balance_conv" id="qty_balance_conv"
                                value="{{ $joborder->qty_balance }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" name="ref_uom" id="ref_uom" class="form-control border-primary"
                            value="{{ $joborder->uom }}" readonly>
                        <input type="hidden" name="base_uom" id="base_uom" class="form-control border-primary"
                            value="{{ $joborder->uom }}">

                    </div>
                </div>
                @if ($issue_by_item_base_uom == 1)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="base_uom"></label>
                        <div class="col-xs-7 col-md-5 col-lg-5">
                            <div class="input-group">
                                <input type="text" name="qty_base_uom" id="qty_base_uom" style="text-align:right"
                                    value="{{ numberFormatPrecision($base_uom['total_amount'], $unit_quantity_format) }}"
                                    class="form-control border-primary" readonly>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input type="text" name="base_uomd" id="base_uomd"
                                class="form-control border-primary" value="{{ $base_uom['uom'] }}" readonly>
                        </div>
                    </div>
                @endif
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="material">{{ __('mobile.label.material') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="citem" id="citem"
                                class="equalto form-control border-primary"
                                data-msg-equalto="{{ __('error.mobile.not_item_match', ['resource' => __('mobile.label.material')]) }}"
                                placeholder="{{ __('mobile.placeholder.material') }}" required>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            {{-- If disable_lot_number_selection is 1 and item is lot tracked, it will be readonly --}}
                            <input type="text" @if ($disable_lot_number_selection == 1 && $joborder->item->lot_tracked == 1) readonly @endif name="loc_num"
                                id="loc_num" class="form-control border-primary" value="{{ old('loc_num') }}"
                                data-value="{{ $defaults['loc_num'] }}" onchange="onLocChange()"
                                placeholder="{{ __('mobile.placeholder.loc_num') }}">
                            <span id="checkLoc"></span>
                        </div>
                    </div>
                    {{-- If item is not lot tracked || item is lot tracked and disable_lot_number_selection is 0, this button will be shown --}}
                    @if ($joborder->item->lot_tracked == 0 || ($joborder->item->lot_tracked == 1 && $disable_lot_number_selection == 0))
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.locations') }}"
                                onClick= "selectionNull('/getIssueLocation','form_name,tparm_issue_location,whse_num,item_num,loc_num','loc_num','loc_num');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" required data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif
                </div>

                @if ($joborder->item->lot_tracked == 1)
                    <div class="form-group row" id="trlotconfirm">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                {{-- If disable_lot_number_selection is 1, it will be readonly and predefined value --}}
                                <input type="text"
                                    @if ($disable_lot_number_selection == 1) data-value="{{ $defaults['lot_num'] }}" readonly
                                    @elseif($defaults['lot_num'])
                                        data-value="{{ $defaults['lot_num'] }}" @endif
                                    name="lot_num" id="lot_num" value="{{ old('lot_num') }}"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.lot_num') }}" onchange="onLocChange();"
                                    maxlength="50">

                            </div>
                        </div>
                        {{-- If disable_lot_number_selection is 0, this button will be shown --}}
                        @if ($disable_lot_number_selection == 0)
                            <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                                <button type="button" name="{{ __('mobile.list.lots') }}"
                                    onCLick= "selectionMultiLineInput('/getLotLocExpiry','whse_num,loc_num,item_num,sortField,sortBy,tparm_issue_location,form_name','lot_num','loc_num,lot_num');modalheader_uom(this.id, this.name);"
                                    class="btn-magnify btn-icon btn-magnify-class" required data-toggle="modal"
                                    data-target="#myModal"><i class="icon-search"></i></button>
                            </div>
                        @endif
                    </div>
                @endif

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_available">{{ __('mobile.label.qty_available') }}</label>
                    <div class="col-xs-7 col-md-5 col-lg-5">
                        <div class="input-group">
                            <input type="text" name="qty_available" id="qty_available" style="text-align:right"
                                placeholder="{{ __('mobile.label.qty_available') }}"
                                value="{{ old('qty_available') }}"
                                data-value="{{ numberFormatPrecision($defaults['qty_available'], $unit_quantity_format) }}"
                                class="form-control border-primary" readonly>
                            <input type="hidden" name="qty_available_conv" id="qty_available_conv"
                                value="{{ old('qty_available_conv') }}"
                                data-value="{{ numberFormatPrecision($defaults['qty_available'], $unit_quantity_format) }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" name="qty_available_uom" id="qty_available_uom"
                            value="{{ $joborder->uom }}" class="form-control border-primary" readonly>
                    </div>
                </div>

                <div class="form-group row">
                    <input type="hidden" id="allow_over_issue" value="{{ $allow_over_issue }}">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="qty_to_issue">{{ __('mobile.label.qty_to_issue') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="text" inputmode="numeric" value="{{ old('qty') }}"
                                style="text-align:right" name="qty" id="qty_to_issue"
                                class="form-control border-primary number-format" required
                                placeholder="{{ __('mobile.placeholder.qty_to_issue') }}">
                        </div>
                    </div>

                    @if ($sap_trans_order_integration == 1)
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input type="text" name="uom" id="uom" readonly
                                value="{{ $joborder->uom }}" class="form-control border-primary">
                        </div>
                    @else
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input type="text" name="uom" id="uom" value="{{ $joborder->uom }}"
                                class="form-control border-primary">
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="padding:0px;">
                            <button type="button" name="UOMs"
                                onclick="selection('/getItemUOMConv','item_num','uom','uom');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif





                </div>
                <br>

                <div class="form-actions center">
                    @if (session('matl_item') != null)
                        <button type="button" onclick="location.href='{{ route('JobMaterialIssue') }}';"
                            class="btn btn-warning mr-1">
                            <i class="icon-cross2"></i> {{ __('mobile.button.cancel') }}
                        </button>
                    @else
                        <button type="button" onclick="historyBack()" class="btn btn-warning mr-1">
                            <i class="icon-cross2"></i> {{ __('mobile.button.cancel') }}
                        </button>
                    @endif
                    <button type="submit" id="next" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.process') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

@include('util.validate_uom')

<script>
    unit_quantity_format = "{{ $unit_quantity_format }}";
    var errorMessage = "{{ __('error.mobile.qty_notmoreorequalqty_available') }}";
    var errorMessageBalance = "{{ __('error.mobile.qty_notmoreorequalqty_balance') }}";
    jQuery(function($) {
        $.validator.addMethod('minStrict', function(value, el, param) {
            return value > param;
        });
        $.validator.addMethod('QoH', function(value, el, param) {
            qty_available = parseFloat($('#qty_available').val().replace(/,/g, ""));
            return qty_available > 0;
        });
        //var errorMessage = 'Default error message';
        $("#jobmatlissueform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $("#next").attr('disabled', false);
            }
        });

        $('#jobmatlissueform').validate({
             onchange: false,
            //  onfocusout:false,
            rules: {
                /*loc_num:{
                    required: true,
                    remote: {
                        url: "{{ route('LocValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num:
                                function () {
                                    return $("#whse_num").val();
                                }
                        },
                        dataFilter: function(data){
                            if(data == 'true'){
                                var result = 'true';
                                $.ajax({
                                    url: '{{ route('checkLocNotTransit') }}',
                                    type: "GET",
                                    async: false,
                                    data: {
                                        whse_num: $("#whse_num").val(),
                                        loc_num: $("#loc_num").val(),
                                    },
                                    dataFilter: function(data){
                                        if (data == "transit") {
                                            result = 'false';
                                            errorMessage = "{{ __('error.mobile.transit_loc') }}";
                                        }
                                        else{
                                            showNewLoc();
                                        }
                                    }
                                });

                                return result;
                            }

                            errorMessage = "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.loc_num')]) }}";
                            return false;
                        }
                    }
                },*/

                loc_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            tparm_issue_location: function() {
                                return $("#tparm_issue_location").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);

                            // validate for issue location
                            if ($("#tparm_issue_location").val() != 0 && data.length == 0) {
                                errorMessage = "{{ __('error.mobile.loc_not_defined') }}";
                                return false;
                            }

                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    //  $("#loc_info").html("");
                                    // $("#locnumnotexist").html('');
                                    //  $("#qty").val("");
                                    //  $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    return false;
                                } else {
                                    //    showNewLoc();
                                    return true;
                                }
                            } else {
                                errorMessage = "{{ __('error.mobile.loc_not_exists') }}";

                                return false;
                            }
                        }
                    }
                },

                lot_num: {
                    required: {
                        depends: function(element) {
                            return $("#lot_num").is(":visible");
                        }
                    },
                    remote: {
                        url: "{{ route('ExistingLotValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            loc_num: function() {
                                return $("#loc_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                        }
                    }
                },
                qty: {
                    required: true,
                    number: true,
                    number_size: true,
                    QoH: true,
                    minStrict_value: 0,
                    
                    max_value: function() {
                        var allow = $("#allow_over_issue").val();
                        var req = parseFloat($("#qty_balance_conv").val().replace(/,/g,
                        "")); // qty_required = $joborder->qty_required - $joborder->qty_issued
                        var avail = parseFloat($("#qty_available_conv").val().replace(/,/g, ""));
                       var qtyinput =parseFloat($("#qty_to_issue").val().replace(/,/g, ""));
                        
                        if(qtyinput > avail)
                        {
                            return avail;
                        }
                        else
                        {
                            if(allow != 1)
                            {
                                return req;
                            }
                           
                        }
                        
                        /*if (allow == 1) {
                            //return avail;
                            return Math.max(avail, req);
                        } else {
                            return Math.min(avail, req);
                        }*/
                    }
                },
                uom: {
                    required: true,
                    uom_validation: [$('#item_num').val(), $('#cust_num').val(), null, $(
                        '#qty_available_uom').val(), $('#base_uom').val()]
                    
                    // remote:{
                    //     url: "{{ route('validateUomConv') }}",
                    //     type: "post",
                    //     data: { _token : $('input[name="_token"]').val(),
                    //         item_num:
                    //             function() {
                    //                 return $("#citem").val();
                    //             },
                    //         uom:
                    //             function() {
                    //                 return $("#uom").val();
                    //             },
                    //     }
                    // }
                },
            },
            messages: {
                whse_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]) }}"
                },
                item_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.item_num')]) }}"
                },
                loc_num: {
                    remote: function() {
                        return errorMessage;
                    }
                },
                lot_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}"
                },
                qty: {
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.label.qty')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}",
                    QoH: "{{ __('error.mobile.insufficient') }}",
                    minStrict_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty')]) }} {0} ",
                    //max_value: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.label.qty')]) }} {0}"


                     max_value: function(value, element) {
                         var allow = $("#allow_over_issue").val();
                        var req = parseFloat($("#qty_balance_conv").val().replace(/,/g,
                        "")); // qty_required = $joborder->qty_required - $joborder->qty_issued
                        var avail = parseFloat($("#qty_available_conv").val().replace(/,/g, ""));
                        var qtyinput =parseFloat($("#qty_to_issue").val().replace(/,/g, ""));

                        
                       
                       
                       if(qtyinput > avail)
                        {
                            var maxQty = parseFloat($('#qty_available_conv').val().replace(/,/g, ""));
                             return errorMessage.replace(':resource_qty_available', maxQty.toFixed(unit_quantity_format));
                        }
                        else
                        {
                            if(allow != 1)
                            {
                               var maxQty = parseFloat($('#qty_balance_conv').val().replace(/,/g, ""));
                              return errorMessageBalance.replace(':resource_qty_available', maxQty.toFixed(unit_quantity_format));
                            }
                           
                        }
                       
                        /*if (allow == 1) {
                            //return avail;
                           // return Math.max(avail, req);
                             var maxQty = parseFloat($('#qty_balance_conv').val().replace(/,/g, ""));
                              return errorMessageBalance.replace(':resource_qty_available', maxQty.toFixed(unit_quantity_format));
                        } else {
                           // return Math.min(avail, req);
                            var maxQty = parseFloat($('#qty_available_conv').val().replace(/,/g, ""));
                             return errorMessage.replace(':resource_qty_available', maxQty.toFixed(unit_quantity_format));
                            
                        }*/


                        
                       
                    }


                },
                uom: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                },
            },
            submitHandler: function(form) {
                // check expiry date
                $("#next").attr('disabled', true);

                $.ajax({
                    url: '{{ route('checkExpiryDateJobMaterialIssue') }}',
                    type: "GET",
                    data: {
                        item_num: $("#item_num").val(),
                        lot_num: $("#lot_num").val(),
                        prompt: 'true',
                    },
                    success: function(data) {
                        // If false, submit form
                        if (data == "false") {
                            $(".pageloader").css("display", "block");
                            $(".submitloader").attr("disabled", true);
                            setTimeout(function() {
                                form.submit();
                                setTimeout(() => {
                                    $("#next").attr('disabled', false);

                                }, 500);
                            }, 300);
                        }
                        // else, prompt confirmation
                        else {
                            var m = '{{ __('mobile.message.item_expired_prompt') }}';
                            m = m.replace(':resource', data['item_num']);
                            m = m.replace(':resource2', data['expiry_date']);
                            Swal.fire({
                                title: 'Warning',
                                text: m,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes'
                            }).then((result) => {
                                if (result.value) {
                                    $(".pageloader").css("display", "block");
                                    $(".submitloader").attr("disabled", true);
                                    setTimeout(function() {
                                        form.submit();
                                        setTimeout(() => {
                                            $("#next").attr(
                                                'disabled',
                                                false);

                                        }, 500);
                                    }, 300);
                                } else {
                                    $(".submitloader").attr("disabled", false);
                                    return false;
                                }
                            });
                        }
                    }
                });
            }
        });


    });

    function onLocChange() {
        $("#jobmatlissueform").validate().element(':input[name="loc_num"]');
        if ($("#loc_num").val() != '') {
            if ($("#lot_num").length === 0) {
                display('/displayQtyItemAvailable', 'whse_num,item_num,loc_num',
                    'qty_available,qty_available_conv,qty_available_uom,base_uom');
            } else {
                if ($("#lot_num").val()) {
                    $("#jobmatlissueform").validate().element(':input[name="lot_num"]');
                    display('/displayLotQuantityJobIssue', 'item_num,whse_num,loc_num,lot_num',
                        'qty_available,qty_available_conv,qty_available_uom,base_uom');
                }
            }
        } else {
            $('#qty_available').val('');
            $('#qty_available_conv').val('');
        }
    }

    $(document).ready(function() {

           displayConvQty('/displayQuantityConverted',
                        'base_uom,item_num,qty_available,uom', 'qty_available_conv');
                    displayConvQty('/displayQuantityConverted',
                        'ref_uom,item_num,qty_balance,uom', 'qty_balance_conv');

        $("#loc_num").change();
        $("#uom").on("change", function() {
      
             var lot_num = "undefined";
            if ($("#lot_num").val() != "") {
                lot_num = $("#lot_num").val();
            }
            
            if ($("#item_num").val() == "") {
            
                $("#qty_available_conv").val("");
            } else {
                let validate = validateConvUOM($('#item_num').val(), $('#cust_num').val(), null, $(
                    '#qty_available_uom').val(), $('#base_uom').val(), $('#uom').val());
                    // alert($('#qty_to_issue').val() +  validate);


                validate.then(function(resp) {
                        // true
                         // $("#qty_to_issue").val('');
                    },
                    function(err) {
                        // false
                        $("#qty_to_issue").val('');
                         $("#qty_to_issue-error").hide();
                        $("#uom").val($("#base_uom").val());
                    }).finally(function() {
                        $("#qty_to_issue").val('');
                          $("#qty_to_issue-error").hide();
                    displayConvQty('/displayQuantityConverted',
                        'base_uom,item_num,qty_available,uom', 'qty_available_conv');
                    displayConvQty('/displayQuantityConverted',
                        'ref_uom,item_num,qty_balance,uom', 'qty_balance_conv');


              


/*calculateQtyLimit($("#ref_uom").val(), $("#qty_to_issue").val(), $(
                            "#uom").val(), $("#item_num").val(), $("#whse_num").val(), $(
                            "#loc_num").val(), lot_num, $("#base_uom").val(), "null",
                        "null",
                        "null", "", 'qty_available_conv');*/

                });
            }
        });

        $("#loc_num").on("change", function() {
            $("#loc_info").html("");
            $("#checkLoc").html("");
            $("#locnumnotexist").html('');

            if ($("#loc_num").val() == "") {
                $("#loc_info").html("");
                $("#checkLoc").html("");
                $("#locnumnotexist").html('');
            }
        });

    });

    function submitForm(payload) {
        var form = document.createElement('form');
        form.style.visibility = 'hidden';
        form.method = 'POST';
        form.action = "{{ route('showSelectionList') }}";

        payload._token = $("[name=_token]").val();

        if (payload.butt_name) {
            delete payload.butt_name;
        }

        $.each(payload, function(i, v) {
            var input = document.createElement('input');
            input.name = i;
            input.value = v;
            form.appendChild(input); // add key/value pair to form
        });

        document.body.appendChild(form); // forms cannot be submitted outside of body
        form.submit(); // send the payload and navigate
    }

    function historyBack() {
        if (document.location.href.includes("job-material-issue/details")) {
            var payload = <?= json_encode(Session::get('request_data_jobmaterial')) ?>;
            submitForm(payload);
        } else {
            window.history.go(-1);
        }
    }
</script>
@include('errors.maxchar')
@include('Pallet.palletMobileValidation')
@include('util.selection')
@include('util.convert_alternade_barcode_to_item')
@endsection
