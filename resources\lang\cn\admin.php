<?php

return [
    'title' => [
        'allocation' => 'Allocation',
        'count_batch' =>
        [
            'add' => 'Add Inventory Count',
            'edit' => 'Edit Inventory Count',
            'view' => 'Inventory Count Batch'
        ],
        'homepage' => '主页',
        'register' => 'Register Site',
        // Alternate Barcode
        'add_alternate_barcode' => 'Add Alternate Barcode',
        'edit_alternate_barcode' => 'Edit Alternate Barcode',
        'alternate_barcode_list' => 'Alternate Barcode List',
        'view_alternate_barcode' => 'View Alternate Barcode',
        // Super
        'create_site' => 'Create Site',
        'delete_site' => 'Delete Site',
        'lego_game' => 'Lego Game',
        // Partnership
        'partnership' => 'Partnership',
        // Warehouse
        'add_whse' => 'Add Warehouse',
        'edit_whse' => 'Edit Warehouse',
        'whse_list' => 'Warehouse List',
        'view_whse' => 'View Warehouse',
        // Vendor
        'add_vendor' => 'Add Vendor',
        'edit_vendor' => 'Edit Vendor',
        'view_vendor' => 'View Vendor',
        'vend_list' => 'Vendor List',
        // Work Center
        'add_wc' => 'Add Work Center',
        'edit_wc' => 'Edit Work Center',
        'wc_list' => 'Work Center List',
        'view_wc' => 'View Work Center',
        // UOM
        'add_uomcon' => 'Add UOM Conversion',
        'edit_uomcon' => 'Edit UOM Conversion',
        'uom_conlist' => 'UOM Conversion List',
        'add_uom' => 'Add Unit of Measurements',
        'edit_uom' => 'Edit Unit of Measurements',
        'uom_list' => 'UOM List',
        'view_uom' => 'View Unit of Measurements',
        'view_uomcon' => 'View UOM Conversion',
        // TO
        'add_toline' => 'Add Transfer Order Line',
        'edit_toline' => 'Edit TO Line',
        'add_to' => 'Add Transfer Order',
        'edit_to' => 'Edit Transfer Order',
        'edittoline' => 'Edit Transfer Order Line',
        'to_list' => 'Transfer Order List',
        'import_toline' => 'Import TO Line',
        'view_to' => 'View Transfer Order',
        'line_listto' => 'TO Lines',
        'view_toline' => 'View Transfer Order Line',
        // Objects
        'objects_list' => 'Objects List',
        'view_object' => 'View Object',
        'add_object' => 'Add Object',
        // Task Titles
        'add_task' => 'Add Indirect Task',
        'edit_task' => 'Edit Indirect Task',
        'task_list' => 'Indirect Task List',
        'view_task' => 'View Indirect Task',
        // Reason Code Titles
        'add_rc' => 'Add Reason Code',
        'edit_rc' => 'Edit Reason Code',
        'rc_list' => 'Reason Code List',
        'view_rc' => 'View Reason Code',
        'sync_status_SAP' => 'Sync to SAP',
        'sync_status_SL' => 'Sync to Site Lines',
        // Product Code Titles
        'add_pc' => 'Add Product Code',
        'edit_pc' => 'Edit Product Code',
        'pc_list' => 'Product Code List',
        'view_pc' => 'View Product Code',
        // Purchase Order Titles
        'add_po' => 'Add Purchase Order',
        'edit_po' => 'Edit Purchase Order',
        'po_list' => 'Purchase Order List',
        'view_po' => 'View Purchase Order',
        'add_po_line' => 'Add Purchase Order Line',
        'add_co_line' => 'Add Customer Order Line',
        'add_to_line' => 'Add Transfer Order Line for TO Number: :trn_num',
        'edit_po_line' => 'Edit Purchase Order Line',
        'edit_co_line' => 'Edit Customer Order Line',
        'edit_to_line' => 'Edit Transfer Order Line for TO Number: :trn_num',
        'view_po_line' => 'View Purchase Order Line',
        'view_co_line' => 'View Customer Order Line',
        'view_to_line' => 'View Transfer Order Line',
        'view_po_line_with_ponum' => 'View Purchase Order Line',
        'view_co_line_with_conum' => 'View Customer Order Line',
        'view_to_line_with_tonum' => 'View Transfer Order Line for TO Number: :trn_num',
        // GRN Titles
        'add_grn' => 'Add GRN',
        'edit_grn' => 'Edit GRN',
        'grn_list' => 'Goods Receiving Notes List',
        'view_grn' => 'View GRN',
        'add_grn_line' => 'Add GRN Line',
        'edit_grn_line' => 'Edit GRN Line',
        'view_grn_line' => 'View GRN Line',
        'view_grn_line_with_grnnum' => 'View GRN Line',
        // GRPO
        'generate_grpo' => 'Generate GRPO',
        // Return Request in PO
        'generate_return_request' => 'Generate Return Request',
        // DO
        'generate_do' => 'Generate DO',
        // Inventory Transfer
        'generate_inv_transfer' => 'Generate Inventory Transfer',
        // History
        'mat_tran' => 'Material Transactions',
        'mac_tran' => 'Machine Transactions',
        'over_his' => 'Value Change Log',
        // Machine
        'add_mac' => 'Add Machine',
        'edit_mac' => 'Edit Machine',
        'mac_list' => 'Machine List',
        'view_mac' => 'View Machine',
        // Item Lot Location
        'item_lot_loc' => 'Item Lot Location',
        'add_lotloc' => 'Add Item Lot Location',
        'edit_lotloc' => 'Edit Item Lot Location',
        'lotloc_list' => 'Item Lot Location List',
        'view_lotloc' => 'View Item Lot Location',
        // Location
        'add_loc' => 'Add Location',
        'edit_loc' => 'Edit Location',
        'loc_list' => 'Location List',
        'view_loc' => 'View Location',
        'job_trans' => 'Job Transactions',
        // Job Route
        'job_route' => 'Job Route',
        'add_jobr' => 'Add Job Route',
        'edit_jobr' => 'Edit Job Route',
        'jobr_list' => 'Job Route List',
        'view_jobr' => 'View Job Route',
        // Job Material
        'add_jobm' => 'Add Job Material',
        'scrap_factor' => 'Scrap Factor',
        'edit_jobm' => 'Edit Job Material Details',
        'jobm_list' => 'Job Material List',
        'view_jobm' => 'View Job Material',
        // Job Order
        'job_ordernum' => 'Job Order Number',
        'add_job' => 'Add Job Order',
        'add_job_from_bom' => 'Add Job Order from BOM',
        'edit_job' => 'Edit Job Order',
        'job_list' => 'Job Order List',
        'view_job' => 'View Job Order',
        // Item Location
        'item_loc' => 'Item Location',
        'add_item_loc' => 'Add Item Location',
        'edit_item_loc' => 'Edit Item Location',
        'item_loc_list' => 'Item Location List',
        'view_item_loc' => 'View Item Location',
        // Item
        'add_item' => 'Add Item',
        'edit_item' => 'Edit Item',
        'item_list' => 'Item List',
        'view_item' => 'View Item',
        // Item Warehouse
        'item_whse' => 'Item Warehouses',
        'add_item_whse' => 'Add Item Warehouse',
        'edit_item_whse' => 'Edit Item Warehouse',
        // Picker Maintenance
        'pickers' => 'Pickers',
        'add_picker' => 'Add Picker',
        'edit_picker' => 'Edit Picker',
        'view_picker' => 'View Picker',
        // User
        'add_user' => 'Add User',
        'edit_user' => 'Edit User',
        'user_list' => 'Users',
        'view_user' => 'View User',
        'user_lists' => 'Users List',
        // User Group
        'add_usergroup' => 'Add User Group',
        'edit_usergroup' => 'Edit User Group',
        'user' => 'user',
        'object' => 'object',
        'usergroup_list' => 'User Groups',
        // Employee
        'add_emp' => 'Add Employee',
        'edit_emp' => 'Edit Employee',
        'emp_list' => 'Employee List',
        'view_emp' => 'View Employee',
        // Customer
        'add_cust' => 'Add Customer',
        'edit_cust' => 'Edit Customer',
        'cust_list' => 'Customer List',
        'view_cust' => 'View Customer',
        // Count Sheet
        'create_cs' => 'Create Count Sheet',
        'inv_cs' => 'Inventory Count Sheets',
        'countsheet' => 'View Count Sheet',
        'post_cs' => 'Post Count Sheet',
        // CO
        'add_co' => 'Add Customer Order',
        'edit_co' => 'Edit Customer Order',
        'co_list' => 'Customer Order List',
        'view_co' => 'View Customer Order',
        // Count Batch
        'create_batch' => 'Add Batch',
        'batch_name' => 'Batch Name',
        'batch_desc' => 'Description',
        'edit_batch' => 'Edit Count Batch',
        'view_count_batch' => 'View Count Batch',
        'import_cb' => 'Imported Count Batches',
        'generate_cb' => 'Count Batch Preview',
        'inv_cb' => 'Inventory Count Batch List',
        'edit_co' => 'Edit Customer Order',
        'over_his' => 'Value Change Log',
        'view_toline' => 'View Transfer Order Line',
        // Doc
        'add_document' => 'Add Document',
        'add_note' => 'Add Note',
        'attachment_list' => 'Attachment List',
        'user_settings' => 'User Settings',
        // Barcode Label
        'view_label' => 'View Label',
        'edit_label' => 'Edit Label',
        'label_list' => 'Label List',
        'create_label' => 'Add label',
        // Printer
        'view_printer' => 'Printer Details',
        'edit_printer' => 'Edit Printer',
        'printer_list' => 'Printer List',
        'create_printer' => 'Create New Printer',
        // Labor & Machine
        'edit_labmac' => 'Edit Labor & Machine',
        'labmac_list' => 'Labor & Machine List',
        // Pick List
        'add_item_job' => 'Add Material from Job Order',
        'add_item_co' => 'Add Lines from Customer Order',
        'add_item_to' => 'Add Lines from Transfer Order',
        'added_order_lines' => 'Added Order Lines',
        'create_picklist' => 'Create Pick List',
        'edit_picklist' => 'Edit Pick List',
        'edit_picklistitem' => 'Edit Pick List Item',
        'generated_picklist' => 'Generated Pick List',
        'order_lines_selection' => 'Order Lines Selection',
        'picklist' => 'Pick List',
        'picklist_s' => 'Pick List(s)',
        'picklist_generate' => 'Pick List Generation',
        'stock_allocation' => 'Stock Allocation',
        'manual_allocation' => 'Manual Allocation',
        'view_picklist' => 'View Pick List',
        'add_item_picklist' => 'Add Item for Pick list',
        // Report
        'inv_trans_by_item' => 'Inventory Transactions by Item', // Inventory
        'inv_balance' => 'Inventory Balance',
        'inv_expiry' => 'Inventory Expiry',
        'lot_expiry' => 'Lot Expiry',
        'po_history' => 'Purchase Order Receipt History',
        'co_history' => 'Customer Order Shipment History',
        'po_status' => 'Purchase Order Status',
        'co_status' => 'Customer Order Status',
        'reorder_report' => 'Reorder Report',
        'jo_status' => 'Job Order Status',
        'prod_issue' => 'Material Issued',
        'prod_completion' => 'Item Production Completion Summary',
        'reorder_item' => 'Item Below Reorder Level',
        'job_oper_report' => 'Job Operation Hours', // Production
        'job_process_report' => 'Job Processing Hours by Operation', // Production
        'item_scrap_summary' => 'Item Scrap Summary',
        'job_scrap' => 'Scrap Reasons',
        'job_scrap_summary' => 'Job Scrap Summary',
        'machine_status_summary' => 'Machine Status Summary',
        'job_scrap_by_oper_report' => 'Job Scrap Summary By Operation',
        'print_job_sheet' => 'Print Job Sheet', //Transaction
        'print_pick_list' => 'Print Pick List',
        'labour_status_summary' => 'Labour Status Summary',
        'production_efficiency' => 'Production Efficiency',
        'slitting_efficiency' => 'Slitting Efficiency',
        // Site Settings
        'change_plan' => 'Confirm Plan Change',
        'add_license' => 'Add License',
        'reduce_license' => 'Reduce License',
        'mg_payment' => 'Manage Payment Details',
        'cancel_subs' => 'Cancel Subscription',
        'cancel_subscribe' => 'Confirm Subscription Cancellation',
        'site_settings' => 'Site Settings',
        'connection_settings' => "Connected Apps",
        'company_info' => 'Company Information',
        'contact_person' => 'Contact Person Information',
        'erp_setting' => 'Application Connection Setting',
        'erp_title' => 'Application Name',
        'serial_no' => 'No.',
        'process' => "Object",
        'connection_params' => 'Connection Parameters',
        'erp_process_setting' => 'Application Authentication Settings',
        'license_info' => 'License Info',
        'import_export' => 'Import Log',
        'export_log' => 'Export Log',
        'integration_log' => 'Integration Log',
        'integration' => 'Integration',
        // Transaction Parameters
        'trans_parm' => 'Transaction Parameters',
        // Print
        'job_sheet' => 'Job Sheet',
        'grn' => 'Good Receiving Note',
        // Batch
        'batch_assign' => 'Batch Assign',
        // Count Group
        'count_group_list' => 'Count Group List',
        'add_count_group' => 'Add Count Group',
        'edit_count_group' => 'Edit Count Group',
        'view_count_group' => 'View Count Group',
        // Count Sheet
        'count_sheet_line' => 'Count sheet line',
        'edit_countsheet' => 'Edit Count Sheet',
        // Count Line
        'view_count_line' => 'View Count Line',
        //Custom Field
        'custom_field_list' => 'Custom Field List',
        'add_custom_field' => 'Add Custom Field',
        'edit_custom_field' => 'Edit Custom Field',
        'view_custom_field' => 'View Custom Field',
        // Inventory Count Batch
        'count_sheet_lines_selection' => 'Count Sheet Line Selection',
        // Lot Number Definition
        'lot_number_definition_list' => 'Lot Number Definition List',
        'add_lot_number_definition' => 'Add Lot Number Definition',
        'edit_lot_number_definition' => 'Edit Lot Number Definition',
        'view_lot_number_definition' => 'View Lot Number Definition',
        // Order Number Definition
        'order_number_definition_list' => 'Order Number Definition List',
        'add_order_number_definition' => 'Add Order Number Definition',
        'edit_order_number_definition' => 'Edit Order Number Definition',
        'view_order_number_definition' => 'View Order Number Definition',
        // Lot
        'lot_list' => 'Lot List',
        'add_lot' => 'Add Lot',
        'edit_lot' => 'Edit Lot',
        'view_lot' => 'View Lot',
        // LPN Definition
        'lpn_definition_list' => 'License Plate Number Definition List',
        'add_lpn_definition' => 'Add License Plate Number Definition',
        'edit_lpn_definition' => 'Edit License Plate Number Definition',
        'view_lpn_definition' => 'View License Plate Number Definition',
        // Zone
        'zone_list' => 'Zone List',
        'add_zone' => 'Add Zone',
        'edit_zone' => 'Edit Zone',
        'view_zone' => 'View Zone',
        // Allocation
        'allocation_list' => 'Allocation List',
        'add_allocation' => 'Add Allocation',
        'edit_allocation' => 'Edit Allocation',
        'view_allocation' => 'View Allocation',
        'orderLine' => 'order line',
        // BOM
        'boms' => 'Single Level Bill of Materials',
        'add_bom' => 'Add Item BOM',
        'view_bom' => 'BOM Details',
        'edit_bom' => 'Edit Item BOM',
        'bom_list' => 'Bill of Material List',
        'create_bom' => 'Create New BOM',
        'copy_job_bom' => 'Copy Job BOM to Item BOM',
        // Allocation
        'add_picklist' => 'Add Pick List',
        'edit_picklist' => 'Edit Pick List',
        'view_picklist' => 'View Pick List',
        // Shipping Zone
        'shipping_zone' => 'Shipping Zone',
        'add_shipping_zone' => 'Add Shipping Zone',
        'edit_shipping_zone' => 'Edit Shipping Zone',
        'view_shipping_zone' => 'View Shipping Zone',
        'shipping_zonelist' => 'Shipping Zone List',
        // GRN Titles
        'add_grn' => 'Add GRN',
        'edit_grn' => 'Edit GRN',
        'grn_list' => 'GRN List',
        'view_grn' => 'View GRN',
        'add_grn_line' => 'Add GRN Line',
        'add_co_line' => 'Add Customer Order Line',
        'add_to_line' => 'Add Transfer Order Line for TO Number: :trn_num',
        'edit_grn_line' => 'Edit GRN Line',
        'edit_co_line' => 'Edit Customer Order Line',
        'edit_to_line' => 'Edit Transfer Order Line for TO Number: :trn_num',
        'view_grn_line' => 'View GRN Line',
        'view_co_line' => 'View Customer Order Line',
        'view_to_line' => 'View Transfer Order Line',
        'view_grn_line_with_grnnum' => 'View GRN Line',
        'view_co_line_with_conum' => 'View Customer Order Line',
        'view_to_line_with_tonum' => 'View Transfer Order Line for TO Number: :trn_num',
        // Integration Setting
        'integrate_sap' => 'Integrate with SAP B1',
        'integrate_qb' => 'Integrate with QuickBooks',
        'connect' => 'Connect',
        // Pallet
        'pallet_list' => 'Pallet List',
        'add_pallet' => 'Add Pallet',
        'add_pallet_line' => 'Add Pallet Line',
        'edit_pallet' => 'Edit Pallet',
        'view_pallet' => 'View Pallet',
        'view_pallet_item' => 'View Pallet Line',
        'edit_pallet_item' => 'Edit Pallet Line',
        'lpn_num' => 'LPN',
    ],
    'label' => [
        // A
        'api_type' => "Application Type",
        'alternate_barcode' => 'Alternate Barcode',
        'address1' => 'Address 1',
        'address2' => 'Address 2',
        'address3' => 'Address 3',
        'allocation' => 'Allocation',
        'accum' => 'Accumulated Qty',
        'active' => 'Active',
        'action' => 'Action',
        'actual_count_date' => 'Actual Count Date',
        'add' => 'Add',
        'add_num' => 'Address',
        'admin' => 'Admin',
        'after_qty_available' => 'After Qty Available',
        'all' => 'All',
        'allocated' => 'Allocated',
        'allocation' => 'Allocation',
        'api' => 'API',
        'application' => 'Application',
        'attachment' => 'Attachment',
        'attachments' => 'Attachments',
        'ave_labour_run_time_per_piece' => 'Ave Labour Run Time per Piece',
        'ave_machine_run_time_per_piece' => 'Ave Machine Run Time per Piece',
        'ave_total_run_per_piece' => 'Ave Total Run Time per Piece',
        // B
        'batch' => 'Batch',
        'billing' => 'Subscription',
        'base_uom' => 'Base UOM',
        'batch_desc' => 'Description',
        'batch_name' => 'Batch Name',
        'before_qty_available' => 'Before Qty Available',
        'bom' => 'BOM',
        'bill_of_material' => 'Bill of Material',
        'bom_name' => 'BOM Name',
        'bom_status' => 'BOM Status',
        'bom_revision' => 'BOM Revision',
        'bom_type' => 'BOM Type',
        // C
        'change_pwd' => 'Change Password',
        'current_pwd' => 'Current Password',
        'calculating' => 'Calculating...',
        'city' => 'City',
        'closed' => 'Closed',
        'closed_date' => 'Closed Date',
        'co' => 'CO',
        'co_num' => 'CO Number',
        'co_line' => 'CO Line',
        'co_notes' => 'Notes',
        'co_rel' => 'CO Release',
        'co_status' => 'Co Status',
        'columns'=>"Columns",
        'company_logo' => 'Company Logo',
        'company_name' => 'Company Name',
        'completed' => 'Completed',
        'completed_date' => 'Completed Date',
        'completed_time' => 'Completed Time',
        'confirm_password' => 'Confirm Password',
        'contact_num' => 'Contact Number',
        'container_num' => 'Container Number',
        'content' => 'Content',
        'conv_factor' => 'Conversion Factor',
        'conv_type' => 'Conversion Type',
        'converted_uom' => 'Converted UOM',
        'count_group' => 'Count Group',
        'count_group_desc' => 'Count Group Desc',
        'count_num' => 'Count Sheet Number',
        'count_sheet' => 'Count Sheet',
        'count_sheet_by_location' => 'Count Sheet by Location',
        'count_sheet_line' => 'Count Sheet Line',
        'count_sheet_lines' => 'Count Sheet Lines',
        'count_sheet_num' => 'Count Sheet Number',
        'counted' => 'Counted',
        'counter1_name' => 'Counter 1 Name',
        'counter2_name' => 'Counter 2 Name',
        'counter1_num' => 'Counter 1',
        'counter2_num' => 'Counter 2',
        'country' => 'Country',
        'connector' => 'Connector',
        'created_by' => 'Created By',
        'created_date' => 'Created Date',
        'created_time' => 'Created Time',
        'creation_date' => 'Creation Date',
        'cust' => 'Customer',
        'cust_address' => 'Customer Address',
        'cust_name' => 'Customer Name',
        'cust_num' => 'Customer Code',
        'cutoff_qty' => 'Cut-off Quantity',
        'customer_order' => 'Customer Order',
        // D
        'do' => 'DO',
        'date' => 'Date',
        'date_format' => 'Date Format',
        'days_to_expiry' => 'Days to Expiry',
        'DD' => '[DD] (2-digit day)',
        'decimal_point' => 'Decimal Point',
        'default' => 'Default',
        'default_counter' => 'Default Counter 1',
        'default_picker' => 'Default Picker',
        'default_shelf_life' => 'Default Shelf Life Day(s)',
        'default_warehouse' => 'Default Warehouse',
        'default_trn_loc' => 'Default Transit Location',
        'default_view' => 'Default View',
        'definition' => 'Definition',
        'deleted' => 'Deleted',
        'desc' => 'Description',
        'details' => 'Details',
        'display_date_format' => 'Display Date Format',
        'display_with_list' => 'Display with List',
        'do_num' => 'DO Number',
        'doc_num' => 'Document Number',
        'docs_and_api' => 'Support',
        'document' => 'Document',
        'down_time' => 'Down Time(Hrs)',
        'down_occurrences' => 'Down Time Occurrences',
        'driver_contact' => 'Driver Contact',
        'driver_name' => 'Driver Name',
        'due_date' => 'Due Date',
        'dd_mm_yyyy' => 'DD/MM/YYYY',
        'description' => 'Description',
        // E
        'enable' => 'Enable',
        'email' => 'Email',
        'emp_name' => 'Name',
        'emp_firstname' => 'First Name',
        'emp_lastname' => 'Last Name',
        'employee' => 'Employee',
        'employee_name' => 'Employee Name',
        'emp_num' => 'Employee ID',
        'emp_desc' => 'Employee Description',
        'end_date_plan' => 'End Date',
        'end_time_date' => 'End Time Date',
        'error'=>'Error',
        'error_code' => 'Error Code',
        'exception' => 'Exception',
        'expiry_date' => 'Expiry Date',
        'expiry_tracked' => 'Expiry Tracked',
        // F
        'failed' => 'Failed',
        'fax_num' => 'Fax',
        'fefo' => 'FEFO',
        'fifo' => 'FIFO',
        'field_name' => 'Field Name',
        'field_type' => 'Field Type',
        'filename' => 'File Name',
        'filtered_count_sheet_lines' => 'Filtered Count Sheet Lines',
        'filtered_order_lines' => 'Filtered Order Lines',
        'filtered_lines' => 'Filtered Lines',
        'filtered_grpo' => 'Filtered GRPO',
        'filtered_do' => 'Filtered DO',
        'filtered_return_request' => 'Filtered Return Request',
        'filtered_inv_transfer' => 'Filtered Inventory Transfer',
        'finished_job' => 'Finished Job',
        'first_name' => 'First Name',
        'first_received' => 'First Received',
        'first_received_date' => 'First Received Date',
        'finished_goods' => 'Finished Goods',
        'form_name' => 'Form Name',
        'from' => 'From',
        'from_variable' => 'From :variable',
        'from_site' => 'From Site',
        'from_whse' => 'From Warehouse',
        'from_vendor' => 'From Vendor',
        'from_cust' => 'From Customer',
        'frozen' => 'Frozen',
        'file_name' => 'File Name',
        'full' => 'Full',
        // G
        'generate_grn' => 'Generate GRN',
        'group_name' => 'Group Name',
        'group_users' => 'Group Users',
        'group_objects' => 'Group Objects',
        'group_picklist_by' => 'Group pick list by',
        'grpo' => 'GRPO',
        'grn' => 'GRN',
        'grn_line' => 'GRN Line',
        'grn_num' => 'GRN Number',
        'grn_date' => 'GRN Date',
        // H
        'help' => 'Help',
        'host' => 'Host',
        'height'=>"Height",
        // I
        'itemwhse' => 'Item Warehouse',
        'id' => 'Machine/Employee Id',
        'import' => 'Import',
        'imported_date' => 'Imported Date',
        'inactive' => 'Inactive',
        'indirect_task' => 'Indirect Task',
        'indirect_tasktime' => 'Indirect Task Time (Hrs)',
        'indirect_taskoccur' => 'Indirect Task Occurences',
        'indirect_type' => 'Indirect Type',
        'input_date_format' => 'Input Date Format',
        'in_progress' => 'In Progress',
        'inv_transfer' => 'Inventory Transfer',
        'inventory' => 'Inventory',
        'is_counted' => 'Is Being Counted',
        'is_editable' => 'Field is Editable',
        'is_required' => 'Field is Required',
        'item_desc' => 'Description',
        'item_descpt' => 'Item Description',
        'item' => 'Item',
        'item_code' => 'Item Code',
        'item_num' => 'Item',
        'item_type' => 'Item Type',
        'issuance_priority' => 'Issuance Priority',
        'issue_by' => 'Issue By',
        'issue_by_unit' => 'Issue By Unit',
        'internal_item_num' => 'Internal Item Code',
        // J
        'job_created' => 'Job Created',
        'job_date' => 'Job Date',
        'job_num' => 'Job Order',
        'job_item' => 'Job Item',
        'job_item_desc' => 'Job Item Description',
        'job_type' => 'Job Type',
        'job_order' => 'Job Order',
        'job_matl' => 'Job Material',
        // L
        'key' => "Key",
        'labor_run' => 'Labor Run',
        'labour_hours_per_piece' => 'Labour Hours per Piece',
        'labour_hrs_pcs' => 'Labour Hrs/Pcs',
        'label_body' => 'Label Body',
        'label_name' => 'Label Name',
        'label_size' => 'Label Size (mm)',
        'last_count_date' => 'Last Count Date',
        'last_name' => 'Last Name',
        'list_items' => 'List Items',
        'lot_last_count_date' => 'Lot Last Count',
        'license' => 'Licenses',
        'license_qty' => 'License Qty',
        'lines_per_sheet' => 'Line per Sheet',
        'loc_name' => 'Description',
        'loc_num' => 'Location',
        'loc_code' => 'Location Code',
        'loc_type' => 'Location Type',
        'lot' => 'Lot',
        'lot_num' => 'Lot Number',
        'lot_num_def' => 'Lot Number Definition',
        'lot_tracked' => 'Lot Tracked',
        'lot_tracked_item' => 'Lot Tracked Item',
        'lot_source' => 'Lot Source',
        'lpn_num' => 'LPN',
        'lpn_num_def' => 'License Plate Number Definition',
        'lpn_line' => 'Line',
        'line_or_oper' => 'Line / Operation',
        'limit_running_number' => 'Running Number Digits',
        'last_running_number' => 'Last Number',

        // M
        'machine' => 'Machine',
        'machine_hours' => 'Machine Hours',
        'machine_hours_per_piece' => 'Machine Hours per Piece',
        'machine_hrs_pcs' => 'Machine Hrs/Pcs',
        'machine_time' => 'Machine Run Time(Hrs)',
        'match' => 'Match',
        'matl_item' => 'Material',
        'matl_conv' => 'Material Conversion Rule',
        'matl_qty' => 'Material Qty',
        'matl_code' => 'Material',
        'matl_desc' => 'Material Description',
        'maxlength' => 'Max Characters',
        'max_num' => 'Max number of',
        'max_user' => 'Max User',

        'margin_top'=>"Margin Top",
        'margin_bottom'=>"Margin Bottom",
        'margin_left'=>"Margin Left",
        'margin_right'=>"Margin Right",
        'MM' => '[MM] (2-digit month)',
        'mm_dd_yyyy' => 'MM/DD/YYYY',
        'modified_by' => 'Modified By',
        'modified_date' => 'Modified Date',
        'modified_time' => 'Modified Time',
        'module' => 'Module',
        'multiple_orders' => 'Multiple Orders',
        // N
        'new' => 'New',
        'new_pwd' => 'New Password',
        'name' => 'Name',
        'net_received' => 'Net Received',
        'non_inv' => 'Non-Inventory',
        'notes' => 'Notes',
        'notes_content' => 'Notes Content',
        'notes_subject' => 'Notes Subject',
        'num_of_pickers' => 'Number of pickers',
        'no_match' => 'No Match',
        // O
        'object' => 'Object',
        'object_auth' => 'Auth Objects',
        'object_list' => 'Object List',
        'object_type' => 'Type',
        'open' => 'Open',
        'oper' => 'Oper.',
        'oper_num' => 'Operation',
        'order_line' => 'Order line',
        'order_lines' => 'Order Lines',
        'order_num' => 'Order Number',
        'order_type' => 'Order Type',
        'order_number_definitions' => 'Order Prefix',
        'order_num_def' => 'Order Number',
        // P
        'pallet' => 'Pallet',
        'pallet_line' => 'Pallet Line',
        'parameter' => 'Parameter',
        'parent_job' => 'Parent Job',
        'packing_loc' => 'Packing Location',
        'password' => 'Password',
        'per_picklist' => 'per pick list',
        'pick_date' => 'Pick Date',
        'picker_id' => 'Picker ID',
        'pickup_date' => 'Pickup Date',
        'picker_name' => 'Picker Name',
        'pick_num' => 'Pick List Number',
        'plan_code' => 'Plan Code',
        'plan_id' => 'Plan ID',
        'plan_name' => 'Plan',
        'plan_labour_per_piece' => 'Planned Labour per Piece',
        'plan_machine_per_piece' => 'Planned Machine per Piece',
        'planned' => 'Planned',
        'po' => 'PO',
        'po_line' => 'PO Line',
        'po_num' => 'PO Number',
        'purchase_order' => 'Purchase Order',
        'po_return' => 'Purchase Order Return',
        'po_nums' => 'Purchase Order Number',
        'po_rel' => 'Release',
        'position' => 'Position',
        'post_all_by' => 'Post All By',
        'posted' => 'Posted',
        'posted_by' => 'Posted By',
        'posted_date' => 'Posted Date',
        'posted_qty' => 'Posted Qty',
        'print_inv_count' => 'Print Count Sheet Status',
        'print_sorting' => 'Print Sorting',
        'processed' => 'Processed',
        'product_code' => 'Product Code',
        'production' => 'Production',
        'price' => 'Price',
        'pick_ship_by' => 'Pick and Ship by',
        'port' => 'Port',
        'partner_name' => 'Partner Name',
        'partner_desc' => 'Partner Description',
        'postal_code' => 'Postal Code',
        'package_type' => 'Package Type',
        'picking_locs' => 'Picking Location',


        'list_partner' => 'Partner List',
        'list_site' => 'Registered Site List',
        // Q
        'qty' => 'Quantity',
        'qty_allocate' => 'Qty Allocate',
        'qty_allocated' => 'Qty Allocated',
        'qty_available' => 'Qty Available',
        'qty_balance' => 'Qty Balance',
        'quantity_completed' => 'Quantity Completed',
        'qty_completed' => 'Qty Completed',
        'qty_contained' => 'Qty Contained',
        'qty_counter1' => 'Qty Counter 1',
        'qty_counter2' => 'Qty Counter 2',
        'qty_issued' => 'Qty Issued',
        'qty_loss' => 'Qty Lost',
        'qty_moved' => 'Qty Moved',
        'qty_matl' => 'Qty Material',
        'qty_on_hand' => 'Qty on Hand',
        'qty_on_rsvd' => 'Qty Reserved',
        'qty_ordered' => 'Qty Ordered',
        'quantity_ordered' => 'Quantity Ordered',
        'qty_ordered_conv' => 'Qty Ordered Conv',
        'qty_per' => 'Qty Per',
        'qty_picked' => 'Qty Picked',
        'qty_rec' => 'Qty Received',
        'qty_received' => 'Qty Received',
        'qty_released' => 'Qty Released',
        'quantity_released' => 'Quantity Released',
        'qty_released_conv' => 'Qty Released Conv',
        'qty_required' => 'Qty Required',
        'qty_returned' => 'Qty Returned',
        'qty_scrapped' => 'Qty Scrapped',
        'qty_ship' => 'Qty Shipped',
        'qty_shipped' => 'Qty Shipped',
        'quantity_shipped' => 'Quantity Shipped',
        'qty_shortage' => 'Qty Shortage',
        'qty_to_pick' => 'Qty to Pick',
        'qty_to_reorder' => 'Qty to Reorder',
        'quickbook_wh' => "",
        'quickbook_app_id' => "QuickBooks Company ID",
        'quickbook_wh' => "Default Warehouse",
        'qty_variance' => 'Qty Variance',
        // R
        'rank' => 'Rank',
        'reason' => 'Reason',
        'reason_num' => 'Reason Code',
        'reason_code' => 'Reason Code',
        'reason_desc' => 'Reason Description',
        'received_by' => 'Received By',
        'received_date' => 'Received Date',
        'received_uom' => 'UOM',
        'ref_num' => 'Ref Num',
        'ref_line' => 'Ref Line',
        'ref_release' => 'Ref Release',
        'reference' => 'Reference Number',
        'reference_line' => 'Reference Line',
        'reference_rel' => 'Reference Release',
        'reg_num' => 'Registration Number',
        'registered_user' => 'Active Registered User',
        'release' => 'Release',
        'released' => 'Released',
        'released_date' => 'Released Date',
        'remark' => 'Remark',
        'remarks' => 'Remarks',
        'reorder_level' => 'Reorder Level',
        'res_desc' => 'Machine Description',
        'res_id' => 'Machine ID',
        'returned_by' => 'Returned By',
        'review' => 'Review',
        'revision_num' => 'Revision Number',
        'round_up_total' => 'Round Up Total (No Decimal)',
        'rounding' => 'Rounding Decimals',
        'route_listing' => 'Route Listing for Job Number: ',
        'run_hours' => 'Run Hours',
        'run_time' => 'Run Time(Hrs)',
        'running' => 'Running',
        'run_allocation' => 'Run Allocation',
        'return_request' => 'Return Request',
        'rows'=>"Rows",
        // S
        'sst' => 'SST 6% (6%)',
        'subtotal' => 'SUBTOTAL',
        'scrap_factor' => 'Scrap Factor',
        'schedule_receive_date' => 'Scheduled Receipt Date',
        'schedule_ship_date' => 'Scheduled Ship Date',
        'segment' => 'Segment',
        'select_order_lines' => 'Select Order Lines',
        'selected_count_sheet_lines' => 'Selected Count Sheet Lines',
        'selected_order_lines' => 'Selected Order Lines',
        'selected_lines' => 'Selected Lines',
        'selected_GRPO' => 'Selected GRPO',
        'selected_DO' => 'Selected DO',
        'selected_return_request' => 'Selected Return Request',
        'selected_inv_transfer' => 'Selected Inventory Transfer',
        'select_user_group' => 'Select User Group',
        'sequence' => 'Sequence',
        'seq' => 'Seq.',
        'setup_hours' => 'Setup Hours',
        'setup_time' => 'Setup Time(Hrs)',
        'semi_finished_goods' => 'Semi-Finished Goods',
        'sheet_num' => 'Sheet Num',
        'shift' => 'Shift',
        'ship_uom' => 'UOM',
        'ship_via' => 'Ship Via',
        'shipped_date' => 'Shipped Date',
        'shipment' => 'Shipment',
        'shipment_id' => 'Shipment ID',
        'single_item' => 'Single Item',
        'single_order' => 'Single Order',
        'site' => 'Site',
        'site_id' => 'Site ID',
        'site_info' => 'Site Info',
        'site_name' => 'Site Name',
        'source' => 'Source',
        'source_pallet' => 'Pallet Source',
        'start_date_plan' => 'Start Date',
        'start_datetime' => 'Start Date Time',
        'start_time_date' => 'Start Time Date',
        'state' => 'State',
        'status' => 'Status',
        'stop_date' => 'Stop Date',
        'stop_datetime' => 'Stop Date Time',
        'stop_time' => 'Stop Time',
        'street' => 'Street',
        'subject' => 'Subject',
        'started_time' => 'Started Time',
        'submitted_by' => 'Submitted By',
        'submitted_date' => 'Submitted Date',
        'submitted_time' => 'Submitted Time',
        'started_date' => 'Started Date',
        'success' => 'Success',
        'suffix' => 'Suffix',
        'summary' => 'Summary',
        'support' => 'Write to Us',
        'start_running_number' => 'Start Running Number',
        'site_administration' => 'Site Administrator',
        'site_owner' => 'Site Owner',
        'shipping_zone' => 'Shipping Zone',
        'def_shipping_zone' => 'Default Shipping Zone',
        'shipping_zone_code' => 'Shipping Zone',
        'shipping_zone_rank' => 'Shipping Zone Rank',
        'shipping_zone_code1' => 'Shipping Zone Code',
        'std_actual' => 'Std/Actual',
        // T
        'table_name' => 'Table Name',
        'task_num' => 'Task Num',
        'task_name' => 'Task Name',
        'task_desc' => 'Task Description',
        'time' => 'Start Time',
        'timezone' => 'Timezone',
        'to' => 'To',
        'to_variable' => 'To :variable',
        'to_site' => 'To Site',
        'to_whse' => 'To Warehouse',
        'tolerance' => 'Tolerance% (+-)',
        'tot' => 'Total Time(Hrs)',
        'total' => 'TOTAL',
        'total_time' => 'Total Time(Hrs)',
        'total_qty_allocate' => 'Total Qty Allocate',
        'total_qty_allocated' => 'Total Qty Allocated',
        'total_item_num' => 'Total number of item(s)',
        'total_order_num' => 'Total number of order(s)',
        'total_picklist_num' => 'Total number of pick list(s)',
        'ttl_qty_scrapped' => 'Total Qty Scrapped',
        'to_vendor' => 'To Vendor',
        'to_cust' => 'To Customer',
        'tracking_num' => 'Tracking Number',
        'trans' => 'Transaction',
        'transaction_list' => 'Transaction List',
        'transaction_type' => 'Transaction Type',
        'transaction_date' => 'Transaction Date',
        'trans_date' => 'Trans. Date',
        'trans_num' => 'Trans. Num',
        'trans_type' => 'Trans. Type',
        'trans_uom' => 'UOM',
        'trans_qty' => 'Qty Issued',
        'trn_line' => 'Line',
        'transfer_order' => 'Transfer Order',
        'trn_linenum' => 'Transfer Order Line Number',
        'trn_loc' => 'Transit Loc',
        'trn_num' => 'TO Number',
        'type' => 'Type',
        'total_occurrences' => 'Total Occurrences',
        'ttl_standard_weight' => 'Total Standard Weight (kg)',
        'ttl_actual_weight' => 'Total Actual Weight (kg)',
        'ttl_fiber_issued' => 'Total Fiber Issued (kg)',
        'ttl_fiber_recovered' => 'Total Fiber Recovered (kg)',
        'prod_standard_efficiency' => 'Production Standard Efficiency (%)',
        'prod_actual_efficiency' => 'Production Actual Efficiency (%)',
        // U
        'uncounted' => 'Uncounted',
        'update_allocation' => 'Update Allocation',
        'user_group' => 'User Group',
        'users' => 'Users',
        'unit_weight' => 'Unit Weight',
        'uom' => 'UOM',
        'uom_num' => 'UOM',
        'uom_from' => 'Base UOM',
        'uom_num_from' => 'UOM Number From',
        'uom_num_to' => 'UOM Number To',
        'uom_to' => 'Converted UOM',
        'up_count' => 'Update Count',
        'url' => 'URL',
        'usage_restriction' => 'Usage Restriction',
        'user' => 'User',
        'user_desc' => 'User Description',
        'user_info' => 'User Info',
        'username' => 'Username',
        'uom_conversion' => 'UOM Conversion',
        // V
        "value" => "Value",
        'variance' => 'Variance',
        'vehicle_num' => 'Vehicle Number',
        'vend_cust_num' => 'Vend Number',
        'vend_do' => 'Vendor DO',
        'vend_name' => 'Vendor Name',
        'vend_num' => 'Vendor Code',
        'vendor' => 'Vendor',
        'vend_status' => 'Vendor Status',
        'view' => 'View',
        // W
        'waiting' => 'Waiting',
        'wc_num' => 'Work Center',
        'wc_desc' => 'Work Center Description',
        'web_user' => 'Web User',
        'whse_address' => 'Address',
        'whse_name' => 'Description',
        'whse_num' => 'Warehouse',
        'whse_code' => 'Warehouse Code',
        'width'=>'Width',
        // Y
        'yield' => 'Yield',
        'YY' => '[YY] (2-digit year)',
        'YYYY' => '[YYYY] (4-digit year)',
        // Z
        'zip' => 'Zip',
        'zone' => 'Zone',
        'zone_small' => 'zone',
        'zone_code' => 'Zone Code',
        'zones' => 'Zones',
    ],
    'list' => [
        // C
        'co_lines' => 'Customer Order Lines',
        'co_line' => 'Customer Order Line',
        'co_linenum' => 'Customer Order Line Number',
        'co_rels' => 'Customer Order Releases',
        'cos' => 'Customer Orders',
        'co_nums' => 'Customer Order Number',
        'customers' => 'Customers',
        'customers_add' => 'Customers Address',
        // E
        'employees' => 'Employees',
        // F
        'from_locations' => 'From Locations',
        // G
        'grn_line' => 'GRN Line',
        // I
        'items' => 'Items',
        'item_locations' => 'Item Locations',
        // J
        'jobs' => 'Job Orders',
        'job_routes' => 'Operations',
        // L
        'label' => 'Labels',
        'locations' => 'Locations',
        'lot_locations' => 'Lot Locations',
        'lot_numbers' => 'Lot Numbers',
        'lpn_num' => 'LPN Numbers',
        // M
        'machines' => 'Machines',
        'matl_items' => 'Material Items',
        // P
        'po_lines' => 'Purchase Order Lines',
        'po_line' => 'Purchase Order Line',
        'po_linenum' => 'Purchase Order Line Number',
        'po_rels' => 'Purchase Order Releases',
        'pos' => 'Purchase Orders',
        'product_codes' => 'Product Codes',
        // R
        'reasons' => 'Reasons',
        // S
        'stage_loc' => 'Stage Locs',
        'shipping_zone' => 'Shipping Zone',
        // T
        'to_lines' => 'Transfer Lines',
        'to_locations' => 'To Locations',
        'tos' => 'Transfer Orders',
        // U
        'uoms' => 'UOMs',
        'uomconversion' => 'UOM Conversion',
        // V
        'vend_lots' => 'Vend Lots',
        // W
        'warehouses' => 'Warehouses',
        'work_centers' => 'Work Centers',
        // Z
        'zones' => 'Zones',
    ],
    'menu' => [
        // A
        'admins' => 'Administration',
        'allocations' => 'Allocations',
        'alternate_barcodes' => 'Alternate Barcodes',
        // B
        'boms' => 'Bill of Materials',
        'background_tasks' => 'Background Tasks',
        'barcode_admin' => 'Barcode Admin',
        // C
        'customers' => 'Customers',
        'customer_orders' => 'Customer Orders',
        'custom_fields' => 'Custom Fields',
        'count_groups' => 'Count Groups',
        //D
        'dashboard' => 'Dashboard',
        // E
        'employees' => 'Employees',
        'export_log' => 'Export Log',
        // F
        'files' => 'Files',
        // G
        'grn' => 'Goods Receiving Notes',
        // H
        'history' => 'History',
        'home' => 'Home',
        // I
        'incoming' => 'Incoming',
        'import_log' => 'Import Log',
        'integration_log' => 'Integration Log',
        'item_warehouses' => 'Item Warehouses',
        'item_locations' => 'Item Locations',
        'item_lot_locations' => 'Item Lot Locations',
        'inventory' => 'Inventory',
        'inventory_count' => 'Inventory Count',
        'items' => 'Items',
        'indirect_tasks' => 'Indirect Tasks',
        // J
        'job_trans' => 'Job Transactions',
        'job_orders' => 'Job orders',
        'job_routes' => 'Job Routes',
        'job_matls' => 'Job Materials',
        // L
        'lots' => 'Lots',
        'labels' => 'Labels',
        'logout' => 'Logout',
        'locations' => 'Locations',
        'labor_machine' => 'Labor & Machine',
        'lot_number_definition' => 'Lot Number Definition',
        'lpn_definition' => 'License Plate Number Definition',
        // M
        'machines' => 'Machines',
        'machine_trans' => 'Machine Transactions',
        'maintenance' => 'Maintenance',
        'master_files' => 'Master Files',
        'matl_trans' => 'Material Transactions',
        // N
        'number_definition' => 'Number Definitions',
        // O
        'outgoing' => 'Outgoing',
        'others' => 'Others',
        'objects' => 'Objects',
        'order_number_definition' => 'Order Number Definition',
        // P
        'pallet' => 'Pallets',
        'printers' => 'Printers',
        'purchase' => 'Purchase',
        'production' => 'Production',
        'productions' => 'Productions',
        'pick_lists' => 'Pick Lists',
        'product_codes' => 'Product Codes',
        'purchase_orders' => 'Purchase Orders',
        'qrcodes' => 'QR Codes',
        // R
        'reports' => 'Reports',
        'reason_codes' => 'Reason Codes',
        // S
        'sales' => 'Sales',
        'settings' => 'Settings',
        'connection_settings' => "Connected Apps",
        'site_settings' => 'Site Settings',
        'switch_to_mobile' => 'Switch to Mobile',
        // T
        'transaction' => 'Transaction',
        'transactions' => 'Transactions',
        'transfer_orders' => 'Transfer Orders',
        'transparam' => 'Transaction Parameters',
        // U
        'user' => 'User',
        'users' => 'Users',
        'uoms' => 'UOMs',
        'uom_conversions' => 'UOM Conversions',
        'user_groups' => 'User Groups',
        // V
        'vendors' => 'Vendors',
        'value_change_log' => 'Value Change Log',
        //W
        'warehouse' => 'Warehouse',
        'warehouses' => 'Warehouses',
        'work_centers' => 'Work Centers',
        //Z
        'zones' => 'Zones',
    ],
    'placeholder' => [
        // A
        'address' => "Address",
        'add_address' => "Add Address",
        // B
        'bom_status' => 'BOM Status',
        'bom_revision' => 'BOM Revision',
        // C
        'constant_divider' => 'Constant / Divider',
        'co_line' => 'CO Line',
        'co_num' => 'CO Number',
        'co_rel' => 'CO Release',
        'count_group' => 'Count Group Name',
        'count_group_desc' => 'Count Group Desc',
        'created_by' => 'Created By',
        'cust_name' => 'Customer Name',
        'cust_num' => 'Customer Code',
        'cutoff_qty' => 'Qty Cutoff',
        // D
        'desc' => 'Description',
        'doc' => 'Document',
        'driver_name' => 'Driver Name',
        'driver_contact' => 'Driver Contact',
        // E
        'emp_name' => 'Name',
        'employee_name' => 'Employee Name',
        'emp_num' => 'Employee ID',
        // F
        'from_co_num' => 'From CO Number',
        'from_date' => 'From Date',
        'from_item_num' => 'From Item',
        'from_loc' => 'From Location Number',
        'from_trn_num' => 'From TO Number',
        'from_whse' => 'From Warehouse',
        // I
        'item' => 'Item',
        'item_desc' => 'Item Description',
        'item_num' => 'Item',
        'internal_item_num' => 'Internal Item Code',
        // J
        'job_num' => 'Job Order',
        // L
        'last_count_date' => 'Last Count Date',
        'loc_name' => 'Description',
        'loc_num' => 'Location',
        'lot_num' => 'Lot Number',
        // M
        'matl_item' => 'Material Item',
        'modified_date' => 'Modified Date',
        // O
        'oper_num' => 'Operation',
        // P
        'packing_loc' => 'Packing Location',
        'picker_id' => 'Picker ID',
        'picker_name' => 'Picker Name',
        'pick_num' => 'Pick List Number',
        'pickup_date' => 'Pickup Date',
        'po_num' => 'PO Number',
        'po_line' => 'PO Line',
        'po_rel' => 'PO Release',
        'posted_by' => 'Posted By',
        'posted_date' => 'Posted Date',
        'posted_qty' => 'Qty Posted',
        'product_code' => 'Product Code',
        // Q
        'qty' => 'Quantity',
        'qty_counter1' => 'Qty Counter 1',
        'qty_counter2' => 'Qty Counter 2',
        'qty_loss' => 'Qty Lost',
        'qty_on_hand' => 'Qty on Hand',
        'qty_required' => 'Qty Required',
        'qty_to_receive' => 'Qty to Receive',
        'qty_to_issue' => 'Qty to Issue',
        // R
        'rank' => 'Rank',
        'reason_code' => 'Reason Code',
        'reason_num' => 'Reason Code',
        'reorder_level' => 'Re-Order Level',
        'res_desc' => 'Description',
        'res_id' => 'Machine ID',
        'rounding' => 'Rounding Decimals',
        // S
        'schedule_receive_date' => 'Scheduled Receipt Date',
        'schedule_ship_date' => 'Scheduled Ship Date',
        'scrap_factor' => 'Scrap Factor',
        'shift' => 'Shift',
        'stage_loc' => 'Stage Location',
        'status' => 'Status',
        'stop_date' => 'Stop Date',
        'stop_time' => 'Stop Time',
        'suffix' => 'Suffix',
        'shipping_zone' => 'Shipping Zone',
        // T
        'task_desc' => 'Description',
        'task_name' => 'Indirect Task',
        'to_co_num' => 'To CO Number',
        'to_date' => 'To Date',
        'to_item_num' => 'To Item',
        'to_loc' => 'To Location Number',
        'to_num' => 'Transfer Order Number',
        'to_trn_num' => 'To TO Number',
        'to_whse' => 'To Warehouse',
        'trans_date' => 'Trans. Date',
        'trn_line' => 'Transfer Order Line',
        'trn_loc' => 'Transit Location',
        'trn_num' => 'Transfer Order Number',
        'type' => 'Type',
        // U
        'unit_weight' => 'Unit Weight',
        'uom' => 'Unit',
        'uom_desc' => 'Description',
        // V
        'variance' => 'Variance',
        'vehicle_num' => 'Vehicle Number',
        'vend_do' => 'Vendor DO',
        'vend_lot' => 'Vendor Lot',
        'vend_name' => 'Vendor Name',
        'vend_num' => 'Vendor Code',
        // W
        'whse_address' => 'Address',
        'whse_name' => 'Description',
        'wc_num' => 'Work Center',
        'whse_num' => 'Warehouse',
        // Z
        'zone' => 'Zone',
    ],
    'option' => [
        // A
        'active' => 'Active',
        'all' => 'All',
        'alternateBarcode' => 'AlternateBarcode',
        // B
        'by_lot' => 'By Lot',
        'bom' => 'Bom',
        // C
        'closed' => 'Closed',
        'CO' => 'CO',
        'co_num' => 'Customer Order',
        'company_name' => 'Company Name',
        'completed' => 'Completed',
        'COReturn' => 'CO Return',
        'COShip' => 'CO Ship',
        'customer' => 'Customer',
        'custom_field_decimals' => 'Numeric',
        'custom_field_list' => 'List',
        'custom_field_textfield' => 'Long Text',
        // D
        'default' => 'Default',
        'disabled' => 'Disabled',
        'DownTime' => 'Down Time',
        // E
        'EndJob' => 'End Job',
        'employee' => 'Employee',
        'export' => 'Export',
        // F
        'FRCoPick' => 'CO Pick From',
        'FRCoUnpick' => 'CO Unpick From',
        'FRPutAway' => 'Put Away From',
        'FRStockMove' => 'Stock Move From',
        // G
        'global' => 'Global',
        'grn' => 'GRN',
        // I
        'import' => 'Import',
        'inactive' => 'Inactive',
        'IndirectTask' => 'Indirect Task',
        'InvCount' => 'Inventory Count',
        'item' => 'Item',
        'itemLocation' => 'ItemLocation',
        'itemWhse' => 'ItemWarehouse',
        // J
        'job' => 'Job',
        'JobMaterialIssue' => 'Job Material Issue',
        'JobMaterialUnIssue' => 'Job Material Return',
        'jobMatl' => 'JobMatl',
        'JobMatl' => 'Job Matl',
        'JobOrder' => 'Job Order',
        'JobReceipt' => 'Job Receipt',
        'JobReturn' => 'Job Return',
        'jobRoute' => 'JobRoute',
        'JobRun' => 'Job Run',
        'JobScrap' => 'Job Scrap',
        'jo_num' => 'Job Order',
        // L
        'location' => 'Location',
        'locked' => 'Locked',
        'lot' => 'Lot',
        'lotLocation' => 'LotLocation',
        // M
        'machine' => 'Machine',
        'MachineDowntime' => 'Machine Downtime',
        'MachineRun' => 'Machine Run',
        'manufactured' => 'Manufactured',
        'MiscIssue' => 'Misc Issue',
        'MiscReceipt' => 'Misc Receipt',
        'mobile' => 'Mobile',
        // O
        'open' => 'Open',
        'order' => 'Order',
        // P
        'past_due' => 'Past Due',
        'PickNShip' => 'Pick N Ship',
        'pick_from' => 'Pick From',
        'pick_to' => 'Pick To',
        'PO' => 'PO',
        'POReturn' => 'PO Return',
        'POReceipt' => 'PO Receipt',
        'productCode' => 'ProductCode',
        'purchased' => 'Purchased',
        'PutAwayItem' => 'PutAwayItem',
        'po_num' => 'Purchase Order',
        'Pallet_cust' => 'For Customer',
        'Pallet_res' => 'No Restriction',
        'pallet' => 'Pallet',
        'pallet_build_from' => 'Pallet Build From',
        'pallet_build_to' => 'Pallet Build To',
        'pallet_move_from' => 'Pallet Move From',
        'pallet_move_to' => 'Pallet Move To',
        'pallet_letdown_from' => 'Pallet Letdown From',
        'pallet_letdown_to' => 'Pallet Letdown To',
        'pallet_transfer_from' => 'Pallet Transfer From',
        'pallet_transfer_to' => 'Pallet Transfer To',
        'pallet_destruct_from' => 'Pallet Destruct From',
        'pallet_destruct_to' => 'Pallet Destruct To',
        // Q
        'Quarantine' => 'Quarantine',
        // R
        'reasonCode' => 'ReasonCode',
        'released' => 'Released',
        'review' => 'Review',
        'revision' => 'Revision',
        // S
        'status' => 'Status',
        'selected' => 'Select Type',
        'selected_status' => 'Select Status',
        'SetupJob' => 'Job Setup',
        'shippingZone' => 'ShippingZone',
        'Stock' => 'Stock',
        // T
        'task' => 'Task',
        'TO' => 'TO',
        'TOCoPick' => 'CO Pick To',
        'TOCoUnpick' => 'CO Unpick To',
        'TOLine' => 'TO Line',
        'TOLoss' => 'TO Lost',
        'TOPutAway' => 'Put Away To',
        'TOReceipt' => 'TO Receipt',
        'TOReceiptFrom' => 'TO Receipt From',
        'TOReceiptTo' => 'TO Receipt To',
        'TOShip' => 'TO Ship',
        'TOShipping' => 'TO Ship',
        'TOShipFrom' => 'TO Ship From',
        'TOShipTo' => 'TO Ship To',
        'TOStockMove' => 'Stock Move To',
        'trans_num' => 'Transfer Order',
        'TransOrder' => 'TransOrder',
        'Transit' => 'Transit',
        'type' => 'Type',
        // U
        'unit' => 'Unit',
        'unpick_from' => 'Unpick From',
        'unpick_to' => 'Unpick To',
        'uom' => 'UOM',
        // V
        'vendor' => 'Vendor',
        // W
        'web' => 'Web',
        'whse' => 'Warehouse',
        'workCenter' => 'WorkCenter',
        'WIPMove' => 'WIP Move',
        //Z
        'zone' => 'Zone',
    ],
    'button' => [
        // A
        'add' => 'Add',
        'add_alternate_barcode' => 'Add Alternate Barcode',
        'add_params' => "Add Params",
        'add_co_line' => 'Add CO Line',
        'add_item' => 'Add Item',
        'add_count_items' => 'Add Count Items',
        'add_count_sheet' => 'Add Count Sheet',
        'add_grn_line' => 'Add GRN Line',
        'add_job_route' => 'Add Job Route',
        'add_material' => 'Add Material',
        'add_po_line' => 'Add PO Line',
        'add_to_line' => 'Add TO Line',
        'add_to_picklist' => 'Add to Pick List',
        'apply' => 'Apply',
        'add_attachments' => 'Add Attachments',
        'add_pallet_line' => 'Add Line',
        // B
        'batch_assign' => 'Batch Assign',
        'batch_edit' => 'Batch Edit',
        'batch_update' => 'Batch Update',
        'back' => 'Back',
        // C
        'cancel' => 'Cancel',
        'confirm' => 'Confirm',
        'close' => 'Close',
        'close_picklist' => 'Close Pick List',
        'copy_job_bom' => 'Copy Job BOM to Item BOM',
        'copy_site' => 'Copy PHIDemoData to PHIDemoMaster',
        'create_job_from_bom' => 'Create Job from BOM',
        'create_site' => 'Create Site',
        'connect' => 'Connect',
        'connect_qb' => 'Connect to QuickBooks',
        // D
        'do' => 'DO',
        'delete' => 'Delete',
        'delete_all' => 'Delete All',
        'delete_grn' => 'Delete GRN',
        'delete_po' => 'Delete PO',
        'delete_co' => 'Delete CO',
        'delete_to' => 'Delete TO',
        'delete_pallet' => 'Delete',
        'delete_co_all_line' => 'Delete CO & All CO Line',
        'delete_po_all_line' => 'Delete PO & All PO Line',
        'delete_route' => 'Delete Route',
        'delete_site' => 'Delete Site',
        'delete_to_all_line' => 'Delete TO & All TO Line',
        // E
        'edit' => 'Edit',
        'edit_grn' => 'Edit GRN',
        'edit_po' => 'Edit PO',
        'edit_co' => 'Edit CO',
        'edit_to' => 'Edit TO',
        'edit_pallet' => 'Edit',
        'edit_co_header' => 'Edit CO Header',
        'edit_picklist_header' => 'Edit Pick List Header',
        'edit_po_header' => 'Edit PO Header',
        'edit_to_header' => 'Edit TO Header',
        // F
        'filter' => 'Filter',
        // G
        'generate' => 'Generate',
        'generate_allocation' => 'Generate Allocation',
        'generate_allocation_line' => 'Generate Allocation Line',
        'generate_count_sheets' => 'Generate Count Sheets',
        'generate_count_list' => 'Add Count List',
        'generate_count_sheet' => 'Generate Count Sheet',
        'generate_picklist' => 'Generate Pick List',
        'GRPO' => 'GRPO',
        'generate_url' => 'Generate URL',
        // I
        'import_data' => ' Import',
        'inv_transfer' => 'Inventory Transfer',
        // N
        'next' => 'Next',
        // P
        'post' => 'Post',
        'print' => 'Print',
        'proceed' => 'Proceed',
        'process' => 'Process',
        'purge' => 'Purge Record(s)',
        'purge_sheet' => 'Purge Sheet',
        // R
        'register' => 'Register',
        'remove' => 'Remove',
        'rel_cb' => 'Release Batch',
        'reset' => 'Reset',
        'reset_demo_data' => 'Reset Demo Data',
        'return_request' => 'Return Request',
        // S
        'save' => 'Save',
        'save_reload' => 'Save & Reload',

        'save_back' => 'Save & Back',
        'save_add_item' => 'Save & Add Item',
        'save_add_line' => 'Save & Add New Line',
        'save_add_material' => 'Save & Add New Material',
        'save_add_po_line' => 'Save & Add PO Line',
        'save_add_co_line' => 'Save & Add CO Line',
        'save_add_route' => 'Save & Add New Route',
        'save_add_sheets' => 'Save & Add Sheet Lines',
        'save_add_to_line' => 'Save & Add TO Line',
        'save_back_job_order' => 'Save & Back to Job Order',
        'save_back_job_route' => 'Save & Back to Job Route',
        'save_release' => 'Save & Release',
        'select' => 'Select',
        'stop' => 'Stop',
        'show_added_order_lines' => 'Show Added Order Lines',
        // U
        'upgrade' => 'Upgrade',
        'upload' => 'Upload',
        // V
        'view_attachments' => 'View Attachments',
    ],
    'message' => [
        'import_submitted' => 'File importation task submitted. Please check the status in Background Tasks',
        'export_submitted' => 'File exportation task submitted. Please check the status in Background Tasks',
        'not_allow_lesser_than' => ':resource1 is not allow to be lesser than :resource2.',
        'not_allow_zero' => ':resource is not allow to be 0',
        'cannot_be_same' => ':resource1 and :resource2 cannot be same.',
        'confirm_last_receive_po' => 'Confirm this is Last Receive? <br>Next transaction will create in a new GRPO',
        'confirm_last_return_po' => 'Confirm this is Last Return? <br>Next transaction will create in a new Return Request.',
        'confirm_last_receive_to' => 'Confirm this is Last Receive? <br>Next transaction will create in a new Inventory Transfer.',
        'confirm_last_return_co' => 'Confirm this is Last Return? <br>Next transaction will create in a new Return Request.',
        'confirm_last_pickandship' => 'Confirm this is Last Pick and Ship? <br>Next transaction will create in a new DO.',
        'select_material_item' => 'Select Material Item',
        'sure' => 'Are you sure?',
        'completed' => 'Packing completed for :resource',
        'emptypackage' => 'Empty the :resource?',
        'suredelete' => 'Delete the :resource(s)?',
        'suredelete1' => 'Delete the :resource?',
        'sureremove' => 'Remove the :resource(s)?',
        'create_packing' => 'Are you sure you want to create the packing list?',
        'stop' => 'Are you sure you want to stop the labor and machine?',
        'duplicate_operation_number' => 'There is a duplicate operation number',
        'duplicate_material_sequence_number' => 'There is a duplicate material sequence number',
        'must_have_atleast_one_operation_one_material' => 'Must have at least one operation and one material',
        'new_site_id' => 'The ID for your company. Will be used during login and cannot change.',
        'new_site_name' => 'Determine your site name, e.g. company name. Change is allowed.',
        'new_timezone' => 'Your local timezone.',
        'new_admin_name' => 'Admin name. Changes is not allowed.',
        'new_user_name' => 'Login username and cannot be changed.',
        'new_admin_email' => 'Contact email and cannot be changed.',
        'new_user_email' => 'Login email. Changes is allowed.',
        'new_admin_password' => 'Login password and change is allowed. The password must be 8-30 characters, and include a number, a symbol, a lower and an upper case letter.',
        'operation_must_have_one_material' => 'Each operation must have one material',
        'one_item_active_only' => 'Only one Item can be active at a time',
        'existing_active_bom' => 'There is existing active Bill of Materials for the item.',
        'material_cannot_same_as_item' => 'Material cannot be the same as BOM Item',
        'item_cannot_same_as_material' => 'BOM Item cannot be the same as Material',
        'logo' => 'Allowed file types : JPEG, PNG, JPG.  Max size: 140px x 140px',
        'maxchar' => 'Max characters reached',
        'maxnum' => 'Max numbers reached',
        'surecreate' => 'Create this new :resource(s)?',
        'sureadd' => 'Add :resource license(s)?',
        'sureupgrade' => 'Are you sure you want to upgrade current plan/license qty?',
        'sureallocate' => 'Allocation for order will override existing allocation of the order lines',
        'welcome' => 'Welcome to Axacute!',
        'register_admin' => 'You may need to create your company site in Axacute before you can start using the system.
                        The admin user is the first user to access the system. Admin is with privileges such as adding users and users groups.',
        'site_information' => 'Please fills up the details below to create your company site in Axacute.',
        'site_owner' => 'The Site Owner is the first user to access the system. Site Owner has full access to all functionalities of the system and cannot be removed.',
        'support' => 'Having trouble to sign up? Please contact ',
        'support1' => 'Having trouble to create site? Please contact ',
        'not_allow_more_one' => ':resource must less than 1',
        'item_cannot_same_with_parents' => 'Material cannot be the Parent Item of BOM Item',
        'bom_item_same_with_job_item' => 'This field cannot be the same as the Job Order item.',
    ],
    'printer' => [
        'name' => 'Name',
        'desc' => 'Description',
        'created_time' => 'Created Time',
        'modified_time' => 'Modified Time',
        'support' => 'Having trouble to sign up? Please contact ',
        'support1' => 'Having trouble to create site? Please contact ',
        'not_allow_more_one' => ':resource must less than 1',
        'item_cannot_same_with_parents' => 'Material cannot be the Parent Item of BOM Item',
    ]
        ]
?>
