<?php

namespace App\Services;
use App\View\TparmView;
use App\PurchaseOrderItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use App\UomConv;
use App\ItemLoc;
use App\LotLoc;
use App\GRNItem;
use App\Services\PreassignLotsService;
use App\Services\MatltransService;

class POService
{
    public static function  updatePoItemQty($po_num,$po_line,$po_rel,$qty){
        //Update Existing PO Number
            $tparm = new TparmView();
            $po_item = new PurchaseOrderItem();
            $po_item = $po_item->where('po_num',$po_num)->where('po_line',$po_line);
            if ($po_rel) {
                $po_item->where('po_rel',$po_rel);
            }
            $po_item = $po_item->first();

            $po_item->qty_received = number_format($po_item->qty_received + $qty, 10,'.','');
            $allowoverreceive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');
            if($allowoverreceive == 0){
                $qty_ordered = (float)$po_item->qty_ordered;
                $qty_calculated = (float)$po_item->qty_received - (float)$po_item->qty_returned;

                if($qty_ordered < $qty_calculated){
                    throw ValidationException::withMessages(['Qty to receive is more than Qty required']);
                }
            }
            //Change PO Status when it is set to auto close
            $tparm = new TparmView();
            $auto_close = $tparm->getTparmValue('POReceipt', 'auto_close');
            if ($auto_close == 1) {
                if ($po_item->tolerance > 0 && abs(($po_item->qty_received-$po_item->qty_returned-$po_item->qty_ordered) / $po_item->qty_ordered)*100 <= $po_item->tolerance) {
                    $po_item->rel_status = 'C';
                }
                else if($po_item->qty_received-$po_item->qty_returned >= $po_item->qty_ordered) {
                    $po_item->rel_status = 'C';
                }
            }
            //Save model
            $po_item->save();
        return true;
    }

    public static function  updateGrnItemQty($request){

        $grn_num = $request->grn_num;
        $grn_line = $request->grn_line;
        $qty = $request->grn_qty;
        //Update Existing PO Number
        $tparm = new TparmView();
        $grn_item = new GRNItem();
        $grn_item = $grn_item->where('grn_num',$grn_num)->where('grn_line',$grn_line)->first();

        $grn_item->qty_received = number_format($grn_item->qty_received + $qty, 10,'.','');
        $allowoverreceive = $tparm->getTparmValue('POReceipt', 'allow_over_receive');

        if($allowoverreceive == 0){ // cannot over receive
            // get the qty balance of the PO Line
            $po_balance = PurchaseOrderItem::where('po_num',$request->ref_num)
                                ->where('po_line',$request->ref_line)
                                ->get()->pluck('qty_balance')
                                ->first();

            // // get sum of the GRN net received for the same PO Line
            // $total_net_received = GRNItem::select(DB::raw('SUM(IFNULL(net_received,0)) as total_net_received'))
            //                         ->where('po_num',$request->ref_num)
            //                         ->where('po_line',$request->ref_line)
            //                         ->get()->pluck('total_net_received')
            //                         ->first();

            // throw error if qty to receive exceeds PO balance
            if(($qty) > $po_balance){ // + $total_net_received
                $tparm = new TparmView();
                $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
                $po_balance = number_format($po_balance, $unit_quantity_format);
                throw ValidationException::withMessages([__('error.mobile.lessthan', ['resource' => __('mobile.placeholder.qty') ]) . " PO Qty Balance [". $po_balance ."][". $grn_item->uom ."]"]);
            }
        }

        //Change GRN Status when it is set to auto close
        $tparm = new TparmView();
        $auto_complete_grn_line = $tparm->getTparmValue('POReceipt', 'auto_complete_grn_line');
        if ($auto_complete_grn_line == 1) {
            if($grn_item->qty_received-$grn_item->qty_returned >= $grn_item->qty_shipped) {
                $grn_item->status = 'C';
            }
        }

        //Save model
        $grn_item->save();

        POService::updatePoItemQty($request->ref_num, $request->ref_line, $request->po_rel, $qty);

        return true;
    }

    public static function updatePoReturn($po_num,$po_line,$po_rel,$qty){
        $po_item = new PurchaseOrderItem();
        $po_item = $po_item->where('po_num',$po_num)->where('po_line',$po_line)->where('po_rel',$po_rel)->lockForUpdate()->first();

        //Check if PO is Open
        if($po_item->status == 'C'){
            throw ValidationException::withMessages(['po_closed'=> 'PO status is Completed']);
        }
        //Update returned Qty
        $po_item->qty_returned += $qty;
        $qty_returnable = $po_item->qty_received - $po_item->qty_returned;

        //Check if returnable can be less than 0
        $tparm = new TparmView();
        $allowoverreturn = $tparm->getTparmValue('POReturn', 'allow_over_return');
        if($allowoverreturn == 0){
            if($qty_returnable < 0){
                throw ValidationException::withMessages(['qty_returnable'=>'Qty Returned is more than Qty Received']);
            }
        }

        //Change PO Status when it is set to auto close
        $tparm = new TparmView();
        $auto_close = $tparm->getTparmValue('POReceipt', 'auto_close');
        if($po_item->qty_received - $po_item->qty_returned >= $po_item->qty_ordered && $auto_close == 1){
            $po_item->rel_status = 'C';
        }

        $po_item->save();
        return true;
    }

    public static function executePOReturn($request) {

       // DB::beginTransaction();
      //  try {
            // Conversion for inventory and material trans
           // $request = UOMService::convertRequest($request);
            // Conversion for PO item using Qty and UOM converted to item's base UOM
           // $po_qty = UOMService::convertPORequest($request);
           // dd($request);
            $transType = config('icapt.transtype.po_return');

            $selectuom = $request->uom;
            $vendor_num = $request->vend_num;
            $poNum = new PurchaseOrderItem();
            $lineuom = $poNum->where('po_num', $request->ref_num)->where('po_line', $request->ref_line)->value('uom');
            if($request->item_num!="NON-INV"){
                if($request->lot_num != ""){
                    $baseuom = LotLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->where('item_num', $request->item_num)->value('uom');
                }
                else{
                    $baseuom = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $request->item_num)->value('uom');
                }
             }
             else{
                $baseuom = $lineuom;
             }



            $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, '', $vendor_num, $transType);
            // dd($getQtyConv);
            $base_qty = $getQtyConv['conv_qty_to_base']['qty'];
            $po_qty = $getQtyConv['conv_qty_to_line']['qty'];
            $uom_conv = $getQtyConv['conv_qty_to_line']['uom'];
            // dd($getQtyConv, $baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, $cust_num, '', $type_mode, $request->loc_num);
            $request['qty_conv'] = $base_qty;

           // $request['qty_conv'] =  $po_qty;


            //dd($request);
            $request['uom_conv'] = $getQtyConv['conv_qty_to_line']['uom'];
            //$requestTo['qty_conv'] = $getQtyConv['conv_qty_to_line']['qty'];
        //   dd($request,$transType);


            // if item is catch weight
            if(@$request->catch_weight == 1){
                $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $request->tolerance_uom, $transType, $transType);
            }
            else{
                if($request->item_num!="NON-INV"){
                    $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num,$request->loc_num,$request->item_num,-$base_qty,$request->lot_num,$request->uom_conv, 0, null, 'item');
                }
                //$request['qty_conv'] =  $base_qty;

                // $request['trans_type'] = $transType;
                // $request['trans_uom'] = $request->uom;
                // $request['trans_qty'] = -$request->qty;
                // $request->replace(['qty' => -$request->qty, 'qty_conv' => -$request->qty_conv]);
                $insertmatl = GeneralService::newMatlTrans($transType,$request);
                // $insertmatl = MatltransService::postTransaction($request);
                // update preassign lots
                PreassignLotsService::updatePreassignLot('po', $request->ref_num, $request->ref_line, $request->item_num, $request->lot_num, auth()->user()->site_id, ($po_qty * -1));
            }

            $updatePOItem = POService::updatePoReturn($request->ref_num,$request->ref_line,$request->ref_release,$po_qty);

            $po_rel = new PurchaseOrderItem();
            $poItem = $po_rel->where('po_num', $request->ref_num)->where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->first();

            if ($poItem->isDirty('rel_status') && $poItem->rel_status == 'C' && $poItem->po_header->canClose()){
                $poItem->po_header->po_status = 'C';
                $poItem->po_header->save();
            }
            else if ($poItem->isDirty('rel_status') && $poItem->rel_status == 'O'){
                if ($poItem->po_header->po_status = 'C') $poItem->po_header->update(['po_status' => 'O']);
            }

        return true;
    }
    public static function executeGRNReturn($request) {

        // DB::beginTransaction();
       //  try {
             // Conversion for inventory and material trans
            // $request = UOMService::convertRequest($request);
             // Conversion for PO item using Qty and UOM converted to item's base UOM
            // $po_qty = UOMService::convertPORequest($request);
            // dd($request);
             $transType = 'PO Return';

             $selectuom = $request->uom;
             $vendor_num = $request->vend_num;
             $poNum = new GRNItem();
             $lineuom = $poNum->where('grn_num', $request->grn_num)->where('grn_line', $request->grn_line)->value('uom');
             if($request->item_num!="NON-INV"){
                 if($request->lot_num != ""){
                     $baseuom = LotLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->where('item_num', $request->item_num)->value('uom');
                 }
                 else{
                     $baseuom = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->loc_num)->where('item_num', $request->item_num)->value('uom');
                 }
              }
              else{
                 $baseuom = $lineuom;
              }

             $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, '', $vendor_num, $transType);
             //dd($getQtyConv);
             $base_qty = $getQtyConv['conv_qty_to_base']['qty'];
             $po_qty = $getQtyConv['conv_qty_to_line']['qty'];
             $uom_conv = $getQtyConv['conv_qty_to_line']['uom'];
             // dd($getQtyConv, $baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, $cust_num, '', $type_mode, $request->loc_num);
             $request['qty_conv'] = $base_qty;

            // $request['qty_conv'] =  $po_qty;


             //dd($request);
             $request['uom_conv'] = $getQtyConv['conv_qty_to_line']['uom'];
             //$requestTo['qty_conv'] = $getQtyConv['conv_qty_to_line']['qty'];
           // dd($request,$transType);
             $updatePOItem = POService::updateGrnReturn($request->grn_num,$request->grn_line,$po_qty);

             if($request->item_num!="NON-INV"){
            //   $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num,$request->loc_num,$request->item_num,-$base_qty,$request->lot_num,$request->uom_conv);
             }
             //$request['qty_conv'] =  $base_qty;

            //  $insertmatl = GeneralService::newMatlTrans($transType,$request);

            //dd($insertmatl);

             $grnItem = GRNItem::where('grn_num', $request->grn_num)->where('item_num', 'like', '' . $request->item_num . '')->first();

             if ($grnItem->isDirty('status') && $grnItem->status == 'C' && $grnItem->grn_header->canClose()){
                 $grnItem->grn_header->grn_status = 'C';
                 $grnItem->grn_header->save();
             }
             else if ($grnItem->isDirty('status') && $grnItem->status == 'O'){
                 if ($grnItem->grn_header->grn_status = 'C') $grnItem->grn_header->update(['grn_status' => 'O']);
             }
        // }
       //  catch(\Exception $e)
        // {
            // DB::rollback();
            // throw $e;
        // }
        // DB::commit();
         return true;
     }

    public static function updateGrnReturn($grn_num,$grn_line,$qty){
        $grn_item = GRNItem::where('grn_num',$grn_num)->where('grn_line',$grn_line)->lockForUpdate()->first();

        //Check if PO is Open
        if($grn_item->status == 'C'){
            throw ValidationException::withMessages(['grn_closed'=> 'GRN status is Completed']);
        }
        //Update returned Qty
        $grn_item->qty_returned += $qty;
        $qty_returnable = $grn_item->net_received; // - $grn_item->qty_returned;

        $tparm = new TparmView();
        $allowoverreturn = $tparm->getTparmValue('POReturn', 'allow_over_return');
        if($allowoverreturn == 0){
            if($qty > $grn_item->net_received){
                throw ValidationException::withMessages(['qty_returnable'=>'Qty Returned is more than Qty Received']);
            }
        }

        //Change PO Status when it is set to auto close
        $tparm = new TparmView();
        $auto_close = $tparm->getTparmValue('POReceipt', 'auto_complete_grn_line');
        if($qty_returnable >= $grn_item->qty_shipped && $auto_close == 1){
            $grn_item->status = 'C';
        }

        $grn_item->save();
        return true;
    }
}
