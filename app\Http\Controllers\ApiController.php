<?php

namespace App\Http\Controllers;


use App\Services\SapCallService;
use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\AlternateBarcode;
use DB;

use App\Job;
use App\Bom;
use App\BomMatl;
use App\Loc;
use App\Lot;
use App\UOM;
use App\Item;
use App\Task;
use App\Zone;
use APp\CustomerOrderItem;
use App\LotLoc;
use App\Vendor;
use App\co_line;
use App\Counter;
use App\ItemLoc;
use App\JobMatl;
use App\Machine;
use App\PurchaseOrderItem;
use App\Customer;
use App\CustomerOrder;
use App\Employee;
use App\PicklistTestItems;
use App\PicklistSummaryDetails;
use App\Pack;
use App\IssuedLot;
use App\ItemWarehouse;
use App\PurchaseOrder;
use App\JobRoute;
use App\ShippingZone;
use App\StageLoc;
use App\UomConv;
use App\Warehouse;
use App\ReasonCode;
use App\WorkCenter;
use App\ProductCode;
use App\TransferLine;
use App\TransferOrder;
use App\View\CoPickView;
use App\View\TparmView;
use Illuminate\Http\Request;
use App\helpers;
use App\TransferOrderLinesSubline;
use App\CustomerAddress;
use App\Allocation;
use App\GRN;
use App\GRNItem;
use App\PackageType;
use App\Container;
use App\ContainerItem;
use App\SiteSetting;
use App\MetaDataExt;
use App\BundleBuilder;
use App\CustomerReturnLine;
use App\CustomerReturn;
use App\ProductionTrans;
use Egulias\EmailValidator\Result\Reason\Reason;
use PhpParser\Node\Expr\Cast\Object_;
use Camroncade\Timezone\Facades\Timezone;
use App\PreassignLots;

class ApiController extends Controller
{

    use \App\Traits\HasDefaultLoc;

    // Check Counter
    public function getCheckCount(Request $request)
    {
        $counter = $request->counter;
        if ($this->is_base64_string($counter)) {
            $counter = base64_decode($counter);
        }
        $cust = new Employee();
        $cust = $cust->where('emp_num', $counter)->first();
        //dd($cust->emp_status);
        if ($cust != null) {
            if ($cust->emp_status == 0) {
                return 'Inactive';
            }
        } else {
            return 'Invalid';
        }
        return $cust;
    }




    // Check checkContaminationCount by JR lot
    public function checkContaminationCount(Request $request)
    {
        if ($this->is_base64_string($request->jr_lot)) {
            $jr_lot = base64_decode($request->jr_lot);
        }

        @$getDataContaminationCount = ProductionTrans::select('contamination_count')->where('lot_num', $jr_lot)->value('contamination_count');
        return @$getDataContaminationCount ?? NULL;
    }


    public function getLPNdetails($co_num, $item_num, $co_line, $lpn_line = "", $lpn_num = "", $listData = "No", $type_mode)
    {

        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
            $lpn_line = base64_decode($lpn_line);
            $co_num = base64_decode($co_num);
            $item_num = base64_decode($item_num);
            $co_line = base64_decode($co_line);
            $listData = base64_decode($listData);
            $type_mode = base64_decode($type_mode);
        }
        // $co_num = 'TO0018';
        // $lpn_num = 'LPN2706230054';
        // $type_mode = 'TO Receipt';
        // $type_mode = 'CO Unpick';
        // $co_num = 'TestCoPick3';
        // $lpn_num = 'TestCoPick3';
        // $item_num = 'Item01';
        // $co_line = '1';
        // $lpn_line = '1';

        $splitlpn_line = explode(",", $lpn_line);
        $reqTotal = 0;
        $arrLineUom = array();



        // order line data
        if ($type_mode == 'TO Shipping') {
            $coDetails = TransferLine::where('trn_num', $co_num)->orderByRaw('CAST(trn_line as UNSIGNED) ASC')->get();
            $line = 'trn_line';
            $qtyName = 'qty';
            $userType = 'vend_num';
            $uomConvType = __('mobile.nav.to_shipping');
        } else if ($type_mode == 'TO Receipt') {
            $coDetails = TransferOrderLinesSubline::where('trn_num', $co_num)->orderByRaw('CAST(trn_line as UNSIGNED) ASC')->get();
            $line = 'trn_line';
            $qtyName = 'qty';
            $userType = 'vend_num';
            $uomConvType = __('mobile.nav.to_shipping');
        } else if ($type_mode == 'CO Unpick') {
            $coDetails = StageLoc::where('co_num', $co_num)->orderByRaw('CAST(co_line as UNSIGNED) ASC')->get();
            $cust_num = CustomerOrderItem::where('co_num', $co_num)->value('cust_num');
            $line = 'co_line';
            $qtyName = 'qty';
            $userType = 'cust_num';
            $uomConvType = __('mobile.nav.co_picking');
        } else {
            $coDetails = CustomerOrderItem::where('co_num', $co_num)->orderByRaw('CAST(co_line as UNSIGNED) ASC')->get();
            $line = 'co_line';
            $qtyName = 'qty';
            $userType = 'cust_num';
            $uomConvType = __('mobile.nav.co_picking');
        }

        foreach ($coDetails as $coDet) {
            if (!isset($coDetArr[$coDet->$line][$qtyName])) {
                $totalSumQty = 0;
            }
            $coDetArr[$coDet->$line]['id'] = $coDet->id;
            $coDetArr[$coDet->$line]['order_line'] = $coDet->$line;
            $coDetArr[$coDet->$line]['item_num'] = $coDet->item_num;
            $coDetArr[$coDet->$line]['uom'] = $coDet->uom;
            if ($type_mode == 'TO Shipping') {
                $coDetArr[$coDet->$line][$qtyName] = $coDet->qty_required - $coDet->qty_shipped;
            } else if ($type_mode == 'TO Receipt') {
                $totalSumQty = $totalSumQty + $coDet->qty_shipped;
                $coDetArr[$coDet->$line][$qtyName] = $totalSumQty;
            } else if ($type_mode == 'CO Unpick') {
                $totalSumQty = $totalSumQty + $coDet->qty_staged;
                $coDetArr[$coDet->$line][$qtyName] = $totalSumQty;
            } else {
                $coDetArr[$coDet->$line][$qtyName] = $coDet->qty_shortage;
            }
            $coDetArr[$coDet->$line]['vend_num'] = $coDet->$userType ?? null;
            $coDetArr[$coDet->$line]['cust_num'] = $coDet->$userType ?? $cust_num ?? null;
            $coDetArr[$coDet->$line]['status'] = 'NEW';
        }
        // dd($coDetArr);
        // lpn data
        $lpnDatas = ContainerItem::select('id', 'lpn_line', 'item_num', 'qty_contained', 'uom', 'lot_num')->where('lpn_num', $lpn_num)->orderBy('lpn_line', 'ASC')->get();
        foreach ($lpnDatas as $lpnData) {

            $lpnDetArr[$lpnData->lpn_line]['id'] = $lpnData->id;
            $lpnDetArr[$lpnData->lpn_line]['item_num'] = $lpnData->item_num;
            $lpnDetArr[$lpnData->lpn_line]['qty_contained'] = $lpnData->qty_contained;
            $lpnDetArr[$lpnData->lpn_line]['uom'] = $lpnData->uom;
            $lpnDetArr[$lpnData->lpn_line]['lot_num'] = $lpnData->lot_num;

            if ($lpnData->lot_num != "") {
                $arrItemDet[$lpnData->item_num][$lpnData->lot_num]['id'] = $lpnData->id;
                $arrItemDet[$lpnData->item_num][$lpnData->lot_num]['lpn_line'] = $lpnData->lpn_line;
                $arrItemDet[$lpnData->item_num][$lpnData->lot_num]['qty_contained'] = $lpnData->qty_contained;
                $arrItemDet[$lpnData->item_num][$lpnData->lot_num]['uom'] = $lpnData->uom;
            } else {
                $arrItemDet[$lpnData->item_num]['!NONLOTITEM!']['id'] = $lpnData->id;
                $arrItemDet[$lpnData->item_num]['!NONLOTITEM!']['lpn_line'] = $lpnData->lpn_line;
                $arrItemDet[$lpnData->item_num]['!NONLOTITEM!']['qty_contained'] = $lpnData->qty_contained;
                $arrItemDet[$lpnData->item_num]['!NONLOTITEM!']['uom'] = $lpnData->uom;
            }
        }

        // mashup order and lpn data
        foreach ($coDetArr as $key => $orderDetails) {
            $coDetArr[$key]['lpn_details'] = $arrItemDet[$orderDetails['item_num']] ?? [];
            if (count($coDetArr[$key]['lpn_details'] ?? []) == 0) {
                $coDetArr[$key]['status'] = 'Unfulfilled';
            }
        }

        // convert to order line uom
        foreach ($coDetArr as $key => $convertOrderLine) {
            $orderLineUom = $convertOrderLine['uom'];
            $item_num = $convertOrderLine['item_num'];
            $cust_num = $convertOrderLine['cust_num'];
            $vend_num = $convertOrderLine['vend_num'];
            $orderQty = $convertOrderLine['qty'];
            $qtyContainedArr[$key] = $orderQty;
            $total = 0;

            //get base uom
            $baseuom = Item::where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->value('uom');

            if ($convertOrderLine['status'] == 'NEW') {
                foreach ($convertOrderLine['lpn_details'] as $lot => $getLpnUom) {
                    $convertUom = UomConv::convertUOM($baseuom, $getLpnUom['uom'], $orderLineUom, $getLpnUom['qty_contained'], $item_num, $cust_num, '', $uomConvType);
                    $qty_contained = $convertUom['conv_qty_to_line']['qty'];
                    $total = $total + $qty_contained;
                    $coDetArr[$key]['lpn_details'][$lot]['qty_contained'] = $qty_contained;
                    $coDetArr[$key]['lpn_details'][$lot]['uom'] = $orderLineUom;
                    $coDetArr[$key]['lpn_details'][$lot]['item_num'] = $item_num;
                }
                // dd($total);
                if (!isset($itemQtyTotal[$convertOrderLine['item_num']])) {
                    $itemQtyTotal[$convertOrderLine['item_num']]['qty'] = $total;
                    $itemQtyTotal[$convertOrderLine['item_num']]['uom'] = $orderLineUom;
                }
            }
        }

        $arrStoreEng = array();
        // calculation for each co line qty with lpn qty
        foreach ($coDetArr as $k => $eqty) {
            if (isset($itemQtyTotal[$eqty['item_num']]['qty'])) {
                if ($eqty['uom'] != $itemQtyTotal[$eqty['item_num']]['uom']) {
                    $convertedTotal = UomConv::convertUOM($baseuom, $itemQtyTotal[$eqty['item_num']]['uom'],  $eqty['uom'],  $itemQtyTotal[$eqty['item_num']]['qty'], $eqty['item_num'], $cust_num, '', $uomConvType);
                    $totalConv = $convertedTotal['conv_qty_to_line']['qty'];
                } else {
                    $totalConv = $itemQtyTotal[$eqty['item_num']]['qty'];
                }
                $qty = $eqty['qty'] - $totalConv;
                if ($qty <= 0) {
                    $arrStoreEng[$k] = $eqty['qty'];
                    $arrStoreMatchUnmatch[$k] = $eqty['qty'];
                    $itemQtyTotal[$eqty['item_num']]['qty'] = abs($qty);
                } else {
                    $arrStoreEng[$k] = abs($itemQtyTotal[$eqty['item_num']]['qty']);
                    $arrStoreMatchUnmatch[$k] = abs($itemQtyTotal[$eqty['item_num']]['qty']);
                }
                $arrCheckOverfulfilled[$eqty['item_num']]['qty_bal'] = $qty;
                $arrCheckOverfulfilled[$eqty['item_num']]['order_line'] = $k;
            } else {
                $arrStoreMatchUnmatch[$k] = 0;
            }
        }

        // count by each line then get the lpn_line accordingly.
        $indicator = [];
        $arrStoreLpnLine = array();
        foreach ($coDetArr as $k => $eqty) {
            $line = "";
            $arrStoreMatchUnmatch[$k] = 0;
            foreach ($eqty['lpn_details'] as $lot => $lineDetails) {
                if (isset($indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot])) {
                    $convertUom1 = UomConv::convertUOM($baseuom, $indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot]['uom'],  $lineDetails['uom'],  $indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot]['qty'], $lineDetails['item_num'], $cust_num, '', $uomConvType);
                    $qty_contained = $convertUom1['conv_qty_to_line']['qty'];
                    $minusQty = $qty_contained;
                } else {
                    $minusQty = $arrStoreEng[$k];
                }
                $qty = $lineDetails['qty_contained'] - $minusQty;

                if (isset($coDetArr[$k]['lpn_line'])) {
                    $line = $line . ',' . $lineDetails['lpn_line'];
                } else {
                    $line = $lineDetails['lpn_line'];
                }

                if ($qty <= 0) {
                    $arrStoreLpnLine[$k][$lot]['line_qty'] = $lineDetails['qty_contained'];
                    $arrStoreLpnLine[$k][$lot]['lpn_line'] = $lineDetails['lpn_line'];
                    $indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot]['qty'] = 0;
                    $indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot]['uom'] = $eqty['uom'];
                    $arrStoreMatchUnmatch[$k] = $arrStoreMatchUnmatch[$k] + $lineDetails['qty_contained']; // change to converted uom value.
                    $coDetArr[$k]['lpn_line'] = $line;
                    $lpnQtyByLine[$k][$lineDetails['lpn_line']]['qty'] = abs($qty);
                } else {
                    if ($minusQty != 0) {
                        // $arrStoreLpnLine[$k][$lot]['line_qty'] = abs($arrStoreEng[$k]);
                        $arrStoreLpnLine[$k][$lot]['line_qty'] = abs($qty);
                        $arrStoreLpnLine[$k][$lot]['lpn_line'] = $lineDetails['lpn_line'];
                        $indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot]['qty'] = $qty;
                        $indicator[$lineDetails['lpn_line']][$lineDetails['item_num']][$lot]['uom'] = $eqty['uom'];
                        // $arrStoreMatchUnmatch[$k] = $arrStoreMatchUnmatch[$k] + $qty; // change to converted uom value.
                        $lpnQtyByLine[$k][$lineDetails['lpn_line']]['qty'] = abs($minusQty);
                        $coDetArr[$k]['lpn_line'] = $line;
                        break;
                    }
                }
            }
        }
        // dd($coDetArr, $eqty, $arrStoreMatchUnmatch, $arrCheckOverfulfilled, $itemQtyTotal,   $arrStoreLpnLine, $indicator);
        foreach ($coDetArr as $k => $eqty) {
            if (isset($arrCheckOverfulfilled[$eqty['item_num']])) {
                // if($eqty['qty'] <= $arrStoreMatchUnmatch[$k] && $arrCheckOverfulfilled[$eqty['item_num']]['qty_bal'] < 0 && $arrCheckOverfulfilled[$eqty['item_num']]['order_line'] == $k){
                if ($arrCheckOverfulfilled[$eqty['item_num']]['qty_bal'] < 0 && $arrCheckOverfulfilled[$eqty['item_num']]['order_line'] == $k) {
                    $coDetArr[$k]['status'] = "Overfulfilled";
                } else if ($eqty['qty'] == $arrStoreMatchUnmatch[$k]) {
                    $coDetArr[$k]['status'] = "Fulfilled";
                } else if ($eqty['qty'] >= $arrStoreMatchUnmatch[$k] && $arrStoreMatchUnmatch[$k] == 0) {
                    $coDetArr[$k]['status'] = "Unfulfilled";
                } else if ($eqty['qty'] >= $arrStoreMatchUnmatch[$k] && $arrStoreMatchUnmatch[$k] > 0) {
                    $coDetArr[$k]['status'] = "Partial";
                }
                // if($k == 2){
                //     dd($coDetArr, $eqty, $arrStoreMatchUnmatch, $arrCheckOverfulfilled);

                // }
            }
        }
        // dd($coDetArr, $eqty, $arrStoreMatchUnmatch, $arrCheckOverfulfilled, $itemQtyTotal,   $arrStoreLpnLine, $indicator);
        if ($listData == 'Yes') {
            //dd($coDetArr);
            foreach ($coDetArr as $orderLine => $lpnCoData) {
                if (isset($arrCheckOverfulfilled[$lpnCoData['item_num']])) {
                    if ($arrCheckOverfulfilled[$lpnCoData['item_num']]['order_line'] == $orderLine) {
                        if ($arrCheckOverfulfilled[$lpnCoData['item_num']]['qty_bal'] < 0) {
                            $totalOverFulfilled = $arrStoreMatchUnmatch[$orderLine] + abs($arrCheckOverfulfilled[$lpnCoData['item_num']]['qty_bal']);
                        } else {
                            $totalOverFulfilled = $arrStoreMatchUnmatch[$orderLine];
                        }
                        $line = $lpnCoData['lpn_line'] ?? null;
                        if ($line != null) {
                            $arrPallet[$orderLine] = array('lpn_line' => $lpnCoData['lpn_line'], 'qty_contained' => $totalOverFulfilled, 'lpn_uom' => $lpnCoData['uom'], 'result' => $lpnCoData['status'], 'lpn_each_qty' => $arrStoreLpnLine[$orderLine]);
                        }
                    } else {
                        $line = $lpnCoData['lpn_line'] ?? null;
                        if ($line != null) {
                            $arrPallet[$orderLine] = array('lpn_line' => @$lpnCoData['lpn_line'], 'qty_contained' => $arrStoreMatchUnmatch[$orderLine], 'lpn_uom' => $lpnCoData['uom'], 'result' => $lpnCoData['status'], 'lpn_each_qty' => $arrStoreLpnLine[$orderLine]);
                        }
                    }
                } else {
                    $arrPallet[$orderLine] = [];
                }
            }
            $arrSetPallet = $arrPallet[$co_line] ?? [];

            $arrResult['lpn_details'] = $arrSetPallet;
            return $arrResult;
        } else {
            if (isset($coDetArr[$co_line]['lpn_line'])) {
                $lpnLineArr = explode(',', $coDetArr[$co_line]['lpn_line']);
                $lpnLineArr1 = array_flip($lpnLineArr);
                $totalOrderQty = $coDetArr[$co_line]['qty'];

                $getDetails = ContainerItem::where('lpn_num', $lpn_num)->whereIn('lpn_line', array_keys($lpnLineArr1))->get();
                foreach ($getDetails as $key => $getDetail) {
                    if ($getDetail->lot_num != "") {
                        $lot_num = $getDetail->lot_num;
                    } else {
                        $lot_num = '!NONLOTITEM!';
                    }
                    if (isset($arrStoreLpnLine[$co_line][$lot_num])) {
                        $arrPallet[$key]['lpn_line'] = $arrStoreLpnLine[$co_line][$lot_num]['lpn_line'];
                        if ($lot_num != '!NONLOTITEM!') {
                            $arrPallet[$key]['lot_num'] = $lot_num;
                        }
                        $arrPallet[$key]['qty_contained'] = $arrStoreLpnLine[$co_line][$lot_num]['line_qty'];
                        $arrPallet[$key]['uom'] = $coDetArr[$co_line]['uom'];
                    }
                }
            }
            return json_encode($arrPallet);
        }



        // convert to co uom
        // foreach($arrCoDet as $coDetLine => $coDetail){

        //     $needLoop = "Yes";
        //     $lpnLine = $coDetLine;
        //     // // foreach($arrlpns as $lpnLine => $lpnDetail){
        //     //     // dd($arrlpns);

        //         if($qty[$lpnLine] <= 0 && $needLoop == 'Yes'){
        //             //convert to CO Uom
        //             $convertUom = UomConv::convertUOM( $baseuom, $arrlpns[$coDetLine]['uom'], $coDetail['uom'], $arrlpns[$coDetLine]['qty_contained'], $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             // $qty[$lpnLine] = $convertUom['conv_qty_to_line']['qty'] - $coDetail['qty_shortage'];
        //             $qty_contained = $convertUom['conv_qty_to_line']['qty'];

        //             $arrCoLpnConvert[$coDetLine][$lpnLine] = $qty_contained;
        //             $qty[$lpnLine] = $qty_contained;
        //         }
        //         else{
        //             break;
        //         }
        //         // $arrPal[$coDetLine][$arrlpns[$coDetLine]['id']] = $lpnLine;


        //     //     // $arrStoreCoLpn[$coLine][$lpnId] = $lpnDetail->lpn_line;
        //         $qty[$lpnLine] = $qty[$lpnLine] - $coDetail['qty_shortage'];

        //         if ($qty[$lpnLine] == 0) {
        //             $qtyToConvert = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $convertUom = UomConv::convertUOM( $baseuom, $coDetail['uom'], $arrlpns[$coDetLine]['uom'], $qtyToConvert, $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             $qty_contained = $convertUom['conv_qty_to_line']['qty'];
        //             $arrStoreEng[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty_contained;
        //             $arrMatchUnmatch[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $reqTotal = abs($qty[$lpnLine]);
        //             $needLoop = "No";
        //         }
        //         else if($qty[$lpnLine] < 0){
        //             $qtyToConvert = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $convertUom = UomConv::convertUOM( $baseuom, $coDetail['uom'], $arrlpns[$coDetLine]['uom'], $qtyToConvert, $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             $qty_contained = $convertUom['conv_qty_to_line']['qty'];
        //             $arrStoreEng[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty_contained;
        //             $arrMatchUnmatch[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $reqTotal = abs($qty[$lpnLine]);
        //         }
        //         else if($coDetLine == $arrLastCoLine && $qty[$lpnLine] > 0){ // over fulfilled - last line
        //             $arrStoreEng[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $arrMatchUnmatch[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             break;
        //         }
        //         else {
        //             $qtyToConvert = $coDetail['qty_shortage'];
        //             // convert to lpn uom
        //             $convertUom = UomConv::convertUOM( $baseuom, $coDetail['uom'], $arrlpns[$coDetLine]['uom'], $qtyToConvert, $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             $qty_contained = $convertUom['conv_qty_to_line']['qty'];
        //             // dd($convertUom, $qty_contained);
        //             $arrStoreEng[$coDetLine][$arrlpns[$coDetLine]['id']] = $qty_contained;
        //             $arrMatchUnmatch[$coDetLine][$arrlpns[$coDetLine]['id']] = $coDetail['qty_shortage'];
        //             break;

        //         }

        //     // }
        // }
        // foreach($arrCoDet as $coDetLine => $coDetail){
        //     $needLoop = "Yes";
        //     foreach($arrlpns as $lpnLine => $lpnDetail){
        //         // dd($arrlpns);
        //         if($qty[$lpnLine] <= 0 && $needLoop == 'Yes'){
        //             //convert to CO Uom
        //             $convertUom = UomConv::convertUOM( $baseuom, $lpnDetail['uom'], $coDetail['uom'], $lpnDetail['qty_contained'], $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             // $qty[$lpnLine] = $convertUom['conv_qty_to_line']['qty'] - $coDetail['qty_shortage'];
        //             $qty_contained = $convertUom['conv_qty_to_line']['qty'];

        //             $arrCoLpnConvert[$coDetLine][$lpnLine] = $qty_contained;
        //             $qty[$lpnLine] = $qty_contained;
        //         }
        //         // else{
        //         //     break;
        //         // }
        //         $arrPal[$coDetLine][$lpnData->id] = $lpnLine;

        //         // $arrStoreCoLpn[$coLine][$lpnId] = $lpnDetail->lpn_line;
        //         $qty[$lpnLine] = $qty[$lpnLine] - $coDetail['qty_shortage'];
        //         $arrLpnBal[$lpnLine] = $qty[$lpnLine];
        //         if ($qty[$lpnLine] == 0) {
        //             $qtyToConvert = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             // $convertUom = UomConv::convertUOM( $baseuom, $coDetail['uom'], $lpnDetail['uom'], $qtyToConvert, $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             // $qty_contained = $convertUom['conv_qty_to_line']['qty'];
        //             $arrStoreEng[$coDetLine][$lpnDetail['id']] = $qty_contained;
        //             $arrMatchUnmatch[$coDetLine][$lpnDetail['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $reqTotal = abs($qty[$lpnLine]);
        //             $needLoop = "No";
        //             // break;
        //         }
        //         else if($qty[$lpnLine] < 0){
        //             $qtyToConvert = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             // $convertUom = UomConv::convertUOM( $baseuom, $coDetail['uom'], $lpnDetail['uom'], $qtyToConvert, $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             // $qty_contained = $convertUom['conv_qty_to_line']['qty'];
        //             $arrStoreEng[$coDetLine][$lpnDetail['id']] = $qty_contained;
        //             $arrMatchUnmatch[$coDetLine][$lpnDetail['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $reqTotal = abs($qty[$lpnLine]);
        //         }
        //         else if($coDetLine == $arrLastCoLine && $qty[$lpnLine] > 0){ // over fulfilled - last line
        //             $arrStoreEng[$coDetLine][$lpnDetail['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             $arrMatchUnmatch[$coDetLine][$lpnDetail['id']] = $qty[$lpnLine] + $coDetail['qty_shortage'];
        //             break;
        //         }
        //         else {
        //             $qtyToConvert = $coDetail['qty_shortage'];
        //             // convert to lpn uom
        //             // $convertUom = UomConv::convertUOM( $baseuom, $coDetail['uom'], $lpnDetail['uom'], $qtyToConvert, $item_num, $coDetail['cust_num'], '', __('mobile.nav.co_picking'));
        //             // $qty_contained = $convertUom['conv_qty_to_line']['qty'];
        //             // dd($convertUom, $qty_contained);
        //             $arrStoreEng[$coDetLine][$lpnDetail['id']] = $qty_contained;
        //             $arrMatchUnmatch[$coDetLine][$lpnDetail['id']] = $coDetail['qty_shortage'];
        //             break;

        //         }

        //     }
        // }
        // dd($arrLpnBal);
        // dd($arrPal, $arrlpns, $arrCoDet);
        // $arrBal = [];
        // $arrBalCo = [];
        // foreach($arrStoreEng as $coLine => $totalData){
        //     foreach($totalData as $lpnId => $lpnDet){
        //         // $qty = $eqty - $reqTotal;
        //         if(array_key_exists($lpnId, $arrBal)){
        //             if($arrBal[$lpnId] > 0){
        //                 $arrTotal[$coLine][$lpnId] = $arrBal[$lpnId] - $arrCoLineQty[$coLine];
        //             }
        //             else{
        //                 $arrTotal[$coLine][$lpnId] = 0;
        //             }
        //         }
        //         else{
        //             if(array_key_exists($coLine, $arrBalCo)){
        //                 $arrTotal[$coLine][$lpnId] = $arrStoreEng[$coLine][$lpnId] - $arrBalCo[$coLine];
        //                 $arrCoLineQty[$coLine] = $arrTotal[$coLine][$lpnId];
        //                 // dd($arrBalCo[$coLine], $arrLpnLineQty[$lpnId], $arrTotal[$coLine][$lpnId]);
        //             }
        //             else{
        //                 if(@$arrBal[$lpnId] == 0){
        //                     $arrTotal[$coLine][$lpnId] = 0;
        //                 }
        //                 else{
        //                     $arrTotal[$coLine][$lpnId] = $arrStoreEng[$coLine][$lpnId] - $arrCoLineQty[$coLine];
        //                 }
        //                 // dd($arrTotal, $arrStoreEng[$coLine][$lpnId], $arrCoLineQty[$coLine]);
        //             }

        //         }

        //         if (@$arrTotal[$coLine][$lpnId] <= 0) {
        //             // $arrStoreCoLpn[$coLine][$lpnId] = $arrLpnLineQty[$lpnId];
        //             // dd($arrBal[$lpnId]);
        //             $reqTotal = abs(@$arrTotal[$coLine][$lpnId]);
        //             // dd($arrTotal[$coLine][$lpnId]);
        //             $arrBalCo[$coLine] = @$arrTotal[$coLine][$lpnId] * -1;

        //             $arrTotalLpnUse[$coLine][$lpnId] = $arrStoreEng[$coLine][$lpnId];

        //             @$arrStoreCoLpn[$coLine][$lpnId] = $arrBal[$lpnId];
        //             $arrBal[$lpnId] = 0;
        //             // @$arrStoreCoLpn[$coLine][$lpnId] = $arrBal[$lpnId];
        //             // $arrStoreCoLpn[$coLine][$lpnId] = 0;



        //         } else {

        //             $arrStoreCoLpn[$coLine][$lpnId] = $arrCoLineQty[$coLine];

        //             // $arrStoreCoLpn[$coLine][$lpnId] = $arrCoLineQty[$coLine];
        //             $arrTotalLpnUse[$coLine][$lpnId] = $arrCoLineQty[$coLine];

        //             $arrBal[$lpnId] = $arrTotal[$coLine][$lpnId];
        //             break;

        //         }
        //     }
        // }
        // dd($arrTotal);
        // if($co_line == 4){

        //     dd($arrPal,$arrStoreEng, $arrlpns, $arrCoDet, $arrLpnLineQty, $arrCoLineQty, $arrTotal, $arrStoreCoLpn, $arrTotalLpnUse);
        // }
        // dd($arrStoreCoLpn);
        // $lpnLine = "";
        // $arrLPNLinesQty = array();
        // $arrPallet = array();
        // if($listData == 'Yes'){
        //     if(isset($arrPal[$co_line])){
        //         foreach($arrPal[$co_line] as $key => $dataList){
        //             if($lpnLine == ""){
        //                 $lpnLine = $dataList;
        //             }
        //             else{
        //                 $lpnLine = $lpnLine . ',' . $dataList;
        //             }


        //         }
        //     }

        //     if(isset($arrStoreCoLpn[$co_line])){
        //         $getDetails = ContainerItem::whereIn('id', array_keys($arrStoreCoLpn[$co_line]))->get();
        //         foreach($getDetails as $key => $getDetail){
        //             // dd($arrTotal[$coLine][$lpnId], $coLine, $lpnId);
        //             @$arrPallet[$key]['lpn_line'] = $arrPal[$co_line][$getDetail->id];
        //             @$arrPallet[$key]['order_line'] = $co_line;
        //             @$arrPallet[$key]['lot_num'] = $getDetail->lot_num;
        //             @$arrPallet[$key]['qty_contained'] = abs($arrStoreEng[$co_line][$getDetail->id]);
        //             @$arrPallet[$key]['item_num'] = $getDetail->item_num;
        //             @$arrPallet[$key]['lpn_uom'] = $getDetail->uom;
        //             @$arrPallet[$key]['qty_bal_lpn'] = $arrTotal[$coLine][$lpnId];
        //             @$arrPallet[$key]['qty_shortage'] = abs($arrMatchUnmatch[$co_line][$getDetail->id]);
        //         }
        //     }

        //     $arrResult = array('lpnDetails' => $arrPallet, 'lpn_line' => $lpnLine);
        //     return $arrResult;
        // }
        // else{
        //     // dd($arrPal);
        //     $getDetails = ContainerItem::whereIn('id', array_keys($arrStoreCoLpn[$co_line]))->get();
        //     foreach($getDetails as $key => $getDetail){
        //         $arrPallet[$key]['lpn_line'] = $arrPal[$co_line][$getDetail->id];
        //         // $arrPallet[$key]['lpn_line'] = $getDetail->lpn_line;
        //         $arrPallet[$key]['lot_num'] = $getDetail->lot_num;
        //         // $arrPallet[$key]['qty_contained'] = $arrStoreCoLpn[$co_line][$getDetail->id];
        //         $arrPallet[$key]['qty_contained'] = $arrMatchUnmatch[$co_line][$getDetail->id];
        //         $arrPallet[$key]['uom'] = $arrCoDet[$co_line]['uom'];
        //     }
        //     return json_encode($arrPallet);
        // }
    }

    public function checkActualStandard($JobIdSuffix)
    {
        if ($this->is_base64_string($JobIdSuffix)) {
            $JobIdSuffix = base64_decode($JobIdSuffix);
        }



        // $arrJob = explode("-", $JobIdSuffix);
        // $getJobId = Job::select('id')->where('job_num', $arrJob[0])->where('suffix', $arrJob[1])->value('id');

        $arrJob =  mb_substr($JobIdSuffix, 0, -5);
        $suffix = mb_substr($JobIdSuffix,  -4);
        $getJobId = Job::select('id')->where('job_num',  $arrJob)->where('suffix', $suffix)->value('id');

        @$getMetaDataExt = MetaDataExt::select('value')->where('model_id', $getJobId)->where('model', 'Job')->where('status', 1)->value('value');
        if (@$getMetaDataExt) {
            return $getMetaDataExt;
        } else {
            return "empty";
        }
        //return  $getJobId;
    }

    public function getReasonCodeValidate($reason_num, $reason_class)
    {
        if ($this->is_base64_string($reason_num)) {
            $reason_num = base64_decode($reason_num);
            $reason_class = base64_decode($reason_class);
        }

        $toReason = new ReasonCode();
        $toReason = $toReason->select('sync_status')
            ->where('reason_num', $reason_num)
            ->where('reason_class', $reason_class)

            ->get();
        //dd($reason_num,$reason_class);
        return $toReason;
    }

    public function getReasonCodeMiscRecipt()
    {
        $ReasonCode = new ReasonCode();
        $reason_class = "MiscReceipt";
        $ReasonCode->select('reason_num', 'reason_desc', 'reason_class', 'id')->get();
        return $ReasonCode->toSql();
    }



    public function getLotExpiryDate($item_num, $lot_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $lot_num = base64_decode($lot_num);
        }

        $totExp = new Lot();
        $totExp = $totExp->select('expiry_date')
            ->where('item_num', $item_num)
            ->where('lot_num', $lot_num)
            ->get();
        return $totExp;
    }
    public function getLotToNum($item_num, $trn_num, $trn_line)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);

            $trn_num = base64_decode($trn_num);
            $trn_line = base64_decode($trn_line);
        }
        $tolot = new TransferOrderLinesSubline();
        $tolot = $tolot->select('*', DB::raw("SUM(qty_receivable) as qty_receivable"), DB::raw("SUM(qty_shipped) as qty_shipped"))
            ->where('item_num', $item_num)
            ->where('trn_num', $trn_num)
            // ->where('trn_loc',  $loc_num )
            ->where('trn_line', $trn_line)
            ->groupBy('trn_lot')
            ->get();


        // dd($tolot);
        return $tolot;
    }


    public function getLotToUnitNum($item_num, $trn_num, $trn_line)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);

            $trn_num = base64_decode($trn_num);
            $trn_line = base64_decode($trn_line);
        }
        $tolot = new TransferOrderLinesSubline();
        $tolot = $tolot->select('*',  DB::raw("SUM(qty_received) as qty_received"),DB::raw("SUM(qty_receivable) as qty_receivable"), DB::raw("SUM(qty_shipped) as qty_shipped"))
            ->where('item_num', $item_num)
            ->where('trn_num', $trn_num)
            ->where('lpn_num', "")
            ->where('trn_line', $trn_line)
            ->groupBy('trn_lot')
            ->get();


        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($tolot as $each) {
            $each['qty_receivable'] = numberFormatPrecision($each->qty_receivable, $unit_quantity_format, '.', '');
            $each['qty_shipped'] = numberFormatPrecision($each->qty_shipped, $unit_quantity_format, '.', '');
            $each['qty_received'] = numberFormatPrecision($each->qty_received, $unit_quantity_format, '.', '');
            // $each['qty_loss'] = numberFormatPrecision($each->qty_loss, $unit_quantity_format, '.', '');


            // $each['qty_receivable'] = $each->qty_receivable;
            // $each['qty_shipped'] = $each->qty_shipped;
            // $each['qty_received'] = $each->qty_received;
            $each['qty_loss'] = $each->qty_loss;
        }

        return $tolot;
    }



    public function getWhse($whse_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }
        $whse = new Warehouse();
        $whse = $whse->select('whse_num', 'whse_name')
            ->orderBy('whse_num', 'asc')->active()->get();
        return $whse;
    }

    public function getPalletTransaction()
    {

        $plan = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->first();
        $planId = customDecrypt($plan->plan_id);

        if ($planId == 7) {
            $setPlan = "Free";
        } else if ($planId == 1 || $planId == 4) {
            $setPlan = "Starter";
        } else {
            $setPlan = "All";
        }

        /* V2
        $arrPlanModule = array(
            'Free'=>
                [
                    'Stock Move',
                    'Misc Issue',
                    'Misc Receipt',
                    'Pick List',
                    'PO Receipt',
                    'Put Away',
                    'CO Return ',
                    'PO Return',
                    'Pick and Ship',
                    'CO Picking',
                    'CO UnPicking',
                    'TO Shipping',
                    'Job Material Return',
                    'Job Receipt',
                    'Job Return',
                    'WIP Move',
                ],

           'Starter'=>
                [
                    'Stock Move',
                    'Misc Issue',
                    'Misc Receipt',
                    'Pick List',
                    'PO Receipt',
                    'Put Away',
                    'CO Return ',
                    'PO Return',
                    'Pick and Ship',
                    'CO Picking',
                    'CO UnPicking',
                    'TO Shipping',
                    'Job Material Issue',
                ],

           'All'=>
                [
                    'Stock Move',
                    'Misc Issue',
                    'Misc Receipt',
                    'Pick List',
                    'PO Receipt',
                    'Put Away',
                    'CO Return ',
                    'TO Receipt ',
                    'PO Return',
                    'Pick and Ship',
                    'CO Picking',
                    'CO UnPicking',
                    'TO Shipping',
                    'Job Material Issue',
                    'Job Material Return',
                    'Job Receipt',
                    'Job Return',
                    'WIP Move',
                ],
        );
        */

        $arrPlanModule = array(
            'Free' =>
            [
                // 'Misc Receipt',
                // 'PO Receipt',
                'CO Picking',
                'CO UnPicking',
                // 'TO Shipping',
                // 'Job Receipt',
            ],

            'Starter' =>
            [
                // 'Misc Receipt',
                // 'PO Receipt',
                'CO Picking',
                'CO UnPicking',
                // 'TO Shipping',
            ],

            'All' =>
            [
                // 'Misc Receipt',
                // 'PO Receipt',
                'TO Receipt',
                'CO Picking',
                'CO UnPicking',
                'TO Shipping',
                // 'Job Receipt',
            ],
        );

        $arr_plan_module = array();
        if (isset($arrPlanModule[$setPlan])) {
            foreach ($arrPlanModule[$setPlan] as $qwe) {
                array_push($arr_plan_module, ['module_name' => $qwe]);
            }
        }

        // Sort multidimensional array
        // reference: https://stackoverflow.com/a/1598385
        usort($arr_plan_module, function ($item1, $item2) {
            return $item1['module_name'] <=> $item2['module_name'];
        });

        return $arr_plan_module;
    }

    public function getWhseIntSetting($whse_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }
        $whse = new Warehouse();
        $whse = $whse->select('whse_num', 'whse_name')
            ->where('site_id', auth()->user()->site_id)
            ->orderBy('whse_num', 'asc')->active()->get();

        return $whse;
    }

    public function getPackageType($pkgtype_num = "")
    {
        if ($this->is_base64_string($pkgtype_num)) {
            $pkgtype_num = base64_decode($pkgtype_num);
        }
        $pkgtype = new PackageType();
        $pkgtype = $pkgtype->select('pkgtype_num', 'package_desc')
            ->orderBy('pkgtype_num', 'asc')->active()->get();
        return $pkgtype;
    }

    public function getOpenPoNum(Request $request, $whse_num, $po_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $po_num = base64_decode($po_num);
            $whse_num = base64_decode($whse_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num', 'vend_name')
            ->where('whse_num', $whse_num)
            ->where('po_num', 'like', '%' . $po_num . '%')
            ->where('rel_status', 'O')
            ->distinct()
            ->get();
        return $po;
    }

    public function getOpenPoNo(Request $request, $whse_num, $item_num, $vend_num, $po_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $po_num = base64_decode($po_num);
            $item_num = base64_decode($item_num);
            $whse_num = base64_decode($whse_num);
            $vend_num = base64_decode($vend_num);
        }

        $po = new PurchaseOrderItem();
        $po = $po->select('po_num')
            ->where('whse_num', $whse_num)
            ->where('vend_num', $vend_num)
            ->where('rel_status', 'O');

        if ($item_num != null && $item_num != "null" && $item_num != "")
            $po = $po->where('item_num', $item_num);

        if ($po_num != null && $po_num != "null" && $po_num != "")
            $po = $po->where('po_num', 'like', '%' . $po_num . '%');

        $po = $po->distinct()->get();

        return $po;
    }

    public function getPonum($whse_num = "", $po_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $po_num = base64_decode($po_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num')->where('whse_num', 'like', '%' . $whse_num . '%')->where('po_num', 'like', '%' . $po_num . '%')->inventory()->open()->distinct()->get();
        return $po;
    }

    public function getPoNumber(Request $request, $po_num = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num')
            ->where('po_num', 'like', '%' . $po_num . '%')
            ->orderBy('po_num', 'asc')->distinct()->get();
        return $po;
    }

    public function getPoNumberOpen(Request $request, $po_num = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num')
            ->where('po_num', 'like', '%' . $po_num . '%')
            ->where('rel_status', 'O')
            ->orderBy('po_num', 'asc')->distinct()->get();
        return $po;
    }

    public function getPONumWithoutWhse($po_num = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num')->where('po_num', 'like', '%' . $po_num . '%')->inventory()->open()->distinct()->get();

        return $po;
    }

    public function getCONumWithoutWhse($co_num = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
        }

        $co = new CustomerOrderItem();
        $co = $co->select('co_num')->where('co_num', 'like', '%' . $co_num . '%')->distinct()->returnable()->get();
        return $co;
    }

    public function getFirstPoLine($po_num, $po_line)
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
        }
        $po = new PurchaseOrderItem();
        $po = $po->where('po_num', $po_num)
            ->where('po_line', 'like', '%' . $po_line . '%')
            ->distinct()->first();
        $sum = $po->where('po_num', $po_num)->where('po_line', 'like', '%' . $po_line . '%')->sum('qty_received');
        return array_merge($po->toArray(), array('balance' => numberFormatPrecision(($po->qty_ordered - $sum), 2, '.', '')));
    }

    public function getFirstGrnLine($grn_num, $grn_line)
    {
        if ($this->is_base64_string($grn_num)) {
            $grn_num = base64_decode($grn_num);
            $grn_line = base64_decode($grn_line);
        }
        $grn = new GRNItem();
        $grn = $grn->where('grn_num', $grn_num)
            ->where('grn_line', 'like', '%' . $grn_line . '%')
            ->distinct()->first();
        $sum = $grn->where('grn_num', $grn_num)->where('grn_line', 'like', '%' . $grn_line . '%')->sum('qty_received');
        return array_merge($grn->toArray(), array('balance' => numberFormatPrecision(($grn->qty_ordered - $sum), 2, '.', '')));
    }

    public function getReturnablePonum($whse_num, $po_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $po_num = base64_decode($po_num);
        }

        //dd($whse_num,$po_num);
        $po = new PurchaseOrderItem();

        $po = new PurchaseOrderItem();
        $checkNonItem = $po->select('item_num')->where('whse_num', $whse_num)->where('po_num', 'like', '%' . $po_num . '%')->where('rel_status', 'O')->value('item_num');

        $po = $po->select('po_num', 'vend_name');
        // if($checkNonItem!="NON-INV"){
        //     $po->whereHas('item', function ($q) {
        //         $q->where('item_status', 1);
        //     });
        // }
        $po = $po->where('whse_num', $whse_num)->where('po_num', 'like', '%' . $po_num . '%')->where('rel_status', 'O')->distinct()->get();
        return $po;
    }

    public function getReturnableGrnNum($whse_num, $grn_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $grn_num = base64_decode($grn_num);
        }

        $grnitem = GRNItem::select('grn_num')->where('status', 'O')->where('net_received', '>', '0')->distinct()->get()->pluck('grn_num')->toArray();

        $grn = GRN::select('grn_num', 'vend_name')->where('whse_num', $whse_num)->whereIn('grn_num', $grnitem)->where('grn_status', 'O')->get();

        // $grnitem = new GRNItem();
        // $checkNonItem = $grn->with('grn')->select('item_num')->where('grn.whse_num', $whse_num)->where('grn_num', 'like', '%' . $grn_num . '%')->where('status', 'O')->value('item_num');

        // $grn = $grn->select('grn_num');
        // if($checkNonItem!="NON-INV"){
        //     $po->whereHas('item', function ($q) {
        //         $q->where('item_status', 1);
        //     });
        // }

        return $grn;
    }

    public function getReturnableCoLine($whse_num, $co_num, $co_line = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
        }
        $co = new CustomerOrderItem();
        $co = $co->select('co_line')->where('whse_num', $whse_num)->where('co_num', $co_num)->where('co_line', 'like', '%' . $co_line . '%')->distinct()->returnable()->get();
        return $co;
    }

    public function getReturnNum($whse_num, $cust_num, $return_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $cust_num = base64_decode($cust_num);
            $return_num = base64_decode($return_num);
        }
        $cr = new CustomerReturn();
        //$cr = $cr->select('return_num')->where('whse_num', $whse_num)->where('cust_num', $cust_num)->where('return_num', 'like', '%' . $return_num . '%')->distinct()->get();

        $cr = $cr->select('return_num')->where('status', 'O')->distinct()->get();
        // dd($cr);
        return $cr;
    }


    public function getCOReturnNum($return_num = "")
    {
        if ($this->is_base64_string($return_num)) {
            $return_num = base64_decode($return_num);
        }
        $cr = new CustomerReturn();
        //$cr = $cr->select('return_num')->where('whse_num', $whse_num)->where('cust_num', $cust_num)->where('return_num', 'like', '%' . $return_num . '%')->distinct()->get();

        $cr = $cr->select('return_num')->where('status', 'O');

        if (!empty($return_num)) {
            $cr->where('return_num', $return_num);
        }

        $cr = $cr->distinct()->get();
        // dd($cr);
        return $cr;
    }

    public function getAllCOReturnNum($return_num = "")
    {
        if ($this->is_base64_string($return_num)) {
            $return_num = base64_decode($return_num);
        }
        $cr = new CustomerReturn();
        //$cr = $cr->select('return_num')->where('whse_num', $whse_num)->where('cust_num', $cust_num)->where('return_num', 'like', '%' . $return_num . '%')->distinct()->get();

        $cr = $cr->select('return_num');

        if (!empty($return_num)) {
            $cr->where('return_num', $return_num);
        }

        $cr = $cr->distinct()->get();
        // dd($cr);
        return $cr;
    }



    public function getReturnableConum($whse_num, $co_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $co_num = base64_decode($co_num);
        }
        $co = new CustomerOrderItem();
        $co = $co
            ->select('coitems.co_num', 'customers.cust_name as cust_name')
            ->where('coitems.whse_num', $whse_num)
            ->where('coitems.co_num', 'like', '%' . $co_num . '%')
            ->where('coitems.rel_status', 'O')
            ->distinct()
            ->leftJoin("customers", function($join) {
                $join->on("customers.cust_num", "=", "coitems.cust_num");
                $join->on("customers.site_id", "=", "coitems.site_id");
            })
            ->returnable()
            ->get();
        return $co;
    }

    public function getReturnablePoLine($whse_num, $po_num, $po_line = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_line')->where('whse_num', $whse_num)->where('po_num', $po_num)->where('po_line', 'like', '%' . $po_line . '%')->distinct()->returnable()->get();
        return $po;
    }

    public function getPonumByPo($po_num = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num', 'vend_num')->where('po_num', 'like', '%' . $po_num . '%')->inventory()->open()->distinct()->get();
        return $po;
    }

    public function getfirstPOByPo($po_num = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
        }
        $po = new PurchaseOrderItem();
        $po = $po->select('po_num', 'whse_num', 'vend_num', 'vend_name')->where('po_num', 'like', '%' . $po_num . '%')->open()->distinct()->first();
        return $po;
    }

    public function getAllPoLine($po_num, $po_line = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
        }
        $po = new PurchaseOrderItem();
        $po_line = $po->select('po_line')->where('po_num', $po_num)->where('po_line', 'like', '%' . $po_line . '%')->distinct()->get();
        return $po_line;
    }

    public function getPoLine($po_num, $po_line = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
        }
        $po = new PurchaseOrderItem();
        $po_line = $po->select('po_line')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('po_num', $po_num)->where('po_line', 'like', '%' . $po_line . '%')->open()->distinct()->get();
        return $po_line;
    }

    public function getPoLineByPoRange($from_po_num = "", $to_po_num = "")
    {
        if ($this->is_base64_string($from_po_num)) {
            $from_po_num = base64_decode($from_po_num);
            $to_po_num = base64_decode($to_po_num);
        }

        $po = new PurchaseOrderItem();
        $poLine = $po->select('po_line');

        if ($from_po_num)
            $poLine = $poLine->whereBetween('po_num', [$from_po_num, $to_po_num]);

        $poLine = $poLine->open()->distinct()->get();

        return $poLine;
    }

    public function getPoRel($po_num, $po_line, $po_rel = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
            $po_rel = base64_decode($po_rel);
        }
        $po = new PurchaseOrderItem();
        $po_rel = $po->select('po_rel')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('po_num', $po_num)->where('po_line', $po_line)->where('po_rel', 'like', '%' . $po_rel . '%')->open()->distinct()->get();
        return $po_rel;
    }

    public function getItemByPoRel($po_num, $po_line, $po_rel = "")
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
            $po_rel = base64_decode($po_rel);
        }
        $po = new PurchaseOrderItem();
        $po_rel = $po->select('item_num', 'item_desc', 'uom', 'qty_received')->where('po_num', $po_num)->where('po_line', $po_line)->where('po_rel', 'like', '%' . $po_rel . '%')->first();
        $lot_tracked = Item::select('lot_tracked')->where('item_num', $po_rel->item_num)->first();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($po_rel) {
            $po_rel['qty_received'] = numberFormatPrecision($po_rel->qty_received, $unit_quantity_format);
            // $po_line['qty_received'] = $po_line->qty_received;
        }

        $array = array_merge($lot_tracked->toArray(), $po_rel->toArray());
        return $array;
    }

    public function getItemByPoLine($po_num, $po_line = "")
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
            $po_line = base64_decode($po_line);
        }
        $po = new PurchaseOrderItem();
        $po_line = $po->select('item_num', 'item_desc', 'uom', 'qty_received')->where('po_num', $po_num)->where('po_line', $po_line)->first();
        if ($po_line) {
            $po_line['qty_received'] = numberFormatPrecision($po_line->qty_received, $unit_quantity_format);
            // $po_line['qty_received'] = $po_line->qty_received;
        }
        $lot_tracked = Item::select('lot_tracked')->where('item_num', $po_line->item_num)->first();

        $array = array_merge($lot_tracked->toArray(), $po_line->toArray());
        return $array;
    }

    public function getItemByCoRel($co_num, $co_line, $co_rel = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
            $co_rel = base64_decode($co_rel);
        }
        $co = new CustomerOrderItem();
        $co_rel = $co->select('item_num', 'item_desc', 'qty_shipped', 'uom')->where('co_num', $co_num)->where('co_line', $co_line)->where('co_rel', 'like', '%' . $co_rel . '%')->first();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($co_rel) {
            $co_rel['qty_shipped'] = numberFormatPrecision($co_rel->qty_shipped, $unit_quantity_format);
        }

        $lot_tracked = Item::select('lot_tracked')->where('item_num', $co_rel->item_num)->first();
        $array = array_merge($lot_tracked->toArray(), $co_rel->toArray());
        return $array;
    }

    public function getItemByCoLine($co_num, $co_line = "")
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
        }
        $co = new CustomerOrderItem();
        $co_line = $co->select('item_num', 'item_desc', 'qty_shipped', 'uom')->where('co_num', $co_num)->where('co_line', $co_line)->first();
        if ($co_line) {
            $co_line['qty_shipped'] = numberFormatPrecision($co_line->qty_shipped, $unit_quantity_format);
        }
        $lot_tracked = Item::select('lot_tracked')->where('item_num', $co_line->item_num)->first();
        $array = array_merge($lot_tracked->toArray(), $co_line->toArray());
        return $array;
    }

    public function getItemByCoNum($whse_num, $co_num, $co_line, $item_num = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        $co = new CustomerOrderItem();
        $co_num = $co->select('item_num')
            ->where('whse_num', $whse_num)
            ->where('co_num', $co_num);
        if ($co_line != "") {
            $co_num = $co_num->where('co_line', $co_line);
        }
        $co_num = $co_num->where('rel_status', 'O')
            ->where('item_num', 'like', '%' . $item_num . '%')
            ->distinct()
            ->get();
        return $co_num;
    }


    public function getItemByCoNumCus($whse_num,  $cust_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);

            $cust_num = base64_decode($cust_num);
        }
        //dd($whse_num, $cust_num);
        /*$co = new CustomerOrderItem();
        $co_num = $co->select('item_num')
            ->where('whse_num', $whse_num)
            ->where('co_num', $co_num);
        if ($co_line != "") {
            $co_num = $co_num->where('co_line', $co_line);
        }
        $co_num = $co_num->where('rel_status', 'O')
            ->where('item_num', 'like', '%' . $item_num . '%')
            ->distinct()
            ->get();*/

        $ItemWhse = ItemWarehouse::select('item_num')->where('whse_num', $whse_num)->where('site_id', auth()->user()->site_id);
        $item =  $ItemWhse->whereHas('item', function ($query) {
            return $query->where('item_status', 1);
        })->get();



        return $item;
    }

    public function getItemByCoNumPicknShip($whse_num, $co_num, $item_num = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        $co = new CoPickView();
        $co_num = $co->select('item_num')
            ->where('co_num', $co_num)
            ->where('whse_num', $whse_num)
            ->where('rel_status', '!=', 'C')
            ->where('item_num', 'like', '%' . $item_num . '%')
            // ->where('qty_staged', NULL)
            ->orderBy('co_num')
            ->distinct()
            ->get();
        return $co_num;
    }

    public function getItemByJobSequence($job_num, $oper_num, $sequence = "")
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $oper_num = base64_decode($oper_num);
            $sequence = base64_decode($sequence);
        }
        $job_matl = new JobMatl();
        $job_matl = $job_matl->select('matl_item', 'matl_desc', 'uom', 'qty_issued')->where('job_num', $job_num)->where('oper_num', $oper_num)->where('sequence', 'like', '%' . $sequence . '%')->first();
        if ($job_matl) {
            $job_matl['qty_issued'] = numberFormatPrecision($job_matl->qty_issued, $unit_quantity_format);
        }
        $lot_tracked = Item::select('lot_tracked')->where('item_num', $job_matl->matl_item)->first();
        $array = array_merge($lot_tracked->toArray(), $job_matl->toArray());
        return $array;
    }

    public function getItem($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        // dd($item_num);
        $item = new Item();
        $item = $item->select('item_num', 'item_desc')->active()->orderBy('item_num', 'asc')->get();
        // dd($item_num);
        return $item;
    }

    public function getItemByWhse($whse_num = "")
    {

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }
        $ItemWhse = ItemWarehouse::select('item_num')->where('whse_num', $whse_num);
        $item =  $ItemWhse->whereHas('item', function ($query) {
            return $query->where('item_status', 1);
        })->get();

        return $item;
    }




    public function getItemByUom($uom = "")
    {
        if ($this->is_base64_string($uom)) {
            $uom = base64_decode($uom);
        }
        // dd($uom);
        $item = new Item();
        $item = $item->select('item_num', 'item_desc')->where('uom', $uom)->active()->orderBy('item_num', 'asc')->get();
        return $item;
    }

    public function getItemByTO($whse_num, $trn_num, $item_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $trn_num = base64_decode($trn_num);
            $item_num = base64_decode($item_num);
        }

        $data = [];

        $items = Item::with(['transfer_lines' => function ($q) use ($whse_num, $trn_num) {
            $q->where('from_whse', $whse_num)->where('trn_num', $trn_num);
        }])
            ->whereHas('transfer_lines', function ($q) use ($whse_num, $trn_num) {
                $q->where('from_whse', $whse_num)->where('trn_num', $trn_num);
            })
            ->select('item_num', 'item_desc', 'site_id')
            ->orderBy('item_num', 'ASC')
            ->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($items as $item) {
            $data[] = [
                'item_num' => $item->item_num,
                'item_desc' => $item->item_desc,
                // 'qty_required' => $item->transfer_lines ? numberFormatPrecision(array_sum(array_column($item->transfer_lines->toArray(), 'total_qty_required')), $unit_quantity_format,'.',1) : numberFormatPrecision(0, $unit_quantity_format,'.',1),

                'qty_required' => $item->transfer_lines ? array_sum(array_column($item->transfer_lines->toArray(), 'total_qty_required')) : numberFormatPrecision(0, $unit_quantity_format, '.', 1),
            ];
        }

        return $data;
    }

    public function getItemWithUOM($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $item = new Item();
        $item = $item->select('item_num', 'item_desc', 'uom')->active()->orderBy('item_num', 'asc')->get();
        return $item;
    }

    public function getManufacturedItem($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $item = new Item();
        $item = $item->select('item_num', 'item_desc')->where('item_num', 'like', '%' . $item_num . '%')->where('item_type', 'M')->active()->orderBy('item_num', 'asc')->get();
        return $item;
    }

    public function getLotTrackedItem($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $item = new Item();
        $item = $item->select('item_num', 'item_desc')
            ->where('item_num', 'like', '%' . $item_num . '%')
            ->where('lot_tracked', 1)
            ->where('item_status', 1)
            ->get();
        return $item;
    }

    public function whsev2(Request $request)
    {
        if ($this->is_base64_string($request->whse_num)) {
            $whse_num = base64_decode($request->whse_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->whse_num)));
        }

        $warehouse = Warehouse::where('whse_num', $whse_num)->first();
        if ($warehouse && strcmp($whse_num, $warehouse->whse_num) == 0) { // compare whse, case-sensitive, return 0 if same
            if ($warehouse->whse_status == 0) {
                return 'inactive';
            } else {
                return 'active';
            }
        } else {
            return 'not exist';
        }
    }

    public function suffixv(Request $request)
    {
        $job_num = $request->job_num;
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        } else {
            $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->job_num)));
        }

        $suffixv = Job::where('job_num', $job_num)->where('suffix', $request->suffix)->first();
        if ($suffixv) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function getLocPackingValidated(Request $request)
    {
        $loc_num = $request->loc_num;
        $whse_num = $request->whse_num;
        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        } else {
            $loc_num = utf8_encode(htmlspecialchars_decode(base64_decode($loc_num)));
        }
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));
        }
        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name', 'loc_status', 'loc_type');
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        $loc = //$loc->where('loc_type', 'S');
            $loc->where('loc_num', $loc_num)
            ->orderBy('loc_num', 'asc')
            ->first();
        //dd($loc_num,$whse_num,$loc);
        if ($loc == null) {
            return 'not exist';
        } else {
            if ($loc->loc_status == 0) {
                return 'inactive';
            } else {
                return 'active';
            }
        }
    }

    public function itemValidationCommon(Request $request)
    {
        $item_num = $request->item_num;
        $whse_num = $request->whse_num; //
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));
        }
        // validate item
        $item = Item::where('item_num', $item_num)->where('site_id', auth()->user()->site_id)->first();

        if(!$item){
            $result = __('error.admin.notexist', ['resource' => __('admin.label.item_num')]);
            return json_encode($result);
        }

        if ($item->item_status == 0) {
            $result = __('error.admin.inactive_without_value', ['resource' => __('admin.label.item_num')]);
            return json_encode($result);
        }

        // validate item whse
        $itemWhse = ItemWarehouse::where('whse_num', $whse_num)->where('item_num', $item_num)->where('site_id', auth()->user()->site_id);

        if ($item_num == "NON-INV") {
            $result = true;
            return json_encode($result);
            // return 'active';
        } else {
            if ($itemWhse->exists()) {
                if ($itemWhse->first()->item->item_status) {
                    $result = true;
                    return json_encode($result);
                    // return 'active';
                } else {
                    $result = __('error.admin.inactive_without_value', ['resource' => __('admin.label.item_num')]);
                    return json_encode($result);
                    // return 'inactive';
                }
            } else {
                $tparm = new TparmView;
                $auto_create_item_whse_if_missing = $tparm->getTparmValue('System', 'auto_create_item_whse_if_missing'); // 1,0
                $has_item_warehouse = \Gate::allows('hasItemWarehouse'); // true,false

                if ($auto_create_item_whse_if_missing == "0") {
                    $result = __('error.admin.item_whse_not_exist_in_itemwhse', ['resource1' => $item_num, "resource2" => $whse_num]);
                    return json_encode($result);
                }

                if ($auto_create_item_whse_if_missing == "1" && !$has_item_warehouse){
                    $result = __('error.admin.permission_denied_auto_create_itemwhse', ['resource1' => $item_num, "resource2" => $whse_num]);
                    return json_encode($result);
                }

                if ($auto_create_item_whse_if_missing == "1" && $has_item_warehouse){
                    $result = true;
                    return json_encode($result);
                }

                $result = __('error.admin.notexist', ['resource' => __('admin.label.item_num')]);
                return json_encode($result);
                // return 'not exist';
            }
        }
    }
    public function itemv2(Request $request)
    {
        $item_num = $request->item_num;
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }

        $item = Item::where('item_num', $item_num)->first();
        if ($item) {
            if ($item->item_status == 0) {
                return 'inactive';
            } else {
                return 'active';
            }
        } else {
            return 'not exist';
        }
    }



    // Validate item warehouse pair if exist
    public function itemwhse_pallet(Request $request)
    {
        // dd($request->whse_num,$request->item_num);

        $item_num = $request->item_num;
        $whse_num = $request->whse_num;
        $loc_num = $request->loc_num;
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));
        }

        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        } else {
            $loc_num = utf8_encode(htmlspecialchars_decode(base64_decode($loc_num)));
        }


        $itemwhse = ItemLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->exists();

        // dd($itemwhse);
        if ($itemwhse) {
            $data['qty_available'] = "";
            $checkActive = Item::where('item_num', $item_num)->value('item_status');
            $base_uom = Item::where('item_num', $item_num)->value('uom');
            if ($checkActive == 0) {
                $data['type'] = 'inactive';
            } else {
                $data['type'] = 'exist';
                $acc_lpn_qty = 0;
                $itemlot = LotLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->exists();
                if ($itemlot) {
                    $data['lot'] = 'exist';
                    // $qty_available = LotLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->value('qty_available');
                } else {
                    $data['lot'] = 'not exist';
                }
                $qty_available = ItemLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->value('qty_available');
                // $acc_lpn_qty = Container::join('container_items', 'containers.lpn_num', '=', 'container_items.lpn_num')
                // ->where('containers.whse_num', $whse_num)->where('containers.loc_num', $loc_num)->where('container_items.qty_contained', '>', '0')->where('container_items.item_num', $item_num)
                // ->havingRaw('total_qty > 0')
                // ->selectRaw('SUM(container_items.qty_contained) as total_qty')
                // ->first();
                // $convertUom = UomConv::convertUOM($base_uom, $getLpnItem->uom, $getLpnItem->uom, $getLpnItem->qty_contained, $item_num, '', '', '');
                // $qtyConvert = $convertUom['conv_qty_to_base']['qty'];

                // $getLpns = Container::where('whse_num', $whse_num)->where('loc_num', $loc_num)->get();
                // foreach($getLpns as $getLpn){
                //     $getLpnItems = ContainerItem::where('lpn_num', $getLpn->lpn_num)->where('item_num', $item_num)->where('qty_contained', '>', 0)->get();
                //     foreach($getLpnItems as $getLpnItem){
                //         if($base_uom != $getLpnItem->uom){
                //             $convertUom = UomConv::convertUOM($base_uom, $getLpnItem->uom, $getLpnItem->uom, $getLpnItem->qty_contained, $item_num, '', '', '');
                //             $qtyConvert = $convertUom['conv_qty_to_base']['qty'];
                //             $acc_lpn_qty = $acc_lpn_qty + $qtyConvert;
                //         }
                //         else{
                //             $acc_lpn_qty = $acc_lpn_qty + $getLpnItem->qty_contained;
                //         }
                //     }
                // }
                // if($acc_lpn_qty){
                //     $totalQty = $acc_lpn_qty;
                // }
                // else{
                //     $totalQty = 0;
                // }

                // $data['qty_available'] = $qty_available - $totalQty;
                $data['qty_available'] = $qty_available;
                $data['base_uom'] = $base_uom;
            }
        } else {
            $data['type'] =  'not exist';
        }

        return $data;
    }

    // Validate pallet lot num
    public function lotnum_pallet(Request $request)
    {
        // dd($request->whse_num,$request->item_num);

        $item_num = $request->item_num;
        $whse_num = $request->whse_num;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));
        }

        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        } else {
            $loc_num = utf8_encode(htmlspecialchars_decode(base64_decode($loc_num)));
        }

        if ($this->is_base64_string($lot_num)) {
            $lot_num = base64_decode($lot_num);
        } else {
            $lot_num = utf8_encode(htmlspecialchars_decode(base64_decode($lot_num)));
        }

        $itemlot = LotLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->where('lot_num', $lot_num)->first();
        if (!empty($itemlot)) {
            if ($itemlot->freeze == 'Y') {
                return 'freeze';
            } else {
                return 'exist';
            }
        } else {
            return 'not exist';
        }
    }

    // Get LPN max Qty Contained
    public function max_qty_contained(Request $request)
    {
        // dd($request->whse_num,$request->item_num);

        $item_num = $request->item_num;
        $whse_num = $request->whse_num;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));
        }

        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        } else {
            $loc_num = utf8_encode(htmlspecialchars_decode(base64_decode($loc_num)));
        }

        if ($this->is_base64_string($lot_num)) {
            $lot_num = base64_decode($lot_num);
        } else {
            $lot_num = utf8_encode(htmlspecialchars_decode(base64_decode($lot_num)));
        }

        $acc_lpn_qty = 0;

        $base_uom = Item::where('item_num', $item_num)->value('uom');
        $qty_available = LotLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->where('lot_num', $lot_num)->value('qty_available');

        // $getLpns = Container::where('whse_num', $whse_num)->where('loc_num', $loc_num)->get();
        // foreach($getLpns as $getLpn){
        //     $getLpnItems = ContainerItem::where('lpn_num', $getLpn->lpn_num)->where('item_num', $item_num)->where('lot_num', $lot_num)->where('qty_contained', '>', 0)->get();
        //     foreach($getLpnItems as $getLpnItem){
        //         if($base_uom != $getLpnItem->uom){
        //             $convertUom = UomConv::convertUOM($base_uom, $getLpnItem->uom, $getLpnItem->uom, $getLpnItem->qty_contained, $item_num, '', '', '');
        //             $qtyConvert = $convertUom['conv_qty_to_base']['qty'];
        //             $acc_lpn_qty = $acc_lpn_qty + $qtyConvert;
        //         }
        //         else{
        //             $acc_lpn_qty = $acc_lpn_qty + $getLpnItem->qty_contained;
        //         }
        //     }
        // }

        // $totalQty = $acc_lpn_qty;

        $data['qty_available'] = $qty_available;

        return $data;
    }

    public function lotv2(Request $request)
    {
        $lot_num = $request->lot_num;
        if ($this->is_base64_string($lot_num)) {
            $lot_num = base64_decode($lot_num);
        } else {
            $lot_num = utf8_encode(htmlspecialchars_decode(base64_decode($lot_num)));
        }

        $lot = Lot::where('lot_num', $lot_num)->first();

        // dd($lot);
        if ($lot == null) {
            return 'not exist';
        }
    }

    public function itemvalidation(Request $request)
    {
        $item_num = $request->item_num;
        // dd($this->is_base64_string($item_num));
        if ($this->is_base64_string($item_num)) {

            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }


        if ($item_num == "NON-INV") {
            return 'active';
        } else {
            $item = Item::where('item_num', $item_num)->first();
            if ($item) {
                if ($item->item_status == 0) {
                    return 'inactive';
                } else {
                    return 'active';
                }
            } else {
                return 'not exist';
            }
        }
    }
    public function itemvalidation2(Request $request)
    {
        $item_num = $request->item_num;
        // dd($this->is_base64_string($item_num));
        // if ($this->is_base64_string($item_num)) {

        //     $item_num = base64_decode($item_num);
        // } else {
        //     $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        // }


        if ($item_num == "NON-INV") {
            return 'active';
        } else {
            $item = Item::where('item_num', $item_num)->first();
            if ($item) {
                if ($item->item_status == 0) {
                    return 'inactive';
                } else {
                    return 'active';
                }
            } else {
                return 'not exist';
            }
        }
    }


    public function productCodeValidation(Request $request)
    {
        $product_code = ProductCode::where('product_code', $request->product_code)->first();
        if ($product_code) {
            if ($product_code->product_status == 0) {
                return 'inactive';
            } else {
                return 'active';
            }
        } else {
            return 'not exist';
        }
    }

    public function customerValidation(Request $request)
    {
        $customer = Customer::where('cust_num', $request->cust_num)->first();
        if ($customer) {
            if ($customer->cust_status == 0) {
                return 'inactive';
            } else {
                return 'active';
            }
        } else {
            return 'not exist';
        }
    }

    public function getItemLocLotPallet($whse_num, $item_num, $lot_num = "")
    {

        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $lot_num = base64_decode($lot_num);
        }

        if ($lot_num != "") {
            $loc = LotLoc::select('loc_num', 'qty_available', 'uom')->where('whse_num', $whse_num)->where('lot_num', $lot_num)->where('item_num', $item_num)->where('freeze', 'N')->whereHas('location', function ($query) {
                return $query->where('loc_type', '=', 'S')->where('loc_status', 1);
            })->get();
        } else {
            $loc = ItemLoc::select('loc_num', 'qty_available', 'uom')->where('whse_num', $whse_num)->where('item_num', $item_num)->where('freeze', 'N')->whereHas('location', function ($query) {
                return $query->where('loc_type', '=', 'S')->where('loc_status', 1);
            })->get();
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($loc) {
            foreach ($loc as $each) {
                $each['qty_available'] = numberFormatPrecision($each->qty_available, $unit_quantity_format);
            }
        }

        if (count($loc) > 0) {
            return $loc;
        } else {
            return 'not exist';
        }


        // if($lot_num!="")
        // {
        //     $loc = LotLoc::select('loc_num', 'lot_num', 'qty_available', 'uom')
        //     ->where('qty_available', '>', 0)
        //    // ->where('loc_num', $loc_num)
        //     ->where('whse_num', $whse_num)
        //     ->where('item_num', $item_num)
        //     ->where('lot_num', $lot_num)
        //     ->whereHas('location', function ($q) use ($whse_num) {
        //         $q->where('whse_num', $whse_num)->where('loc_status', 1)->where('loc_type', 'S');
        //     })
        //     ->get();
        //     //dd($loc,$loc_num);
        //     if (!empty($loc)) {
        //         return $loc;
        //     }
        //     else {
        //         return 'not exist';
        //     }

        // }
        // else{
        //     $loc = ItemLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->first();
        //     //return $loc;
        //     if (!empty($loc)) {
        //         if ($loc->freeze == 'Y') {
        //             return 'Freeze';
        //         } else {
        //             $checkLocStat = Loc::where('loc_num', $loc_num)->first();
        //             if ($checkLocStat->loc_status == 0) {
        //                 return 'inactive';
        //             } else if ($checkLocStat->loc_type == 'T') {
        //                 return 'transit';
        //             } else {

        //             }
        //         }
        //     } else {
        //         return 'not exist';
        //     }
        // }
    }



    public function getItemLoc($whse_num, $item_num, $loc_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "null")
                $item_num = base64_decode($item_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        }

        $loc = ItemLoc::select(
            'loc_num',
            'qty_on_hand',
            'qty_on_rsvd',
            'qty_available',
            'uom'
        )->where('qty_available', '>', 0);
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        if ($item_num != "null")
            $loc = $loc->where('item_num', $item_num);
        $loc = $loc->whereHas('location', function ($q) use ($whse_num) {
            if ($whse_num != "null")
                $q->where('whse_num', $whse_num);
            $q->where('loc_status', 1)->where('loc_type', 'S')->where('pick_locs', 0);
        })->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($loc as $each) {
            $each['qty_on_hand'] = numberFormatPrecision($each->qty_on_hand, $unit_quantity_format, '.', '');
            $each['qty_available'] = numberFormatPrecision($each->qty_available, $unit_quantity_format, '.', '');

            // $each['qty_on_hand'] = $each->qty_on_hand;
            // $each['qty_available'] = $each->qty_available;
        }

        return $loc;
    }

    public function checkItemLoc($whse_num, $item_num, $loc_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = ItemLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', $loc_num)->first();
        if (!empty($loc)) {
            if ($loc->freeze == 'Y') {
                return 'Freeze';
            } else {
                $checkLocStat = Loc::where('loc_num', $loc_num)->first();
                if ($checkLocStat->loc_status == 0) {
                    return 'inactive';
                } else if ($checkLocStat->loc_type == 'T') {
                    return 'transit';
                } else {
                    return $loc;
                }
            }
        } else {
            return 'not exist';
        }
    }

    public function getItemLocation($whse_num, $item_num, $loc_num = "")
    {

        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = LotLoc::select('loc_num', 'lot_num', 'qty_available', 'uom')
            ->where('qty_available', '>', 0)
            ->where('whse_num', $whse_num)
            ->where('item_num', $item_num)
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('loc_status', 1)->where('loc_type', 'S')->where('pick_locs', 0);
            })
            ->get();

        return $loc;
    }

    public function getItemLocLot($whse_num, $item_num, $loc_num, $lot_num = "")
    {

        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
        }

        $loc = LotLoc::select('loc_num', 'lot_num', 'qty_available', 'uom')
            ->where('qty_available', '>', 0)
            ->where('loc_num', $loc_num)
            ->where('whse_num', $whse_num)
            ->where('item_num', $item_num)
            ->where('lot_num', $lot_num)
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('loc_status', 1)->where('loc_type', 'S');
            })
            ->first();

        return $loc;
    }

    public function getItemLocOpen($whse_num, $item_num, $loc_num = "")
    {

        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = ItemLoc::select('loc_num', 'qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')->where('whse_num', $whse_num)->where('item_num', $item_num)
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('loc_status', 1)->where('loc_type', 'S');
            })->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($loc as $each) {
            $each['qty_available'] = numberFormatPrecision($each['qty_available'], $unit_quantity_format);
        }

        return $loc->map(function ($item, $key) {
            $item['qty_available'] = $item->qty_available;
            return $item->only('loc_num', 'qty_available', 'uom');
        });
    }

    public function getItemLocCheckPicking($whse_num = "", $item_num = "", $loc_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "null")
                $item_num = base64_decode($item_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($item_num != "null") {
                $item_num = utf8_encode(base64_decode($item_num));
                $item_num = htmlspecialchars_decode($item_num);
            }
            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }
        $loc = ItemLoc::select('loc_num', 'qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')->where('qty_available', '>', 0);
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        if ($item_num != "null")
            $loc = $loc->where('item_num', $item_num);
        $loc = $loc->whereHas('location', function ($q) use ($whse_num) {
            if ($whse_num != "null")
                $q->where('whse_num', $whse_num);
            $q->where('loc_status', 1)->where('loc_type', 'S')->where('pick_locs', 0);
        })->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($loc as $each) {
            $each['qty_available'] = numberFormatPrecision($each['qty_available'], $unit_quantity_format);
            // $each['qty_on_hand'] = numberFormatPrecision($each['qty_on_hand'], $unit_quantity_format, '.', '');

            // $each['qty_available'] = $each['qty_available'];
        }

        $list = $loc->map(function ($item, $key) {
            $item['qty_available'] = $item->qty_available;
            return $item->only('loc_num', 'qty_available', 'uom');
        });

        return $list;
    }

    public function getIssueLocation($form_name, $tparm_issue_location = 0, $whse_num = "", $item_num = "", $loc_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            if ($tparm_issue_location != "null")
                $tparm_issue_location = base64_decode($tparm_issue_location);
            if ($form_name != "null")
                $form_name = base64_decode($form_name);
        } else {
            if ($tparm_issue_location != "null") {
                $tparm_issue_location = utf8_encode(base64_decode($tparm_issue_location));
                $tparm_issue_location = htmlspecialchars_decode($tparm_issue_location);
            }
            if ($form_name != "null") {
                $form_name = utf8_encode(base64_decode($form_name));
                $form_name = htmlspecialchars_decode($form_name);
            }
        }

        if ($form_name === 'JobMaterialIssue')
            $masterLocation = $this->getItemLocCheckPicking($whse_num, $item_num, $loc_num)->toArray();
        if ($form_name === 'BatchJobMaterialIssue')
            $masterLocation = $this->getWhseLoc($whse_num, $loc_num)->toArray();

        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "null")
                $item_num = base64_decode($item_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($item_num != "null") {
                $item_num = utf8_encode(base64_decode($item_num));
                $item_num = htmlspecialchars_decode($item_num);
            }
            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }

        if ($tparm_issue_location != 0) {
            $tparm = new TparmView();
            $tparm_issue_location = $tparm->getTparmValue($tparm_issue_location, 'issue_location');

            $default_issue_location = json_decode($tparm_issue_location, true);

            if (!empty($default_issue_location) && $default_issue_location[0]['whse_num']) {
                // filter out the warehouse
                $searchKey = 'whse_num';
                $searchValue = $whse_num;
                $arrFilterWhse = array_filter($default_issue_location, function ($subarray) use ($searchKey, $searchValue) {
                    return isset($subarray[$searchKey]) && $subarray[$searchKey] === $searchValue;
                });

                // if the whse_num not found in the issue location
                // return the data from master location
                if (empty($arrFilterWhse)) {
                    return $masterLocation;
                }

                $arrFilterLoc = [];
                // filter out the location
                if ($loc_num != "null" && $loc_num != '') {
                    $searchKey = 'loc_num';
                    $searchValue = $loc_num;
                    $arrFilterLoc = array_filter($arrFilterWhse, function ($subarray) use ($searchKey, $searchValue) {
                        return isset($subarray[$searchKey]) && $subarray[$searchKey] === $searchValue;
                    });

                    // if the whse_num found in the issue location, but the location is not
                    // return empty array
                    if (!empty($arrFilterWhse) && empty($arrFilterLoc)) {
                        return [];
                    }
                } else {
                    // if no loc filter, set the loc array with the array filtered by whse
                    $arrFilterLoc = $arrFilterWhse;
                }

                // master location - filter out the location based on the issue location
                $masterResult = [];
                if (!empty($arrFilterLoc)) { //  && ($loc_num == 'null' || $loc_num == '')
                    foreach ($arrFilterLoc as $key => $value) {
                        $searchKey = 'loc_num';
                        $searchValue = $value[$searchKey];

                        $val = array_filter($masterLocation, function ($subarray) use ($searchKey, $searchValue) {
                            return isset($subarray[$searchKey]) && $subarray[$searchKey] === $searchValue;
                        });

                        foreach ($val as $v) {
                            array_push($masterResult, $v);
                        }
                    }
                }

                return $masterResult;
            }
        }

        return $masterLocation;
    }

    public function getItemLocforPickList($whse_num, $item_num, $loc_num = "")
    {

        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = ItemLoc::select('loc_num', 'qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')->where('whse_num', $whse_num)->where('item_num', $item_num)
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('loc_status', 1)->where('loc_type', 'S');
            })->get();

        return $loc->map(function ($item, $key) {
            $item['qty_available'] = $item->qty_available;
            return $item->only('loc_num', 'qty_available', 'uom');
        });
    }

    // Add On
    public function getItemLocforPickListAdd($whse_num, $item_num, $frmDb_loc, $check_qty_to_pick, $loc_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
            $frmDb_loc = base64_decode($frmDb_loc);
            $check_qty_to_pick = base64_decode($check_qty_to_pick);
            // $check_qty_to_pick = base64_decode($check_qty_to_pick);
        }

        // return $whse_num." ::".$frmDb_loc."::".$item_num."::".$loc_num.":::".$check_qty_to_pick;
        $loc = ItemLoc::select('loc_num', 'qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')->where('whse_num', $whse_num)->where('item_num', $item_num)
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('pick_locs', 0)->where('loc_status', 1)->where('loc_type', 'S');
            })->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $arrItem = [];
        $index = 0;
        foreach ($loc as $key) {
            if ($key->loc_num == $frmDb_loc) {
                $arrItem[$index]['loc_num'] = $key->loc_num;
                $arrItem[$index]['qty_available'] = numberFormatPrecision($key->qty_available + $check_qty_to_pick, $unit_quantity_format);
                $arrItem[$index]['uom'] = $key->uom;
            } else {
                $arrItem[$index]['loc_num'] = $key->loc_num;
                $arrItem[$index]['qty_available'] = numberFormatPrecision($key->qty_available, $unit_quantity_format);
                $arrItem[$index]['uom'] = $key->uom;
            }
            $index++;
        }

        return $arrItem;
        // if($frmDb_loc == $loc_num)
        // {
        //     return $loc->map(function ($item, $key) {
        //         $item['qty_available'] = $item->qty_available + $check_qty_to_pick;
        //         return $item->only('loc_num', 'qty_available', 'uom');
        //   });
        // }
        // else{
        //         return $loc->map(function ($item, $key) {
        //             if($item->loc_num ==@$frmDb_loc)
        //             {
        //                 $item['qty_available'] = $item->qty_available + $check_qty_to_pick;
        //             }
        //             else{
        //                 $item['qty_available'] = $item->qty_available ;
        //             }
        //             return $item->only('loc_num', 'qty_available', 'uom');
        // });
        // }
    }

    public function getItemLocInv($whse_num, $item_num, $loc_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }
        $loc = ItemLoc::select('loc_num', 'qty_on_hand', 'qty_on_rsvd', 'uom')->where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num', 'like', '%' . $loc_num . '%')
            ->whereHas('location', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num)->where('loc_status', 1);
            })->get();


        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($loc as $each) {
            $each['qty_on_hand'] = numberFormatPrecision($each['qty_on_hand'], $unit_quantity_format);
        }

        return $loc->map(function ($item, $key) {
            $item['qty_on_hand'] = $item->qty_on_hand;
            return $item->only('loc_num', 'qty_on_hand', 'uom');
        });
    }

    public function getExistingItem($whse_num, $item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        $loc = ItemLoc::select('item_num')->where('whse_num', $whse_num)
            ->where('qty_on_hand', '>', 0)
            ->where('item_num', 'like', '%' . $item_num . '%')
            ->groupBy('item_num')->get();
        $items = Item::select('item_num', 'item_desc')->whereIn('item_num', $loc->pluck('item_num')->toArray())->active()->orderBy('item_num')->get();
        return $items;
    }

    public function getExistingItemNoCheck($whse_num, $item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        $loc = ItemLoc::select('item_num')->where('whse_num', $whse_num)

            ->where('item_num', 'like', '%' . $item_num . '%')
            ->groupBy('item_num')->get();
        $items = Item::select('item_num', 'item_desc')->whereIn('item_num', $loc->pluck('item_num')->toArray())->active()->orderBy('item_num')->get();
        return $items;
    }

    public function getItemLocbyLoc($item_num, $loc_num = "", $whse_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
            $whse_num = base64_decode($whse_num);
        }
        $loc = new ItemLoc();
        $loc = $loc->select('loc_num')->where('whse_num', 'like', '%' . $whse_num . '%')->where('item_num', $item_num)->where('loc_num', 'like', '%' . $loc_num . '%')->groupby('loc_num')->get();
        return $loc;
    }

    public function getLocItem($whse_num, $loc_num, $item_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
            $whse_num = base64_decode($whse_num);
        } else {
            if ($item_num != "" && $item_num != "null") {
                $item_num = htmlspecialchars_decode(utf8_encode(base64_decode($item_num)));
            }
            if ($loc_num != "" && $loc_num != "null") {
                $loc_num = htmlspecialchars_decode(utf8_encode(base64_decode($loc_num)));
            }
            if ($whse_num != "" && $whse_num != "null") {
                $whse_num = htmlspecialchars_decode(utf8_encode(base64_decode($whse_num)));
            }
        }

        $item = Item::select('item_num', 'item_desc')
            ->whereHas('itemlocs', function ($q) use ($loc_num, $whse_num, $item_num) {
                $q
                    ->where('loc_num', $loc_num)
                    ->where('whse_num', $whse_num)
                    ->where('item_num', 'LIKE', "%$item_num%");
            })
            ->where('item_status', 1)
            ->get();

        return $item;
    }

    public function getWhseItem($whse_num, $item_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        } else {
            $whse_num = utf8_encode(base64_decode($whse_num));
            $whse_num = htmlspecialchars_decode($whse_num);
            $item_num = utf8_encode(base64_decode($item_num));
            $item_num = htmlspecialchars_decode($item_num);
        }

        $loc = ItemLoc::select('item_num')->where('whse_num', $whse_num)->get();
        $item = Item::select('item_num', 'item_desc')->whereIn('item_num', $loc->pluck('item_num')->toArray())->active()->orderBy('item_num')->get();

        return $item;

        // $item = Item::select('item_num', 'item_desc')
        //     ->whereHas('itemlocs', function ($q) use ($whse_num) {
        //         $q
        //             ->where('whse_num', $whse_num);
        //     })
        //     ->where('item_status', 1)
        //     ->get();

        // return $item;
    }

    public function getItemFromItemWhse($whse_num, $item_num = "")
    {
        $whse_num = utf8_encode(base64_decode($whse_num));
        $whse_num = htmlspecialchars_decode($whse_num);
        $item_num = utf8_encode(base64_decode($item_num));
        $item_num = htmlspecialchars_decode($item_num);

        // $item_nums = ItemWarehouse::where('whse_num', $whse_num)->pluck('item_num')->toArray();

        //  $items = [];

        // foreach ($item_nums as $item_num) {
        //     $items[] = [
        //         'item_num' => $item_num,
        //         'item_desc' => Item::where('item_num', $item_num)->value('item_desc'),
        //     ];
        // }


        $warehouses = ItemWarehouse::with('item')
            ->where('whse_num', $whse_num)
            ->get();
        foreach ($warehouses as $data) {
            $items[] = [
                'item_num' => $data->item_num,
                'item_desc' => $data->item_desc
            ];
        }


        return $items;
    }






    public function getItemfromItemLocItemWhse($whse_num, $item_num = "")
    {
        // $whse_num = base64_decode($whse_num);
        // $item_num = base64_decode($item_num);
        $whse_num = utf8_encode(base64_decode($whse_num));
        $whse_num = htmlspecialchars_decode($whse_num);
        $item_num = utf8_encode(base64_decode($item_num));
        $item_num = htmlspecialchars_decode($item_num);

        $item_num_from_itemloc = ItemLoc::where('whse_num', $whse_num)->pluck('item_num')->toArray();
        $item_num_from_itemwhse = ItemWarehouse::where('whse_num', $whse_num)->pluck('item_num')->toArray();

        $item_nums = array_unique(array_merge($item_num_from_itemloc, $item_num_from_itemwhse));
        $items = [];

        foreach ($item_nums as $item_num) {
            $items[] = [
                'item_num' => $item_num,
                'item_desc' => Item::where('item_num', $item_num)->value('item_desc'),
            ];
        }

        return $items;
    }
    public function getPOItemByLine($whse_num, $po_num = "", $po_line = "", $item_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $po_num = base64_decode($po_num);
            $item_num = base64_decode($item_num);
            $po_line = base64_decode($po_line);
        }
        // dd($po_line, $po_num, $whse_num);
        $site_id = auth()->user()->site_id;

        $checkItem = PurchaseOrderItem::select('item_num')->where('site_id', $site_id);

        if ($po_num != "") {
            $checkItem = $checkItem->where('po_num', $po_num);
        }
        if ($po_line != "") {
            $checkItem = $checkItem->where('po_line', $po_line);
        }

        $checkItem = $checkItem->get();
        // dd($checkItem);
        $item = Item::select('item_num')
            ->whereHas('po_items', function ($q) use ($po_num, $whse_num, $po_line, $item_num) {
                $q
                    ->where('po_num', 'LIKE', "$po_num")
                    ->where('whse_num', $whse_num)
                    ->where('item_num', 'LIKE', "%$item_num%")
                    // ->where('item_num', addCslashes($item_num, '\\'))
                    ->inventory()
                    ->open()
                    ->where('qty_ordered', '>', 0);
                if ($po_line != "")
                    $q->where('po_line', $po_line);
            })
            //issue 340 because item locs not checked
            //                ->whereHas('itemlocs',function($q) use ( $whse_num){
            //                    $q->where('whse_num', $whse_num);
            ////                                        $q->where('item_num',$item_num);
            //
            //                })
            ->where('item_status', 1)
            ->get();

        foreach ($checkItem as $itemcheck) {

            if ($itemcheck->item_num == "NON-INV") {
                $itemset = new Item();
                $itemset->item_num = $itemcheck->item_num;
                $item[] = $itemset;
            }
        }


        return $item;
    }

    public function getPOItem($whse_num, $po_num = "", $item_num = "", $po_line = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $po_num = base64_decode($po_num);
            $item_num = base64_decode($item_num);
            $po_line = base64_decode($po_line);
        }
        $site_id = auth()->user()->site_id;

        $checkItem = PurchaseOrderItem::select('item_num')->where('site_id', $site_id);

        if ($po_num != "") {
            $checkItem = $checkItem->where('po_num', $po_num);
        }
        if ($po_line != "") {
            $checkItem = $checkItem->where('po_line', $po_line);
        }

        $checkItem = $checkItem->get();

        $item = Item::select('item_num')
            ->whereHas('po_items', function ($q) use ($po_num, $whse_num, $item_num) {
                $q
                    ->where('po_num', 'LIKE', "%$po_num%")
                    ->where('whse_num', $whse_num)
                    ->where('item_num', 'LIKE', "%$item_num%")
                    // ->where('item_num', addCslashes($item_num, '\\'))
                    ->inventory()
                    ->open()
                    ->where('qty_ordered', '>', 0);
            })
            //issue 340 because item locs not checked
            //                ->whereHas('itemlocs',function($q) use ( $whse_num){
            //                    $q->where('whse_num', $whse_num);
            ////                                        $q->where('item_num',$item_num);
            //
            //                })
            ->where('item_status', 1)
            ->get();

        foreach ($checkItem as $itemcheck) {

            if ($itemcheck->item_num == "NON-INV") {
                $itemset = new Item();
                $itemset->item_num = $itemcheck->item_num;
                $item[] = $itemset;
            }
        }


        return $item;
    }

    public function getGRNItem($whse_num, $grn_num, $item_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $grn_num = base64_decode($grn_num);
            $item_num = base64_decode($item_num);
        }
        $site_id = auth()->user()->site_id;

        $checkItem = GRNItem::select('item_num');

        if ($grn_num != "") {
            $checkItem = $checkItem->where('grn_num', $grn_num);
        }

        $checkItem = $checkItem->where('site_id', $site_id)->get();

        $item = Item::select('item_num')
            ->whereHas('grn_items', function ($q) use ($grn_num, $item_num) {
                $q->where('grn_num', 'LIKE', "%$grn_num%")
                    ->where('item_num', 'LIKE', "%$item_num%")
                    ->where('status', 'O');
                // ->where('item_num', addCslashes($item_num, '\\'))
                // ->inventory();
                // ->open();
                // ->where('qty_ordered', '>', 0);
            })
            ->where('item_status', 1)
            ->get();

        foreach ($checkItem as $itemcheck) {
            if ($itemcheck->item_num == "NON-INV") {
                $itemset = new Item();
                $itemset->item_num = $itemcheck->item_num;
                $item[] = $itemset;
            }
        }

        return $item;
    }

    // get Vendor DO list
    public function getVendorDO($whse_num, $po_num = "", $vend_do = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $po_num = base64_decode($po_num);
            $vend_do = base64_decode($vend_do);
        }

        $vendor_do = PurchaseOrder::select('vend_do');

        if ($po_num != "")
            $vendor_do = $vendor_do->where('po_num', $po_num);

        $vendor_do = $vendor_do->where('vend_do', '!=', null)
            ->where('site_id', auth()->user()->site_id)
            ->get();

        return $vendor_do;
    }

    // get Vendor DO list for GRN
    public function getGRNVendorDO($whse_num, $grn_num = "", $vend_do = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $grn_num = base64_decode($grn_num);
            $vend_do = base64_decode($vend_do);
        }

        $vendor_do = GRN::select('vend_do');

        if ($grn_num != "")
            $vendor_do = $vendor_do->where('grn_num', $grn_num);

        $vendor_do = $vendor_do->where('vend_do', '!=', null)
            ->where('site_id', auth()->user()->site_id)
            ->get();

        return $vendor_do;
    }

    public function getLocPacking($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = new PicklistSummaryDetails();
        $loc = $loc->select('loc_num')->where('whse_num', $whse_num)
            ->where('group_by', 'Single Order')
            ->where('status', 'C')
            ->orderBy('loc_num', 'asc')
            ->distinct()
            ->get();
        return $loc;
    }

    public function getPackingList($whse_num, $pack_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $pack_num = base64_decode($pack_num);
        }

        $pack = new Pack();
        $pack = $pack->select('pack_num')->where('whse_num', $whse_num)
            ->orderBy('pack_num', 'asc')
            ->distinct()
            ->get();

        return $pack;
    }

    public function getLocfilterLoc($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        } else {
            $whse_num = utf8_encode(base64_decode($whse_num));
            $whse_num = htmlspecialchars_decode($whse_num);
            $loc_num = utf8_encode(base64_decode($loc_num));
            $loc_num = htmlspecialchars_decode($loc_num);
        }

        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name')->whereNotIn('loc_num', [$loc_num])->where('whse_num', $whse_num)
            ->where('loc_type', 'S')
            ->where('loc_status', 1)
            ->orderBy('loc_num', 'asc')
            ->get();
        return $loc;
    }

    public function getLoc($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }

            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }
        // dd($whse_num,$loc_num);
        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name');
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        $loc = $loc->where('loc_type', 'S')
            ->where('loc_status', 1)
            ->orderBy('loc_num', 'asc')
            ->get();
        return $loc;
    }

    public function getLocMaster2($loc_num = "")
    {
        if ($this->is_base64_string($loc_num)) {
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }
        // dd($whse_num,$loc_num);
        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name');
        $loc = $loc->where('loc_type', 'S')
            ->where('loc_status', 1)
            ->orderBy('loc_num', 'asc')
            ->get();
        return $loc;
    }

    public function getLocNoPicking($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }

            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }

        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name');
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        $loc = $loc->where('loc_type', 'S')
            ->where('loc_status', 1)
            ->where('pick_locs', 0)
            ->orderBy('loc_num', 'asc')
            ->get();
        return $loc;
    }

    public function getReceiptLocation($form_name, $tparm_receipt_location = 0, $whse_num = "", $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            if ($tparm_receipt_location != "null")
                $tparm_receipt_location = base64_decode($tparm_receipt_location);
            if ($form_name != "null")
                $form_name = base64_decode($form_name);
        } else {
            if ($tparm_receipt_location != "null") {
                $tparm_receipt_location = utf8_encode(base64_decode($tparm_receipt_location));
                $tparm_receipt_location = htmlspecialchars_decode($tparm_receipt_location);
            }
            if ($form_name != "null") {
                $form_name = utf8_encode(base64_decode($form_name));
                $form_name = htmlspecialchars_decode($form_name);
            }
        }

        if ($form_name === 'JobReceipt' || $form_name === 'WIPMove')
            $masterLocation = $this->getLocNoPicking($whse_num, $loc_num)->toArray();
        if ($form_name === 'MachineRun' || $form_name === 'JobLabour')
            $masterLocation = $this->getWhseLoc($whse_num, $loc_num)->toArray();

        if ($this->is_base64_string($whse_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }

        if ($tparm_receipt_location != 0) {
            $tparm = new TparmView();
            $tparm_receipt_location = $tparm->getTparmValue($tparm_receipt_location, 'receipt_location');

            $default_receipt_location = json_decode($tparm_receipt_location, true);

            if (!empty($default_receipt_location) && $default_receipt_location[0]['whse_num']) {
                // filter out the warehouse
                $searchKey = 'whse_num';
                $searchValue = $whse_num;
                $arrFilterWhse = array_filter($default_receipt_location, function ($subarray) use ($searchKey, $searchValue) {
                    return isset($subarray[$searchKey]) && $subarray[$searchKey] === $searchValue;
                });

                // if the whse_num not found in the receipt location
                // return the data from master location
                if (empty($arrFilterWhse)) {
                    return $masterLocation;
                }

                $arrFilterLoc = [];
                // filter out the location
                if ($loc_num != "null" && $loc_num != '') {
                    $searchKey = 'loc_num';
                    $searchValue = $loc_num;
                    $arrFilterLoc = array_filter($arrFilterWhse, function ($subarray) use ($searchKey, $searchValue) {
                        return isset($subarray[$searchKey]) && $subarray[$searchKey] === $searchValue;
                    });

                    // if the whse_num found in the receipt location, but the location is not
                    // return empty array
                    if (!empty($arrFilterWhse) && empty($arrFilterLoc)) {
                        return [];
                    }
                } else {
                    // if no loc filter, set the loc array with the array filtered by whse
                    $arrFilterLoc = $arrFilterWhse;
                }

                // master location - filter out the location based on the receipt location
                $masterResult = [];
                if (!empty($arrFilterLoc)) { //  && ($loc_num == 'null' || $loc_num == '')
                    foreach ($arrFilterLoc as $key => $value) {
                        $searchKey = 'loc_num';
                        $searchValue = $value[$searchKey];

                        $val = array_filter($masterLocation, function ($subarray) use ($searchKey, $searchValue) {
                            return isset($subarray[$searchKey]) && $subarray[$searchKey] === $searchValue;
                        });

                        foreach ($val as $v) {
                            array_push($masterResult, $v);
                        }
                    }
                }

                return $masterResult;
            }
        }

        return $masterLocation;
    }

    public function getLocPL($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name')->where('whse_num', $whse_num)
            ->where('loc_type', 'S')
            ->where('loc_status', 1)
            ->where('pick_locs', 0)
            ->orderBy('loc_num', 'asc')
            ->get();
        return $loc;
    }

    public function getLocWithoutWhse($loc_num = "")
    {
        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        }
        $loc = Loc::select('loc_num', 'loc_name')->orderBy('loc_num', 'asc')->where('loc_type', 'S')->where('loc_status', 1)->get();

        return $loc;
    }

    public function getLocWithTransit($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        }
        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name')
            ->where('whse_num', $whse_num)
            ->where('loc_num', 'like', '%' . $loc_num . '%')
            ->orderBy('loc_num', 'asc')
            ->whereIn('loc_type', ['S', 'T'])
            ->where('loc_status', 1)
            ->get();

        return $loc;
    }

    public function getTransitLoc($loc_num = "")
    {
        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        }

        $loc = Loc::select('loc_num', 'loc_name')
            ->where('loc_type', 'T')
            ->where('loc_status', 1)
            ->get();

        return $loc;
    }

    public function getToTransitLoc($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        }

        $loc = Loc::select('loc_num', 'loc_name')
            ->where('whse_num', $whse_num)
            ->where('loc_type', 'T')
            ->where('loc_status', 1)
            ->get();

        return $loc;
    }

    public function getVendor($vend_num = "")
    {
        if ($this->is_base64_string($vend_num)) {
            $loc_num = base64_decode($vend_num);
        }

        $vend = new Vendor();
        $vend = $vend->select('vend_num', 'vend_name')->where('vend_num', 'like', '%' . $vend_num . '%')->orderBy('vend_num', 'asc')->active()->get();

        return $vend;
    }


    public function getItemVendor($whse_num, $item_num, $vend_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $vend_num = base64_decode($vend_num);
        }
        // dd($item_num, $whse_num, $vend_num);
        /*$vend = new PurchaseOrderItem();
        $vend = $vend->select('vend_num')
            ->where('whse_num', $whse_num);
        if ($item_num != "") {
            $vend =   $vend->where('item_num', $item_num);
        }
        if ($vend_num != "") {
            $vend =  $vend->where('vend_num', 'like', '%' . $vend_num . '%');
        }

        $vend =  $vend->where('rel_status', 'O')
            ->distinct()
            ->get();*/

        // Fix #3116
        $vend = new PurchaseOrderItem();
        $vend = $vend->select('po_items.vend_num')
            ->where('po_items.whse_num', $whse_num)
            ->join('vendors', 'vendors.vend_num', '=', 'po_items.vend_num')
            ->where('vend_status', 1); // Ensures vendor is active

        if ($item_num != "") {
            $vend = $vend->where('po_items.item_num', $item_num);
        }
        if ($vend_num != "") {
            $vend = $vend->where('po_items.vend_num', 'like', '%' . $vend_num . '%');
        }

        $vend = $vend->where('po_items.rel_status', 'O')
            ->distinct()
            ->get();

        return $vend;
    }

    public function getGRNVendor($whse_num, $grn_num = "", $item_num = "", $vend_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $grn_num = base64_decode($grn_num);
            $item_num = base64_decode($item_num);
            $vend_num = base64_decode($vend_num);
        }
        // dd($item_num, $grn_num, $whse_num, $vend_num);
        $vend = GRN::select('vend_num')
            ->where('whse_num', $whse_num);
        if ($grn_num != "" || $item_num != "") {
            $vend = $vend->whereHas('grn_items', function ($q) use ($grn_num, $item_num) {
                $q->where('grn_num', $grn_num)
                    ->where('item_num', 'LIKE', "%$item_num%")
                    ->where('status', 'O');
            });
        }
        if ($vend_num != "") {
            $vend =  $vend->where('vend_num', 'like', '%' . $vend_num . '%');
        }

        $vend =  $vend->where('grn_status', 'O')
            ->distinct()
            ->get();

        return $vend;
    }

    public function getVendorByGrn($grn_num)
    {
        if ($this->is_base64_string($grn_num)) {
            $grn_num = base64_decode($grn_num);
        }
        $vend = new GRN();
        $vend = $vend->select('vend_num', 'vend_name')->where('grn_num', $grn_num)->first();
        return $vend;
    }

    public function getVendorByPo($po_num)
    {
        if ($this->is_base64_string($po_num)) {
            $po_num = base64_decode($po_num);
        }
        $vend = new PurchaseOrderItem();
        $vend = $vend->select('vend_num', 'vend_name')->where('po_num', $po_num)->first();
        return $vend;
    }

    public function getWorkCenter($wc_num = "")
    {
        if ($this->is_base64_string($wc_num)) {
            $wc_num = base64_decode($wc_num);
        }
        $wc = new WorkCenter();
        $wc = $wc->select('wc_num', 'wc_desc')->active()->get();
        return $wc;
    }

    public function getWorkCenterJobRoute($job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }
        $wc = new JobRoute();


        $wc = $wc->select('wc_num');
        if ($job_num) {
            $wc =   $wc->where('job_num', $job_num);
        }
        $wc = $wc->distinct('wc_num')->active()->get();


        return $wc;
    }

    public function getTrnReceiptNum($whse_num, $trn_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $trn_num = base64_decode($trn_num);
        }
        $trn = TransferLine::select('trn_num')->where('to_whse', $whse_num)
            ->whereRaw('IFNULL(qty_shipped,0) - IFNULL(qty_received,0) - IFNULL(qty_loss,0) > 0')
            ->where('line_stat', 'O')->groupBy('trn_num')->get();

        return $trn;
    }

    public function getTrnNum($trn_num = "")
    {
        if ($this->is_base64_string($trn_num)) {
            $trn_num = base64_decode($trn_num);
        }
        $trn = new TransferOrder();
        $trn = $trn->select('trn_num')->where('trn_num', 'like', '%' . $trn_num . '%')->where('status', 'O')->get();
        return $trn;
    }

    public function getTrnNumWhse($whse_num, $trn_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $trn_num = base64_decode($trn_num);
        }

        $trn = new TransferOrder();
        $trn = $trn->select('trn_num')->where('from_whse', $whse_num)->where('trn_num', 'like', '%' . $trn_num . '%')->where('status', 'O')->get();
        return $trn;
    }

    public function getAllTrnNumWhse($whse_num, $trn_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $trn_num = base64_decode($trn_num);
        }

        $trn = new TransferOrder();
        $trn = $trn->select('trn_num')->where('from_whse', $whse_num)->where('trn_num', 'like', '%' . $trn_num . '%')->get();
        return $trn;
    }

    public function getTrnNumFromTrnLine($trn_num = "")
    {
        if ($this->is_base64_string($trn_num)) {
            $trn_num = base64_decode($trn_num);
        }
        $trn = new TransferLine();
        $trn = $trn->select('trn_num')->where('trn_num', 'like', '%' . $trn_num . '%')->where('line_stat', 'O')->whereRaw('IFNULL(qty_required,0) - IFNULL(qty_shipped,0) > 0')->orderBy('trn_num', 'asc')->groupBy('trn_num')->get();
        return $trn;
    }

    public function getTrnLine($trn_num, $trn_line = "")
    {
        if ($this->is_base64_string($trn_num)) {
            $trn_num = base64_decode($trn_num);
            $trn_line = base64_decode($trn_line);
        }
        $transLine = new TransferLine();
        $transLine = $transLine->select('trn_line', 'item_num')->where('trn_num', $trn_num)
            ->where('trn_line', 'like', '%' . $trn_line . '%')
            ->where('qty_required', '>', 0)
            ->where('line_stat', 'O')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where(function ($q) {
                $q->whereHas('from_whse', function ($x) {
                    $x->where('whse_status', 1);
                });
                $q->whereHas('to_whse', function ($x) {
                    $x->where('whse_status', 1);
                });
            })
            ->get();

        return $transLine;
    }

    public function getCoNum(Request $request, $co_num = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
        }
        $co = new CustomerOrderItem();
        $co = $co->select('co_num')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->distinct()->get();
        return $co;
    }
    public function getCoNumLine(Request $request, $co_num = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
        }
        // dd($co_num);
        $co = new CustomerOrderItem();
        $co = $co->select('co_line')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('co_num', 'like',  $co_num)
            ->distinct()->get();
        return $co;
    }


    public function getCoNumOpen(Request $request, $co_num = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
        }
        $co = new CustomerOrderItem();
        $co = $co->select('co_num')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('rel_status', 'O')
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->distinct()->get();
        return $co;
    }

    public function getCoNumWhse($whse_num, $co_num = "")
    {

        if ($this->is_base64_string($co_num)) {
            $whse_num = base64_decode($whse_num);
            $co_num = base64_decode($co_num);
        }
        $co = new CustomerOrderItem();
        $co = $co->select('co_num')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->where('whse_num', $whse_num)
            ->distinct()->get();
        return $co;
    }

    public function getFirstCoNum($co_num)
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
        }
        $co = new CustomerOrderItem();
        $co = $co->where('co_num', 'like', '%' . $co_num . '%')
            ->distinct()->first();
        return $co;
    }

    public function getFirstCoLine($co_num, $co_line)
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
        }
        $co = new CustomerOrderItem();
        $co = $co->where('co_num', $co_num)
            ->where('co_line', 'like', '%' . $co_line . '%')
            ->distinct()->first();
        $sum = $co->where('co_num', $co_num)->where('co_line', 'like', '%' . $co_line . '%')->sum('qty_released');
        return array_merge($co->toArray(), array('balance' => numberFormatPrecision(($co->qty_ordered - $sum), 2, '.', '')));
    }

    public function getAllCoLine($co_num, $co_line)
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
        }
        $coline = new CustomerOrderItem();
        $coline = $coline->where('co_num', $co_num)
            ->where('co_line', $co_line)
            ->get();
        return $coline;
    }

    public function getOpenCoNum(Request $request, $whse_num, $co_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $co_num = base64_decode($co_num);
            $whse_num = base64_decode($whse_num);
        }
        //dd($whse_num);
        $co = new CustomerOrderItem();
        $co = $co->select('co_num', 'cust_name')
            ->where('whse_num', 'like', '%' . $whse_num . '%')
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->where('rel_status', 'O')
            ->distinct()
            // ->leftJoin("customers", "customers.cust_num", "=", "coitems.cust_num")
            ->whereHas('customer', function ($q) {
                $q->where('cust_status', 1);
            })
            ->get();

        return $co;
    }

    public function getCoNumPacking(Request $request, $whse_num, $co_num = "")
    {
        if ($this->is_base64_string($whse_num)) {

            $whse_num = base64_decode($whse_num);
            $co_num = base64_decode($co_num);
        }


        $co = new PicklistTestItems();
        $co = $co->select('ref_num')
            // ->where('picklist_id', $co)
            ->where('line_status', 'C')
            ->where('whse_num', $whse_num)
            ->distinct()
            ->get();

        return $co;
    }


    public function getCoLine($co_num, $whse_num, $co_line = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
            $whse_num = base64_decode($whse_num);
        }
        $coline = new CustomerOrderItem();
        // Check the Item
        $CheckItemNum = $coline->select('item_num', 'co_line')
            ->where('co_num', $co_num)
            ->where('whse_num', $whse_num)
            ->get();

        $coline = $coline->select('co_line')
            ->where('co_num', $co_num)
            ->where('whse_num', $whse_num)
            ->where('co_line', 'like', '%' . $co_line . '%');

        $colinecheck = new CustomerOrderItem();
        $arrColine = [];
        foreach ($CheckItemNum as $key) {
            // dd($key->item_num);
            if ($key->item_num != "NON-INV") {
                $colinedata = $colinecheck->select('co_line')
                    ->whereHas('item', function ($q) {
                        $q->where('item_status', 0);
                    })
                    ->where('co_num', $co_num)
                    ->where('whse_num', $whse_num)
                    ->where('co_line', 'like', '%' . $co_line . '%')
                    ->groupBy('co_line')
                    ->get()->toArray();
            }
        }
        if (count($colinedata ?? []) > 0) {
            foreach ($colinedata as $kk => $co_line) {
                $arrColine[$co_line['co_line']] = $co_line['co_line'] ?? [];
            }
            $coline = $coline->whereNotIn('co_line', $arrColine);
        }
        $coline = $coline->groupBy('co_line')->get();



        return $coline;
    }

    public function getCoRelease($co_num, $co_line, $co_rel = "")
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
            $co_line = base64_decode($co_line);
            $co_rel = base64_decode($co_rel);
        }
        $corel = new CustomerOrderItem();
        $corel = $corel->select('co_rel')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('co_num', $co_num)
            ->where('co_line', $co_line)
            ->where('co_rel', 'like', '%' . $co_rel . '%')
            ->get();
        return $corel;
    }

    public function getStageLoc($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        }
        $list = new StageLoc();
        $stageloc = $list->select('stage_num', 'locs.loc_name')
            ->leftJoin('locs', function ($join) {
                $join->on('locs.loc_num', '=', 'stage_locs.stage_num');
                $join->on('locs.site_id', '=', 'stage_locs.site_id');
            })
            ->where('stage_locs.whse_num', $whse_num)
            ->groupBy('stage_num')
            ->orderBy('stage_num')
            ->get();

        return $stageloc;
    }

    public function getStagedCo($whse_num, $stage_num, $co_num = "")
    {
        if ($this->is_base64_string($stage_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $co_num = base64_decode($co_num);
        }
        $list = new StageLoc;
        $arrGetConum = array();
        $co_num = $list->select('stage_locs.co_num', DB::raw('co_num as cust_name'), DB::raw('count(*) as line_stage'))

            ->where('whse_num', $whse_num)
            ->where('stage_num', $stage_num)
            ->groupby('co_num')

            ->get();

        foreach ($co_num as $co_line) {
            array_push($arrGetConum, $co_line->co_num);

            //$co_line->cust_name =  $co_line->cust_name;
        };

        $co = new CustomerOrderItem();
        //$co = $co->select('coitems.co_num','customers.cust_name as cust_name')->where('coitems.whse_num', $whse_num)->whereIn('coitems.co_num', $arrGetConum)->leftJoin("customers", "customers.cust_num", "=", "coitems.cust_num")->get();

        $co = $co->select('coitems.co_num', 'coitems.cust_name as cust_name')->where('coitems.whse_num', $whse_num)->whereIn('coitems.co_num', $arrGetConum)->get();
        foreach ($co as $getCOdata) {
            $arrGetDataCustName[$getCOdata->co_num] = $getCOdata->cust_name;
        }

        foreach ($co_num as $co_line) {
            $co_line->cust_name =  $arrGetDataCustName[$co_line->co_num];
            $co_line->line_stage = 'Line Staged :' . $co_line->line_stage;
        }


        return $co_num;
    }

    public function getStagedCoLoc($whse_num, $co_num, $stage_num = "")
    {
        if ($this->is_base64_string($stage_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $co_num = base64_decode($co_num);
        }
        $list = new StageLoc();
        $co_num = $list->select('stage_num', DB::raw('count(*) as line_stage'))
            ->where('whse_num', $whse_num)
            ->where('co_num', $co_num)
            ->where('lpn_num', '=', NULL)
            ->where('stage_num', 'like', '%' . $stage_num . '%')
            ->groupby('stage_num')
            ->get();

        foreach ($co_num as $co_line) {
            $co_line->line_stage = 'Line Staged :' . $co_line->line_stage;
        };
        return $co_num;
    }

    public function getStagedCoLocPallet($whse_num, $co_num, $stage_num = "")
    {
        if ($this->is_base64_string($stage_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $co_num = base64_decode($co_num);
        }
        $list = new StageLoc();
        $co_num = $list->select('stage_num', DB::raw('count(*) as line_stage'))
            ->where('whse_num', $whse_num)
            ->where('co_num', $co_num)
            ->where('lpn_num', '!=', NULL)
            ->where('stage_num', 'like', '%' . $stage_num . '%')
            ->groupby('stage_num')
            ->get();

        foreach ($co_num as $co_line) {
            $co_line->line_stage = 'Line Staged :' . $co_line->line_stage;
        };
        return $co_num;
    }

    public function getJobSuffix($whse_num, $job_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new Job();
        $jobsuffix = $list
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->select('job_num')
            ->where('whse_num', $whse_num)->where('job_num', 'like', '%' . $job_num . '%')
            ->where('job_status', 'R')
            ->distinct()
            //                ->released()
            ->get();

        return $jobsuffix;
    }

    public function getJobQtyCompletedMoreThan0($whse_num = "", $item_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }

        $result = Job::whereHas('item', function ($q) {
            $q->where('item_status', 1);
        })
            ->select('job_num')
            ->where('job_status', 'R')
            ->distinct();
        // ->where('qty_completed','>',0);

        if ($whse_num) {
            $result->where('whse_num', $whse_num);
        }
        if ($item_num) {
            $result->where('item_num', $item_num);
        }

        $result = $result->get();
        return $result;
    }

    public function getJobAOCJobLabor($whse_num, $job_num = "")
    {
        $tparm = new TparmView;
        $jobroute = new JobRoute;
        $job = new Job;

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('JobLabour', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return job that has qty_bal more than 0
            $jobsuffix = $jobroute
                ->whereHas('workCenter', function ($q) {
                    $q->where('wc_status', 1);
                })
                ->whereHas('Job', function ($query) use ($whse_num) {
                    $query->where('job_status', 'R')
                        ->where('whse_num', $whse_num)
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('job_num')
                ->where('job_num', 'like', '%' . $job_num . '%')
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('job_num')
                ->get();
        }
        // Allow Over Complete is checked
        else {
            // Return all job for that warehouse
            $jobsuffix = $jobroute
                ->whereHas('workCenter', function ($q) {
                    $q->where('wc_status', 1);
                })
                ->whereHas('Job', function ($query) use ($whse_num) {
                    $query->where('job_status', 'R')
                        ->where('whse_num', $whse_num)
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('job_num')->where('job_num', 'like', '%' . $job_num . '%')
                ->groupby('job_num')
                ->get();
        }
        return $jobsuffix;
    }

    public function getJobAOCMachineRun($whse_num, $job_num = "")
    {
        $tparm = new TparmView;
        $jobroute = new JobRoute;
        $job = new Job;

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('MachineRun', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return job that has qty_bal more than 0
            $jobsuffix = $jobroute->whereHas('Job', function ($query) use ($whse_num) {
                $query->where('job_status', 'R')->where('whse_num', $whse_num);
            })
                ->select('job_num')
                ->where('job_num', 'like', '%' . $job_num . '%')->distinct()
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('job_num')
                ->get();
            return $jobsuffix;
        }
        // Allow Over Complete is checked
        else {
            // Return all job for that warehouse
            $jobsuffix = $job->select('job_num')->where('whse_num', $whse_num)->where('job_num', 'like', '%' . $job_num . '%')->distinct()->released()->get();
            return $jobsuffix;
        }
    }

    public function getJobAOCWIPMove($whse_num, $job_num = "")
    {
        $tparm = new TparmView;
        $jobroute = new JobRoute;
        $job = new Job;

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('WIPMove', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return job that has qty_bal more than 0
            $jobsuffix = $jobroute
                ->whereHas('Job', function ($query) use ($whse_num) {
                    $query->where('job_status', 'R')
                        ->where('whse_num', $whse_num)
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('job_num')
                ->where('job_num', 'like', '%' . $job_num . '%')->distinct()
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('job_num')
                ->get();
        }
        // Allow Over Complete is checked
        else {
            // Return all job for that warehouse
            $jobsuffix = $job
                ->select('job_num')
                ->where('whse_num', $whse_num)
                ->whereHas('item', function ($q) {
                    $q->where('item_status', 1);
                })
                ->where('job_num', 'like', '%' . $job_num . '%')->distinct()
                ->released()
                ->get();
        }
        return $jobsuffix;
    }

    public function getJobItem($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $items = new Job();
        $items = $items->select('item_num')->where('item_num', 'like', '%' . $item_num . '%')->get();
        return $items;
    }

    public function getJobOrder($from_job_num = "")
    {
        $job = Job::select('job_num')->where('job_num', 'like', '%' . $from_job_num . '%')->distinct()->get();
        return $job;
    }

    public function getJobNum1($job_num = "")
    {
        $job = Job::select('job_num')->where('job_num', 'like', '%' . $job_num . '%')->distinct()->get();
        return $job;
    }

    public function getJobNum($job_num = "", $suffix = "")
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
        }
        $list = new Job();
        $list = $list->select('job_num', 'suffix')->where('job_num', 'like', '%' . $job_num . '%')->where('suffix', 'like', '%' . $suffix . '%')->get();
        return $list;
    }

    public function getSuffix($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        } else {
            $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($job_num)));
        }
        $suffix = new Job();
        $suffix = $suffix->select('suffix')->where('job_num', $job_num)->orderBy('id', 'asc')->get();
        return $suffix;
    }

    public function getSuffixJob($suffix = "")
    {
        if ($this->is_base64_string($suffix)) {
            $suffix = base64_decode($suffix);
        }
        $s = new Job();
        $s = $s->select('suffix')->orderBy('suffix', 'asc')->distinct()->get();
        return $s;
    }

    public function getSuffixWithQtyReceivable($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $suffix = new Job();
        $suffix = $suffix->select('suffix', 'qty_receivable', 'uom')->where('job_num', $job_num)->orderBy('id', 'asc')->get();

        foreach ($suffix as $each) {
            $each['qty_receivable'] = numberFormatPrecision($each->qty_receivable, $unit_quantity_format, '.', '');
        }

        return $suffix;
    }

    public function getNonCompletedJobs($whse_num, $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = utf8_encode(base64_decode($whse_num));
            $job_num = utf8_encode(base64_decode($job_num));
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));
            $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($job_num)));
        }
        $list = new Job();
        $jobsuffix = $list->select('job_num')->distinct()->where('whse_num', $whse_num)->where('job_num', 'like', '%' . $job_num . '%')->where('job_status', '!=', 'C')->orderBy('job_num', 'asc')->get();
        return $jobsuffix;
    }

    public function getCompletedJobSuffix(Request $request, $whse_num = "", $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new Job();
        $jobsuffix = $list->select('job_num')->where('whse_num', 'like', '%' . $whse_num . '%')->where('job_num', 'like', '%' . $job_num . '%')->orderBy('job_num', 'asc')->get();
        return $jobsuffix;
    }

    public function getCompletedJobSuffixSorted(Request $request, $whse_num = "", $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new Job();
        $jobsuffix = $list->select('job_num')->where('whse_num', 'like', '%' . $whse_num . '%')->where('job_num', 'like', '%' . $job_num . '%')
            ->groupBy('job_num')->orderBy('job_num', 'asc')->get();
        return $jobsuffix;
    }
    public function getCompletedJobSuffixSorted_Without(Request $request)
    {



        $list = new Job();
        // dd($whse_num, $job_num);

        $jobsuffix = $list->select('job_num')->where('job_status', '!=', 'C')->groupBy('job_num')->orderBy('job_num', 'asc')->get();

        return $jobsuffix;
    }

    public function getCompletedJobSuffixSort(Request $request, $whse_num = "", $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new Job();
        $jobsuffix = $list->select('job_num')->where('whse_num', 'like', '%' . $whse_num . '%')->where('job_num', 'like', '%' . $job_num . '%')
            ->orderBy('job_num', 'asc')->distinct()->get();
        return $jobsuffix;
    }

    public function getCompletedJobOrderSuffix(Request $request, $whse_num = "", $job_num = "", $suffix = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
        }
        $list = new Job();
        $jobsuffix1 = $list->select('job_num', 'suffix')->where('whse_num', 'like', '%' . $whse_num . '%')->where('job_num', 'like', '%' . $job_num . '%')->where('suffix', 'like', '%' . $suffix . '%')
            ->orderBy('job_num', 'asc')->get();
        return $jobsuffix1;
    }

    public function getCompletedJobs($whse_num = "", $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new Job();
        $jobsuffix = $list->select('job_num')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('whse_num', 'like', '%' . $whse_num . '%')->where('job_num', 'like', '%' . $job_num . '%')
            ->where('qty_completed', '>', 'qty_scrapped')->where('qty_completed', '>', 0)->groupBy('job_num')->get();

        return $jobsuffix;
    }

    public function getCompletedPickListSuffixSorted(Request $request, $whse_num = "", $pick_num = "")
    {
        if ($this->is_base64_string($pick_num)) {
            $whse_num = base64_decode($whse_num);
            $pick_num = base64_decode($pick_num);
        }
        $list = new PicklistSummaryDetails();
        $picklistsuffix = $list->select('pick_num')
            ->where('whse_num', 'like', '%' . $whse_num . '%')
            ->where('pick_num', 'like', '%' . $pick_num . '%')
            ->orderBy('id', 'asc')->get();
        return $picklistsuffix;
    }

    public function getPickList($pick_num = "")
    {
        if ($this->is_base64_string($pick_num)) {
            $pick_num = base64_decode($pick_num);
        }
        $picklist = new PicklistSummaryDetails();
        $picklist = $picklist->select('pick_num')
            ->orderBy('pick_num', 'asc')->distinct()->get();
        return $picklist;
    }

    public function getPickListPacking($whse_num, $loc_num, $pick_num = "")
    {
        if ($this->is_base64_string($loc_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $pick_num = base64_decode($pick_num);
        }
        $picklist = new PicklistSummaryDetails();
        $picklist = $picklist->select('pick_num')
            ->where('group_by', 'Single Order')
            ->where('status', 'C')
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->orderBy('pick_num', 'asc')->distinct()->get();
        return $picklist;
    }

    public function getWhseLoc($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }

        // dd($whse_num, $loc_num);
        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name')->where('loc_type', 'S')->where('whse_num', $whse_num)->distinct()->active()->get();
        return $loc;
    }

    public function getWhseZone($whse_num, $zone_num = "")
    {
        if ($this->is_base64_string($zone_num)) {
            $whse_num = base64_decode($whse_num);
            $zone_num = base64_decode($zone_num);
        }
        $zone = Zone::select('zone_num')->where('whse_num', $whse_num)->distinct()->get();

        return $zone;
    }

    public function getWhseLocStock($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($loc_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
        }
        $loc = new Loc();
        $loc = $loc->select('loc_num')->where('whse_num', $whse_num)->where('loc_type', 'S')->distinct()->active()->get();
        return $loc;
    }

    public function getMatlJobSuffix($whse_num, $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new JobMatl();
        $jobsuffix = $list->whereHas('job', function ($query) {
            $query->where('job_status', 'R')
                ->whereHas('item', function ($q) {
                    $q->where('item_status', 1);
                });
        })->select('job_num')
            // ->where('whse_num', $whse_num)
            ->whereHas('job', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num);
            })
            ->where('job_num', 'like', '%' . $job_num . '%')
            ->where('qty_required', '>', 'qty_issued')->distinct()->get()->makeHidden(['whse_num','job']);
            // dd($jobsuffix);
        return $jobsuffix;
    }

    public function getJobMatlIssueOnly($whse_num, $job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $list = new JobMatl();
        $jobsuffix = $list->whereHas('job', function ($query) {
            $query->where('job_status', 'R');
        })
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->select('job_num')
            // ->where('whse_num', $whse_num)
            ->whereHas('job', function ($q) use ($whse_num) {
                $q->where('whse_num', $whse_num);
            })
            ->where('job_num', 'like', '%' . $job_num . '%')
            ->where('qty_required', '>', 'qty_issued')->where('qty_issued', '>', 0)->groupby('job_num')->get()->makeHidden(['whse_num', 'job']);
        return $jobsuffix;
    }

    public function displayJobMatlItem($whse_num, $job_num)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
        }
        $jobitem = new Job();
        $jobitem = $jobitem->with('item')->select('item_num', 'qty_released', 'qty_completed')->where('whse_num', $whse_num)->where('job_num', $job_num)->first();
        $jobitem['item_desc'] = $jobitem->item->item_desc;
        $jobitem['qty_required'] = numberFormatPrecision($jobitem->qty_required, $unit_quantity_format, '.', '');
        return $jobitem;
    }

    public function displayJobMatlItemSuffix($whse_num, $job_num, $suffix)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
        }

        $jobitem = new Job();
        $jobitem = $jobitem->with('item')->select('item_num', 'qty_released', 'qty_completed')->where('whse_num', $whse_num)->where('job_num', $job_num)->where('suffix', $suffix)->first();
        // dd($jobitem);
        // $jobitem['item_desc'] = $jobitem->item->item_desc;
        // $jobitem['qty_required'] = numberFormatPrecision($jobitem->qty_required, $unit_quantity_format, '.', '');

        $data = [];
        if ($jobitem) {
            $data['item_num'] = $jobitem->item_num;
            $data['item_desc'] = $jobitem->item->item_desc;
            $data['qty_required'] = numberFormatPrecision($jobitem->qty_required, $unit_quantity_format);
        }

        return $data;
    }

    public function displayTrnOrderDetails($trn_num, $trn_line)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($trn_num)) {
            $trn_num = base64_decode($trn_num);
            $trn_line = base64_decode($trn_line);
        }
        $list = new TransferLine();
        $trndetails = $list->with('item')->select('item_num', 'from_whse', 'to_whse', 'qty_required', 'qty_shipped', 'uom', 'site_id')->where('trn_num', $trn_num)->where('trn_line', $trn_line)->first();
        if ($trndetails) {
            $trndetails['item_desc'] = $trndetails->item->item_desc ?? null;
            $trndetails['lot_tracked'] = $trndetails->item->lot_tracked ?? null;
            $trndetails['to_uom'] = $trndetails->uom;
            $trndetails['qty_required'] = numberFormatPrecision($trndetails->qty_required - $trndetails->qty_shipped, $unit_quantity_format, '.', '');
            $trndetails['qty_required_conv'] = $trndetails['qty_required'];

            $defaults = $this->getLocByRankIssue($trndetails->from_whse, $trndetails->item_num);
            $trndetails['qty_on_hand'] = $defaults['qty_available'];
            $trndetails['qty_on_hand_conv'] = $defaults['qty_available'];

            $trndetails['qty_available'] = $defaults['qty_available'];
            $trndetails['qty_available_conv'] = $defaults['qty_available'];

            $trndetails['loc_num'] = $defaults['loc_num'];
            $trndetails['base_uom'] = $trndetails->item->uom ?? null;
        } else {
            $trndetails['item_desc'] = "";
            $trndetails['lot_tracked'] = "";
            $trndetails['to_uom'] = "";
            $trndetails['qty_required'] = "";
            $trndetails['qty_required_conv'] = "";
            $trndetails['qty_on_hand'] = "";
            $trndetails['qty_on_hand_conv'] = "";

            $trndetails['qty_available'] = "";
            $trndetails['qty_available_conv'] = "";

            $trndetails['loc_num'] = "";
            $trndetails['base_uom'] = "";
        }

        return $trndetails;
    }

    public function displayEmployeeName($emp_num)
    {
        if ($this->is_base64_string($emp_num)) {
            $emp_num = base64_decode($emp_num);
        }
        $employee = new Employee();
        $data = $employee->getEmployeeName($emp_num);

        $data = $data->toArray();

        $data['counter1_name'] = $data['emp_name'];
        $data['counter2_name'] = $data['emp_name'];

        return $data;
    }

    public function getMatlItem($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $list = new JobMatl();
        $jobsuffix = $list->select('matl_item')->where('matl_item', 'like', '%' . $item_num . '%')
            ->where('qty_required', '>', 0)->distinct()->get()->makeHidden(['whse_num', 'job']);
        return $jobsuffix;
    }

    public function getAllLot($lot_num = "")
    {
        if ($this->is_base64_string($lot_num)) {
            $lot_num = base64_decode($lot_num);
        }
        $lotloc = new LotLoc;
        $list = $lotloc->select('lot_num')
            ->groupby('lot_num')
            ->where('lot_num', 'like', '%' . $lot_num . '%')
            ->orderBy('lot_num', 'asc')
            ->get();

        $lots = Lot::select('lot_num')
            ->where('lot_num', 'like', '%' . $lot_num . '%')
            ->get();

        if (count($lots) > 0) {
            foreach ($lots as $lot) {
                // Only prepend lot_num that does not exist in table: lot_locs
                $checkLotNum = $lotloc->select('lot_num')->where('lot_num', $lot->lot_num)->exists();
                if (!$checkLotNum) {
                    $list->prepend($lot);
                }
            }
        }
        return $list;
    }

    public function getAllLots($whse_num, $item_num, $lot_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $lot_num = base64_decode($lot_num);
        }

        $lotloc = new LotLoc;

        $builder = $lotloc->select('lot_num');
        if ($whse_num != "null") {
            $builder->where('whse_num', $whse_num);
        }
        $builder
            ->where('lot_num', 'like', '%' . $lot_num . '%')
            ->where('item_num', $item_num)
            ->groupby('lot_num')
            ->orderBy('lot_num');

        $list = $builder->get();

        $lots = Lot::select('lot_num')->where('item_num', $item_num)->get();
        // if (count($lots) > 0) {
        //     foreach ($lots as $lot) {
        //         // Only prepend lot_num that does not exist in table: lot_locs
        //         $checkLotNum = $lotloc->select('lot_num')->where('item_num', $item_num)->where('lot_num', $lot->lot_num)->exists();
        //         if ($checkLotNum) {
        //             $list->prepend($lot);
        //         }
        //     }
        // }
        $list = $lots;
        $item_lot_num_def = Item::with('lot_number_definition')
            ->select('lot_num_def_name')
            ->where('item_num', $item_num)
            ->where('lot_num_def_name', '!=', null)
            ->first();

        // Item has lot number definition
        if (isset($item_lot_num_def->lot_number_definition)) {
            // Replace [[!year!]], [[!fullyear!]], [[!month!]], [[!day!]] to current year, fullyear, month, day
            $lot_num_def_segment = str_replace("[[!year!]]", date('y'), $item_lot_num_def->lot_number_definition->lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!fullyear!]]", date('Y'), $lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!month!]]", date('m'), $lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!day!]]", date('d'), $lot_num_def_segment);

            // Check if $item_lot_num_def exists in table: lot_locs
            $checkLotNum = $lotloc->select('lot_num')
                ->where('lot_num', 'like', $lot_num_def_segment . '%')
                ->where('item_num', $item_num)
                ->orderBy('lot_num', 'desc')
                ->first();

            // Set default number at the end of $lot_num_def_segment
            $number_of_zeroes = str_repeat(0, $item_lot_num_def->lot_number_definition->running_number_digits);


            $length_of_start_running_number = strlen($item_lot_num_def->lot_number_definition->start_running_number);
            $default_number = substr_replace($number_of_zeroes, $item_lot_num_def->lot_number_definition->start_running_number, -$length_of_start_running_number);
            $lot_num_def_segment = $lot_num_def_segment . '' . $default_number;


            // If $checkLotNum exists, increment last 2 digits of lot_num by 1 (Example: 01 to 02)
            if ($checkLotNum) {
                $numbers_in_variable = $this->countEndingDigits($checkLotNum->lot_num);

                if ($numbers_in_variable > 0) {
                    // Get characters before the last digits (Example: maziz01 will become maziz; maziz01-02 will become maziz01-)
                    $base_portion = substr($checkLotNum->lot_num, 0, -$numbers_in_variable);
                    // Get last digits after the characters (Example: maziz01 will become 01; maziz01-02 will become 02)
                    $digits_portion = substr($checkLotNum->lot_num, -$numbers_in_variable);

                    // If no characters at all (means everything is numeric), set the substring to be 5 (because the max increment is only till 99999 [5 digits])
                    if (!$base_portion) {
                        $base_portion = substr($checkLotNum->lot_num, 0, -5);
                        $digits_portion = substr($checkLotNum->lot_num, -5);
                    }

                    // If we get 2020-12-1001 (more than 2 digits number at the end), it will become 2020-12-1002
                    if ($numbers_in_variable > 2) {

                        // Max increment is 99999
                        if (strlen($checkLotNum->lot_num) > 5 && ($digits_portion >= 19999 && $digits_portion < 99999)) {
                            // Get 5 last digits from the end (Example: 19999) and plus it by 1
                            $digits_portion_increment = sprintf('%05d', intval(substr($digits_portion, -5)) + 1);

                            // Replace the 5 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -5);
                        } else if (strlen($checkLotNum->lot_num) > 4 && ($digits_portion >= 1999 && $digits_portion < 19999)) {
                            // Get 4 last digits from the end (Example: 1999) and plus it by 1 (This will only work for 1999-19999; because more than 19999 will become 110000)
                            $digits_portion_increment = sprintf('%04d', intval(substr($digits_portion, -4)) + 1);

                            // Replace the 4 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -4);
                        } else if (strlen($checkLotNum->lot_num) > 3 && ($digits_portion >= 199 && $digits_portion < 1999)) {
                            // Get 3 last digits from the end (Example: 199) and plus it by 1 (This will only work for 199-1999; because more than 1999 will become 11000)
                            $digits_portion_increment = sprintf('%03d', intval(substr($digits_portion, -3)) + 1);

                            // Replace the 3 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -3);
                        } else if (strlen($checkLotNum->lot_num) > 2 && ($digits_portion >= 01 && $digits_portion < 199)) {
                            // Get 2 last digits from the end (Example: 01) and plus it by 1 (This will only work for 01-199; because more than 199 will become 1100)
                            $digits_portion_increment = sprintf('%02d', intval(substr($digits_portion, -2)) + 1);

                            // Replace the 2 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -2);
                        }
                    }
                    // If we get testlot01 (only 2 digits number at the end), it will become testlot02
                    else {
                        //$digits_portion = sprintf('%02d', intval($digits_portion) + 1);
                        $digits_portion = intval($digits_portion) + 1;
                        // dd($digits_portion);
                    }
                    $lot_num_def_segment = $base_portion . $digits_portion;
                }
            }

            // Assign value $lotloc to $lot_num_def (turn it into an object)
            $lot_num_def = $lotloc;
            $lot_num_def->lot_num = $lot_num_def_segment;
            $lot_num_def->text = __('mobile.message.recommended_new_lot');

            $list->prepend($lot_num_def);
        }

        return $list;
    }







    public function getLot($whse_num, $item_num, $lot_num = "", $no_recomendation = false)
    { //GET LOT LIST
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "null")
                $item_num = base64_decode($item_num);
            if ($lot_num != "null")
                $lot_num = base64_decode($lot_num);
        }

        $lotloc = new LotLoc;

        $builder = $lotloc->select('lot_num');
        if ($whse_num != "null") {
            $builder->where('whse_num', $whse_num);
        }
        if ($item_num != "null") {
            $builder->where('item_num', $item_num);
        }
        $builder
            ->where('lot_num', 'like', '%' . $lot_num . '%')
            // ->where('item_num', $item_num)
            ->groupby('lot_num')
            ->orderBy('lot_num');

        $list = $builder->get();

        $lots = Lot::select('lot_num')->where('item_num', $item_num)->get();
        if (count($lots) > 0) {
            foreach ($lots as $lot) {
                // Only prepend lot_num that does not exist in table: lot_locs

                // Exclude checking for $no_recomendation = true (currently apply only for Add Item Lot Location)
                if (!$no_recomendation) {
                    $checkLotNum = $lotloc->select('lot_num')->where('item_num', $item_num)->where('lot_num', $lot->lot_num)->exists();
                } else {
                    $checkLotNum = false;
                }
                if (!$checkLotNum) {
                    $list->prepend($lot);
                }
            }
        }

        $item_lot_num_def = Item::with('lot_number_definition')
            ->select('lot_num_def_name')
            ->where('item_num', $item_num)
            ->where('lot_num_def_name', '!=', null)
            ->first();

        // Item has lot number definition
        if (isset($item_lot_num_def->lot_number_definition)) {
            // Replace [[!year!]], [[!fullyear!]], [[!month!]], [[!day!]] to current year, fullyear, month, day
            $lot_num_def_segment = str_replace("[[!year!]]", date('y'), $item_lot_num_def->lot_number_definition->lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!fullyear!]]", date('Y'), $lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!month!]]", date('m'), $lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!day!]]", date('d'), $lot_num_def_segment);

            // Check if $item_lot_num_def exists in table: lot_locs
            $checkLotNum = $lotloc->select('lot_num')
                ->where('lot_num', 'like', $lot_num_def_segment . '%')
                ->where('item_num', $item_num)
                ->orderBy('lot_num', 'desc')
                ->first();

            // Set default number at the end of $lot_num_def_segment
            $number_of_zeroes = str_repeat(0, $item_lot_num_def->lot_number_definition->running_number_digits);


            $length_of_start_running_number = strlen($item_lot_num_def->lot_number_definition->start_running_number);
            $default_number = substr_replace($number_of_zeroes, $item_lot_num_def->lot_number_definition->start_running_number, -$length_of_start_running_number);

            // Change Here #2157
            if ($item_lot_num_def->lot_number_definition->start_running_number == 0 && $item_lot_num_def->lot_number_definition->running_number_digits == 0) {
                $lot_num_def_segment = $lot_num_def_segment;
            } else {
                $lot_num_def_segment = $lot_num_def_segment . '' . $default_number;
            }

            // If $checkLotNum exists, increment last 2 digits of lot_num by 1 (Example: 01 to 02)
            if ($checkLotNum) {
                $numbers_in_variable = $this->countEndingDigits($checkLotNum->lot_num);

                if ($numbers_in_variable > 0) {
                    // Get characters before the last digits (Example: maziz01 will become maziz; maziz01-02 will become maziz01-)
                    $base_portion = substr($checkLotNum->lot_num, 0, -$numbers_in_variable);
                    // Get last digits after the characters (Example: maziz01 will become 01; maziz01-02 will become 02)
                    $digits_portion = substr($checkLotNum->lot_num, -$numbers_in_variable);

                    // If no characters at all (means everything is numeric), set the substring to be 5 (because the max increment is only till 99999 [5 digits])
                    if (!$base_portion) {
                        $base_portion = substr($checkLotNum->lot_num, 0, -5);
                        $digits_portion = substr($checkLotNum->lot_num, -5);
                    }

                    // If we get 2020-12-1001 (more than 2 digits number at the end), it will become 2020-12-1002
                    if ($numbers_in_variable > 2) {

                        // Max increment is 99999
                        if (strlen($checkLotNum->lot_num) > 5 && ($digits_portion >= 19999 && $digits_portion < 99999)) {
                            // Get 5 last digits from the end (Example: 19999) and plus it by 1
                            $digits_portion_increment = sprintf('%05d', intval(substr($digits_portion, -5)) + 1);

                            // Replace the 5 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -5);
                        } else if (strlen($checkLotNum->lot_num) > 4 && ($digits_portion >= 1999 && $digits_portion < 19999)) {
                            // Get 4 last digits from the end (Example: 1999) and plus it by 1 (This will only work for 1999-19999; because more than 19999 will become 110000)
                            $digits_portion_increment = sprintf('%04d', intval(substr($digits_portion, -4)) + 1);

                            // Replace the 4 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -4);
                        } else if (strlen($checkLotNum->lot_num) > 3 && ($digits_portion >= 199 && $digits_portion < 1999)) {
                            // Get 3 last digits from the end (Example: 199) and plus it by 1 (This will only work for 199-1999; because more than 1999 will become 11000)
                            $digits_portion_increment = sprintf('%03d', intval(substr($digits_portion, -3)) + 1);

                            // Replace the 3 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -3);
                        } else if (strlen($checkLotNum->lot_num) > 2 && ($digits_portion >= 01 && $digits_portion < 199)) {
                            // Get 2 last digits from the end (Example: 01) and plus it by 1 (This will only work for 01-199; because more than 199 will become 1100)
                            $digits_portion_increment = sprintf('%02d', intval(substr($digits_portion, -2)) + 1);

                            // Replace the 2 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -2);
                        }
                    }
                    // If we get testlot01 (only 2 digits number at the end), it will become testlot02
                    else {
                        //$digits_portion = sprintf('%02d', intval($digits_portion) + 1);
                        $digits_portion = intval($digits_portion) + 1;
                        // dd($digits_portion);
                    }
                    // Change Here #2157
                    if ($item_lot_num_def->lot_number_definition->start_running_number == 0 && $item_lot_num_def->lot_number_definition->running_number_digits == 0) {
                        $lot_num_def_segment = $base_portion;
                    } else {
                        $lot_num_def_segment = $base_portion . $digits_portion;
                    }
                }
            }

            // Assign value $lotloc to $lot_num_def (turn it into an object)
            // Don't add recommendation Lot if $no_recomendation = true (currently apply only for Add Item Lot Location)
            if (!$no_recomendation) {
                $lot_num_def = $lotloc;
                $lot_num_def->lot_num = $lot_num_def_segment;
                $lot_num_def->text = __('mobile.message.recommended_new_lot');
                $list->prepend($lot_num_def);
            }
        }

        return $list;
    }


    public function getLotpreassign($whse_num, $item_num, $type, $reference_no, $reference_line, $lot_num = "", $no_recomendation = false)
    { //GET LOT LIST
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "null")
                $item_num = base64_decode($item_num);
            if ($lot_num != "null")
                $lot_num = base64_decode($lot_num);
        }

        $lotloc = new LotLoc;

        $builder = $lotloc->select('lot_num');
        if ($whse_num != "null") {
            $builder->where('whse_num', $whse_num);
        }
        if ($item_num != "null") {
            $builder->where('item_num', $item_num);
        }
        $builder
            ->where('lot_num', 'like', '%' . $lot_num . '%')
            // ->where('item_num', $item_num)
            ->groupby('lot_num')
            ->orderBy('lot_num');

        $list = $builder->get();

        $lots = Lot::select('lot_num')->where('item_num', $item_num)->get();
        if (count($lots) > 0) {
            foreach ($lots as $lot) {
                // Only prepend lot_num that does not exist in table: lot_locs

                // Exclude checking for $no_recomendation = true (currently apply only for Add Item Lot Location)
                if (!$no_recomendation) {
                    $checkLotNum = $lotloc->select('lot_num')->where('item_num', $item_num)->where('lot_num', $lot->lot_num)->exists();
                } else {
                    $checkLotNum = false;
                }
                if (!$checkLotNum) {
                    $list->prepend($lot);
                }
            }
        }

        $item_lot_num_def = Item::with('lot_number_definition')
            ->select('lot_num_def_name')
            ->where('item_num', $item_num)
            ->where('lot_num_def_name', '!=', null)
            ->first();

        // Item has lot number definition
        if (isset($item_lot_num_def->lot_number_definition)) {
            // Replace [[!year!]], [[!fullyear!]], [[!month!]], [[!day!]] to current year, fullyear, month, day
            $lot_num_def_segment = str_replace("[[!year!]]", date('y'), $item_lot_num_def->lot_number_definition->lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!fullyear!]]", date('Y'), $lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!month!]]", date('m'), $lot_num_def_segment);
            $lot_num_def_segment = str_replace("[[!day!]]", date('d'), $lot_num_def_segment);

            // Check if $item_lot_num_def exists in table: lot_locs
            $checkLotNum = $lotloc->select('lot_num')
                ->where('lot_num', 'like', $lot_num_def_segment . '%')
                ->where('item_num', $item_num)
                ->orderBy('lot_num', 'desc')
                ->first();

            // Set default number at the end of $lot_num_def_segment
            $number_of_zeroes = str_repeat(0, $item_lot_num_def->lot_number_definition->running_number_digits);


            $length_of_start_running_number = strlen($item_lot_num_def->lot_number_definition->start_running_number);
            $default_number = substr_replace($number_of_zeroes, $item_lot_num_def->lot_number_definition->start_running_number, -$length_of_start_running_number);

            // Change Here #2157
            if ($item_lot_num_def->lot_number_definition->start_running_number == 0 && $item_lot_num_def->lot_number_definition->running_number_digits == 0) {
                $lot_num_def_segment = $lot_num_def_segment;
            } else {
                $lot_num_def_segment = $lot_num_def_segment . '' . $default_number;
            }

            // If $checkLotNum exists, increment last 2 digits of lot_num by 1 (Example: 01 to 02)
            if ($checkLotNum) {
                $numbers_in_variable = $this->countEndingDigits($checkLotNum->lot_num);

                if ($numbers_in_variable > 0) {
                    // Get characters before the last digits (Example: maziz01 will become maziz; maziz01-02 will become maziz01-)
                    $base_portion = substr($checkLotNum->lot_num, 0, -$numbers_in_variable);
                    // Get last digits after the characters (Example: maziz01 will become 01; maziz01-02 will become 02)
                    $digits_portion = substr($checkLotNum->lot_num, -$numbers_in_variable);

                    // If no characters at all (means everything is numeric), set the substring to be 5 (because the max increment is only till 99999 [5 digits])
                    if (!$base_portion) {
                        $base_portion = substr($checkLotNum->lot_num, 0, -5);
                        $digits_portion = substr($checkLotNum->lot_num, -5);
                    }

                    // If we get 2020-12-1001 (more than 2 digits number at the end), it will become 2020-12-1002
                    if ($numbers_in_variable > 2) {

                        // Max increment is 99999
                        if (strlen($checkLotNum->lot_num) > 5 && ($digits_portion >= 19999 && $digits_portion < 99999)) {
                            // Get 5 last digits from the end (Example: 19999) and plus it by 1
                            $digits_portion_increment = sprintf('%05d', intval(substr($digits_portion, -5)) + 1);

                            // Replace the 5 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -5);
                        } else if (strlen($checkLotNum->lot_num) > 4 && ($digits_portion >= 1999 && $digits_portion < 19999)) {
                            // Get 4 last digits from the end (Example: 1999) and plus it by 1 (This will only work for 1999-19999; because more than 19999 will become 110000)
                            $digits_portion_increment = sprintf('%04d', intval(substr($digits_portion, -4)) + 1);

                            // Replace the 4 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -4);
                        } else if (strlen($checkLotNum->lot_num) > 3 && ($digits_portion >= 199 && $digits_portion < 1999)) {
                            // Get 3 last digits from the end (Example: 199) and plus it by 1 (This will only work for 199-1999; because more than 1999 will become 11000)
                            $digits_portion_increment = sprintf('%03d', intval(substr($digits_portion, -3)) + 1);

                            // Replace the 3 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -3);
                        } else if (strlen($checkLotNum->lot_num) > 2 && ($digits_portion >= 01 && $digits_portion < 199)) {
                            // Get 2 last digits from the end (Example: 01) and plus it by 1 (This will only work for 01-199; because more than 199 will become 1100)
                            $digits_portion_increment = sprintf('%02d', intval(substr($digits_portion, -2)) + 1);

                            // Replace the 2 last digits that plus it by 1
                            $digits_portion = substr_replace($digits_portion, $digits_portion_increment, -2);
                        }
                    }
                    // If we get testlot01 (only 2 digits number at the end), it will become testlot02
                    else {
                        //$digits_portion = sprintf('%02d', intval($digits_portion) + 1);
                        $digits_portion = intval($digits_portion) + 1;
                        // dd($digits_portion);
                    }
                    // Change Here #2157
                    if ($item_lot_num_def->lot_number_definition->start_running_number == 0 && $item_lot_num_def->lot_number_definition->running_number_digits == 0) {
                        $lot_num_def_segment = $base_portion;
                    } else {
                        $lot_num_def_segment = $base_portion . $digits_portion;
                    }
                }
            }

            // Assign value $lotloc to $lot_num_def (turn it into an object)
            // Don't add recommendation Lot if $no_recomendation = true (currently apply only for Add Item Lot Location)
            if (!$no_recomendation) {
                $lot_num_def = $lotloc;
                $lot_num_def->lot_num = $lot_num_def_segment;
                $lot_num_def->text = __('mobile.message.recommended_new_lot');
                $list->prepend($lot_num_def);
            }
        }

        if (!empty($type) && !empty($reference_no) && !empty($reference_line) && !empty($item_num)) {
            $type = base64_decode($type);
            $reference_no = base64_decode($reference_no);
            $reference_line = base64_decode($reference_line);

            $preassign_lots = PreassignLots::select('lot_num')
                ->where('reference_no', $reference_no)
                ->where('reference_line', $reference_line)
                ->where('item_num', $item_num)
                ->where('trans_type', $type)
                ->where('site_id', auth()->user()->site_id)
                ->orderBy('lot_num', 'ASC')
                ->get();

            foreach ($preassign_lots as $key => $value) {
                if (!$list->contains('lot_num', $value->lot_num)) {
                    $list->prepend($value);
                }
            }

            $list = $list->sortBy('lot_num')->values()->all();
        }

        return $list;
    }

    // Count the no. of digits at the end of string
    public function countEndingDigits($string)
    {
        if ($this->is_base64_string($string)) {
            $trn_num = base64_decode($string);
        }
        $tailing_number_digits = 0;
        $i = 0;
        $from_end = -1;
        while ($i < strlen($string)) :
            if (is_numeric(substr($string, $from_end - $i, 1))) :
                $tailing_number_digits++;
            else :
                // End our while if we don't find a number anymore
                break;
            endif;
            $i++;
        endwhile;
        return $tailing_number_digits;
    }

    public function getLotNumAddOn($whse_num, $frmDB_loc, $check_qty_to_pick, $picklist_allocate_lot_num, $item_num, $loc_num = "", $sortField = "", $sortBy = "")
    { //GET LOT LIST
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $frmDB_loc = base64_decode($frmDB_loc);
            $check_qty_to_pick = base64_decode($check_qty_to_pick);
            $picklist_allocate_lot_num = base64_decode($picklist_allocate_lot_num);
        }
        if ($loc_num != "" && $loc_num != "null") {
            $loc_num = utf8_encode(base64_decode($loc_num));
            $loc_num = htmlspecialchars_decode($loc_num);
        }
        if ($sortField != "" && $sortField != "null") {
            $sortField = utf8_encode(base64_decode($sortField));
            $sortField = htmlspecialchars_decode($sortField);
        }
        if ($sortBy != "" && $sortBy != "null") {
            $sortBy = utf8_encode(base64_decode($sortBy));
            $sortBy = htmlspecialchars_decode($sortBy);
        }

        $list = LotLoc::select('lot_locs.lot_num', 'lot_locs.qty_available', 'lot_locs.loc_num', 'lots.expiry_date')
            ->leftJoin('lots', function ($join) {
                $join->on('lots.lot_num', '=', 'lot_locs.lot_num');
                $join->on('lots.item_num', '=', 'lot_locs.item_num');
                $join->on('lots.site_id', '=', 'lot_locs.site_id');
            })
            ->where('lot_locs.whse_num', $whse_num)
            ->where('lot_locs.item_num', $item_num)
            ->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('lot_locs.qty_available', '>', 0);

        if ($loc_num != "" && $loc_num != "null")
            $list = $list->where('lot_locs.loc_num', $loc_num);

        $list = $list->whereHas('location', function ($q) use ($whse_num) {
            if ($whse_num != "" && $whse_num != "null")
                $q->where('whse_num', $whse_num);
            $q->where('loc_status', 1)->where('loc_type', 'S')->where('pick_locs', 0);
        });

        if ($sortField != '' && $sortBy != '')
            $list = $list->orderBy($sortField, $sortBy);
        else
            $list = $list->orderBy('lot_num', 'asc');

        // if the sort field chosen is not by location, default the second sort to location
        if ($sortField != '' && $sortField != 'loc_num')
            $list = $list->orderBy('loc_num', 'asc');

        $list = $list->get();

        $arrItem = [];
        $index = 0;
        $find = ",";
        $replace = "";

        foreach ($list as $key) {
            if ($key->lot_num == @$picklist_allocate_lot_num) {
                $qtyAdd = $key->qty_available + $check_qty_to_pick;
                $arrItem[$index]['lot_num'] = $key->lot_num;
                $arrItem[$index]['qty_available'] = numberFormatPrecision($qtyAdd, $unit_quantity_format);
                $arrItem[$index]['loc_num'] = $key->loc_num;
                $arrItem[$index]['expiry_date'] = $key->expiry_date
                    ? getDateTimeConverted($key->expiry_date, false, true)
                    : '';
            } else {
                $qtyAdd = $key->qty_available;
                $arrItem[$index]['lot_num'] = $key->lot_num;
                $arrItem[$index]['qty_available'] = numberFormatPrecision($qtyAdd, $unit_quantity_format);
                $arrItem[$index]['loc_num'] = $key->loc_num;
                $arrItem[$index]['expiry_date'] = $key->expiry_date
                    ? getDateTimeConverted($key->expiry_date, false, true)
                    : '';
            }
            $index++;
        }

        return $arrItem;
        //return $list;
    }

    public function getLocNum($whse_num, $item_num)
    { //GET LOT LIST
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "null")
                $item_num = base64_decode($item_num);
        }
        $list = new ItemLoc();

        // dd($list,'hasifuhasui');
        $list = $list->select('loc_num', 'qty_available', 'uom');
        if ($whse_num != "null")
            $list = $list->where('whse_num', $whse_num);
        if ($whse_num != "null")
            $list = $list->where('item_num', $item_num);
        $list = $list->groupby('loc_num')->get();

        // dd($list);
        return $list;
    }

    public function getLotNum($whse_num = "", $loc_num = "", $item_num = "")
    { //GET LOT LIST
        // dd($whse_num, $loc_num, $item_num);
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "" && $whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "" && $item_num != "null")
                $item_num = base64_decode($item_num);
            if ($loc_num != "" && $loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "" && $whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($item_num != "" && $item_num != "null") {
                $item_num = utf8_encode(base64_decode($item_num));
                $item_num = htmlspecialchars_decode($item_num);
            }
            if ($loc_num != "" && $loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }
        // dd($loc_num, $item_num, $whse_num);
        $list = new LotLoc();

        $list = $list->select('lot_num', 'qty_available', 'uom');
        // dd($whse_num);
        if ($whse_num != "" && $whse_num != "null")
            $list = $list->where('whse_num', $whse_num);
        if ($loc_num != "" && $loc_num != "null")
            $list = $list->where('loc_num', $loc_num);
        if ($item_num != "" && $item_num != "null")
            $list = $list->where('item_num', $item_num);
        // Removed qty contained because lots were not showing on stock move and misc receipt
        // $list = $list->where('qty_contained', '=', 0)->groupby('lot_num')->get();
        $list = $list->groupby('lot_num')->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($list as $each) {
            $each['qty_available'] = numberFormatPrecision($each->qty_available, $unit_quantity_format);
        }

        return $list;
    }

    public function getLotLocExpiry($whse_num = "", $loc_num = "", $item_num = "", $sortField = "", $sortBy = "", $tparm_issue_location = 0, $form_name = "")
    { //GET LOT LIST

        $issue_loc_array = [];
        if ($tparm_issue_location > 0) {
            $issue_loc_list = $this->getIssueLocation($form_name, $tparm_issue_location, $whse_num, $item_num, $loc_num);

            // if no matching whse and loc is found, return empty array
            if (empty($issue_loc_list)) {
                return [];
            }

            $issue_loc_array = array_column($issue_loc_list, 'loc_num');
        }

        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "" && $whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "" && $item_num != "null")
                $item_num = base64_decode($item_num);
            if ($loc_num != "" && $loc_num != "null")
                $loc_num = base64_decode($loc_num);
            if ($sortField != "" && $sortField != "null")
                $sortField = base64_decode($sortField);
            if ($sortBy != "" && $sortBy != "null")
                $sortBy = base64_decode($sortBy);
        } else {
            if ($whse_num != "" && $whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($item_num != "" && $item_num != "null") {
                $item_num = utf8_encode(base64_decode($item_num));
                $item_num = htmlspecialchars_decode($item_num);
            }
            if ($loc_num != "" && $loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
            if ($sortField != "" && $sortField != "null") {
                $sortField = utf8_encode(base64_decode($sortField));
                $sortField = htmlspecialchars_decode($sortField);
            }
            if ($sortBy != "" && $sortBy != "null") {
                $sortBy = utf8_encode(base64_decode($sortBy));
                $sortBy = htmlspecialchars_decode($sortBy);
            }
        }

        $list = LotLoc::select('lot_locs.lot_num', 'lot_locs.qty_available', 'lot_locs.loc_num', 'lots.expiry_date')
            ->leftJoin('lots', function ($join) {
                $join->on('lots.lot_num', '=', 'lot_locs.lot_num');
                $join->on('lots.item_num', '=', 'lot_locs.item_num');
                $join->on('lots.site_id', '=', 'lot_locs.site_id');
            })
            ->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('lot_locs.qty_available', '>', 0);

        if ($whse_num != "" && $whse_num != "null")
            $list = $list->where('lot_locs.whse_num', $whse_num);
        if ($loc_num != "" && $loc_num != "null")
            $list = $list->where('lot_locs.loc_num', $loc_num);
        if ($item_num != "" && $item_num != "null")
            $list = $list->where('lot_locs.item_num', $item_num);

        $list = $list->whereHas('location', function ($q) use ($whse_num) {
            if ($whse_num != "" && $whse_num != "null")
                $q->where('whse_num', $whse_num);
            $q->where('loc_status', 1)->where('loc_type', 'S')->where('pick_locs', 0);
        });

        if (!empty($issue_loc_array)) {
            $list = $list->whereIn('lot_locs.loc_num', $issue_loc_array);
        }

        if ($sortField != '' && $sortBy != '')
            $list = $list->orderBy($sortField, $sortBy);
        else
            $list = $list->orderBy('lot_num', 'asc');

        // if the sort field chosen is not by location, default the second sort to location
        if ($sortField != '' && $sortField != 'loc_num')
            $list = $list->orderBy('loc_num', 'asc');

        $list = $list->get()->toArray();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($list as $key => $each) {
            $list[$key]['qty_available'] = numberFormatPrecision($each['qty_available'], $unit_quantity_format);
            $list[$key]['expiry_date'] = $each['expiry_date']
                ? getDateTimeConverted($each['expiry_date'], false, true)
                : '';
        }

        return $list;
    }

    public function getLotNumOpen($whse_num, $loc_num, $item_num)
    { //GET LOT LIST
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }
        $list = new Lot();
        $list = $list->select('lot_num')
            // ->where('whse_num', $whse_num)
            // ->where('loc_num', $loc_num)
            // ->groupby('lot_num')
            ->where('item_num', $item_num)
            ->orderBy('lot_num')
            ->get();
        return $list;
    }

    public function getIssuedLot($from_module, $whse_num, $ref_num, $ref_line, $ref_release = null)
    {
        if ($this->is_base64_string($from_module)) {
            $from_module = base64_decode($from_module);
            $whse_num = base64_decode($whse_num);
            $ref_num = base64_decode($ref_num);
            $ref_line = base64_decode($ref_line);

            if ($ref_release) {
                $ref_release = base64_decode($ref_release);
            }
        }

        $issued_lots = IssuedLot::select('lot_num', 'qty')
            ->where('from_module', $from_module)
            ->where('whse_num', $whse_num)
            ->where('ref_num', $ref_num)
            ->where('ref_line', $ref_line)
            ->where('ref_release', $ref_release)
            ->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($issued_lots as $each) {
            $each['qty'] = numberFormatPrecision($each->qty, $unit_quantity_format);
        }

        return $issued_lots;
    }

    public function getLotNumInv($whse_num, $loc_num, $item_num)
    { //GET LOT LIST for inv label
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }
        $list = new LotLoc();
        $list = $list->select('lot_num', 'qty_on_hand', 'uom')
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->groupby('lot_num')
            ->where('item_num', $item_num)
            ->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($list as $each) {
            $each['qty_on_hand'] = numberFormatPrecision($each->qty_on_hand, $unit_quantity_format);
        }

        return $list;
    }

    public function getLocLotQtyELNS()
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $item = Item::select('item_num', 'lot_tracked', 'uom', 'issue_by')
            ->where('item_num', request()->item_num)
            ->first();

        $item->whse_num = request()->whse_num;
        $lotloc = $this->getDefaultLocLotQtyELNS($item, $item, request()->disable_lot_number_selection);
        if ($lotloc) {
            $lotloc->qty_available = numberFormatPrecision($lotloc->qty_available, $unit_quantity_format, '.', '');
            $lotloc->qty_available_conv = numberFormatPrecision($lotloc->qty_available, $unit_quantity_format, '.', '');
        } else {
            $lotloc->qty_available = null;
            $lotloc->qty_available_conv = null;
        }
        return response()->json($lotloc);
    }


    public function getLocLotQtyELNSPL()
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $item = Item::select('item_num', 'lot_tracked', 'uom', 'issue_by')
            ->where('item_num', request()->item_num)
            ->first();

        $item->whse_num = request()->whse_num;
        $lotloc = $this->getDefaultLocLotQtyELNSPL($item, $item, request()->disable_lot_number_selection);
        if ($lotloc) {
            $lotloc->qty_available = numberFormatPrecision($lotloc->qty_available, $unit_quantity_format, '.', '');
            $lotloc->qty_available_conv = numberFormatPrecision($lotloc->qty_available, $unit_quantity_format, '.', '');
        } else {
            $lotloc->qty_available = null;
            $lotloc->qty_available_conv = null;
        }
        return response()->json($lotloc);
    }

    public function getTrnItem($trn_num, $whse_num, $item_num = "")
    {
        if ($this->is_base64_string($trn_num)) {
            $trn_num = base64_decode($trn_num);
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        $list = new TransferLine();
        $list = $list->select('item_num')->where('trn_num', $trn_num)->where('to_whse', $whse_num)->where('item_num', 'like', '%' . $item_num . '%')->distinct()->get();
        return $list;
    }

    public function getOper($job_num, $suffix, $oper_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            if ($suffix) {
                $suffix = base64_decode($suffix);
            }
        } else {
            $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($job_num)));
            $suffix = base64_decode($suffix);
        }
        // dd($job_num, $suffix);
        //        dd($suffix);
        $list = new JobRoute();
        $list = $list->select('oper_num')->where('job_num', $job_num);
        if ($suffix) {
            $list = $list->where('suffix', $suffix);
        }
        $list = $list->groupby('oper_num')->orderByRaw('CAST(oper_num as UNSIGNED) ASC')->active()->get();
        //        $list = $list->select('oper_num')->where('job_num', $job_num)->where('suffix', $suffix)->groupby('oper_num')->active()->get();
        return $list;
    }

    public function getOperMatlIssueOnly($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }
        $list = new JobMatl;
        $list = $list->select('oper_num')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('job_num', $job_num)
            ->where('qty_issued', '>', 0)
            ->groupby('oper_num')
            ->distinct()
            ->get()->makeHidden(['whse_num', 'job']);
        return $list;
    }

    public function getOperforLabour($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }
        $list = new JobRoute();
        // Get first route
        $firstRoute = $list->select('oper_num', 'qty_completed', 'oper_status')->where('job_num', $job_num)->orderBy('oper_num', 'asc')->first();
        // This is first Route?
        if ($firstRoute->qty_completed == 0 && $firstRoute->getFirstOper($job_num) == $firstRoute->oper_num) {
            // First job route qty_completed is 0, only allow to select first job route
            if ($firstRoute->oper_status == 1)
                return $list->select('oper_num')->where('job_num', $job_num)->where('oper_status', 1)->groupby('oper_num')->take(1)->get();
            else
                return '';
        } else {
            // First job route has qty_completed more than 0, allow to select all routes with qty_received > 0
            return $list->select('oper_num')->where('job_num', $job_num)->where('qty_received', '>', '0')->where('oper_status', 1)->groupby('oper_num')->get();
        }
    }

    public function getOperAOCJobLabor($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }
        $tparm = new TparmView;
        $jobroute = new JobRoute;

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('JobLabour', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return oper that has qty_bal more than 0
            $oper = $jobroute
                ->whereHas('workCenter', function ($q) {
                    $q->where('wc_status', 1);
                })
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('oper_status', 1)
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        // Allow Over Complete is checked
        else {
            // Return all oper for that job
            $oper = $jobroute
                ->whereHas('workCenter', function ($q) {
                    $q->where('wc_status', 1);
                })
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('oper_status', 1)
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        return $oper;
    }

    public function getOperAOCJobLaborSuffix($job_num, $suffix)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
        }
        $tparm = new TparmView;
        $jobroute = new JobRoute;

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('JobLabour', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return oper that has qty_bal more than 0
            $oper = $jobroute
                ->whereHas('workCenter', function ($q) {
                    $q->where('wc_status', 1);
                })
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('suffix', $suffix)
                ->where('oper_status', 1)
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        // Allow Over Complete is checked
        else {
            // Return all oper for that job
            $oper = $jobroute
                ->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('suffix', $suffix)
                ->where('oper_status', 1)
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        return $oper;
    }

    public function getOperAOCMachineRun($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }
        $tparm = new TparmView;
        $jobroute = new JobRoute;

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('MachineRun', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return oper that has qty_bal more than 0
            $oper = $jobroute->whereHas('Job', function ($query) {
                $query->where('job_status', 'R');
            })
                ->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('oper_status', 1)
                ->whereRaw('IFNULL(qty_received, 0) - IFNULL(qty_completed, 0) > 0')
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
            return $oper;
        }
        // Allow Over Complete is checked
        else {
            // Return all oper for that job
            $oper = $jobroute->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('oper_status', 1)->orderByRaw('CAST(oper_num as UNSIGNED) ASC')->get();
            return $oper;
        }
    }

    public function getOperAOCMachineRunSuffix($job_num, $suffix)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
        }
        $tparm = new TparmView;
        $jobroute = new JobRoute;

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('MachineRun', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return oper that has qty_bal more than 0
            $oper = $jobroute->whereHas('Job', function ($query) {
                $query->where('job_status', 'R');
            })
                ->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('suffix', $suffix)
                ->where('oper_status', 1)
                ->whereRaw('IFNULL(qty_received, 0) - IFNULL(qty_completed, 0) > 0')
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
            return $oper;
        }
        // Allow Over Complete is checked
        else {
            // Return all oper for that job
            $oper = $jobroute->select('oper_num', 'wc_num')
                ->where('job_num', $job_num)
                ->where('suffix', $suffix)
                ->where('oper_status', 1)->orderByRaw('CAST(oper_num as UNSIGNED) ASC')->get();
            return $oper;
        }
    }

    public function getOperAOCWIPMove($job_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }

        $tparm = new TparmView;
        $jobroute = new JobRoute;

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('WIPMove', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return oper that has qty_bal more than 0
            $oper = $jobroute
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num')
                ->where('job_num', $job_num)
                ->where('oper_status', 1)
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        // Allow Over Complete is checked
        else {
            // Return all oper for that job
            $oper = $jobroute
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num')
                ->where('job_num', $job_num)
                ->where('oper_status', 1)
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        return $oper;
    }

    public function getOperAOCWIPMoveSuffix($job_num, $suffix)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
        }

        $tparm = new TparmView;
        $jobroute = new JobRoute;

        // Get Allow Over Complete.
        $getAOCvalue = $tparm->getTparmValue('WIPMove', 'allow_over_complete');

        // Allow Over Complete is not checked
        if ($getAOCvalue == 0) {
            // Return oper that has qty_bal more than 0
            $oper = $jobroute
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num')
                ->where('job_num', $job_num)
                ->where('suffix', $suffix)
                ->where('oper_status', 1)
                ->whereRaw('IFNULL(qty_received,0) - IFNULL(qty_completed,0) > 0')
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        // Allow Over Complete is checked
        else {
            // Return all oper for that job
            $oper = $jobroute
                ->whereHas('Job', function ($query) {
                    $query->where('job_status', 'R')
                        ->whereHas('item', function ($q) {
                            $q->where('item_status', 1);
                        });
                })
                ->select('oper_num')
                ->where('job_num', $job_num)
                ->where('suffix', $suffix)
                ->where('oper_status', 1)
                ->groupby('oper_num')
                ->orderByRaw('CAST(oper_num as UNSIGNED) ASC')
                ->get();
        }
        return $oper;
    }

    public function getJobMatlItem($job_num, $oper_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $oper_num = base64_decode($oper_num);
        }
        $list = new JobMatl();
        $list = $list->select('matl_item', 'matl_desc')->where('job_num', $job_num)->where('oper_num', $oper_num)->distinct()
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->get()->makeHidden(['whse_num', 'job']);
        return $list;
    }

    public function getJobMatlItemSuffix($job_num, $suffix, $oper_num)
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
            $oper_num = base64_decode($oper_num);
        }
        $list = new JobMatl();
        $list = $list->select('matl_item', 'matl_desc')->where('job_num', $job_num)->where('suffix', $suffix)->where('oper_num', $oper_num)->distinct()
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->get()->makeHidden(['whse_num', 'job']);
        return $list;
    }

    public function checkReasonCodeMetaData(Request $request)
    {
        $id = 0;
        $reasoncode = $request->reason_code;
        @$id         = ReasonCode::select('id')->where('reason_num', $reasoncode)->where('reason_class', 'MiscReceipt')->value('id');
        if (@$id > 0) {
            // query MetaDataExts
            @$result = MetaDataExt::select('value')->where('model_id', $id)->where('model', 'ReasonCode')->value('value');
            if (@$result) {
                return @$result;
            } else {
                return "no exists";
            }
        } else {
            return "no exists";
        }
    }


    public function getReasonCode($reason_class, $reason_code = "")
    {


        if ($this->is_base64_string($reason_code)) {
            $reason_code = base64_decode($reason_code);
        }
        $list = new ReasonCode();
        // $list = $list->select('reason_num', 'reason_desc')->where('reason_class', $reason_class)->get();
        $list = $list->select('reason_num', 'reason_desc', 'reason_class')->where('reason_class', $reason_class)->orWhereRaw("REPLACE(reason_class, ' ', '') LIKE  '%" . $reason_class . "%'")->get();

        // if(config('icapt.client_prefix')=="OceanCash")
        // {
        //     $arrMashup = array();
        //     $arrTotal = array();
        //     foreach($list as $data){
        //         //$arrMashup[$data->id]['id'] = $data->id;
        //         $arrMashup[$data->id]['reason_desc'] = $data->reason_desc;
        //         $arrMashup[$data->id]['reason_num'] = $data->reason_num;
        //         //$arrMashup[$data->id]['reason_class'] = $data->reason_class;

        //        $resultMetaData =  MetaDataExt::where('model_id',$data->id)->where('model','ReasonCode')->first();
        //        if($resultMetaData!=null )
        //        {
        //             $arrRecord[$data->id] = $resultMetaData->value;
        //             $arrMashup[$data->id]['compulsary_doc'] = $resultMetaData->value;
        //        }
        //        else{
        //         $arrMashup[$data->id]['compulsary_doc'] = 'N';
        //        }

        //     }

        //    // dd($list,$arrRecord,json_encode($arrMashup));
        //   // echo "<pre>";
        //     return json_encode(array_values($arrMashup));
        // }


        return $list;
    }

    public function getVendDo()
    {
        $list = new PurchaseOrderItem();
        $list = $list->select('vend_do')->whereNotNull('vend_do')->orderBy('vend_do', 'asc')->distinct()->get();

        return $list;
    }

    public function getBomRevisionStatus(Request $request)
    {
        $result = [];
        // Convert string to number because bom_revision is a string
        $bom_builder = Bom::where('item_num', $request->item_num)
            ->orderBy(DB::raw('CONVERT(bom_revision,UNSIGNED)'), 'desc');

        // If in Edit page, don't check on its bom revision
        if ($request->edit_page == "true") {
            $bom_builder->where('bom_revision', '!=', $request->bom_revision);
        }

        $bom = $bom_builder->first();

        $result['bom_revision'] = $bom ? $bom->bom_revision + 1 : 1;
        $result['bom_status_exists'] = $bom_builder->where('bom_status', 'A')->exists();

        if (count($result) > 0) {
            return $result;
        }
    }

    public function checkBomItemRevision(Request $request)
    {
        $bom = Bom::where('item_num', $request->item_num)
            ->where('bom_revision', $request->bom_revision)
            ->exists();

        if ($bom) {
            return 'exists';
        }
    }

    public function getItemValidation($item_num, $malt_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }

        // $checkActive = new Bom();
        // $checkActive = $checkActive->where('item_num', $malt_num)->where('bom_status', 'A')->first();
        // if($checkActive){
        // }
    }

    public function getUOM($uom_num = "")
    {
        if ($this->is_base64_string($uom_num)) {
            $uom_num = base64_decode($uom_num);
        }
        $uom = new UOM();
        $uom = $uom->select('uom', 'uom_desc')->where('uom', 'like', '%' . $uom_num . '%')->orderBy('uom', 'ASC')->get();
        return $uom;
    }

    private function getItemUOMConvPriv($item_num)
    {
        $base_uom = Item::where('item_num', $item_num)->select('uom')->first();
        // $item_uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
        //     ->where(function ($q) use ($base_uom) {
        //         $q->where('uom_to', 'like', '%' . $base_uom->uom . '%')
        //             ->orWhere('uom_from', 'like', '%' . $base_uom->uom . '%');
        //     })
        //     ->where('conv_type', 'I')
        //     ->where('item_num', $item_num)
        //     ->get();
        // $uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
        //     ->where(function ($q) use ($base_uom) {
        //         $q->where('uom_to', 'like', '%' . $base_uom->uom . '%')
        //             ->orWhere('uom_from', 'like', '%' . $base_uom->uom . '%');
        //     })
        //     ->where('conv_type', 'G')
        //     ->get();

        $item_uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($base_uom) {
                $q->where('uom_to', $base_uom->uom)
                    ->orWhere('uom_from', $base_uom->uom);
            })
            ->where('conv_type', 'I')
            ->where('item_num', $item_num)
            ->get();
        $uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($base_uom) {
                $q->where('uom_to', $base_uom->uom)
                    ->orWhere('uom_from', $base_uom->uom);
            })
            ->where('conv_type', 'G')
            ->get();
        $item_uom_from = $item_uom_conv->pluck('uom_from')->all();
        $item_uom_to = $item_uom_conv->pluck('uom_to')->all();
        $uom_from = $uom_conv->pluck('uom_from')->all();
        $uom_to = $uom_conv->pluck('uom_to')->all();
        $uom = collect($base_uom)->concat($uom_to)->concat($uom_from)->concat($item_uom_from)->concat($item_uom_to)->unique();
        $uom_new = array();
        foreach ($uom as $key => $value) {
            array_push($uom_new, array("uom" => $value));
        }

        usort($uom_new, function ($item1, $item2) {
            return $item1['uom'] <=> $item2['uom'];
        });

        return $uom_new;
    }

    private function getAllUomwithBase($item_num, $base_uom)
    {
        // $base_uom = Item::where('item_num', $item_num)->select('uom')->first();
        $item_uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($base_uom) {
                $q->where('uom_from', $base_uom);
            })
            ->where('conv_type', 'I')
            ->where('item_num', $item_num)
            ->get();
        $uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($base_uom) {
                $q->where('uom_from',  $base_uom);
            })
            ->where('conv_type', 'G')
            ->get();
        $item_uom_from = $item_uom_conv->pluck('uom_from')->all();
        $item_uom_to = $item_uom_conv->pluck('uom_to')->all();
        $uom_from = $uom_conv->pluck('uom_from')->all();
        $uom_to = $uom_conv->pluck('uom_to')->all();
        $uom = collect($base_uom)->concat($uom_to)->concat($uom_from)->concat($item_uom_from)->concat($item_uom_to)->unique();
        $uom_new = array();
        foreach ($uom as $key => $value) {
            array_push($uom_new, array("uom" => $value));
        }

        usort($uom_new, function ($item1, $item2) {
            return $item1['uom'] <=> $item2['uom'];
        });

        return $uom_new;
    }

    private function getCompatibleUOMConvPriv($item_num, $uom)
    {
        $item_uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($uom) {
                $q->where('uom_to', 'like', '%' . $uom . '%')
                    ->orWhere('uom_from', 'like', '%' . $uom . '%');
            })
            ->where('conv_type', 'I')
            ->where('item_num', $item_num)
            ->get();
        $uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($q) use ($uom) {
                $q->where('uom_to', 'like', '%' . $uom . '%')
                    ->orWhere('uom_from', 'like', '%' . $uom . '%');
            })
            ->where('conv_type', 'G')
            ->get();
        $item_uom_from = $item_uom_conv->pluck('uom_from')->all();
        $item_uom_to = $item_uom_conv->pluck('uom_to')->all();
        $uom_from = $uom_conv->pluck('uom_from')->all();
        $uom_to = $uom_conv->pluck('uom_to')->all();
        $uom = collect($uom)->concat($uom_to)->concat($uom_from)->concat($item_uom_from)->concat($item_uom_to)->unique();
        $uom_new = array();
        foreach ($uom as $key => $value) {
            array_push($uom_new, array("uom" => $value));
        }
        return $uom_new;
    }

    public function getConvFactor($from_uom, $to_uom, $item_num)
    {
        if ($this->is_base64_string($from_uom)) {
            $from_uom = base64_decode($from_uom);
            $to_uom = base64_decode($to_uom);
            $item_num = base64_decode($item_num);
        }
    }

    public function getItemUOMConv($item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        return $this->getItemUOMConvPriv($item_num);
    }

    public function getAllUOMConv($item_num, $base_uom, $cust_num = "", $vend_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $base_uom = base64_decode($base_uom);
            $cust_num = base64_decode($cust_num);
            $vend_num = base64_decode($vend_num);
        }

        // dd($item_num, $base_uom, $cust_num, $vend_num);
        $base_uom = Item::where('item_num', $item_num)->value('uom');

        if ($cust_num == "null") {
            $cust_num = "";
        }

        if ($vend_num == "null") {
            $vend_num = "";
        }

        $list = new UomConv();
        $uom_cust = array();
        $uom_vend = array();
        if (!empty($cust_num)) {
            $uom_cust = $list->select('uom_to')->where('item_num', $item_num)->where('cust_num', $cust_num)->where('uom_from', $base_uom)->where('conv_type', 'C')->distinct()->pluck('uom_to')->all();
        }

        if (!empty($vend_num)) {
            $uom_vend = $list->select('uom_to')->where('item_num', $item_num)->where('vend_num', $vend_num)->where('uom_from', $base_uom)->where('conv_type', 'V')->distinct()->pluck('uom_to')->all();
        }

        // $uom_item = $list->select('uom_to')->where('item_num', $item_num)->where('uom_from', $base_uom)->where('conv_type', 'I')->distinct()->pluck('uom_to')->all();
        $uom_item_list = $list->where('item_num', $item_num)->where('conv_type', 'I')->distinct();
        $uom_item = collect($uom_item_list->pluck('uom_to')->all())->concat($uom_item_list->pluck('uom_from')->all());

        $uom_global = $list->select('uom_to')->where('conv_type', 'G')->where('uom_from', $base_uom)->distinct()->pluck('uom_to')->all();

        $uom = collect($base_uom)->concat($uom_item)->concat($uom_global);
        if (!empty($uom_cust)) {
            $uom = $uom->concat($uom_cust);
        }

        if (!empty($uom_vend)) {
            $uom = $uom->concat($uom_vend);
        }

        $uom = $uom->unique();

        $list = array();
        foreach ($uom as $key => $value) {
            array_push($list, array("uom" => $value));
        }

        return $list;
    }

    public function getPOUOMConv($item_num, $vend_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $vend_num = base64_decode($vend_num);
        }
        //If item is NON-INV, allow all
        if ($item_num == 'NON-INV') {
            return $this->getUOM("");
        }
        //Get UOM pairs for this Item and Vend Num
        $vend_uom = UomConv::where('item_num', $item_num)->where('vend_num', $vend_num)->get();
        //Get ItemUOM for this Item
        $item_uom = $this->getItemUOMConvPriv($item_num);
        $uom_new = array();

        if (!$vend_uom->isEmpty()) {
            $uom_from = $vend_uom->pluck('uom_from')->all();
            $uom_to = $vend_uom->pluck('uom_to')->all();
            $uom_new = collect($uom_to)->concat($uom_from)->unique()->toArray();  // get values in array
        }

        $item_uom = array_values(array_column($item_uom, 'uom')); // get values in array
        $uom = array_merge($uom_new, $item_uom); // merge both arrays
        natcasesort($uom); // sort values into asc order

        $uoms = [];
        foreach ($uom as $key => $value) {
            array_push($uoms, array("uom" => $value));
        }

        return array_values(array_map("unserialize", array_unique(array_map("serialize", $uoms))));
    }

    public function getCOUOMConv($item_num, $cust_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $cust_num = base64_decode($cust_num);
        }

        //If item is NON-INV, allow all
        if ($item_num == 'NON-INV') {
            return $this->getUOM("");
        }
        //Get UOM pairs for this Item and Cust Num
        $cust_uom = UomConv::where('item_num', $item_num)->where('cust_num', $cust_num)->get();
        //Get ItemUOM for this Item
        $item_uom = $this->getItemUOMConvPriv($item_num);
        $uom_new = array();
        if (!$cust_uom->isEmpty()) {
            $uom_from = $cust_uom->pluck('uom_from')->all();
            $uom_to = $cust_uom->pluck('uom_to')->all();
            $uom_new = collect($uom_to)->concat($uom_from)->unique()->toArray(); // get values in array
        }
        $item_uom = array_values(array_column($item_uom, 'uom')); // get values in array
        $uom = array_merge($uom_new, $item_uom); // merge both arrays
        natcasesort($uom); // sort values into asc order

        $uoms = [];
        foreach ($uom as $key => $value) {
            array_push($uoms, array("uom" => $value));
        }

        return array_values(array_map("unserialize", array_unique(array_map("serialize", $uoms))));
    }

    public function getCOUOMConvDiffUom($item_num, $cust_num, $base_uom)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $cust_num = base64_decode($cust_num);
            $base_uom = base64_decode($base_uom);
        }

        //Get UOM pairs for this Item and Cust Num
        $cust_uom = UomConv::where('item_num', $item_num)->where('cust_num', $cust_num)->get();
        //Get ItemUOM for this Item
        $item_uom = $this->getAllUomwithBase($item_num, $base_uom);
        $uom_new = array();
        if (!$cust_uom->isEmpty()) {
            $uom_from = $cust_uom->pluck('uom_from')->all();
            $uom_to = $cust_uom->pluck('uom_to')->all();
            $uom_new = collect($uom_to)->concat($uom_from)->unique()->toArray(); // get values in array
        }
        $item_uom = array_values(array_column($item_uom, 'uom')); // get values in array
        $uom = array_merge($uom_new, $item_uom); // merge both arrays
        natcasesort($uom); // sort values into asc order

        $uoms = [];
        foreach ($uom as $key => $value) {
            array_push($uoms, array("uom" => $value));
        }

        return array_values(array_map("unserialize", array_unique(array_map("serialize", $uoms))));
    }

    private function getTOUOMConvPriv($item_num, $trn_num, $trn_line)
    {
        //Get UOM pairs for this Item and Trn Line
        $to_uom = TransferLine::where('trn_num', $trn_num)->where('trn_line', $trn_line)->select('uom')->first();
        //Get ItemUOM for this Item
        $item_uom = $this->getItemUOMConvPriv($item_num);
        $uoms = array_merge(array($to_uom->toArray()), $item_uom);
        return array_values(array_map("unserialize", array_unique(array_map("serialize", $uoms))));
    }

    public function getTOUOMConv($item_num, $trn_num, $trn_line)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $trn_num = base64_decode($trn_num);
            $trn_line = base64_decode($trn_line);
        }
        return $this->getTOUOMConvPriv($item_num, $trn_num, $trn_line);
    }

    public function getTOLossUOMConv($item_num, $trn_num, $trn_line, $uom)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $trn_num = base64_decode($trn_num);
            $trn_line = base64_decode($trn_line);
            $uom = base64_decode($uom);
        }
        $trn_uom = $this->getTOUOMConvPriv($item_num, $trn_num, $trn_line);
        //Only select compatible ones.
        $compatible_uom = $this->getCompatibleUOMConvPriv($item_num, $uom);
        //Find common uoms
        $uoms = array_uintersect($trn_uom, $compatible_uom, function ($val1, $val2) {
            return strcmp($val1['uom'], $val2['uom']);
        });
        return array_values(array_map("unserialize", array_unique(array_map("serialize", $uoms))));
    }

    public function getRelatedUOM($item_num, $cust_num, $base_uom, $line_uom)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
            $cust_num = base64_decode($cust_num);
            $base_uom = base64_decode($base_uom);
            $line_uom = base64_decode($line_uom);
        }

        if ($item_num != 'NON-INV') {
            $item_uom = Item::where('item_num', $item_num)->select('uom')->first()->uom;
        } else {
            $item_uom = $line_uom;
            // $item_uom = CustomerOrderItem::where('item_num', $item_num);
        }

        $uom_conv = UomConv::select('uom_from', 'uom_to', 'conv_type')
            ->where(function ($query) use ($base_uom, $line_uom, $item_uom, $item_num) {
                $query->where('uom_from', $base_uom)
                    ->orWhere('uom_to', $base_uom)
                    ->orWhere('uom_from', $line_uom)
                    ->orWhere('uom_to', $line_uom)
                    ->orWhere('uom_from', $item_uom)
                    ->orWhere('uom_to', $item_uom);
            })
            ->where(function ($query) use ($item_num, $cust_num) {
                $query->where('conv_type', 'G')
                    ->orWhere(function ($q) use ($item_num) {
                        $q->where('conv_type', 'I')->where('item_num', $item_num);
                    })
                    ->orWhere(function ($q) use ($item_num, $cust_num) {
                        $q->where('conv_type', 'C')->where('item_num', $item_num)->where('cust_num', $cust_num);
                    });
            })
            ->get();

        $uom_conv_from = $uom_conv->pluck('uom_from');
        $uom_conv_to = $uom_conv->pluck('uom_to');
        $uom_conv = $uom_conv_from->concat($uom_conv_to)->unique();
        $uom_new = array();

        foreach ($uom_conv as $key => $value) {
            array_push($uom_new, array("uom" => $value));
        }

        usort($uom_new, function ($item1, $item2) {
            return $item1['uom'] <=> $item2['uom'];
        });

        return $uom_new;
    }

    public function getProdCode($prod_code = "")
    {
        if ($this->is_base64_string($prod_code)) {
            $prod_code = base64_decode($prod_code);
        } else {
            $prod_code = utf8_encode(htmlspecialchars_decode(base64_decode($prod_code)));
        }
        $product_code = new ProductCode();
        $product_code = $product_code->select('product_code', 'product_desc')->where('product_code', 'like', '%' . $prod_code . '%')->active()->orderBy('product_code', 'asc')->get();
        return $product_code;
    }

    public function getOrderNum($num = "")
    {

        if ($this->is_base64_string($num)) {
            $num = base64_decode($num);
        }

        $co = CustomerOrderItem::select(DB::raw('co_num as order_num'),)
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('co_num', 'like', '%' . $num . '%')
            ->distinct()->get();

        $job = Job::select(DB::raw('job_num as order_num'),)->where('job_num', 'like', '%' . $num . '%')->orderBy('job_num', 'asc')->get();

        $to = TransferOrder::select(DB::raw('trn_num as order_num'),)->where('trn_num', 'like', '%' . $num . '%')->orderBy('trn_num', 'asc')->get();

        $array = array_merge($co->toArray(), $job->toArray(), $to->toArray());

        return $array;
    }

    public function getOrderNumBasedOnType($check_order_type = "", $num = "")
    {

        if ($this->is_base64_string($check_order_type)) {
            $check_order_type = base64_decode($check_order_type);
        }
        if ($this->is_base64_string($num)) {
            $num = base64_decode($num);
        }

        $co = [];
        $job = [];
        $to = [];
        $datas = [];
        $result = [];

        if (str_contains($check_order_type, 'Customer Order')) {
            $co = CustomerOrderItem::select(DB::raw('co_num as order_num'))
                ->whereHas('item', function ($q) {
                    $q->where('item_status', 1);
                })
                ->where('co_num', 'like', '%' . $num . '%')
                ->where('rel_status', 'O')
                ->distinct()
                ->get()
                ->toArray();
        }

        if (str_contains($check_order_type, 'Job Order')) {
            $job = Job::select(DB::raw('job_num as order_num'))
                ->where('job_num', 'like', '%' . $num . '%')
                ->where('job_status', 'R')
                ->orderBy('job_num', 'asc')
                ->get()
                ->toArray();
        }

        if (str_contains($check_order_type, 'Transfer Order')) {
            $to = TransferOrder::select(DB::raw('trn_num as order_num'))
                ->where('trn_num', 'like', '%' . $num . '%')
                ->where('status', 'O')
                ->orderBy('trn_num', 'asc')
                ->get()
                ->toArray();
        }

        // Merge co, job, to
        $datas = array_merge($co, $job, $to);

        // To remove duplicate order_num
        foreach ($datas as $data) {
            // Check data inside allocation. If status != complete then dont show
            $check_allocation = Allocation::where('ref_num', $data['order_num'])->first();
            if ($check_allocation) {
                if ($check_allocation->status == 'Completed') {
                    $result[$data['order_num']] = [
                        'order_num' => $data['order_num']
                    ];
                }
            } else {
                $result[$data['order_num']] = [
                    'order_num' => $data['order_num']
                ];
            }
        }

        if (count($result) > 0) {
            // To reassign the array key back to ascending numbers
            $result = array_values($result);
        }

        return $result;
    }

    public function displayUOMDesc($uom)
    {
        if ($this->is_base64_string($uom)) {
            $uom = base64_decode($uom);
        } else {
            $uom = utf8_encode(htmlspecialchars_decode(base64_decode($uom)));
        }
        $data = new UOM();
        return $data->select('uom', 'uom_desc', 'rounding')->where('uom', $uom)->first();
    }

    public function displayProdCodeDesc($product_code)
    {
        if ($this->is_base64_string($product_code)) {
            $product_code = base64_decode($product_code);
        } else {
            $product_code = utf8_encode(htmlspecialchars_decode(base64_decode($product_code)));
        }
        $data = new ProductCode();
        return $data->select('product_code', 'product_desc')->where('product_code', $product_code)->first();
    }

    public function getAddress($cust_num = "")
    {
        if ($this->is_base64_string($cust_num)) {
            $cust_num = base64_decode($cust_num);
        }

        $address = new CustomerAddress();
        $address = $address->select('address1', 'address2', 'address3', 'city', 'state', 'postal_code', 'country')
            ->where('cust_num', 'like', '%' . $cust_num . '%')
            ->where('site_id', auth()->user()->site_id)
            ->orderBy('cust_num', 'asc')
            ->get();
        $test = array();
        $i = 0;
        foreach ($address as $add) {
            if ($add->city == null) {
                $city = '';
            } else {
                $city = $add->city . ',';
            }

            if ($add->state == null) {
                $state = '';
            } else {
                $state = $add->state . ',';
            }

            if ($add->postal_code == null) {
                $postal_code = '';
            } else {
                $postal_code = $add->postal_code . ',';
            }

            if ($add->country == null) {
                $country = '';
            } else {
                $country = $add->country;
            }

            $joinaddress = $add->address1 . ' ' . $add->address2 . ' ' . $add->address3 . ', ' . $city . ' ' . $state . ' ' . $postal_code . ' ' . $country;
            $add->fulladdress = $joinaddress;
            $test[$i]['fulladdress'] = $joinaddress;
            $i++;
        }
        $testencode = json_encode($test);
        return $testencode;
    }

    public function displayShippingZone($shipping_zone_code)
    {
        if ($this->is_base64_string($shipping_zone_code)) {
            $shipping_zone_code = base64_decode($shipping_zone_code);
        }
        $data = new ShippingZone();
        return $data->select('shipping_zone_code', 'shipping_zone_desc')->where('shipping_zone_code', $shipping_zone_code)->first();
    }

    public function getCustNum($cust_num = "")
    {
        if ($this->is_base64_string($cust_num)) {
            $cust_num = base64_decode($cust_num);
        }

        $cust = new Customer();
        $cust = $cust->select('cust_num', 'cust_name')
            //->where('cust_num', 'like', '%' . $cust_num . '%')
            ->orderBy('cust_num', 'asc')
            ->active()
            ->get();
        return $cust;
    }

    public function getCustNumPacking($whse_num, $loc_num, $cust_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $cust_num = base64_decode($cust_num);
        }

        $c = new PicklistSummaryDetails();
        $c = $c->select('id')
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->where('status', 'C')
            ->get();

        $cust = new PicklistTestItems();
        $cust = $cust->select('cust_num')
            ->whereIn('picklist_id', $c)
            ->distinct()
            ->get();

        return $cust;
    }

    public function getCustNumCO($cust_num = "")
    {
        if ($this->is_base64_string($cust_num)) {
            $cust_num = base64_decode($cust_num);
        }

        $cust = new CustomerOrderItem();
        $cust = $cust->select('cust_num')
            ->where('cust_num', 'like', '%' . $cust_num . '%')
            ->orderBy('cust_num', 'asc')
            ->groupBy('cust_num')
            //                ->active()
            ->get();
        return $cust;
    }
    public function getCustNumShippingZone($cust_num = "")
    {
        if ($this->is_base64_string($cust_num)) {
            $cust_num = base64_decode($cust_num);
        }

        $cust = new Customer();
        $cust = $cust->select('cust_num', 'cust_name', 'shipping_zone_code')->where('cust_num', 'like', '%' . $cust_num . '%')->orderBy('cust_num', 'asc')->active()->get();
        return $cust;
    }

    public function getCustByCo($co_num)
    {
        if ($this->is_base64_string($co_num)) {
            $co_num = base64_decode($co_num);
        }
        $cust = new CustomerOrderItem();
        $cust = $cust->select('cust_num')->where('co_num', $co_num)->first();
        return $cust;
    }

    public function getShippingZoneCode()
    {
        $shippingzone = new ShippingZone();
        $shippingzone = $shippingzone->select('shipping_zone_code', 'shipping_zone_desc')->orderBy('shipping_zone_code', 'asc')->active()->get();
        return $shippingzone;
    }

    public function getEmpNum($emp_num = "")
    {
        if ($this->is_base64_string($emp_num)) {
            $emp_num = base64_decode($emp_num);
        }
        $cust = new Employee();
        $cust = $cust->select('emp_num', 'emp_name')->orderBy('emp_num', 'asc')->active()->get();
        return $cust;
    }

    public function getJobRoute($job_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
        }
        $jobroute = new JobRoute();
        $jobroute = $jobroute->select('job_num')->where('job_num', 'like', '%' . $job_num . '%')->distinct()->get();

        return $jobroute;
    }

    public function getJobRouteOper($job_num, $oper_num = "")
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $oper_num = base64_decode($oper_num);
        }
        $jobroute = new JobRoute();
        $jobroute = $jobroute->select('oper_num')->where('job_num', $job_num)->where('oper_num', 'like', '%' . $oper_num . '%')->distinct()->get();

        return $jobroute;
    }

    public function getJobRouteOperSeq($job_num, $suffix, $oper_num, $sequence = "")
    {
        if ($this->is_base64_string($job_num)) {
            $job_num = base64_decode($job_num);
            $suffix = base64_decode($suffix);
            $oper_num = base64_decode($oper_num);
            $sequence = base64_decode($sequence);
        }
        $jobroute = new JobMatl();
        $jobroute = $jobroute->select('sequence', 'matl_item')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('job_num', $job_num)
            ->where('suffix', $suffix)
            ->where('oper_num', $oper_num)
            ->where('sequence', 'like', '%' . $sequence . '%')
            ->distinct()
            ->get()->makeHidden(['whse_num', 'job']);
        return $jobroute;
    }

    public function getMachine($res_id = '')
    {
        if ($this->is_base64_string($res_id)) {
            $res_id = base64_decode($res_id);
        }
        $machine = new Machine();
        $machine = $machine->select('res_id', 'res_desc')->where('res_id', 'like', '%' . $res_id . '%')->orderBy('res_id', 'asc')->active()->get();

        return $machine;
    }

    public function getIndirectTask($task_name = '')
    {
        if ($this->is_base64_string($task_name)) {
            $task_name = base64_decode($task_name);
        }
        $task = new Task();
        $task = $task->select('task_name', 'task_desc')->where('task_name', 'like', '%' . $task_name . '%')->get();

        return $task;
    }

    // when you add a new location, it will return 1 for new location.
    // if it exist it will return last rank + 1

    public function getnextRank($whse_num, $item_num, $loc_num)
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
        }
        $item = new ItemLoc();
        $item = $item->select('rank')->where('whse_num', $whse_num)->where('item_num', $item_num)->orderBy('rank', 'desc')->first();

        $loclist = new Loc();
        $loclist = $loclist->select('loc_type')->where('whse_num', $whse_num)->where('loc_num', $loc_num)->first();

        if ($item)
            return $item->rank + 1;
        else if ($loclist)
            if ($loclist->loc_type == 'T' || $loclist->loc_type == 'Q')
                return 2;
            else
                return $item->rank;
        else
            return 1;
    }

    public function getLocByRankIssuePL($whse_num, $item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        return $this->getLocByRankIssuePickingLocation($whse_num, $item_num);
    }




    public function getLocByRankIssueBlade($whse_num, $item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        return $this->getLocByRankIssue($whse_num, $item_num);
    }

    public function getLocByRankReceiptBlade($whse_num, $item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
        }
        return $this->getLocByRankReceipt($whse_num, $item_num);
    }

    public function displayQtyItemAvailable($whse_num, $item_num, $loc_num, $lot_num = null)
    {
        if ($lot_num == null) {
            $qty = $this->displayQuantity($item_num, $whse_num, $loc_num);
        } else {
            $qty = $this->displayLotQuantity($item_num, $whse_num, $loc_num, $lot_num);
        }
        return $qty;
    }

    public function displayQuantityAddOn($frmDB_loc, $check_qty_to_pick, $item_num, $whse_num, $loc_num, $uom_to = null, $cust_num = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $list = new ItemLoc();
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $item_num = base64_decode($item_num);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }

            if ($check_qty_to_pick) {
                $check_qty_to_pick = base64_decode($check_qty_to_pick);
            }

            if ($frmDB_loc) {
                $frmDB_loc = base64_decode($frmDB_loc);
            }
        }


        $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
            ->where('item_num', $item_num)
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->first();
        $find = ",";
        $replace = "";

        if ($frmDB_loc == $loc_num) {
            $qty['base_uom'] = $qty->uom;
            $qty['uom'] = $qty->uom;
            $qtyAdd = $qty->qty_available + $check_qty_to_pick;

            $qty['qty_available'] = numberFormatPrecision($qtyAdd, $unit_quantity_format, '.', '');
            $qty['qty_available_conv'] = str_replace($find, $replace, numberFormatPrecision($qtyAdd, $unit_quantity_format, '.', ''));
            $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');
            $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');

            $qty['frmDB_loc'] = $frmDB_loc;
            $qty['check_qty_to_pick'] = $check_qty_to_pick;

            if ($uom_to) {
                $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
                $qty['qty_available_conv'] = str_replace($find, $replace, numberFormatPrecision($uom_conv['qty'], $unit_quantity_format, '.', ''));
            }
        } else {

            $qty['base_uom'] = $qty->uom;
            $qty['uom'] = $qty->uom;
            $qty['qty_available'] = $qty->qty_available;
            $qty['qty_available_conv'] = str_replace($find, $replace, numberFormatPrecision($qty->qty_available, $unit_quantity_format, '.', ''));
            $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');
            $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');

            if ($uom_to) {
                $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
                $qty['qty_available_conv'] = str_replace($find, $replace, numberFormatPrecision($uom_conv['qty'], $unit_quantity_format, '.', ''));

                $qty['frmDB_loc'] = $frmDB_loc;
                $qty['check_qty_to_pick'] = $check_qty_to_pick;
            }
        }
        return $qty;
    }

    public function displayLotLocQuantity($item_num, $whse_num, $loc_num, $lot_num, $uom_to = null, $cust_num = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($lot_num) {
            $list = new LotLoc();
        } else {
            $list = new ItemLoc();
        }
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $item_num = base64_decode($item_num);
            $lot_num = base64_decode($lot_num);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }

        // dd($item_num, $whse_num, $loc_num,$lot_num);

        if ($lot_num) {
            $qty = $list->select('lot_num', 'qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
                ->where('item_num', $item_num)
                ->where('whse_num', $whse_num)
                ->where('loc_num', $loc_num)
                ->where('lot_num', $lot_num)
                ->first();
        } else {
            $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
                ->where('item_num', $item_num)
                ->where('whse_num', $whse_num)
                ->where('loc_num', $loc_num)
                ->first();
        }


        $qty['lot_num'] = @$qty->lot_num;
        $qty['base_uom'] = @$qty->uom;
        $qty['uom'] = @$qty->uom;
        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available;
        $qty['qty_available_uom'] = $qty->uom;

        $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);
        $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);


        // $qty['qty_on_hand'] = $qty->qty_on_hand;
        // $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

        if ($uom_to) {
            $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
            $qty['qty_available_conv'] = $uom_conv['qty'];
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
        $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);


        // $qty['qty_available'] = $qty->qty_available;
        // $qty['qty_available_conv'] = $qty->qty_available_conv;


        return $qty;
    }

    public function displayQuantity($item_num, $whse_num, $loc_num, $uom_to = null, $cust_num = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $list = new ItemLoc();
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $item_num = base64_decode($item_num);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        // dd($whse_num, $loc_num, $item_num);
        $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
            ->where('item_num', $item_num)
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->first();
        if (!$qty) {
            return false;
        }
        // dd($qty, $item_num, $whse_num, $loc_num);
        $qty['base_uom'] = $qty->uom;
        $qty['uom'] = $qty->uom;
        $qty['qty_available_uom'] = $qty->uom;
        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available;
        $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);
        $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);


        $qty['qty_on_hand'] = $qty->qty_on_hand;
        $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

        $qty['max_qty_input'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);

        $qty['max_qty_input'] = $qty->qty_on_hand;

        if ($uom_to) {
            $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
            $qty['qty_available_conv'] = $uom_conv['qty'];
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
        $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);

        // $qty['qty_available'] = $qty->qty_available;
        // $qty['qty_available_conv'] = $qty->qty_available_conv;

        return $qty;
    }

    public function displayQuantityCoPick($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $cust_num = null, $uom_to = null, $vend_num = null)
    {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $list = new ItemLoc();
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $item_num = base64_decode($item_num);
            $qty_required = base64_decode($qty_required);
            $qty_required_uom = base64_decode($qty_required_uom);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        // dd($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $uom_to, $cust_num, $vend_num);
        $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
            ->where('item_num', $item_num)
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->first();

        $qty['base_uom'] = $qty->uom;
        $qty['uom'] = $qty->uom;
        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available;
        $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);
        $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);

        // Convert qty required to base uom and check which one is smaller. item loc qty on hand or co qty required.
        $convertUom = UomConv::convertUOM($qty->uom, $qty_required_uom, $qty_required_uom, $qty_required, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
        $qtyReqConvert = $convertUom['conv_qty_to_base']['qty'];

        $smallestValue = min($qty->qty_available, $qtyReqConvert);

        if ($smallestValue == $qty->qty_available) {
            if ($uom_to == $qty->uom) {
                $qty['max_qty_input'] = $qty->qty_available;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $qty->uom, $uom_to, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            }
        } else if ($smallestValue == $qtyReqConvert) {
            if ($uom_to == $qty_required_uom) {
                $qty['max_qty_input'] = $qty_required;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $uom_to, $qty_required_uom, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty_required_uom;
            }
        }

        if ($uom_to) {
            $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
            $qty['qty_available_conv'] = $uom_conv['qty'];
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
        $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);

        return $qty;
    }


    public function displayQuantityPOReturn($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $cust_num = null, $uom_to = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_return = $tparm->getTparmValue('POReturn', 'allow_over_return');
        $list = new ItemLoc();
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $item_num = base64_decode($item_num);
            $qty_required = base64_decode($qty_required);
            $qty_required_uom = base64_decode($qty_required_uom);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        // dd($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $uom_to, $cust_num, $vend_num);
        $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
            ->where('item_num', $item_num)
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->first();

        $qty['base_uom'] = $qty->uom;
        $qty['uom'] = $qty->uom;
        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available;
        $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);
        $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);

        // Convert qty required to base uom and check which one is smaller. item loc qty on hand or co qty required.
        $convertUom = UomConv::convertUOM($qty->uom, $qty_required_uom, $qty_required_uom, $qty_required, $item_num, '', $vend_num, __('mobile.nav.po_return'));
        $qtyReqConvert = $convertUom['conv_qty_to_base']['qty'];

        if ($allow_over_return) {
            $smallestValue = max($qty->qty_available, $qtyReqConvert);
        } else {
            $smallestValue = min($qty->qty_available, $qtyReqConvert);
        }

        if ($smallestValue == $qty->qty_available) {
            if ($uom_to == $qty->uom) {
                $qty['max_qty_input'] = $qty->qty_available;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $qty->uom, $uom_to, $smallestValue, $item_num, '', $vend_num, __('mobile.nav.po_return'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            }
        } else if ($smallestValue == $qtyReqConvert) {
            if ($uom_to == $qty_required_uom) {
                $qty['max_qty_input'] = $qty_required;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $uom_to, $qty_required_uom, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty_required_uom;
            }
        }

        if ($uom_to) {
            $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
            $qty['qty_available_conv'] = $uom_conv['qty'];
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
        $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);

        return $qty;
    }
    public function displayQuantityCoPickReturn($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $cust_num = null, $uom_to = null, $vend_num = null)
    {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_over_return = $tparm->getTparmValue('POReturn', 'allow_over_return');
        $list = new ItemLoc();
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $item_num = base64_decode($item_num);
            $qty_required = base64_decode($qty_required);
            $qty_required_uom = base64_decode($qty_required_uom);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        // dd($item_num, $whse_num, $loc_num, $qty_required, $qty_required_uom, $uom_to, $cust_num, $vend_num);
        $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
            ->where('item_num', $item_num)
            ->where('whse_num', $whse_num)
            ->where('loc_num', $loc_num)
            ->first();

        $qty['base_uom'] = $qty->uom;
        $qty['uom'] = $qty->uom;
        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available;
        $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');
        $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format, '.', '');

        // Convert qty required to base uom and check which one is smaller. item loc qty on hand or co qty required.
        $convertUom = UomConv::convertUOM($qty->uom, $qty_required_uom, $qty_required_uom, $qty_required, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
        $qtyReqConvert = $convertUom['conv_qty_to_base']['qty'];

        if ($allow_over_return) {
            $smallestValue = max($qty->qty_available, $qtyReqConvert);
        } else {
            $smallestValue = min($qty->qty_available, $qtyReqConvert);
        }

        if ($smallestValue == $qty->qty_available) {
            if ($uom_to == $qty->uom) {
                $qty['max_qty_input'] = $qty->qty_available;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $qty->uom, $uom_to, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            }
        } else if ($smallestValue == $qtyReqConvert) {
            if ($uom_to == $qty_required_uom) {
                $qty['max_qty_input'] = $qty_required;
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty->uom;
            } else {
                $convertUom = UomConv::convertUOM($qty->uom, $uom_to, $qty_required_uom, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                $qty['qty_need_to_convert'] = $smallestValue;
                $qty['uom_need_to_convert'] = $qty_required_uom;
            }
        }

        if ($uom_to) {
            $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
            $qty['qty_available_conv'] = $uom_conv['qty'];
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        //$qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format, '.', '');
        //  $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format, '.', '');



        $qty['qty_available'] = $qty->qty_available;
        $qty['qty_available_conv'] = $qty->qty_available_conv;


        return $qty;
    }


    public function displayLotQuantity($item_num, $whse_num, $loc_num, $lot_num, $uom_to = null, $cust_num = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
            $item_num = base64_decode($item_num);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        $list = new LotLoc();
        $qty = $list->select('qty_available', 'qty_on_hand', 'qty_on_rsvd', 'uom')->where('item_num', $item_num)->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('lot_num', $lot_num)->first();

        if ($qty) {
            $qty['base_uom'] = $qty->uom;
            $qty['qty_available'] = $qty->qty_available;
            $qty['qty_available_conv'] = $qty->qty_available;
            $qty['qty_on_hand'] = $qty->qty_on_hand;
            $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

            if ($uom_to) {
                $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
                $qty['qty_available_conv'] = $uom_conv['qty'];
            }
        }

        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($qty) {
            $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
            $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);
            $qty['qty_on_hand'] = numberFormatPrecision($qty->qty_on_hand, $unit_quantity_format);
            $qty['qty_on_hand_conv'] = numberFormatPrecision($qty->qty_on_hand_conv, $unit_quantity_format);

            $qty['qty_available_uom'] = $qty->uom;
        }

        // $qty['qty_available'] = $qty->qty_available;
        // $qty['qty_available_conv'] = $qty->qty_available_conv;

        return $qty;
    }

    public function displayLotQuantityCoPick($item_num, $whse_num, $loc_num, $lot_num, $qty_required, $qty_required_uom, $cust_num = null, $uom_to = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
            $item_num = base64_decode($item_num);
            $qty_required = base64_decode($qty_required);
            $qty_required_uom = base64_decode($qty_required_uom);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        $list = new LotLoc();
        $qty = $list->select('qty_available', 'qty_on_hand', 'qty_on_rsvd', 'uom')->where('item_num', $item_num)->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('lot_num', $lot_num)->first();

        if ($qty) {
            $qty['base_uom'] = $qty->uom;
            $qty['qty_available'] = $qty->qty_available;
            $qty['qty_available_conv'] = $qty->qty_available;
            $qty['qty_on_hand'] = $qty->qty_on_hand;
            $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

            // Convert qty required to base uom and check which one is smaller. item loc qty on hand or co qty required.
            $convertUom = UomConv::convertUOM($qty->uom, $qty_required_uom, $qty_required_uom, $qty_required, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
            $qtyReqConvert = $convertUom['conv_qty_to_base']['qty'];

            $smallestValue = min($qty->qty_available, $qtyReqConvert);

            if ($smallestValue == $qty->qty_available) {
                if ($uom_to == $qty->uom) {
                    $qty['max_qty_input'] = $qty->qty_available;
                    $qty['qty_need_to_convert'] = $smallestValue;
                    $qty['uom_need_to_convert'] = $qty->uom;
                } else {
                    $convertUom = UomConv::convertUOM($qty->uom, $qty->uom, $uom_to, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                    $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                    $qty['qty_need_to_convert'] = $smallestValue;
                    $qty['uom_need_to_convert'] = $qty->uom;
                }
            } else if ($smallestValue == $qtyReqConvert) {
                if ($uom_to == $qty_required_uom) {
                    $qty['max_qty_input'] = $qty_required;
                    $qty['qty_need_to_convert'] = $smallestValue;
                    $qty['uom_need_to_convert'] = $qty->uom;
                } else {
                    $convertUom = UomConv::convertUOM($qty->uom, $uom_to, $qty_required_uom, $smallestValue, $item_num, $cust_num, '', __('mobile.nav.co_picking'));
                    $qty['max_qty_input'] = $convertUom['conv_qty_to_line']['qty'];
                    $qty['qty_need_to_convert'] = $smallestValue;
                    $qty['uom_need_to_convert'] = $qty_required_uom;
                }
            }


            if ($uom_to) {
                $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
                $qty['qty_available_conv'] = $uom_conv['qty'];
            }
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
        $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);


        // $qty['qty_available'] = $qty->qty_available;
        // $qty['qty_available_conv'] = $qty->qty_available_conv;

        return $qty;
    }

    public function displayLotQuantityJobIssue($item_num, $whse_num, $loc_num, $lot_num, $uom_to = null, $cust_num = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
            $item_num = base64_decode($item_num);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }
        $list = new LotLoc();
        $qty = $list->select('qty_available', 'qty_on_hand', 'qty_on_rsvd', 'uom')->where('item_num', $item_num)->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('lot_num', $lot_num)->first();

        if ($qty) {
            $qty['qty_available_uom'] = $qty->uom;
            $qty['qty_available'] = $qty->qty_available;
            $qty['qty_available_conv'] = $qty->qty_available;
            $qty['qty_on_hand'] = $qty->qty_on_hand;
            $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

            if ($uom_to) {
                $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
                $qty['qty_available_conv'] = $uom_conv['qty'];
            }

            $tparm = new TparmView;
            $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

            $qty['qty_available'] = numberFormatPrecision($qty->qty_available, $unit_quantity_format);
            $qty['qty_available_conv'] = numberFormatPrecision($qty->qty_available_conv, $unit_quantity_format);

            $qty['base_uom'] = $qty->uom;

            // $qty['qty_available'] = $qty->qty_available;
            // $qty['qty_available_conv'] = $qty->qty_available_conv;
        }

        return $qty;
    }

    /* public function displayQuantity($item_num, $whse_num, $loc_num, $uom_to=null, $cust_num=null, $vend_num=null){
      $tparm = new TparmView;
      $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
      $list = new ItemLoc();
      if ($this->is_base64_string($item_num)) {
      $whse_num = base64_decode($whse_num);
      $loc_num = base64_decode($loc_num);
      $item_num = base64_decode($item_num);
      if ($uom_to) {
      $uom_to = base64_decode($uom_to);
      }
      if ($cust_num) {
      $cust_num = base64_decode($cust_num);
      }
      if ($vend_num) {
      $vend_num = base64_decode($vend_num);
      }
      }

      $qty = $list->select('qty_on_hand', 'qty_on_rsvd', 'qty_available', 'uom')
      ->where('item_num', $item_num)
      ->where('whse_num', $whse_num)
      ->where('loc_num', $loc_num)
      ->first();

      $qty['base_uom'] = $qty->uom;
      $qty['uom'] = $qty->uom;
      $qty['qty_available'] = $qty->qty_available ;
      $qty['qty_available_conv'] = $qty->qty_available;
      $qty['qty_on_hand'] = number_format($qty->qty_on_hand, $unit_quantity_format,'.','');
      $qty['qty_on_hand_conv'] = number_format($qty->qty_on_hand, $unit_quantity_format,'.','');

      if ($uom_to) {
      $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
      $qty['qty_available_conv'] = $uom_conv['qty'];
      }

      return $qty;
      } */

    public function displayLotQuantityAddOnCheck($frmDB_loc, $check_qty_to_pick, $picklist_allocate_lot_num, $item_num, $whse_num, $loc_num, $lot_num, $uom_to = null, $cust_num = null, $vend_num = null)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($item_num)) {
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
            $item_num = base64_decode($item_num);
            if ($uom_to) {
                $uom_to = base64_decode($uom_to);
            }
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }

            if ($frmDB_loc) {
                $frmDB_loc = base64_decode($frmDB_loc);
            }

            if ($check_qty_to_pick) {
                $check_qty_to_pick = base64_decode($check_qty_to_pick);
            }

            if ($picklist_allocate_lot_num) {
                $picklist_allocate_lot_num = base64_decode($picklist_allocate_lot_num);
            }
        }
        $list = new LotLoc();
        $qty = $list->select('qty_available', 'qty_on_hand', 'qty_on_rsvd', 'uom')->where('item_num', $item_num)->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('lot_num', $lot_num)->first();
        //echo $picklist_allocate_lot_num."::".$lot_num."::".$loc_num."::".$frmDB_loc."::".$check_qty_to_pick;
        // exit;
        if ($picklist_allocate_lot_num == $lot_num && $loc_num == $frmDB_loc) {


            if ($qty) {
                $qty_avaible_add = $qty->qty_available + $check_qty_to_pick;
                $qty['base_uom'] = $qty->uom;
                $qty['qty_available'] = numberFormatPrecision($qty_avaible_add, $unit_quantity_format, '.', '');
                $qty['qty_available_conv'] = numberFormatPrecision($qty_avaible_add, $unit_quantity_format, '.', '');
                $qty['qty_on_hand'] = $qty->qty_on_hand;
                $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

                if ($uom_to) {
                    $uom_conv = UomConv::convert($qty->uom, $qty_avaible_add, $item_num, $cust_num, $vend_num, $uom_to);
                    $qty['qty_available_conv'] = $uom_conv['qty'];
                }
            }
        } else {

            if ($qty) {
                $qty['base_uom'] = $qty->uom;
                $qty['qty_available'] = $qty->qty_available;
                $qty['qty_available_conv'] = $qty->qty_available;
                $qty['qty_on_hand'] = $qty->qty_on_hand;
                $qty['qty_on_hand_conv'] = $qty->qty_on_hand;

                if ($uom_to) {
                    $uom_conv = UomConv::convert($qty->uom, $qty->qty_available, $item_num, $cust_num, $vend_num, $uom_to);
                    $qty['qty_available_conv'] = $uom_conv['qty'];
                }
            }
        }

        return $qty;
    }

    public function displayQuantityConverted($uom_from, $item_num, $qty, $uom_to, $cust_num = null, $vend_num = null, $type = '')
    {
        if ($this->is_base64_string($uom_from)) {
            $uom_from = base64_decode($uom_from);
            $item_num = base64_decode($item_num);
            $qty = base64_decode($qty);
            $uom_to = base64_decode($uom_to);
            $type = base64_decode($type);
            if ($cust_num) {
                $cust_num = base64_decode($cust_num);
            }
            if ($vend_num) {
                $vend_num = base64_decode($vend_num);
            }
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $uom_conv = UomConv::convert($uom_from, $qty, $item_num, $cust_num, $vend_num, $uom_to, $type);
        // dd($uom_conv,$uom_from, $item_num, $qty, $uom_to);
        $result['qty_available_conv'] = numberFormatPrecision($uom_conv['qty'], $unit_quantity_format) ?? 0;
        $result['uom_conv'] = $uom_conv['uom'];
        $result['conv_factor'] = $uom_conv['conv_factor'];
        return $result;
    }

    public function displayCOQuantityConverted($uom_from, $item_num, $qty, $uom_to, $co_num)
    {
        if ($this->is_base64_string($uom_from)) {
            $uom_from = base64_decode($uom_from);
            $item_num = base64_decode($item_num);
            $qty = base64_decode($qty);
            $uom_to = base64_decode($uom_to);
            $co_num = base64_decode($co_num);
        }
        $cust_num = CustomerOrderItem::where('co_num', $co_num)->pluck('co_num')->first();
        $uom_conv = UomConv::convert($uom_from, $qty, $item_num, $cust_num, null, $uom_to);
        $result['qty_available_conv'] = $uom_conv['qty'];
        $result['uom_conv'] = $uom_conv['uom'];
        return $result;
    }

    public function displayPOQuantityConverted($uom_from, $item_num, $qty, $uom_to, $vend_num)
    {
        if ($this->is_base64_string($uom_from)) {
            $uom_from = base64_decode($uom_from);
            $item_num = base64_decode($item_num);
            $qty = base64_decode($qty);
            $uom_to = base64_decode($uom_to);
            $vend_num = base64_decode($vend_num);
        }
        $uom_conv = UomConv::convert($uom_from, $qty, $item_num, $vend_num, $uom_to);
        $result['qty_on_hand_conv'] = $uom_conv['qty'];
        $result['uom_conv'] = $uom_conv['uom'];
        return $result;
    }

    public function displayItemNum($item_num)
    {
        if ($this->is_base64_string($item_num))
            $item_num = base64_decode($item_num);
        if ($item_num) {
            $itemnum = Item::select('item_num')->where('item_num', $item_num)->first();

            if ($itemnum) {
                $req_itemnum = $itemnum->item_num;
            } else {
                $req_itemnum = $item_num;
            }
        }
        $item = Item::select('item_desc', 'uom', 'lot_tracked', 'product_code', 'expiry_tracked', 'default_shelf_life')->where('item_num', $req_itemnum)->first();
        $item['base_uom'] = $item->uom;
        $item['uom_desc'] = $item->uom_detail->uom_desc ?? '';
        return $item;
    }

    // Display Item Description
    public function displayItemDesc($item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(base64_decode($item_num));
            $item_num = htmlspecialchars_decode($item_num);
        }

        if ($item_num) {
            $itemnum = Item::select('item_num')->where('item_num', $item_num)->first();

            if ($itemnum) {
                $req_itemnum = $itemnum->item_num;
            } else {
                $req_itemnum = $item_num;
            }
        }
        $item = Item::select('id as item_id', 'item_desc', 'uom', 'lot_tracked', 'product_code', 'expiry_tracked', 'default_shelf_life')->where('item_num', $req_itemnum)->first();
        $item['base_uom'] = $item->uom;
        $item['uom_desc'] = $item->uom_detail->uom_desc ?? '';

        return $item;
    }

    // Display Item Description (total qty on hand)
    public function displayItemDesc_QtyOnHand($whse_num, $item_num)
    {
        if ($this->is_base64_string($item_num))
            $item_num = base64_decode($item_num);
        $item = ItemLoc::select('item_num')->where('whse_num', $whse_num)
            ->where('qty_on_hand', '>', 0)
            ->where('item_num', $item_num)
            ->groupBy('item_num', 'whse_num')->sum('qty_on_hand')->first();
        $item['base_uom'] = $item->uom;
        return $item;
    }

    public function displayJobItem(Request $request, $job_num)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($this->is_base64_string($job_num))
            $job_num = base64_decode($job_num);
        if (isset($request->suffix)) {
            $job = Job::where('job_num', $job_num)->where('suffix', $request->suffix)->first();
        } else {
            $job = Job::where('job_num', $job_num)->first();
        }
        //        dd($job);
        $item = Item::select('lot_tracked', 'expiry_tracked', 'default_shelf_life', 'item_desc')
            ->where('item_num', $job->item_num)
            ->first();
        $find = ",";
        $replace = "";

        // dd($job);

        $merge = array_merge($job->toArray(), $item->toArray());
        $merge['qty_available'] = $job->qty_available;
        $merge['u_m2'] = $job->uom ?? null;
        $merge['qty_completed'] = numberFormatPrecision($job->qty_completed, $unit_quantity_format);
        $merge['qty_released'] = str_replace($find, $replace, numberFormatPrecision($job->qty_released, $unit_quantity_format));
        $merge['qty_receivable'] = str_replace($find, $replace, numberFormatPrecision($job->qty_receivable, $unit_quantity_format));
        $merge['qty_available'] = str_replace($find, $replace, $job->qty_available);
        $merge['u_m2'] = $job->uom ?? null;
        $merge['qty_required'] = str_replace($find, $replace, numberFormatPrecision($job->qty_released, 2));

        $merge['job_oper_qty_diff'] = str_replace($find, $replace, numberFormatPrecision($job->qty_released - $job->qty_completed + $job->qty_returned, $unit_quantity_format));

        $merge['job_max_qty_to_receive'] = $job->qty_released;
        $merge['last_oper_qty_diff'] = 0;

        if (isset($request->suffix)) {
            $job_route_last_oper = $job->job_routes()->where('suffix', $request->suffix)->orderBy('oper_num', 'desc')->first();
        } else {
            $job_route_last_oper = $job->job_routes()->orderBy('oper_num', 'desc')->first();
        }
        if ($job_route_last_oper) {
            $merge['last_oper_qty_diff'] = str_replace($find, $replace, numberFormatPrecision($job_route_last_oper->qty_completed - $job_route_last_oper->qty_moved, $unit_quantity_format));
            $merge['job_route_last_oper'] = $job_route_last_oper;
            $merge['job'] = $job;

            $merge['job_max_qty_to_receive'] = str_replace($find, $replace, numberFormatPrecision($job_route_last_oper->qty_completed - $job->qty_completed, $unit_quantity_format));
        }

        return $merge;
    }

    public function is_base64_string($s)
    {
        //Regex!
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s) || (strlen($s) % 4 != 0))
            return false;

        // first check if we're dealing with an actual valid base64 encoded string
        if (($b = base64_decode($s, TRUE)) === FALSE) {
            return FALSE;
        }

        // now check whether the decoded data could be actual text
        $e = mb_detect_encoding($b);
        if (in_array($e, array('UTF-8', 'ASCII'))) { // YMMV
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function displayLineStaged($whse_num, $stage_num, $co_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $co_num = base64_decode($co_num);
        }
        // substitute with slash
        $data = new StageLoc();
        $data = $data->select('co_num', DB::raw('count(*) as line_stage'))
            ->where('whse_num', $whse_num)
            ->where('stage_num', $stage_num)
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->groupBy('co_num')
            ->first();
        return $data;
    }

    public function displayLineStagedwithshippingzonecode($whse_num, $stage_num, $co_num = "", $shipping_zone_code = null, $salesperson = null)
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $co_num = base64_decode($co_num);
            $shipping_zone_code = base64_decode($shipping_zone_code);
            $salesperson = base64_decode($salesperson);
        }
        // substitute with slash
        $data = new StageLoc();
        $data = $data->select('co_num', DB::raw('count(*) as line_stage'))
            ->whereHas('customer_order', function ($q) use ($co_num, $shipping_zone_code, $salesperson) {
                $q->where('co_num', $co_num);
                // if ($shipping_zone_code) {
                //     $q->where('shipping_zone_code', $shipping_zone_code);
                // }
                // if ($salesperson) {
                //     $q->where('strsales_person', $salesperson);
                // }
            })
            ->where('whse_num', $whse_num)
            ->where('stage_num', $stage_num)
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->groupBy('co_num')
            ->first();
        return $data;
    }

    public function displayLineStagedwithsalesperson($whse_num, $stage_num, $co_num = "", $salesperson = null)
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $co_num = base64_decode($co_num);

            $salesperson = base64_decode($salesperson);
        }
        // substitute with slash
        $data = new StageLoc();
        $data = $data->select('co_num', DB::raw('count(*) as line_stage'))
            ->whereHas('customer_order', function ($q) use ($co_num, $salesperson) {
                $q->where('co_num', $co_num);

                // if ($salesperson) {
                //     $q->where('strsales_person', $salesperson);
                // }
            })
            ->where('whse_num', $whse_num)
            ->where('stage_num', $stage_num)
            ->where('co_num', 'like', '%' . $co_num . '%')
            ->groupBy('co_num')
            ->first();
        return $data;
    }





    public function displayLineStagedByCustNum($whse_num, $stage_num, $cust_num = "", $shipping_zone_code = null, $salesperson = null)
    {

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $cust_num = base64_decode($cust_num);
            $shipping_zone_code = base64_decode($shipping_zone_code);
            $salesperson = base64_decode($salesperson);
        }

        $data = StageLoc::select('co_num', DB::raw('count(*) as line_stage'))
            ->whereHas('customer_order', function ($q) use ($cust_num, $shipping_zone_code, $salesperson) {
                $q->where('cust_num', $cust_num);
                // if ($shipping_zone_code) {
                //     $q->where('shipping_zone_code', $shipping_zone_code);
                // }
                // if ($salesperson) {
                //     $q->where('strsales_person', $salesperson);
                // }
            })
            ->where('whse_num', $whse_num)
            ->where('stage_num', $stage_num)
            ->first();

        return $data;
    }

    public function displayLineStagedByCustNumnoshipping($whse_num, $stage_num, $cust_num = "", $salesperson = null)
    {

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $stage_num = base64_decode($stage_num);
            $cust_num = base64_decode($cust_num);

            $salesperson = base64_decode($salesperson);
        }

        $data = StageLoc::select('co_num', DB::raw('count(*) as line_stage'))
            ->whereHas('customer_order', function ($q) use ($cust_num, $salesperson) {
                $q->where('cust_num', $cust_num);

                // if ($salesperson) {
                //     $q->where('strsales_person', $salesperson);
                // }
            })
            ->where('whse_num', $whse_num)
            ->where('stage_num', $stage_num)
            ->first();

        return $data;
    }

    // ==================================================
    // USE FOR AJAX
    // ==================================================
    public function getJobData(Request $request)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $job = Job::with(['job_routes' => function ($q) {
            $q->orderBy('oper_num', 'desc');
        }])
            ->where('job_num', $request->job_num)
            ->first();
        $job['qty_returnable'] = numberFormatPrecision($job->qty_returnable, $unit_quantity_format, '.', '');
        return $job;
    }

    public function getSuffixData(Request $request)
    {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $job = Job::with(['job_routes' => function ($q) use ($request) {
            $q->orderBy('oper_num', 'desc')
                ->where('suffix', $request->suffix);
        }])
            ->where('job_num', $request->job_num)
            ->where('suffix', $request->suffix)
            ->first();

        // $job = Job::with(['job_routes' => function ($q) use ($request) {
        //     $q->orderBy('oper_num', 'desc')
        //         ->where('suffix', '0000');
        // }])
        //     ->where('job_num', 'Job0001TestUDF')
        //     ->where('suffix', '0000')
        //     ->first();

        $getStdAct = MetaDataExt::select('value')->where('model_id', $job->id)->where('model', 'Job')->where('status', 1)->value('value');
        if (@$getStdAct) {
            $job['std_actual'] = $getStdAct;
        }



        $job['qty_returnable'] = numberFormatPrecision($job->qty_returnable, $unit_quantity_format, '.', '');






        return $job;
    }

    public function getItemLocOrLotLoc(Request $request)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($request->lot_num) {
            $result = LotLoc::where('whse_num', $request->whse_num)
                ->where('loc_num', $request->loc_num)
                ->where('item_num', $request->item_num)
                ->where('lot_num', $request->lot_num)
                ->first();
        } else {
            $result = ItemLoc::where('whse_num', $request->whse_num)
                ->where('loc_num', $request->loc_num)
                ->where('item_num', $request->item_num)
                ->first();
        }
        $result['qty_available'] = numberFormatPrecision($result->qty_available, $unit_quantity_format, '.', '');
        return $result;
    }

    public function getEmployee(Request $request)
    {
        $employee = Employee::where('emp_num', $request->emp_num)->first();
        return response()->json($employee);
    }

    public function getVendLot(Request $request)
    {
        return Lot::where('lot_num', $request->lot_num)->value('vend_lot');
    }

    public static function getDefaultOperNum(Request $request)
    {
        // Get latest oper_num and plus it by 10
        $default_oper_num = JobRoute::with('job')
            ->whereHas('job', function ($q) use ($request) {
                $q->where('whse_num', $request->whse_num);
            })
            ->where('job_num', $request->job_num)
            ->orderBy('oper_num', 'desc')
            ->value('oper_num') + 10;

        return $default_oper_num;
    }

    public static function getDefaultOperNumNew(Request $request)
    {
        if ($this->is_base64_string($request->whse_num)) {
            $whse_num = base64_decode($request->whse_num);
            $job_num = base64_decode($request->job_num);
        } else {
            $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->whse_num)));
            $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->job_num)));
        }
        // Get latest oper_num and plus it by 10
        $default_oper_num = JobRoute::with('job')
            ->whereHas('job', function ($q) use ($request) {
                $q->where('whse_num', $whse_num);
            })
            ->where('job_num', $job_num)
            ->where('suffix', $request->suffix)
            ->orderBy('oper_num', 'desc')
            ->value('oper_num') + 10;

        return $default_oper_num;
    }

    public function checkIssuedLot(Request $request)
    {
        $exists = IssuedLot::where('from_module', $request->from_module)
            ->where('whse_num', $request->whse_num)
            ->where('ref_num', $request->ref_num)
            ->where('ref_line', $request->ref_line)
            ->where('ref_release', $request->ref_release)
            ->where('lot_num', $request->lot_num)
            ->exists();

        return response()->json($exists);
    }

    public function getIssuedLotQty(Request $request)
    {
        $issued_lot = IssuedLot::where('from_module', $request->from_module)
            ->where('whse_num', $request->whse_num)
            ->where('ref_num', $request->ref_num)
            ->where('ref_line', $request->ref_line)
            ->where('ref_release', $request->ref_release)
            ->where('lot_num', $request->lot_num)
            ->first();

        if ($issued_lot) {
            return response()->json($issued_lot->qty);
        }
    }

    public function convertAlternateBarcodetoItem(Request $request)
    {
        $alternate_barcode = AlternateBarcode::where('alternate_barcode', $request->alternate_barcode)->first();
        return response()->json($alternate_barcode);
    }

    public function getItemWithUOMAndParent($item_num = "")
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }

        $parents = "";
        $acentor = "";

        $data['ancestor'] = DB::select(DB::raw("with recursive cte as ( select item_num, parent_id, 1 lvl from bom_matls where item_num = '$item_num' union all select c.item_num, t.parent_id, lvl + 1 from cte c inner join bom_matls t on t.item_num = c.parent_id ) select item_num, group_concat(parent_id order by lvl) all_parents from cte group by item_num"));
        $dataA = array();
        foreach ($data['ancestor'] as $ancestor => $key) {
            $parents = $key->all_parents;
            $acentor = $key->item_num;
        }
        $arrSplit = explode(',', $parents);
        foreach ($arrSplit as $key => $value) {
            $dataA[$value] = $value;
        }
        $dataA[$acentor] = $acentor;
        $data['item'] = Item::select('item_num', 'item_desc', 'uom')->active()->orderBy('item_num', 'asc')->whereNotIn('item_num', $dataA)->get();

        return $data['item'];
    }

    public function itemv3(Request $request)
    {
        if ($request->item_num) {
            if ($this->is_base64_string($request->item_num)) {
                $item_num = base64_decode($request->item_num);
            } else {
                $item_num = $request->item_num;
            }

            $arrayMatl = $request->arrayMatl;

            $itemnum = Item::select('item_num')->where('item_num', $item_num)->first();

            if ($itemnum) {
                $req_itemnum = $itemnum->item_num;
            } else {
                $req_itemnum = $request->item_num;
            }
        }

        $data['ancestor'] = DB::select(DB::raw(
            "with recursive cte as (
                select item_num, parent_id, 1 lvl from bom_matls
                where item_num = '$req_itemnum'
                union all
                select c.item_num, t.parent_id, lvl + 1
                from cte c
                inner join bom_matls t on t.item_num = c.parent_id
            )
            select item_num, group_concat(parent_id order by lvl) all_parents
            from cte
            group by item_num"
        ));
        $dataA = array();
        $parents = "";
        $grandparents = "";
        foreach ($data['ancestor'] as $ancestor => $key) {
            $parents = $key->all_parents;
            $grandparents = $key->item_num;
        }
        $arrSplit = explode(',', $parents);
        foreach ($arrSplit as $key => $value) {
            $dataA[$value] = $value;
        }

        $parent = array_intersect($arrayMatl, $dataA);
        $result['parent'] = implode(',', $parent);

        if (array_intersect($arrayMatl, $dataA)) {
            $result['check'] = true;
        } else {
            $result['check'] = false;
        }
        // dd($data);
        return $result;
    }


    public function getGrnNum(Request $request, $grn_num = "")
    {
        if ($this->is_base64_string($grn_num)) {
            $grn_num = base64_decode($grn_num);
            // $whse_num = base64_decode($whse_num);
        }
        $grn = new GRN();
        $grn = $grn->select('grn_num')
            ->where('grn_num', 'like', '%' . $grn_num . '%')
            ->distinct()
            ->get();
        return $grn;
    }

    public function getOpenGrnNum(Request $request, $whse_num, $grn_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $grn_num = base64_decode($grn_num);
            $whse_num = base64_decode($whse_num);
        }
        $grn = new GRN();
        $grn = $grn->select('grn_num')
            ->where('whse_num', $whse_num)
            ->where('grn_num', 'like', '%' . $grn_num . '%')
            ->where('grn_status', 'O')
            ->distinct()
            ->get();
        return $grn;
    }

    public function getLPN($whse_num, $loc_num = "")
    {
        // if ($this->is_base64_string($whse_num)) {
        //    // $whse_num = base64_decode($whse_num);
        $whse_num = utf8_encode(base64_decode($whse_num));
        $whse_num = htmlspecialchars_decode($whse_num);

        // }
        // if ($this->is_base64_string($loc_num)) {
        //$loc_num = base64_decode($loc_num);
        $loc_num = utf8_encode(base64_decode($loc_num));
        $loc_num = htmlspecialchars_decode($loc_num);
        // }

        $lpn = Container::select('lpn_num')->where('whse_num', $whse_num);
        if ($loc_num != "") {
            $lpn = $lpn->where('loc_num', $loc_num);
        }
        $lpn = $lpn->get();
        //dd($lpn,$whse_num);
        if ($lpn) {
            return response()->json($lpn);
        }
    }

    public function getLPNStatus($lpn_num, $whse_num, $loc_num = "")
    {
        $lpn = Container::where('lpn_num', $lpn_num)->where('whse_num', $whse_num)->where('status', '!=', 'Open');
        if ($loc_num != "") {
            $lpn = $lpn->where('loc_num', $loc_num);
        }
        $lpn = $lpn->where('site_id', auth()->user()->site_id)->exists();

        return $lpn;
    }

    public function getLPNList($whse_num = "", $lpn_num = "")
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }

        $lpn = Container::select('lpn_num');
        if ($whse_num != "") {
            $lpn = $lpn->where('whse_num', $whse_num);
        }
        $lpn = $lpn->orderBy('lpn_num', 'asc')->get();
        return $lpn;
    }

    //LPN with loc not transit
    public function getLPNListNotTransit($whse_num = "", $lpn_num = "")
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }

        $site_id = auth()->user()->site_id;
        $lpn = Container::select('lpn_num')->where('status', 'Open');

        if ($whse_num != "" && $whse_num != "null") {
            $lpn = $lpn->where('containers.whse_num', '=', $whse_num);
        }

        $lpn = $lpn->leftJoin('locs', function ($join) {
            $join->on('containers.loc_num', '=', 'locs.loc_num');
        })
            ->where('locs.loc_type', '!=', 'T')->where('locs.site_id', $site_id)->orderBy('lpn_num', 'asc')->distinct()->get();
        return $lpn;
    }

    // LPN with no restriction
    public function getLPNListRestric($whse_num = "", $lpn_num = "")
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }

        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }
        // ->where('usage_restriction', 0)
        $lpn = Container::select('lpn_num');
        if ($whse_num != "") {
            $lpn = $lpn->where('whse_num', $whse_num);
        }
        $lpn = $lpn->where('status', 'Open')->orderBy('lpn_num', 'asc')->get();
        return $lpn;
    }

    //LPN with loc_num
    public function getLPNwithLocList($lpn_num = "", $loc_num)
    {

        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }

        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        }
        $lpn = Container::select('lpn_num')->whereNotIn('lpn_num', [$lpn_num])->where('loc_num', $loc_num)->orderBy('lpn_num', 'asc')->get();
        return $lpn;
    }

    //LPN with loc_num
    public function getCheckLPNwithLocList($lpn_num = "", $loc_num, $item_num)
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }
        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        }
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $lpn = Container::select('lpn_num', 'single_item', 'status')->where('lpn_num', $lpn_num)->where('loc_num', $loc_num)->orderBy('lpn_num', 'asc')->first();
        if ($lpn != null) {
            if ($lpn->single_item == 1) {
                $checkLine = ContainerItem::where('lpn_num', $lpn->lpn_num)->count();
                if ($checkLine >= 2) {
                    $checkItem = ContainerItem::where('lpn_num', $lpn->lpn_num)->where('item_num', $item_num)->exists();
                    $data = "single item";
                } else {
                    $checkItem = ContainerItem::where('lpn_num', $lpn->lpn_num)->where('item_num', $item_num)->exists();
                    if ($checkLine == 1) {
                        if ($checkItem) {
                            $data = "true";
                        } else {
                            $data = "single item";
                        }
                    } else {

                        $data = "true";
                    }
                }
            } elseif ($lpn->status == 'Transferred') {
                $data = 'transfer';
            } elseif ($lpn->status == 'Picked') {
                $data = 'picked';
            } else {
                $data = "exist";
            }
        } else {
            $data = "not exist";
        }
        return $data;
    }

    //LPN List for CO Ship
    public function getLPNListCoShip($whse_num, $lpn_num = "")
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }
        $site_id = auth()->user()->site_id;
        $lpn = Container::select('lpn_num')
            ->leftJoin('locs', function ($join) {
                $join->on('containers.loc_num', '=', 'locs.loc_num');
            })
            ->where('locs.loc_type', '!=', 'T')
            ->where('containers.whse_num', '=', $whse_num)
            ->where('containers.status', '=', 'Open')
            ->where('locs.site_id', $site_id)
            ->orderBy('lpn_num', 'asc')
            ->distinct()
            ->get();
        return $lpn;
    }

    //LPN List for CO Unpick
    public function getLPNListCoUnpick($whse_num, $loc_num, $lpn_num = "")
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
        }
        if ($this->is_base64_string($loc_num)) {
            $loc_num = base64_decode($loc_num);
        }
        $site_id = auth()->user()->site_id;
        $lpn = Container::select('lpn_num')
            ->where('loc_num', $loc_num)
            ->where('whse_num', $whse_num)
            ->where('status', 'Picked')
            ->where('site_id', $site_id)
            ->orderBy('lpn_num', 'asc')
            ->get();
        return $lpn;
    }

    //LPN List for TO Receipt
    public function getLPNListTOReceipt($trn_num, $lpn_num = "")
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }
        if ($this->is_base64_string($trn_num)) {
            $trn_num = base64_decode($trn_num);
        }

        $site_id = auth()->user()->site_id;
        $lpn = TransferOrderLinesSubline::select('lpn_num')
            ->where('trn_num', $trn_num)
            ->where('site_id', $site_id)
            ->whereRaw('IFNULL(qty_receivable,0) > 0')
            ->distinct()
            ->orderBy('lpn_num', 'asc')
            ->get();
        // dd($lpn_num,$trn_num, $lpn);
        return $lpn;
    }

    public function getPalletItemList($lpn_num)
    {
        $lpn_num = utf8_encode(base64_decode($lpn_num));
        $lpn_num = htmlspecialchars_decode($lpn_num);
        // if ($this->is_base64_string($lpn_num)) {
        //     $lpn_num = base64_decode($lpn_num);
        //     dd('qwe');
        // }

        $lpn = ContainerItem::select('item_num', 'qty_contained')->where('lpn_num', $lpn_num)
            ->whereHas('item', function ($query) {
                $query->where('item_status', '=', 1);
            })
            ->orderBy('item_num', 'asc')->get();


        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($lpn as $each) {
            $each['qty_contained'] = numberFormatPrecision($each->qty_contained, $unit_quantity_format);
        }

        return $lpn;
    }

    public function getPalletItemDetails($lpn_num, $item_num)
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }

        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }

        $checkItemExistence = Item::where('item_num', $item_num)->exists();
        if (!$checkItemExistence) {

            $alt_bc = AlternateBarcode::where('alternate_barcode', $item_num)->first();
            //    dd($alt_bc);
            if (!$alt_bc) {
                return 'not exist';
            } else {
                $item_num = $alt_bc->item_num;
            }
        }

        //check item active
        $checkItem = Item::where('item_num', $item_num)->value('item_status');
        if ($checkItem == 0) {
            return $data = 'inactive';
        }

        $lpn = ContainerItem::select('item_num', 'qty_contained', 'uom', 'lot_num')->where('lpn_num', $lpn_num)->where('item_num', $item_num)->orderBy('item_num', 'asc')->first();
        if ($lpn) {
            $getItemDetails = Item::where('item_num', $lpn->item_num)->first();
            $data = $lpn;
            $data['lot_tracked'] = $getItemDetails->lot_tracked;

            if ($getItemDetails->lot_tracked == 1) {
                $getLPN = Container::select('whse_num', 'loc_num')->where('lpn_num', $lpn_num)->first();
                // dd($item_num, $getLPN->loc_num,$getLPN->whse_num);
                // $lotloc = new LotLoc;
                // $list = $lotloc->select('lot_num')

                //     ->where('item_num', $item_num)
                //     ->where('loc_num', $getLPN->loc_num)
                //     ->where('whse_num', $getLPN->whse_num)


                //     ->first();

                // @$data['lot_num'] =  @$list->lot_num;
                @$data['lot_num'] =  @$lpn->lot_num;
                //dd($list,$lpn->item_num,$getItemDetails->whse_num);
            }

            $tparm = new TparmView;
            $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

            // foreach ($lpn as $each) {
            $lpn['qty_contained'] = numberFormatPrecision($lpn->qty_contained, $unit_quantity_format);
            // }

        } else {
            $data = 'not exist';
        }
        return $data;
    }

    // Get the Pallet Lot
    public function checkLotNumLPN($item_num, $lpn_num, $lot_num)
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }
        if ($this->is_base64_string($lot_num)) {
            $lot_num = base64_decode($lot_num);
        }

        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }
        $getLPN = ContainerItem::select('lot_num', 'qty_contained', 'uom')->where('lot_num', $lot_num)->where('item_num', $item_num)->where('lpn_num', $lpn_num)->first();


        if ($getLPN) {
            $data = $getLPN;

            $tparm = new TparmView;
            $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

            $data['qty_contained'] = numberFormatPrecision($data->qty_contained, $unit_quantity_format);
        } else {
            $data = 'not exist';
        }
        return $data;
    }









    // Display LPN Source
    public function displayLpnSource($lpn_num)
    {
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        }

        $source = Container::select('source')->where('lpn_num', $lpn_num)->first();
        // dd($source->source);
        return $source->source;
    }
    // Display LPN Item
    public function displayLpnItemCount($lpn_num)
    {
        $itemCount = ContainerItem::where('lpn_num', $lpn_num)->get();
        $totalCount = $itemCount->count();

        return $totalCount;
    }

    // Check Loc LPN
    public function checkLocLpn($whse_num, $loc_num)
    {
        $checkLoc = Loc::where('whse_num', $whse_num)->where('loc_num', $loc_num)->first();
        // dd($whse_num, $loc_num, $checkLoc);
        // if ($checkLoc) {
        //     return 'exist';
        // } else {
        //     return 'not exist';
        // }

        if ($checkLoc) {
            if ($checkLoc->loc_status == 1) {
                return 'exist';
            } else {
                return 'inactive';
            }
        } else {
            return 'not exist';
        }
    }

    //check LPN num
    public function checkLpn(Request $request)
    {
        $lpn_num = $request->lpn_num;
        if ($this->is_base64_string($lpn_num)) {
            $lpn_num = base64_decode($lpn_num);
        } else {
            $lpn_num = utf8_encode(htmlspecialchars_decode(base64_decode($request->lpn_num)));
        }

        $checklpn = Container::where('lpn_num', $lpn_num)->first();
        if ($checklpn) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    //check Pallet item for single item
    public function checkPalletItem(Request $request)
    {
        $item_num = $request->item_num;
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            $item_num = utf8_encode(htmlspecialchars_decode(base64_decode($item_num)));
        }

        $item = ContainerItem::where('item_num', $item_num)->first();
        if ($item) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function getItemList($item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        } else {
            if ($item_num != "" && $item_num != "null") {
                $item_num = utf8_encode(base64_decode($item_num));
                $item_num = htmlspecialchars_decode($item_num);
            }
        }
        // dd($item_num);
        $item = new Item();
        $item = $item->select('item_num', 'item_desc', 'lot_tracked')->where('item_num', $item_num)->active()->orderBy('item_num', 'asc')->first();
        // dd($item_num);
        if ($item) {
            $rslt = $item;
        } else {
            $alt_bc = AlternateBarcode::where('alternate_barcode', $item)->first();
            if ($alt_bc) {
                $item = $alt_bc->item_num;
                $item = $item->select('item_num', 'item_desc', 'lot_tracked')->where('item_num', $item_num)->active()->orderBy('item_num', 'asc')->first();
            } else {
                $rslt = 'false';
            }
        }
        return $rslt;
    }

    public function getShippingZoneSearch(Request $request)
    {
        $keyword = $request->keyword;

        $shippingZone = ShippingZone::select('shipping_zone_code', 'shipping_zone_desc')
            ->where('shipping_zone_status', 1);

        if ($request->keyword && $request->keyword != 'null') {
            $shippingZone->where('shipping_zone_code', 'LIKE', $keyword . '%');
        }

        if ($request->excluded && $request->excluded != 'null') {
            $excluded = explode(',', $request->excluded);
            $shippingZone->whereNotIn('shipping_zone_code', $excluded);
        }

        $shippingZone = $shippingZone->get();

        return $shippingZone;
    }

    public function getZoneSearch(Request $request)
    {
        $keyword = $request->keyword;

        $zone = Zone::select('zone_num', 'desc');

        if ($request->keyword && $request->keyword != 'null') {
            $zone->where('zone_num', 'LIKE', $keyword . '%');
        }

        if ($request->excluded && $request->excluded != 'null') {
            $excluded = explode(',', $request->excluded);
            $zone->whereNotIn('zone_num', $excluded);
        }

        $zone = $zone->get();

        return $zone;
    }

    public function getLocationSearch(Request $request)
    {
        $keyword = $request->keyword;

        $loc = Loc::select('loc_num', 'loc_name')
            ->where('loc_status', 1)
            ->where('loc_type', '!=', 'T')
            ->where('pick_locs', 0);

        if ($request->keyword && $request->keyword != 'null') {
            $loc->where('loc_num', 'LIKE', $keyword . '%');
        }

        if ($request->excluded && $request->excluded != 'null') {
            $excluded = explode(',', $request->excluded);
            $loc->whereNotIn('loc_num', $excluded);
        }

        $loc = $loc->get();

        return $loc;
    }

    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 23/02/2023
    -- Description    : Get the available qty after conversion of UOM
    -- Method         : GET
    -- Return         : Array
                            "conv_qty_limit" => array:3 [▼
                                "uom" => "MM"
                                "qty" => 300.0
                                "conv_factor" => "10.0000000000"
                            ]
    -- Source File(s) : palletMobileValidation.blade.php
    -- URL            : /home/<USER>/palletMobileValidation
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code     Modified By - Date           Description

        '-----    ------------     --------------------         ----------------------
        'R1       Gitlab #609      Afiq - 23/02/2023            Get the available qty after conversion of UOM.

    ==============================================================================================================================*/

    public function getConvQtyLimit($lineuom = "", $qty = "", $selectuom = "", $item_num = "",  $whse_num = "",  $loc_num = "",  $lot_num = "",  $ori_uom = "", $cust_num = "", $vend_num = "", $form_type = "")
    {

        if ($this->is_base64_string($lineuom)) {
            $lineuom = base64_decode($lineuom);
            $qty = base64_decode($qty);
            $selectuom = base64_decode($selectuom);
            $item_num = base64_decode($item_num);
            $whse_num = base64_decode($whse_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
            $ori_uom = base64_decode($ori_uom);
            $cust_num = base64_decode($cust_num);
            $vend_num = base64_decode($vend_num);
            $form_type = base64_decode($form_type);
        }
        // dd($lineuom, $selectuom,$qty, $item_num, $whse_num, $loc_num, $lot_num, $ori_uom, $cust_num, $vend_num, $form_type);
        if ($form_type == "ToReceipt" || $form_type == "PalletMaster" || $form_type == __('mobile.nav.co_picking')) {
            $baseuom = $ori_uom;
        } else {
            $baseuom = Item::where('item_num', $item_num)->value('uom');
            // if($lot_num != "" || $lot_num != "undefined")
            // {
            //     $checkItemLot = LotLoc::where('whse_num', $whse_num)->where('lot_num', $lot_num)->where('item_num',  $item_num)->where('loc_num', $loc_num)->first();
            //     $baseuom = $checkItemLot->uom;
            // }
            // else
            // {
            //     $checkItemLoc = ItemLoc::where('whse_num', $whse_num)->where('item_num', $item_num)->where('loc_num',  $loc_num)->first();
            //     $baseuom = $checkItemLoc->uom;
            // }
        }

        $convertQty = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $qty, $item_num, $cust_num, $vend_num, $form_type);
        return $convertQty;
    }


    public function getLotNumPallet($whse_num = "", $loc_num = "", $item_num = "", $lpn_num = "", $sortField = "", $sortBy = "")
    { //GET LOT LIST
        if ($this->is_base64_string($item_num)) {
            if ($whse_num != "" && $whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($item_num != "" && $item_num != "null")
                $item_num = base64_decode($item_num);
            if ($loc_num != "" && $loc_num != "null")
                $loc_num = base64_decode($loc_num);
            if ($lpn_num != "" && $lpn_num != "null")
                $lpn_num = base64_decode($lpn_num);
            if ($sortField != "" && $sortField != "null")
                $sortField = base64_decode($sortField);
            if ($sortBy != "" && $sortBy != "null")
                $sortBy = base64_decode($sortBy);
        } else {
            if ($whse_num != "" && $whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }
            if ($item_num != "" && $item_num != "null") {
                $item_num = utf8_encode(base64_decode($item_num));
                $item_num = htmlspecialchars_decode($item_num);
            }
            if ($loc_num != "" && $loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
            if ($lpn_num != "" && $lpn_num != "null") {
                $lpn_num = utf8_encode(base64_decode($lpn_num));
                $lpn_num = htmlspecialchars_decode($lpn_num);
            }
            if ($sortField != "" && $sortField != "null") {
                $sortField = utf8_encode(base64_decode($sortField));
                $sortField = htmlspecialchars_decode($sortField);
            }
            if ($sortBy != "" && $sortBy != "null") {
                $sortBy = utf8_encode(base64_decode($sortBy));
                $sortBy = htmlspecialchars_decode($sortBy);
            }
        }

        $list = new ContainerItem();
        $list = $list->select('container_items.lot_num', 'container_items.qty_contained', 'containers.loc_num', 'lots.expiry_date')
            ->leftJoin('containers', function ($join) {
                $join->on('container_items.lpn_num', '=', 'containers.lpn_num');
                $join->on('container_items.site_id', '=', 'containers.site_id');
            })
            ->leftJoin('lots', function ($join) {
                $join->on('lots.lot_num', '=', 'container_items.lot_num');
                $join->on('lots.item_num', '=', 'container_items.item_num');
                $join->on('lots.site_id', '=', 'container_items.site_id');
            })
            ->whereNotNull('container_items.lot_num')
            ->where('container_items.qty_contained', '>', 0);

        if ($lpn_num != "" && $lpn_num != "null")
            $list = $list->where('container_items.lpn_num', $lpn_num);
        if ($whse_num != "" && $whse_num != "null")
            $list = $list->where('containers.whse_num', $whse_num);
        if ($loc_num != "" && $loc_num != "null")
            $list = $list->where('containers.loc_num', $loc_num);
        if ($item_num != "" && $item_num != "null")
            $list = $list->where('container_items.item_num', $item_num);

        if ($sortField != '' && $sortBy != '')
            $list = $list->orderBy($sortField, $sortBy);
        else
            $list = $list->orderBy('lot_num', 'asc');

        // if the sort field chosen is not by location, default the second sort to location
        if ($sortField != '' && $sortField != 'loc_num')
            $list = $list->orderBy('loc_num', 'asc');

        $list = $list->get()->toArray();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        foreach ($list as $key => $each) {
            $list[$key]['qty_contained'] = numberFormatPrecision($each['qty_contained'], $unit_quantity_format);
            $list[$key]['expiry_date'] = $each['expiry_date']
                ? getDateTimeConverted($each['expiry_date'], false, true)
                : '';
        }

        return $list;
    }


    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 04/05/2023
    -- Description    : Get all bundle builder list based on item num
    -- Method         : GET
    -- Return         : Array
                            "bundle_lot" => "Bundle001"
                            ]
    -- Source File(s) : view.blade.php
    -- URL            : /OceanCash/BundleBuilder/view
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code              Modified By - Date           Description

        '-----    ------------              --------------------         ----------------------
        'R1       OC Customization #10      Afiq - 04/05/2023            Get all bundle builder list based on item num.

    ==============================================================================================================================*/

    public function getAllBundleList($item_num)
    {
        if ($this->is_base64_string($item_num)) {
            $item_num = base64_decode($item_num);
        }

        $lot = Lot::select('lot_num')->where('item_num', $item_num)->distinct()->get();

        return $lot;
    }

    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 10/05/2023
    -- Description    : check if the reels already exist for the bundle lot.
    -- Method         : GET
    -- Return         : Array
                            "data" => "axist"
                            ]
    -- Source File(s) : view.blade.php
    -- URL            : /OceanCash/BundleBuilder/view
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code              Modified By - Date           Description

        '-----    ------------              --------------------         ----------------------
        'R1       OC Customization #10      Afiq - 10/05/2023            check if the reels already exist for the bundle lot.

    ==============================================================================================================================*/

    public function checkReelsExist(Request $request)
    {
        if ($this->is_base64_string($request->bundle_lot)) {
            $bundleLot = base64_decode($request->bundle_lot);
            $reels = base64_decode($request->reels);
        }

        $check = BundleBuilder::where('bundle_lot', $bundleLot)->where('reels', $reels)->exists();
        if ($check) {
            return 'exist';
        }
        return 'not exist';
    }


    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 10/05/2023
    -- Description    : Get all lot num based on whse, item and loc num if exist
    -- Method         : GET
    -- Return         : Array
                            "lot_num" => "Lot001"
                            "qty"     => "10.00"
                            ]
    -- Source File(s) : palletbuilder.blade.php
    -- URL            : /views/Pallet/palletBuilder
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code              Modified By - Date           Description

        '-----    ------------              --------------------         ----------------------
        'R1       #1368                     Afiq - 10/05/2023            Get all lot num based on whse, item and loc num if exist.

    ==============================================================================================================================*/

    public function getLotPallet($whse_num, $item_num, $loc_num = "", $lot_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            $whse_num = base64_decode($whse_num);
            $item_num = base64_decode($item_num);
            $loc_num = base64_decode($loc_num);
            $lot_num = base64_decode($lot_num);
        }

        $lotList = LotLoc::select('lot_num', 'qty_available', 'uom')->where('whse_num', $whse_num)->where('item_num', $item_num);
        if ($loc_num != "" && $loc_num != "null") {
            $lotList = $lotList->where('loc_num', $loc_num);
        }
        $lotList = $lotList->get();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($lotList) {
            foreach ($lotList as $each) {
                $each['qty_available'] = numberFormatPrecision($each->qty_available, $unit_quantity_format);
            }
        }

        return $lotList;
    }

    public function getLocPackingValidate($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }

            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }
        // dd($whse_num,$loc_num);
        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name', 'loc_status', 'loc_type');
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        $loc = //$loc->where('loc_type', 'S')
            $loc->where('loc_num', $loc_num)
            ->orderBy('loc_num', 'asc')
            ->first();
        //return $loc;
        //return response()->json($loc);
        return response()->json($loc, 200, ['Content-Type' => 'application/json']);
    }

    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 12/05/2023
    -- Description    : check if the lot num + item num exist or not
    -- Method         : GET
    -- Return         : true / false
    -- Source File(s) : add.blade.php
    -- URL            : /CustomizedViews/OceanCahs/MasterMaintenance/bundlebuilder/add
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code              Modified By - Date           Description

        '-----    ------------              --------------------         ----------------------
        'R1       OC Customization #10      Afiq - 12/05/2023            check if the lot num + item num exist or not

    ==============================================================================================================================*/

    public function checkItemLot(Request $request)
    {
        if ($this->is_base64_string($request->lot_num)) {
            $lot_num = base64_decode($request->lot_num);
            $item_num = base64_decode($request->item_num);
        }

        $lotList = Lot::where('lot_num', $lot_num)->where('item_num', $item_num);
        $lotList = $lotList->exists();

        return $lotList;
    }

    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 12/05/2023
    -- Description    : check if the lot num + item num + reels exist or not
    -- Method         : GET
    -- Return         : true / false
    -- Source File(s) : add.blade.php
    -- URL            : /CustomizedViews/OceanCahs/MasterMaintenance/bundlebuilder/add
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code              Modified By - Date           Description

        '-----    ------------              --------------------         ----------------------
        'R1       OC Customization #10      Afiq - 12/05/2023            check if the lot num + item num + reels exist or not

    ==============================================================================================================================*/

    public function checkReels(Request $request)
    {
        if ($this->is_base64_string($request->lot_num)) {
            $lot_num = base64_decode($request->lot_num);
            $item_num = base64_decode($request->item_num);
            $reels = base64_decode($request->reels);
        }

        $itemId = Item::where('item_num', $item_num)->value('id');

        $lotList = BundleBuilder::where('bundle_lot', $lot_num)->where('item_id', $itemId)->where('reels', $reels);
        $lotList = $lotList->exists();

        return $lotList;
    }

    //anas
    //Function to get LPN Num from Material trans for reprint of label.
    public function getLpnForReprint($ref_num, $ref_line = "", $type = "")
    {
        // if ($this->is_base64_string($whse_num)) {
        //     $whse_num = base64_decode($whse_num);
        // }

        if ($this->is_base64_string($ref_num)) {
            $ref_num = base64_decode($ref_num);
        }
        if ($this->is_base64_string($ref_line)) {
            $ref_line = base64_decode($ref_line);
        }
        if ($this->is_base64_string($type)) {
            $type = base64_decode($type);
        }
        $lpn = DB::table('matl_trans')->whereNotNull('lpn_num')->where('ref_num', $ref_num);
        if ($ref_line != "") {
            $lpn = $lpn->where('ref_line', $ref_line);
        }
        if ($type != "") {
            $lpn = $lpn->where('trans_type', $type);
        }
        $lpn = $lpn->groupBy('lpn_num')->get();
        // dd($lpn);
        if ($lpn) {
            return response()->json($lpn);
        }
    }

    public function getLocMaster($whse_num, $loc_num = "")
    {
        if ($this->is_base64_string($whse_num)) {
            if ($whse_num != "null")
                $whse_num = base64_decode($whse_num);
            if ($loc_num != "null")
                $loc_num = base64_decode($loc_num);
        } else {
            if ($whse_num != "null") {
                $whse_num = utf8_encode(base64_decode($whse_num));
                $whse_num = htmlspecialchars_decode($whse_num);
            }

            if ($loc_num != "null") {
                $loc_num = utf8_encode(base64_decode($loc_num));
                $loc_num = htmlspecialchars_decode($loc_num);
            }
        }

        $loc = new Loc();
        $loc = $loc->select('loc_num', 'loc_name');
        if ($whse_num != "null")
            $loc = $loc->where('whse_num', $whse_num);
        $loc = $loc->where('loc_type', 'S')
            ->where('loc_status', 1)
            ->where('pick_locs', 0)
            ->orderBy('loc_num', 'asc')
            ->get();
        return $loc;
    }

    public function checkReasonCodeValidation(Request $request)
    {


        if ($this->is_base64_string($request->reason_num)) {
            $reason_num = base64_decode($request->reason_num);
            $reason_class = base64_decode($request->reason_class);
        }
        //return $reason_class . " >>>" . $reason_num;
        $toReason = new ReasonCode();
        $toReason = $toReason->select('sync_status')
            ->where('reason_num', $reason_num)
            ->where('reason_class', $reason_class)

            ->get()->toArray();
        //dd($reason_num,$reason_class);
        return $toReason;
    }

    public function checkSAPConnnection()
    {

        $site_id =  auth()->user()->site_id;
        $resp = SapCallService::getSQPConnection($site_id);
        //dd($resp);
        //if(count($resp->getSession())==0)
        if ($resp == false || count($resp->getSession() ?? []) == 0) {
            return 2;
        } else {
            session()->put('SAPErrors', 0);
            return 1;
        }
    }

    public function getLotRef($lot_source = "", $ref_num = "")
    {
        if ($this->is_base64_string($ref_num)) {
            $ref_num = base64_decode($ref_num);
        }
        if ($this->is_base64_string($lot_source)) {
            $lot_source = base64_decode($lot_source);
        }

        $lot = new Lot();
        $lot = $lot->select('reference_no')->whereNotNull('reference_no')->orderBy('reference_no', 'asc')->distinct('reference_no');

        if (!empty($ref_num)) {
            $lot->where('reference_no', $ref_num);
        }
        if (!empty($lot_source)) {
            $lot->where('lot_source', $lot_source);
        }


        return $lot->get();
    }

    // public function whseValidate($warehouse)
    // {
    //     if ($this->is_base64_string($warehouse)) {
    //         $whse_num = base64_decode($warehouse);
    //     } else {
    //         $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($warehouse)));
    //     }
    //     $warehouse = Warehouse::where('whse_num', $whse_num)->first();
    //     if ($warehouse) {
    //         if ($warehouse->whse_status == 0) {
    //             return 'inactive';
    //         } else {
    //             return 'active';
    //         }
    //     } else {
    //         return 'not exist';
    //     }
    // }

    public function getReferenceNo($trans_type)
    {
        $trans_type = base64_decode($trans_type);
        $result = [];

        if ($trans_type == 'job') {
            $result = Job::select('job_num AS reference_no')->where('job_status', '<>', 'C')->groupBy('job_num')->get();
        } else if ($trans_type == 'po') {
            $result = PurchaseOrderItem::select('po_num AS reference_no')->where('rel_status', '<>', 'C')->groupBy('po_num')->get();
        } else if ($trans_type == 'to') {
            $result = TransferLine::select('trn_num AS reference_no')->where('line_stat', '<>', 'C')->groupBy('trn_num')->get();
        }

        return $result;
    }

    // function to check catch weight
    public function checkCatchWeight(Request $request)
    {
        $item_num = $request->item_num;

        $item = Item::select('catch_weight')->where('item_num', $item_num)->first();

        if ($item) {
            return $item->catch_weight;
        } else {
            return 0;
        }
    }
}
