<?php

namespace App\Http\Controllers\Admin;

use App\Services\SapCallService;
use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Validation\ValidationException;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\TransParm;
use App\TparmSite;

use App\SiteSetting;
use App\Warehouse;
use App\Loc;
use Alert;
use App\View\TparmView;
use Form;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

//Create by Zen Vin

class TransParmController extends Controller
{

    private $module_Name;
    private $parm;
    private $trans_Parm;
    private $page = 'transparm'; //global variable for navigation tab

    //

    public function index()
    {

        if (!\Gate::allows('hasSysadmin')) {
            return view('errors.404v2')->with('page', 'error');
        }

        $dropdown = new \stdClass();
        $dropdown->tparm_module = '';

        $trans_Parm = $this->getDropdown();




        $tparm = new TparmView();

        $sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $sap_integration_id = TransParm::where('tparm_name', '=', 'sap_trans_order_integration')->first();
        $disable_create_new_item_location = TransParm::where('tparm_name', '=', 'disable_create_new_item_location')->first();
        $transupdates = TransParm::select('id', 'module_label')->where('module_label', 'decimal_setting')->get();
        foreach ($transupdates as $transupdate) {
            $results = TparmSite::select('id', 'tparm_id', 'tparm_value')->where('tparm_id', $transupdate->id)->get();
            foreach ($results as $result) {
                if ($result->tparm_value == 0 || $result->tparm_value == NULL) {
                    DB::table('tparm_sites')
                        ->where('id', $result->id)
                        ->update([
                            "tparm_value" => "2"
                        ]);
                }
            }
        }

        //$trans_Parm = sort($trans_Parm);
        return view('admin.transparm')
            ->with('trans_Parm', $trans_Parm)
            ->with('page', $this->page)
            ->with('sap_integration', $sap_integration)
            ->with('sap_integration_id', $sap_integration_id)
            ->with('disable_create_new_item_location', $disable_create_new_item_location)

            ->with('dropdown', $dropdown);
    }

    public function update(Request $request)
    {
        $planId = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->value('plan_id');
        $planId = customDecrypt($planId);


        $trans_Parm = $this->getDropdown();
        $dropdown = $request->dropdown;
        $title = TransParm::where('tparm_module', $dropdown)->first();
        $abc = $request->parm;

        $tparm = new TparmView();
        $sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $sap_integration_id = TransParm::where('tparm_name', '=', 'sap_trans_order_integration')->first();
        $disable_create_new_item_location = TransParm::where('tparm_name', '=', 'disable_create_new_item_location')->where('tparm_module')->first();

        //dd($sap_integration);
        //        dd($disable_create_new_item_location);
        if (@$request->parm[$sap_integration_id->id] == 1) {


            // Need check the validation for setting
            
            //$info = DB::table('middleware_connections')->where('site_id', auth()->user()->site_id)->get() ?? NULL;
            //dd($info);
            $site_id =  auth()->user()->site_id;
            $info = DB::table('site_connections')->where('site_id', $site_id)->where('connector', 'sap')->where('type', 'erp')->where('active', 1)->first() ?? NULL;
           
            //dd($info);
            if($info==null)
            {
                //throw ValidationException::withMessages([__('error.admin.invalid_sap_setting')]);

                 $parm = $this->parmEdit($request->dropdown);

                $parm = $this->reArrangeParm($parm, $title);
                return view('admin.transparm')->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('disable_create_new_item_location', $disable_create_new_item_location)
                    ->with('sap_integration_id', $sap_integration_id)
                    ->with('sap_integration', $sap_integration)
                    ->with('planId',$planId)
                    ->with('parm', $parm)->with('dropdown', $title)->with('errormsg', __(
                        'error.admin.invalid_sap_setting',
                        ['resource' => __('Object'), 'name' => $title->module_label]
                    ));


            }


            $resp = SapCallService::getSQPConnection($site_id);
            //dd($resp);
            //if(count($resp->getSession())==0)
            if ($resp == false || count($resp->getSession() ?? []) == 0) {
                // throw ValidationException::withMessages([__('error.admin.invalid_sap_setting')]);


                //Alert::error(__('error.mobile.sap_error'), 'Connection SAP failed . Server is closed / Invalid Login.')->persistent('Dismiss');
                // exit;

                $parm = $this->parmEdit($request->dropdown);

                $parm = $this->reArrangeParm($parm, $title);
                return view('admin.transparm')->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('disable_create_new_item_location', $disable_create_new_item_location)
                    ->with('sap_integration_id', $sap_integration_id)
                    ->with('sap_integration', $sap_integration)
                    ->with('planId',$planId)
                    ->with('parm', $parm)->with('dropdown', $title)->with('errormsg', __(
                        'error.admin.invalid_sap_setting',
                        ['resource' => __('Object'), 'name' => $title->module_label]
                    ));

                // $parm = $this->parmEdit($request->dropdown);

                // return view('admin.transparm')
                //                 ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                //                 ->with('disable_create_new_item_location', $disable_create_new_item_location)
                //                 ->with('sap_integration_id', $sap_integration_id)
                //                 ->with('sap_integration', $sap_integration)
                //                 ->with('remaining_trial_days',1)
                //                 ->with('parm', $parm)->with('dropdown', $title);






            } else {
                // Update Site Setting's date format
                $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
                $site->date_format = 'Y-m-d';
                $site->input_date_format = 'Y-m-d';
                $site->save();
            }
        }

        //exit;

        switch ($request->input('submit')) {
            case 'update':

                // $messages = [
                //     'required' => 'Please Fill in the Parameter before submit',
                // ];
                //      $this->validate($request, [
                //         "parm.*" => "required"
                //         ],$messages);

                if (array_key_exists("217", $abc)) {
                    $num = "217";
                } else if (array_key_exists("220", $abc)) {
                    $num = "220";
                } else if (array_key_exists("151", $abc)) {
                    $num = "151";
                } else {
                    $num = "0";
                }

                if ($num != 0) {
                    $arrWhse = json_decode($abc[$num]);
                    $arrMashup = array();

                    foreach ($arrWhse as $key => $value) {
                        $whsenum = Warehouse::select('whse_num')->where('whse_num', $value->whse_num)->first();

                        if ($whsenum) {
                            $req_whsenum = $whsenum->whse_num;
                        } else {
                            $req_whsenum = $value->whse_num;
                        }

                        $trnloc = Loc::select('loc_num')->where('loc_num', $value->loc_num)->first();

                        if ($trnloc) {
                            $req_trnloc = $trnloc->loc_num;
                        } else {
                            $req_trnloc = $value->loc_num;
                        }

                        $arrSet[$key] = array('whse_num' => $req_whsenum, 'loc_num' => $req_trnloc);
                    }

                    $arrMerge = json_encode($arrSet, JSON_FORCE_OBJECT);
                    $abc[$num] = $arrMerge;
                }

                $lower = 0;
                $upper = 0;
                $po = 0;
                $co = 0;
                $to = 0;

                foreach ($request->parm as $tparm_id => $value) {
                    $tparmview = DB::table('tparmview')->where('tparm_id', $tparm_id)
                        ->where('site_id', auth()->user()->site_id)
                        ->first();
                    if ($tparmview) {
                        if ($tparmview->tparm_name == "qty_scrapped_indicator_lower_limit") {
                            $lower = $value;
                        } else if ($tparmview->tparm_name == "qty_scrapped_indicator_upper_limit") {
                            $upper = $value;
                        } else if ($tparmview->tparm_name == "default_tolerance_PO") {
                            $po = $value;
                        } else if ($tparmview->tparm_name == "default_tolerance_CO") {
                            $co = $value;
                        } else if ($tparmview->tparm_name == "default_tolerance_TO") {
                            $to = $value;
                        }

                        if ($lower > 100 || $upper > 100 || $po > 100 || $co > 100 || $to > 100) {
                            // Throw error

                            $parm = $this->parmEdit($request->dropdown);
                            $parm = $this->reArrangeParm($parm, $title);
                            return view('admin.transparm')
                                ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                                ->with('disable_create_new_item_location', $disable_create_new_item_location)
                                ->with('sap_integration_id', $sap_integration_id)
                                ->with('planId',$planId)
                                ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                                ->with('parm', $parm)->with('dropdown', $title)->with('errormsg', 'Value must be less than or equal to 100.');
                        }
                    }
                }

                if ($lower > $upper || ($lower == $upper && $lower != 0 && $upper != 0)) {
                    // throw error

                    $parm = $this->parmEdit($request->dropdown);
                    $parm = $this->reArrangeParm($parm, $title);
                    return view('admin.transparm')->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                        ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                        ->with('disable_create_new_item_location', $disable_create_new_item_location)
                        ->with('sap_integration_id', $sap_integration_id)
                        ->with('planId',$planId)
                        ->with('sap_integration', $sap_integration)
                        ->with('parm', $parm)->with('dropdown', $title)->with('errormsg', 'Lower limit must be less than Upper limit.');;
                }
                // dd($abc);
                foreach ($abc as $key => $value) {
                    $tparm_sites = array('tparm_id' => $key, 'site_id' => auth()->user()->site_id);

                    // if ($sap_integration_id->id == $key && $value) {
                    //     $tparm_item_warehosue_errors = TransParm::where('tparm_name', '=', 'item_warehouse_error')->get();

                    //     foreach ($tparm_item_warehosue_errors as $tparm_obj) {
                    //         if ($value) {
                    //             TparmSite::updateOrInsert(array('tparm_id' => $tparm_obj->id, 'site_id' => auth()->user()->site_id), ['tparm_value' => 0]);
                    //         } else {
                    //             TparmSite::updateOrInsert(array('tparm_id' => $tparm_obj->id, 'site_id' => auth()->user()->site_id), ['tparm_value' => 1]);
                    //         }
                    //     }

                    //     // if ($value) {
                    //     //     TparmSite::updateOrInsert(array('tparm_id' => $block_trans_after_sap_error, 'site_id' => auth()->user()->site_id), ['tparm_value' => 0]);
                    //     // } else {
                    //     //     TparmSite::updateOrInsert(array('tparm_id' => $block_trans_after_sap_error, 'site_id' => auth()->user()->site_id), ['tparm_value' => 1]);
                    //     // }
                    // }
                    $value= $value??"";
                    TparmSite::updateOrCreate($tparm_sites, ['tparm_value' => $value,'modified_by' => auth()->user()->name]);

                    //dd($value,$tparm_sites['tparm_id'],$tparm_sites['site_id']);
                    // $checkRecord = TparmSite::where('tparm_id', $tparm_sites['tparm_id'])->where('site_id', $tparm_sites['site_id'])->count();
                    // if ($checkRecord) {
                    //     // dd($value);
                    //     TparmSite::updateOrInsert($tparm_sites, ['tparm_value' => $value]);
                    // } else {
                    //     $tparm_sites['tparm_value'] = $value;
                    //     // dd($tparm_sites);

                    //         TparmSite::Insert($tparm_sites);
                    // }
                }
                if ($sap_integration || @$request->parm[$sap_integration_id->id] == 1) {
                    $tps = TransParm::where('tparm_name', 'disable_create_new_item_location')->get()->pluck('id');
                    foreach ($tps as $tp) {
                        $tparm_sites = array('tparm_id' => $tp, 'site_id' => auth()->user()->site_id);
                        TparmSite::updateOrInsert($tparm_sites, ['tparm_value' => 1]);
                    }
                }
                $parm = $this->parmEdit($request->dropdown);
                $parm = $this->reArrangeParm($parm, $title);
                return view('admin.transparm')->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('disable_create_new_item_location', $disable_create_new_item_location)
                    ->with('sap_integration_id', $sap_integration_id)
                    ->with('planId',$planId)
                    ->with('sap_integration', $sap_integration)
                    ->with('parm', $parm)->with('dropdown', $title)->with('successmsg', __(
                        'success.updated',
                        ['resource' => __('Object'), 'name' => $title->module_label]
                    ));;
                break;

            case 'show':
                $parm = $this->parmEdit($request->dropdown);
                $parm = $this->reArrangeParm($parm, $title);

                return view('admin.transparm')
                    ->with('trans_Parm', $trans_Parm)->with('page', $this->page)
                    ->with('disable_create_new_item_location', $disable_create_new_item_location)
                    ->with('sap_integration_id', $sap_integration_id)
                    ->with('sap_integration', $sap_integration)
                    ->with('planId',$planId)
                    ->with('parm', $parm)->with('dropdown', $title);
                break;
        }
    }

    public function reArrangeParm($parm, $title)
    {
        // Re arrange parm
        if ($title->tparm_module == 'CustOrdPicking') {
            $packing_index = 0;
            $picking_index = 0;

            foreach ($parm as $key => $value) {
                if ($value->tparm_name == 'def_picking_location') {
                    $picking_index = $key;
                }

                if ($value->tparm_name == 'def_location') {
                    $packing_index = $key;
                }
            }

            if ($picking_index != 0) {
                // $parm->unset($picking_index);
                // unset($parm[$picking_index]);
                $itemToMove = $parm->pull($picking_index);
                $parm->splice($packing_index, 0, [$itemToMove]);
            }
        }
        $planId = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->value('plan_id');
        $planId = customDecrypt($planId);
        if($planId==1 || $planId==4)
        {
            $parm = $parm->reject(function ($key) {
                return in_array($key->tparm_label, [
                    'Enable Multi Scan (Production)',
                    'TO Shipping',
                    'Job Material Issue',
                    'Transfer Order'
                ]);
            });
           return $parm;
        }
        else{
            return $parm;
        }

    }

    public function getDropdown()
    {
        // $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
        $resut = config('icapt.special_modules.enable_packing');
        $checkgrn = config('icapt.enable_grn');

        //dd($checkgrn);

        $checkPlan = ['AX-MT-STR-M', 'AX-MT-STR-A', 'AX-MT-FREE-M', 'AX-MT-PRO-M', 'AX-MT-PRO-A'];

        $arrPlanModule = array(
            'AX-MT-STR-M' =>
            [
                'Batch job Material Issue',
                'Machine Run Hours',
                'TO Shipping',
                'TO Receipt',
                'TO Shipping Label',
                'Job Inquiry',
                'Job Labor Hours',
                'Job Material Issue',
                'Job Material Issue Label',
                'Job Material Return',
                'Job Receipt',
                'Job Receipt Label',
                'WIP Move',
                'Pallet',
                'Customer Return'
            ],

            'AX-MT-STR-A' =>
            [
                'Batch job Material Issue',
                'Machine Run Hours',
                'TO Shipping',
                'TO Receipt',
                'TO Shipping Label',
                'Job Inquiry',
                'Job Labor Hours',
                'Job Material Issue',
                'Job Material Issue Label',
                'Job Material Return',
                'Job Receipt',
                'Job Receipt Label',
                'WIP Move',
                'Pallet',
                 'Customer Return'
            ],

            'AX-MT-FREE-M' =>
            [
                'Machine Run Hours',
                'TO Shipping',
                'TO Receipt',
                'TO Shipping Label',
                'Pallet',
                'Customer Return'
            ],

            'AX-MT-PRO-A' =>
            ['Machine Run Hours'],

            'AX-MT-PRO-M' =>
            ['Machine Run Hours'],

        );

        $plan = SiteSetting::select('plan_code')->where('site_id', auth()->user()->site_id)->first();
        if ($resut == true) {
            if ($checkgrn == true) {
                if (in_array($plan->plan_code, $checkPlan)) {

                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->whereNotIn('module_label', $arrPlanModule[$plan->plan_code])->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                } else {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                }
            } else {
                if (in_array($plan->plan_code, $checkPlan)) {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->whereNotIn('module_label', $arrPlanModule[$plan->plan_code])->where('tparm_name', '!=', 'grn_list_by')->where('tparm_name', '!=', 'auto_close_grn')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                } else {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->where('tparm_name', '!=', 'grn_list_by')->where('tparm_name', '!=', 'auto_close_grn')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                }
            }
        } else {
            if ($checkgrn == true) {
                if (in_array($plan->plan_code, $checkPlan)) {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->whereNotIn('module_label', $arrPlanModule[$plan->plan_code])->where('tparm_module', '!=', 'Packing')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                } else {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source')->where('tparm_module', '!=', 'Packing')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                }
            } else {
                if (in_array($plan->plan_code, $checkPlan)) {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source', 'tparm_name')->whereNotIn('module_label', $arrPlanModule[$plan->plan_code])->where('tparm_label', '!=', 'Auto close GRN')->where('tparm_label', '!=', 'GRN List by')->where('tparm_module', '!=', 'Packing')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                } else {
                    $this->trans_parm = TransParm::select('tparm_module', 'module_label', 'tparm_source', 'tparm_name')->where('tparm_label', '!=', 'Auto close GRN')->where('tparm_label', '!=', 'GRN List by')->where('tparm_module', '!=', 'Packing')->groupby('tparm_module', 'module_label')->orderBy('tparm_module')->get();
                }
                //dd($this->trans_parm->toArray());
            }
        }




        return $this->trans_parm;

        //    return $this->trans_parm;
    }

    public function parmEdit($dropdown)
    {
        // Get all Tparms
        $tparm = TransParm::select(['id AS tparm_id', 'tparm_module', 'module_label', 'tparm_name', 'tparm_label', 'tparm_type', 'tparm_source', 'order'])->where('tparm_module', $dropdown)->orderBy('order')->get();

        // dd($tparm->toArray());

        // Get all Tparm Site Settings
        $tparm_sites = TparmView::where('tparm_module', $dropdown)->get();
        // Combine them - to detect missing settings
        $results = $tparm_sites->mergeRecursive($tparm);
        // Make tparm_id unique and sort by it
        if ($dropdown == "System") {
            return $results->unique('tparm_id')->sortBy('tparm_source');
        }

        // dd($results->unique('tparm_id')->sortBy('order'));

        return $results->unique('tparm_id')->sortBy('order');
    }
}
