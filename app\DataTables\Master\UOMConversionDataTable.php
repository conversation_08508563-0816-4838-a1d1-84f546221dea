<?php

namespace App\DataTables\Master;

use App\UomConv;
use App\View\TparmView;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Illuminate\Support\Facades\Cookie;
use App\DataTables\BaseDataTable;
use App\Traits\DataTableOptions;

class UOMConversionDataTable extends BaseDataTable
{
    use DataTableOptions;
    
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */

    protected $exportColumns = [
        ['data' => 'uom_from', 'title' => 'Base UOM'],
        ['data' => 'uom_to', 'title' => 'Converted UOM'],
        ['data' => 'conv_type', 'title' => 'Type'],
        ['data' => 'item_num', 'title' => 'Item'],
        ['data' => 'item_desc', 'title' => 'Description'],
        ['data' => 'cust_num', 'title' => 'Customer'],
        ['data' => 'cust_name', 'title' => 'Customer Name'],
        ['data' => 'conv_factor', 'title' => 'Conversion Factor'],
    ];

    public function dataTable($query)
    {
        $datatables = datatables()
            ->eloquent($query)
            ->editColumn('conv_factor', function($q) {
                $intNumber = (float)($q->conv_factor);
                $tparm = new TparmView;
                 $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
                $conv_factor = numberFormatPrecision($intNumber,$unit_quantity_format) ?? null;
               
                return $conv_factor;
            })
            ->editColumn('conv_type', function ($key) {
                $status_lists = UomConv::conv_type_enum;
                if(array_key_exists($key->conv_type, $status_lists)) return $status_lists[$key->conv_type];
                return '';
            })
            ->addColumn('action', function($uom) {
                return "<a href='" . route('editUOMConversion',
                        ['uom_conv' => $uom->id]
                    ) . "' class='btn btn-icon btn-primary  btn-sm' id='edit' name='edit' title='Edit'><i class='icon-edit'></i></a>"
                    ;
            });


        // for export columns
        if ($this->request->isMethod('post')) {
            $datatables = $datatables
                ->editColumn('item_desc', function($uom) {
                    return $uom->item ? $uom->item->item_desc : '';
                })
                ->editColumn('cust_name', function($uom) {
                    return $uom->customer ? $uom->customer->cust_name : '';
                });
        }

        return $datatables;
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\UomConv $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(UomConv $model)
    {
        $query = $model->newQuery();

        // for export columns
        if ($this->request->isMethod('post'))
        {
            $query->with('item', 'customer');
        }

        return $query;
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('UOMConversions')
            ->addTableClass('nowrap table-bordered table-xs')
            ->parameters([
                'responsive' => true,
                'autoWidth' => false,
                'scrollY' => 400,
                'scrollX' => true,
                'scrollCollapse' => true,
                'colReorder' => [
                    'fixedColumnsLeft' => [1],
                ],
            ])
            ->orderCellsTop(true)
            ->fixedHeader(true)
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Blrtip')
            ->orderBy([ 1, 'asc' ])
            ->buttons(
                // Button::make('export')->className('hidden btn buttons-excel buttons-html5 btn-info')
                Button::make('postExcel')->text('Export to Excel')->className('btn buttons-excel buttons-html5 btn-info')->responsive(false)->autoWidth(false),
                Button::make('postCsv')->text('Export to CSV')->className('btn btn-info')
            );
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $arrData = [
            'id'=>array('title'=> ''),
            'uom_from'=>array('title'=>'Base UOM',
                'render'=>'function() {
                    var url = "'.route('viewUOMConversion', ':id').'";
                    url = url.replace(":id",full.id);

                    var data = "<a href="+url+"> " + full.uom_from + "</a>";
                    return data;
                }'),
            'uom_to'=>array('title'=>'Converted UOM'),
            'conv_factor'=>array('title'=>'Conv.Factor', 'class'=>'text-right'),
            'conv_type'=>array('title'=>'Type'),
            'item_num'=>array('title'=>'Item'),
            'vend_num'=>array('title'=>'Vendor'),
            'cust_num'=>array('title'=>'Customer'),
            'action'=>array('title'=>'Action','orderable'=>false,'exportable'=>false,'printable'=>false,'width'=>80,'class'=>'text-center'),
        ];

        $UOMCArr = array_keys($arrData);

        $index = 0;
        foreach($UOMCArr as $key => $datalocastorage){
            $arrDataFilter[$UOMCArr[$index]] = $arrData[$UOMCArr[$index]];
            $index++; 
        }
        $arrUOMCRender=['uom_from'=>0];
        $arrAddClass=['conv_factor'=>0];
        $arrAction= ['action'=>0];
        array_push($arrAction);
    
        foreach($arrDataFilter as $headertitle => $data)
        {
            if($headertitle!= 'id'){
                $arrRearrange[0] = Column::make('id')
                                        ->title('')
                                        ->addClass('checkbox')
                                        ->orderable(false)
                                        ->searchable(false)
                                        ->exportable(false)
                                        ->printable(false)
                                        ->width('2%')
                                        ->render('"<input name=\"save_value\" type=\"checkbox\" class=\"chk\" value=\"" + full.id + "\"  onChange=\"getValueUsingClass();\">"');
                if(array_key_exists($headertitle,$arrUOMCRender)){
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->render($data['render']);
                }elseif(array_key_exists($headertitle,$arrAddClass)){
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->addClass($data['class']);                                     
                }elseif(array_key_exists($headertitle,$arrAction)){
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->orderable($data['orderable'])->exportable($data['exportable'])->printable($data['printable'])->width($data['width'])->addClass($data['class']);                                     
                }else{
                    $arrRearrange[] = Column::make($headertitle)->title($data['title']);
                }
            }
        }
        return $arrRearrange;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'UOMConversions_' . date('YmdHis');
    }

    /**
     * Get sheetname for export.
     *
     * @return string
     */
    protected function sheetName()
    {
        return 'UOM Conversions';
    }
}
