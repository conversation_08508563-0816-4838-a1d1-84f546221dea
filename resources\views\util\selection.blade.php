<!-- Modal -->
<div class="modal fade text-xs-left" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title modalheader"></h4>
            </div>
            <div class="modal-body">
                <div class="container">
                    <div class="row" id="sortdiv" style="padding-bottom:2px" hidden>
                        <div class="col-xs-2 px-0 h-100 align-middle"><label class="my-0"
                                for="sort_by">{{ __('mobile.label.sort_by') }}</label></div>
                        <div class="col-xs-5 px-0">
                            <select class="form-control border-primary" style="text-align:left;" id="sortField"
                                class="form-control" name="sortField">
                                <option value="lot_num">{{ __('mobile.label.lot_num') }}</option>
                                <option hidden value="qty_available">{{ __('mobile.label.qty_available') }}</option>
                                <option hidden value="qty_contained">{{ __('mobile.label.qty_contained') }}</option>
                                <option value="expiry_date">{{ __('mobile.label.expiry_date') }}</option>
                                <option value="loc_num">{{ __('mobile.label.loc_num') }}</option>
                            </select>
                        </div>
                        <div class="col-xs-5 px-0">
                            <select class="form-control border-primary" style="text-align:left;" id="sortBy"
                                class="form-control" name="sortBy">
                                <option value="asc">Ascending</option>
                                <option value="desc">Descending</option>
                            </select>
                        </div>
                        <!-- <br></br> -->
                    </div>
                </div>
                <input id="search" class="form-control border-primary" placeholder="Search..." />
                <table id="pagtable" style="width:100%;">
                </table>
                <!-- <div id="item-list">
                    {{-- <input id="search"  class="form-control border-primary"  placeholder="Search..."/> --}}
                    <ul class="list list-group" id="list" data-columns="2">
                    </ul>
                </div> -->
            </div>
            <!--        <div class="modal-footer">
            <button type="button" class="btn grey btn-outline-secondary" data-dismiss="modal">Close</button>
        </div>-->
        </div>
    </div>
</div>

<style>
    input#search {
        padding: 0.4rem 0.75rem;
    }

    button.close>span {
        font-size: 20pt;
    }

    h4.modal-title.modalheader>b {
        font-size: 12pt;
    }

    #pagtable_paginate {
        margin-top: 15px;
    }

    #pagtable tbody {
        width: 100%;
        display: table;
    }

    #pagtable tr {
        width: 100%;
        padding: 0px !important;
    }

    #pagtable td {
        width: 100% !important;
        padding: 0px !important;
    }

    #pagtable_wrapper div.dataTables_paginate {
        float: none !important;
    }

    #pagtable .list-group-item {
        padding: 0.1rem 0.7rem;
        margin: 0px;
        border: 1px solid #ddd;
        width: auto;
        margin-bottom: -3px !important;
    }

    div.modal-body {
        padding-left: 0px;
        padding-right: 0px;
        padding-bottom: 0px;
    }
</style>

<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.min.js"></script>

<script type="text/javascript">
    $(":input:not([readonly])[type='text']").on('click', function() {
        // search logic here

        $(this).select();
        //$("#"+input).val('');
        // this function will be executed on click of X (clear button)
    });

    function btoa_utf8(value) {
        return btoa(unescape(encodeURIComponent(value)));
    }

    function atob_utf8(value) {
        // return atob(value);
        return decodeURIComponent(escape(atob(value)));
    }

    function modalheader(clicked_id, clicked_name) {

        $("#search").val('');

        // $("#"+input).val('');

        $(".modalheader").empty().append("<b>" + clicked_name + "</b>");
    }

    // to display the modal title with base uom, currently only apply to issue forms
    function modalheader_uom(clicked_id, clicked_name) {
        $("#search").val('');
        var uom = $("#uom").val();
        $(".modalheader").empty().append("<b>" + clicked_name + " (" + uom + ")</b>");
    }

    $(document).ready(function() {

        $("#search").on("keyup", function() {


            var value = $(this).val().toLowerCase();
            $("#list li").filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });

        $("#myModal").on('hidden.bs.modal', function() {
            $('#sortdiv').attr('hidden', true);
            $("#sortField option[value=lot_num]").prop('selected', true);
            $("#sortBy option[value=asc]").prop('selected', true);
            $("#sortField, #sortBy").unbind("change");
        });
    });

    var options = {
        valueNames: ['item']
    };

    function sortChange(funcName, route, input_string, list, input) {
        $("#sortField, #sortBy").bind("change", function() {
            window[funcName](route, input_string, list, input);
        });
    }

    function select(input, elem) {

        // if (input.length > 0) {
        //     $(input).each(function(i, j) {
        //         selectLoop(j, elem);
        //     })
        // } else {
        selectLoop(input, elem)
        // }

        // console.log(elem, val);

    };

    function selectMultiInput(input, elem, data) {
        if (input.length > 0) {
            $(input).each(function(i, j) {
                if (j.id == 'from_loc' && data[j.id] === undefined)
                    selectLoop(j, btoa_utf8(data['loc_num']));
                else
                    selectLoop(j, btoa_utf8(data[j.id]));
            });
        } else {
            selectLoop(input, elem)
        }
    }

    function selectLoop(input, elem) {
        var val = atob_utf8(elem);
//console.log($('#' + input.id).prop('readonly'));
        if ($('#' + input.id).prop('readonly') == true) {
            return;
        }
        input.value = val;

        $('#' + input.id).focus();
        $('#' + input.id).change();

        if ($('#' + input.id).length == 0) {
            $("input[name='" + input.id + "']").change();
        }
    }

    function selectLot(input, elem, i) {

        var arrlot = $.cookie("arrLot");
        var arrSplit = arrlot.split(",");

        var lot = arrSplit[i];

        $('#' + input.id).focus();
        $('#' + input.id).change();

        if ($('#' + input.id).length == 0) {
            $("input[name='" + input.id + "']").change();
        }

        var whse_num = $('#whse_num').val();
        var item_num = $('#item_num').val();
        elem = atob_utf8(elem);
        var loc_num = elem;
        var lot_num = lot;

        var urlparm = "/getItemLocLot/" + btoa(whse_num) + '/' + btoa(item_num) + '/' + btoa(loc_num) + '/' + btoa(
            lot_num);

        $.getJSON(urlparm, function(data, status) {
            $.each(data, function(i, v) {

                var test = data.qty_available;
                $('#loc_num').val(data.loc_num);
                $('#lot_num').val(data.lot_num);
                $('#qty_available').val(data.qty_available);
                $('#qty_available_conv').val(test);
                $('#base_uom').val(data.uom);

            });
        });

    };



    function selectCPtest(input, elem) {
        input.value = elem;
        $('#' + input.id).focus();
        $('#' + input.id).change();

        if ($('#' + input.id).length == 0) {
            $("input[name='" + input.id + "']").change();
        }
        var whse_num = $('#whse_num').val();
        var item_num = $('#item_num').val();
        var item_num_hidden = "DoNotUsethisCode";

        var urlparm = "/getLocByRankReceiptCheck/" + btoa(whse_num) + '/' + btoa(item_num) + '/' + btoa(
            item_num_hidden);

        $.getJSON(urlparm, function(data, status) {
            $.each(data, function(i, v) {
                // console.log(data);
                $('#loc_num').val(data.loc_num);
                $('#item_desc').val(data.item_desc);

                //$('#expiry_date').val(v['expiry_date']);
            });
        });

        //display('/getLocByRankReceiptCheck','whse_num,item_num,item_num_hidden', 'loc_num,item_desc');
    };



    function selectionself(route, obj, list, input_string = "empty") {
        var field_input = obj[0];
        var input = obj[0].id;
        var urlparm = buildurl(route, input_string);

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
            console.log("Table already");
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        data_lists = "";
                        for (key in v) {
                            data_lists = 'data-' + key + '="' + v[key] + '" ' + data_lists;
                        }
                        if (v['wc_desc'])
                            desc = encodeURIComponent(v['wc_desc']);
                        else if (v['item_desc'])
                            desc = encodeURIComponent(v['item_desc']);
                        else
                            desc = "";
                        //     $("#list").append(
                        //         '<li  class="list-group-item list-group-item-action" onClick="select2Func(' +
                        //         input + ',this.id,$(this).data());" id="' + v[list] +
                        //         '" data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                        //         ' data-target="#myModal" > ' + outputString(v) + '</li>')
                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select2Func(' +
                            input + ',this.id,$(this).data());" id=\'' + v[list] +
                            '\' data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                            ' data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });

                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });
    };

    function selectwithdetails(input, elem, qty_shipped, qty_received, qty_loss, qty_receivabe) {
        // var urlparm = buildurl(route,input_string);
        input.value = elem;
        $('#' + input.id).focus();
        $('#' + input.id).change();
        $('#qty_shipped').val(qty_shipped);
        $('#qty_received').val(qty_received);
        $('#qty_receivable').val(qty_receivabe);

        $('#qty_receivable_conv').val(qty_receivabe);


        $('#qty_conv').val(qty_shipped);
        $('#qty_loss_conv').val(qty_loss);



        $('#qty_lost').val(qty_loss);

        var lot = $('#lot_num').val();
        var item_num = $('#item_num').val();

        // var urlparm = "/getExiprydate/" + btoa(item) +'/' + btoa(lot);


        if (lot) {
            var urlparm = "/getExiprydate/" + btoa(item_num) + '/' + btoa(lot);

            $.getJSON(urlparm, function(data, status) {
                $.each(data, function(i, v) {

                    $('#expiry_date').val(v['expiry_date']);
                });
            });
        }




    }


    function select2Func(input, elem, data) {
        input.value = elem;
        $input_id = (input.id).replace("_button", "")
        $('#' + $input_id).val(elem);
        $('#' + $input_id + '_uom').text(data.uom);
        $('input[name="' + $input_id + '_uom"]').val(data.uom);
        if (data.desc == "") {
            $('#' + $input_id + '_desc').text(data.desc);
        } else {
            $('#' + $input_id + '_desc').text(decodeURIComponent(data.desc));
        }
        $('input[name="' + $input_id + '_desc"]').val(decodeURIComponent(data.desc));
        $('#' + input.id).change()
        $('input:checkbox').change()
        $('#' + $input_id).change();
        $('#' + $input_id).focus();
    };

    function formatDecimal(input) {
        // var val = '' + (+input.value);
        var val = input.value;
        if (val) {
            val = val.split('\.');
            var out = val[0];
            while (out.length < 1) {
                out = '0' + out;
            }
            if (val[1]) {
                out = out + '.' + val[1]
                if (out.length < 6) out = out + '0';
            } else {
                out = out + '.00';
            }
            input.value = out;
        } else {
            input.value = '0.00';
        }
    }

    function buildurlNull(url, input) {
        var array = input.split(',');
        array.forEach(element => {
            console.log(element, $("#" + element).val());
            if (element == 'empty') {
                url = '' + url + '/';
            } else {
                var paramVal = $("#" + element).val() != "" ? btoa($("#" + element).val()) : "null";
                url = '' + url + '/' + paramVal;
            }
        });
        return url;
    }

    function buildurl(url, input) {
        var array = input.split(',');
        array.forEach(element => {
            // console.log(element,$("#" + element).val());
            if (element == 'empty') {
                url = '' + url + '/';
            } else {
                // var paramVal= $("#" + element).val()!=""? btoa($("#" + element).val()):"null";
                var paramVal = btoa($("#" + element).val() ?? "null");

                url = '' + url + '/' + paramVal;
            }
        });
        return url;
    }

    function display(route, input_string, fieldname) {
        var urlparm = buildurl(route, input_string);

        //alert(urlparm);
        return $.get(urlparm, function(data, status) {
            var array = fieldname.split(',');
            console.log(data);
            array.forEach(element => {
                // $("#" + element).val(data[element]).trigger('change');
                $("#" + element).val(data[element]);
                if ($("#" + element).val() == 1) {
                    $("#" + element).prop('checked', true);
                } else if ($("#" + element).val() == 0) {
                    $("#" + element).prop('checked', false);
                }
                if (element == 'lot_tracked' && data[element] == 1) {
                    $("#trlot").prop('hidden', false);
                } else if (element == 'lot_tracked' && data[element] == 0) {
                    $("#trlot").prop('hidden', true);
                }
                if(element == 'qty_available'){
                    $("#uom").trigger('change');
                }
            });
        });
    }

    function displayConvQty(route, input_string, fieldname) {
        //console.log(route);
        var urlparm = buildurl(route, input_string);

        $.get(urlparm, function(data, status) {
            var array = fieldname.split(',');
            array.forEach(element => {
                $("#" + element).val(data['qty_available_conv']);
            });
        });
    };

    function displayConvFactor(route, input_string, fieldname) {
        var urlparm = buildurl(route, input_string);
        $.get(urlparm, function(data, status) {
            var array = fieldname.split(',');
            array.forEach(element => {
                document.getElementById(element).value = data['conv_factor'];
            });
        });
    };

    function displayConvQtyShiped(route, input_string, fieldname, lotnum) {
        //console.log(route);
        var urlparm = buildurl(route, input_string);
        var lotvalue = $("#lot_num").val();
        $.get(urlparm, function(data, status) {
            var array = fieldname.split(',');
            $.each(data, function(i, v) {
                if (lotvalue == v['trn_lot']) {
                    console.log(data);
                    $("#qty_shipped").val(v['qty_shipped']);
                    $("#qty_received").val(v['qty_received']);

                    $("#qty_receivable_conv").val(v['qty_receivable']);


                }


            });

        });
    };

    // function displayConvFactor(route,input_string,fieldname){
    //     var urlparm = sbuildurl(route,input_string);
    //     $.get(urlparm,function(data,status){
    //         var array = fieldname.split(',');
    //         array.forEach(element => {
    //             document.getElementById(element).value = data['conv_factor'];
    //         });
    //     });
    // };


    {{-- function showtextbox(route,input_string,fieldname){
    var urlparm = buildurl(route,input_string);

    $.get(urlparm,function(data,status){
    var array = fieldname.split(',');

    array.forEach(element => {

    document.getElementById(element).value = data[element];

    if(data[element] == 1){
      var item1 = $("#input1").val();
      $("#trlot").empty().append('<td><label class="required">{{__('mobile.label.lot_num')}}</label></td>'
                                +'<td><input type="text" autocomplete="off" name="lot" onChange="checklotnull();" id="input6" class="form-control" placeholder="Lot" maxlength="50" ></td>'
                                +'<td><button type="button" name="Lot" tabindex="-1" onClick="selection(\'/SLLotLoc\',\'input3,input1,input2\',\'lot\',\'input6\');modalheader(this.id, this.name);" class="btn btn-icon btn-outline-secondary btn-square" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>');
      if($("#input6").val() == null || $("#input6").val() == ""){
        $.ajax({
        type: "GET",
        url: "{{ route('getnextlot','item') }}",
        contentType: "application/json; charset=utf-8",
        data: { item: item1 },
        datatype: "json",
          success: function(data){
            $("#input6").val(data);
          },
          error: function(){
            alert("There has been an error occured.");
          }
        });
      }
    }
    if(data[element] == 0 || data[element] == "" || data[element] == null){
      $("#trlot").empty();
      $('#'+element).attr('');
    }
    });
  });
}; --}}

    function outputString(data) {
        var string = Object.values(data);
        string = string.filter(element => {
            return element !== null && element !== undefined && element !== '';
        }).map(escapeHtml);
        string = string.join('&nbsp;&nbsp;&nbsp;| &nbsp;&nbsp;');
        return string;
    }

    function outputString1(data) {
        var string = Object.values(data);
        string = string.join(', ');
        return string;
    }

    function outputStringMultiLine(data) {
        var string = Object.values(data);
        string = string.filter(element => {
            return element !== null && element !== undefined; //  && element !== ''
        }).map(escapeHtml);

        var lastString = '';
        for (i = 1; i <= (string.length); i++) {
            if (i == string.length)
                lastString = lastString + string[i - 1];
            else if (i % 2 != 0)
                lastString = lastString + string[i - 1] + '&nbsp;&nbsp;|&nbsp;&nbsp;';
            else
                lastString = lastString + string[i - 1] + '<br>';
        }
        return lastString;
    }

    function escapeHtml(unsafe) {
        if (unsafe && typeof unsafe == 'string') {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        return unsafe;
    }


    function selectionCP(route, input_string, list, input) {
        console.log(route, input_string, list, input);
        var urlparm = buildurl(route, input_string);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {

                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="selectCPtest(' +
                        //     input + ',this.id);" id="' + v[list] +
                        //     '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +
                        //     '</li>')
                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectCPtest(' +
                            input + ',this.id);" id=\'' + v[list] +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });


    }

    function selectionNull(route, input_string, list, input) {
        var urlparm = buildurlNull(route, input_string);

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        //

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                // console.log(data,list);

                var jsonData = [];

                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select(' +
                            input + ',this.id);" id=\'' + btoa_utf8(v[list]) +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }

            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });

    }

    function selectionMultiLineInput(route, input_string, list, input) {
        $("#sortdiv").attr('hidden', false);

        if (route === '/getLotNumPallet') {
            $("#sortdiv option[value='qty_contained'").attr('hidden', false);
            $("#sortdiv option[value='qty_available'").attr('hidden', true);
        } else {
            $("#sortdiv option[value='qty_available'").attr('hidden', false);
            $("#sortdiv option[value='qty_contained'").attr('hidden', true);
        }

        $("#sortField, #sortBy").unbind("change");
        sortChange('selectionMultiLineInput', route, input_string, list, input);

        var urlparm = buildurlNull(route, input_string);

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        var list_input = input.split(',');
        var list_data = list.split(',');

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];

                if (data && data.length) {
                    $.each(data, function(i, v) {

                        data_lists = "";
                        for (key in v) {
                            data_lists = 'data-' + key + '="' + v[key] + '" ' + data_lists;
                        }
                        desc = "";

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectMultiInput([' +
                            list_input + '],this.id,$(this).data());" id=\'' + v[list] +
                            '\' data-toggle="modal"  data-desc="' + desc + '" ' + data_lists +
                            'data-target="#myModal" > ' + outputStringMultiLine(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        "pageLength": 5,
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }

            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });
    }

    function doStoreJobId(id) {
        $("#job_id").val(id);
    }

    function selectionDetails(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);
        //alert(urlparm);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        //

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                // console.log(data,list);

                var jsonData = [];

                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        //console.log(v[list]+" :: "+ input +"<>"+ this.id +"<>"+ input)  ;


                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" > ' +
                            outputString(v) + '</div>';


                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        "search": false,
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });

                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }

            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });

    }

    function selection(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);
        //alert(urlparm);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        //
        // console.log(urlparm);
        // urlparm= urlparm+"?return_param="+list;
        // console.log(urlparm);
        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                // console.log(data,list);

                var jsonData = [];

                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        //console.log(v[list]+" :: "+ input +"<>"+ this.id +"<>"+ input)  ;
                        // console.log(v,list,(v[list]));
                        var idd = btoa_utf8(v[list]);
                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select(' +
                            input + ',this.id);" id=\'' + idd +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';


                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }

            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });

    };

    function selectionTOShip(route, input_string, list, input) {

        var urlparm = buildurl(route, input_string);

        $.removeCookie('lot');

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {

                var jsonData = [];
                var arrLot = [];

                //input.value="";
                if (data && data.length) {

                    $.each(data, function(i, v) {

                        arrLot.push(v['lot_num']);

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;max-width:545px;" class="list-group-item list-group-item-action" onClick="selectLot(' +
                            input + ',this.id, ' + i + ');" id=\'' + btoa_utf8(v[list]) +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';

                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                    $.cookie('arrLot', arrLot);

                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }

            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });

    };

    function selectionZone(route, input_string, list, input) {
        // console.log(route,input_string,list,input);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();
        var urlparm = buildurl(route, input_string);


        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {

                //input.value="";
                var jsonData = [];

                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        // console.log(outputString(v));

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select(' +
                            input + ',this.id);" id=\'' + btoa_utf8(v[list]) +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });
                    // console.log(jsonData);

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }


            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });

    };

    function selection1(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        //

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {

                //input.value="";
                var jsonData = [];
                if (data && data.length) {
                    $.each(data, function(i, v) {

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select(' +
                            input + ',this.id);" id=\'' + btoa_utf8(v[list]) +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString1(v) +
                            '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="select(' +
                        //     input + ',this.id);" id="' + v[list] +
                        //     '" data-toggle="modal" data-target="#myModal" > ' + outputString1(v) +
                        //     '</li>')
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#list").append(
                    '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                )
            });

    };


    function selectionTONum(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                // alert(urlparm);
                var jsonData = [];
                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {

                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="selectwithdetails(' +
                        //     input + ',this.id, ' + v['qty_shipped'] + ' ,' + v['qty_received'] + ',' +
                        //     v['qty_loss'] + ',' + v['qty_receivable'] + '  );" id="' + v[list] +
                        //     '" data-toggle="modal" data-target="#myModal" > ' + v['trn_lot'] + ' | ' +
                        //     v['qty_receivable'] + '</li>')

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select(' +
                            input + ',this.id);" id=\'' + btoa_utf8(v[list]) +
                            '\' data-toggle="modal" data-target="#myModal" > ' + v['trn_lot'] + ' | ' +
                            v['qty_receivable'] + '</div>';
                            // '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectwithdetails(' +
                            // input + ',this.id, ' + v['qty_shipped'] + ' ,' + v['qty_received'] + ',' +
                            // v['qty_loss'] + ',' + v['qty_receivable'] + '  );" id=\'' + v[list] +
                            // '\' data-toggle="modal" data-target="#myModal" > ' + v['trn_lot'] + ' | ' +
                            // v['qty_receivable'] + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });

    };





    function selectionwithcheckreasoncode(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        //console.log(v[list] + '>> ' +input + list);

                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="selectWithCheckReasonCode(' +
                        //     input + ',this.id);" id="' + v[list] +
                        //     '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +
                        //     '</li>')

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectWithCheckReasonCode(' +
                            input + ',this.id);" id=\'' + v[list] +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )

                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });

    };

    function selectWithCheckReasonCode(input, elem) {
        input.value = elem;
        $('#' + input.id).focus();
        $('#' + input.id).change();

        var reason_code = input.value;
        var reason_class = $('#reason_class').val();
        //var reason_class = "POReturn";

        var urlparm = "/getReasonCodeCheck/" + btoa(reason_code) + "/" + btoa(reason_class);

        $.getJSON(urlparm, function(data, status) {
            $.each(data, function(i, v) {
                console.log(v['sync_status']);
                if (v['sync_status'] == "N") {
                    $('#sap_base_entry').css('display', 'none');
                    $('#sap_base_line').css('display', 'none');
                    $('#document_num').css('display', 'none');
                    $('#last_return_pair').css('display', 'none');

                    // document.getElementById('sap_base_entry').style.display = 'none';
                    // document.getElementById('sap_base_line').style.display = 'none';
                    // document.getElementById('document_num').style.display = 'none';
                    // document.getElementById('last_return_pair').style.display = 'none';
                } else if (v['sync_status'] == "Y") {
                    $('#sap_base_entry').css('display', 'block');
                    $('#sap_base_line').css('display', 'block');
                    $('#document_num').css('display', 'block');
                    $('#last_return_pair').css('display', 'block');
                    // document.getElementById('sap_base_entry').style.display = 'block';
                    // document.getElementById('sap_base_line').style.display = 'block';
                    // document.getElementById('document_num').style.display = 'block';
                    // document.getElementById('last_return_pair').style.display = 'block';
                }

            });
        });










        if ($('#' + input.id).length == 0) {
            $("input[name='" + input.id + "']").change();
        }
    }



    function selectionWithSuffix(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);

        //   alert(urlparm);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        // var JobIdSufix = v[list] + "|" + v['suffix'];
                        // var JobIdSufix = ('"'+v['suffix']+'"').toString();
                        var JobIdSufix1 = v['suffix'];
                        var JobsuffixLength = v['suffix'].length;

                        console.log(JobsuffixLength);


                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="selectWithSuffix(' +
                        //     input + ',this.id,' + JobIdSufix1 + ',' + JobsuffixLength + ');" id="' + v[
                        //         list] + '" data-toggle="modal" data-target="#myModal" > ' +
                        //     outputString(v) + '</li>')

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectWithSuffix(' +
                            input + ',this.id,' + JobIdSufix1 + ',' + JobsuffixLength + ');" id="' + v[
                                list] + '" data-toggle="modal" data-target="#myModal" > ' +
                            outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });

    };

    function selectionWithDesc(route, input_string, list, input) {
        var urlparm = buildurl(route, input_string);
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        var list_data = list.split(',');
        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="selectWithDesc(' +
                        //     input + ',this.id);" ' +
                        //     'id="' + v[list_data] + '" ' +
                        //     'data_' + list_data[1] + '"="' + v[list_data][1] + '" ' +
                        //     'data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</li>')

                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectWithDesc(' +
                            input + ',this.id);" ' +
                            'id="' + v[list_data] + '" ' +
                            'data_' + list_data[1] + '"="' + v[list_data][1] + '" ' +
                            'data-toggle="modal" data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });
    };



    //var field_input = obj[0];
    //  var input = obj[0].id;

    function selectionWithQuantity(route, input_string, list, input) {

        if(input_string.includes('sortField')) {
            $("#sortdiv").attr('hidden',false);

            if(route  === '/getLotNumPallet') {
                $("#sortdiv option[value='qty_contained'").attr('hidden',false);
                $("#sortdiv option[value='qty_available'").attr('hidden',true);
            }
            else{
                $("#sortdiv option[value='qty_available'").attr('hidden',false);
                $("#sortdiv option[value='qty_contained'").attr('hidden',true);
            }

            $("#sortField, #sortBy").unbind("change");
            sortChange('selectionWithQuantity', route, input_string, list, input);
        }

        var urlparm = buildurlNull(route, input_string);

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        var list_data = list.split(',');
        var list_input = input.split(',');
        console.log(list_input[0] + " <><> " + list_input[1]);
        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        data_lists = "";
                        for (key in v) {
                            data_lists = 'data-' + key + '="' + v[key] + '" ' + data_lists;
                        }
                        desc = "";
                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="selectWithQtty(' +
                        //     list_input[0] + ',this.id,$(this).data());" id="' + v[list] +
                        //     '" data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                        //     ' data-target="#myModal" > ' + outputString(v) + '</li>')
                        if(input_string.includes('sortField')) {
                            var itemHtml =
                                '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectWithQtty(' +
                                list_input[0] + ',this.id,$(this).data());" id=\'' + v[list] +
                                '\' data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                                ' data-target="#myModal" > ' + outputStringMultiLine(v) + '</div>';
                        }
                        else{
                            var itemHtml =
                                '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="selectWithQtty(' +
                                list_input[0] + ',this.id,$(this).data());" id=\'' + v[list] +
                                '\' data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                                ' data-target="#myModal" > ' + outputString(v) + '</div>';
                        }
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        "pageLength": 5,
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });
    };


    function selectWithQtty(input, elem, data) {

        var qty_available = data.qty_available;
        var lotnum = data.lot_num;
        var qty_to_pic = $('#qty_to_unpick').val();
        var from_locSelect = data.loc_num.toString().toLowerCase();
        var dbpack_loc = $('#pack_loc').val().toString().toLowerCase();

        // alert(dbpack_loc + " <> "+ from_locSelect);
        if (dbpack_loc == from_locSelect) {
            Alert.warning('Pick from loc not allow same as packing location.');
            $(this).val('');
            $('#from_loc').val('');
            return false;
        }
        console.log(data + "<>" + dbpack_loc + " <> " + data.loc_num);

        // alert(qty_available + " :: "+ qty_to_pic);
        if (parseInt(qty_available) < parseInt(qty_to_pic)) {
            Alert.warning('Quantity Available not enough to unpick.');
            $('#lot_num').val('');
            $('#qty_available').val('');
            $('#qty_available_conv').val('');


        } else {
            $("#loc_info").html("");
            $("#checkLoc").html("");
            $("#locnumnotexist").html('');

            $('#qty_available').val(qty_available);
            $('#lot_num').val(lotnum);
            $('#from_loc').val(data.loc_num);
            $('#base_uom').val($('#uom').val());

            $('#qty_available_conv').val(qty_available);
            $(".submitloader").attr("disabled", false);

        }


    }

    function selectWithSuffix(input, elem, data, JobsuffixLength) {

        var suffix = elem + " | " + pad(data, JobsuffixLength);
        $('#' + input.id).val(suffix);


    }

    function pad(n, width, z) {
        z = z || '0';
        n = n + '';
        return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
    }











    function clearItems() {
        $("#item_desc,#uom,#base_uom").val("");
    }

    function clearLocs() {
        $("#qty_on_hand,#qty_on_hand_conv,#qty").val("");

        // $("#lot_num,#qty_on_hand,#qty_on_hand_conv,#qty").val("");
        $("#uom").val($("#base_uom").val());
    }

    function onLotChange() {
        // return;
        if ($("#lot_num").val() && $("#loc_num").val()) {
            ajaxurl = "{{ route('lotlocv', ['lot_num', 'item_num', 'loc_num', 'whse_num']) }}";
            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
            url = url.replace('item_num', btoa($("#item_num").val()));
            url = url.replace('loc_num', btoa($("#loc_num").val()));
            url = url.replace('whse_num', btoa($("#whse_num").val()));
            // console.log(url);
            $.get(url, function(data) {
                if (data == 'not exist') {
                    clearLocs();
                } else {
                    display('/displayLotQuantity', 'item_num,whse_num,loc_num,lot_num',
                        'qty_on_hand,qty_on_hand_conv,qty_available,qty_available_conv')
                }
            });
        } else {
            $("#lot_num").focus();
            clearLocs();
        }

    }

    function onLotChangeQtyAvailable() {
        if ($("#lot_num").val() && $("#loc_num").val()) {
            ajaxurl = "{{ route('lotlocv', ['lot_num', 'item_num', 'loc_num', 'whse_num']) }}";
            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
            url = url.replace('item_num', btoa($("#item_num").val()));
            url = url.replace('loc_num', btoa($("#loc_num").val()));
            url = url.replace('whse_num', btoa($("#whse_num").val()));

            $.get(url, function(data) {
                if (data == 'not exist') {
                    clearLocs();
                } else {
                    display('/displayLotQuantity', 'item_num,whse_num,loc_num,lot_num',
                        'qty_available,qty_available_conv')
                }
            });
        } else {
            $("#lot_num").focus();
            clearLocs();
        }

    }

    //New

    function selectionself2(route, obj, list, input_string = "empty") {
        var field_input = obj[0];
        var input = obj[0].id;
        var item_num = $('#item_num').val();
        var urlparm = buildurl(route, 'item_num');

        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                var jsonData = [];
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        data_lists = "";
                        for (key in v) {
                            data_lists = 'data-' + key + '="' + v[key] + '" ' + data_lists;
                        }
                        if (v['wc_desc'])
                            desc = encodeURIComponent(v['wc_desc']);
                        else if (v['item_desc'])
                            desc = encodeURIComponent(v['item_desc']);
                        else
                            desc = "";
                        // $("#list").append(
                        //     '<li  class="list-group-item list-group-item-action" onClick="select3(' +
                        //     input + ',this.id,$(this).data());" id="' + v[list] +
                        //     '" data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                        //     ' data-target="#myModal" > ' + outputString(v) + '</li>')
                        var itemHtml =
                            '<div style="cursor:pointer;word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action" onClick="select3(' +
                            input + ',this.id,$(this).data());" id=\'' + v[list] +
                            '\' data-toggle="modal" data-desc="' + desc + '" ' + data_lists +
                            ' data-target="#myModal" > ' + outputString(v) + '</div>';
                        jsonData.push({
                            'item': itemHtml
                        });
                    });
                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [{
                                "data": "item"
                            },

                        ]
                    });
                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )
                    // $("#list").append(
                    //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    //     )
                }
            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )
                // $("#list").append(
                //     '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                //     )
            });
    };

    function select3(input, elem, data) {
        input.value = elem;
        $input_id = (input.id).replace("_button", "")
        $('#' + $input_id).val(elem);
        $('#' + $input_id + '_uom').text(data.uom);
        $('input[name="' + $input_id + '_uom"]').val(data.uom);
        if (data.desc == "") {
            $('#' + $input_id + '_desc').text(data.desc);
        } else {
            $('#' + $input_id + '_desc').text(decodeURIComponent(data.desc));
        }
        $('input[name="' + $input_id + '_desc"]').val(decodeURIComponent(data.desc));
        $('#' + input.id).change()
        $('input:checkbox').change()
        $('#' + $input_id).change();
        $('#' + $input_id).focus();
    };

    function select2Ajax(elem) {
        var target = $(elem).data('target')
        var url = $(elem).data('url')

        var tags = $(elem).data('tags') ? true : false;
        //    console.log(tags);
        //    var $p = $(elem).parent().parent();
        var $p = $('body');


        $(elem).select2Func({
            width: '100% ',
            cache: true,
            dropdownParent: $p,
            placeholder: $(elem).attr('placeholder'),
            tags: tags,
            ajax: {
                url: function(params) {
                    //console.log(params)
                    var term = "";
                    if (params) {
                        term = params.term;
                    }
                    var selectedCont = 0;
                    if (target) {
                        var selectedCont = $(target).val();
                    }

                    //                    var term = _this.select2Func('data')[0].text;
                    return url + "?id=" + selectedCont;
                },
            }
        });
    }

    function selectionPaged(route, input_string, list, input, paged) {
        var urlparm = buildurl(route, input_string);
        var page = paged ? paged : 1;
        urlparm = urlparm + "?page=" + page;
        //      console.log(urlparm);
        $("#list").parent().find('.paginationBox').remove();
        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(res, status) {


                //input.value="";
                data = res.data;
                //           console.log(data);
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        //console.log(v[list] + '>> ' +input + list);

                        $("#list").append(
                            '<li   class="list-group-item list-group-item-action" onClick="select(' +
                            input + ',this.id);" id=\'' + btoa_utf8(v[list]) +
                            '\' data-toggle="modal" data-target="#myModal" > ' + outputString(v) +
                            '</li>')
                    });
                } else {
                    $("#list").append(
                        '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                    )
                }
                var next_button = "";
                var prev_button = "";
                if (res.next_page_url) {
                    var next_page = res.current_page + 1;
                    next_button = '<button onclick="selectionPaged(\'' + route + '\',\'' + input_string + '\',\'' +
                        list + '\',\'' + input + '\',\'' + next_page +
                        '\')" type="button" class="btn btn-primary selection_page_btn next_page_selection" >Next</button>'
                }
                if (res.prev_page_url) {
                    var prev_page = res.current_page - 1;
                    prev_button = '<button onclick="selectionPaged(\'' + route + '\',\'' + input_string + '\',\'' +
                        list + '\',\'' + input + '\',\'' + prev_page +
                        '\')" type="button" class="btn btn-warning selection_page_btn prev_page_selection" >Prev</button>'
                }

                $("#list").parent().append("<div class='text-center paginationBox mt-2 mb-2'>" + prev_button + "" +
                    next_button + "</div>")

            })
            .fail(function() {
                $("#list").append(
                    '<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>'
                )
            });

    }

    var selected_value = {};
    function selectionMultiSelect(route, input_string, list, value_name) {
        // var urlparm = buildurl(route, input_string);
        var urlparm = route + "?excluded=" + $('[id="' + input_string + '"]').val();
        if ($.fn.DataTable.isDataTable('#pagtable')) {
            $('#pagtable').DataTable().destroy();
        }
        $("#list").empty();
        $('#pagtable tbody').empty();

        selected_value[input_string] = [];

        //
        // console.log(urlparm);
        // urlparm= urlparm+"?return_param="+list;
        // console.log(urlparm);
        $(".list-group-item-action").remove();
        $.getJSON(urlparm, function(data, status) {
                // console.log(data,list);

                var jsonData = [];

                //input.value="";
                if (data && data.length) {
                    $.each(data, function(i, v) {
                        //console.log(v[list]+" :: "+ input +"<>"+ this.id +"<>"+ input)  ;
                        // console.log(v,list,(v[list]));
                        var idd = btoa_utf8(v[list]);
                        // var checkboxHtml =
                        //     "<div class='list-group-item'> " +
                        //         "<input type='checkbox' value='" + outputString(v) + "'>" +
                        //     "</div>";
                        var itemHtml =
                            '<label style="word-wrap:break-word;word-break:break-word;" class="list-group-item list-group-item-action"> ' +
                                "<input type='checkbox'  value='" + v[value_name] + "' style='margin-right: 10px' onchange='applyCheckbox(this, \"" + input_string + "\")'>" +
                                outputString(v) +
                            '</label>';


                        jsonData.push({
                            // 'id': checkboxHtml,
                            'item': itemHtml
                        });
                        // $("#list").append('<li style="word-wrap:break-word;" class="list-group-item list-group-item-action" onClick="select(' + input + ',this.id);" id="' + v[list] + '" data-toggle="modal" data-target="#myModal" > ' + outputString(v) +  '</li>')
                    });

                    oTable = $('#pagtable').DataTable({
                        "stateSave": false,
                        "data": jsonData,
                        'ordering': false,
                        "dom": 'tp',
                        // dom: 'Btip<"actions">',
                        "columns": [
                            // {
                            //     "data": "id",
                            //     "className": "d-block"
                            // },
                            { "data": "item" },

                        ],
                        // "render": function (row, type, val, meta) {
                        //     return  '<input type="checkbox"/>';
                        // }
                    });

                    // $('#pagtable tr').addClass('d-block');
                    $('#pagtable thead tr').addClass('d-block');
                    // $('#pagtable tr td:first-child').css('cssText', 'width: auto !important; padding-right: 10px !important');

                    $('#search').keyup(function() {
                        oTable.search($(this).val()).draw();
                    });
                } else {
                    $("#pagtable").append(
                        '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                    )

                    // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
                }

            })
            .fail(function() {
                $("#pagtable").append(
                    '<tr><td><div  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</div></td></tr>'
                )

                // $("#list").append('<li  class="list-group-item list-group-item-action" data-toggle="modal" data-target="#myModal" >{{ __('error.mobile.recordnotfound') }}</li>' )
            });

        var button_apply = '<div class="text-right" id="additionalModalButton">' +
            '<button class="btn btn-primary" onclick="applySelection(\'' + input_string + '\')" data-toggle="modal" data-target="#myModal">Add</button>' +
        '</div>';

        $('#myModal .modal-body').append(button_apply);
    };

    function applyCheckbox(option, input_string)
    {
        var value = $(option).val();
        if($(option).is(':checked')) {
            selected_value[input_string].push(value);
        }
        else
        {
            const index = selected_value[input_string].indexOf(value);
            if (index > -1)
            {
                selected_value[input_string].splice(index, 1);
            }
        }
    }

    function applySelection(input_string)
    {
        // var selectedItems = $('#pagtable').DataTable().rows('.selected').data();
        // $('[id="' + input_string + '"]').val(selected_value[input_string]).change();
        var myElement = $('[id="' + input_string + '"]');
        var myData = $.map(selected_value[input_string], function(v) { return {'id': v, 'text': v}  });

        $.each(selected_value[input_string], function(i, v) {
            var option = new Option(v, v, true, true);
            myElement.append(option).trigger('change');
        });

        myElement.trigger({
            type: 'select2:select',
            params: {
                data: myData
            }
        });

    };


    $(document).ready(function() {
        $('#myModal').on('hidden.bs.modal', function (e) {
            $('#additionalModalButton').remove();
        });
    });
</script>
