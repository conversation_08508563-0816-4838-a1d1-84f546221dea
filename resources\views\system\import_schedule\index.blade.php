@extends('layout.app')

@section('content')
<div class="card">
    <div class="card-header">
        <h4 class="card-title">Smart Field Mapping Import</h4>
    </div>
    <div class="card-body">
        <form id="importForm" enctype="multipart/form-data" method="POST">
            @csrf
            <div class="form-group">
                <label for="objectName">Object Name <span class="text-danger">*</span></label>
                <select id="objectName" name="objectName" class="form-control" required>
                    <option value="">-- Select Object --</option>
                    {{-- Populate with objects where Application Type = Web and Module = Maintenance --}}
                </select>
            </div>
            <div class="form-group">
                <label for="importFile">File <span class="text-danger">*</span></label>
                <input type="file" id="importFile" name="importFile" class="form-control" accept=".xlsx,.xls,.csv" required />
                <small class="form-text text-muted">Supported formats: .xlsx, .xls, .csv</small>
            </div>
            <button type="button" id="mapColumnsBtn" class="btn btn-primary" disabled>Map Columns</button>
            
            <!-- Add template options -->
            <div id="templateSection" class="mt-3" style="display: none;">
                <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input" id="saveTemplate">
                    <label class="form-check-label" for="saveTemplate">Save as Template</label>
                </div>
                <div id="templateNameGroup" class="form-group" style="display: none;">
                    <label for="templateName">Template Name</label>
                    <input type="text" id="templateName" class="form-control" placeholder="Enter template name">
                </div>
                
                <button type="button" id="importBtn" class="btn btn-success" onclick="submitImport()">
                    <i class="fas fa-upload"></i> Start Import
                </button>
            </div>
            
            <div id="uploadStatus" class="mt-3"></div>
        </form>
        <div id="previewSection" style="display:none;">
            <h5 class="mt-4">File Preview</h5>
            <p>Rows: <span id="rowCount"></span></p>
            <div class="table-responsive">
                <table class="table table-bordered" id="previewTable">
                    <thead></thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Toggle template name field
    $('#saveTemplate').on('change', function() {
        if ($(this).is(':checked')) {
            $('#templateNameGroup').slideDown();
        } else {
            $('#templateNameGroup').slideUp();
        }
    });

    // 1. Populate Object Name dropdown
    $.getJSON("/system/import-schedule/objects", function(data) {
        var $dropdown = $('#objectName');
        $dropdown.empty().append('<option value="">-- Select Object --</option>');
        $.each(data, function(i, obj) {
            $dropdown.append('<option value="' + obj.id + '">' + obj.name + '</option>');
        });
    });

    // 2. Handle file selection and upload
    $('#importFile').on('change', function() {
        var fileInput = this;
        var file = fileInput.files[0];
        var $status = $('#uploadStatus');
        var $mapBtn = $('#mapColumnsBtn');
        var $previewSection = $('#previewSection');
        var $rowCount = $('#rowCount');
        var $previewTable = $('#previewTable');
        $status.hide();
        $mapBtn.prop('disabled', true);
        $previewSection.hide();
        if (!file) return;
        var ext = file.name.split('.').pop().toLowerCase();
        if ($.inArray(ext, ['xlsx','xls','csv']) === -1) {
            $status.text('Invalid file type.').show();
            fileInput.value = '';
            return;
        }
        if (file.size === 0) {
            $status.text('File is empty.').show();
            fileInput.value = '';
            return;
        }
        $status.text('Uploading...').show();
        var formData = new FormData($('#importForm')[0]);
        $.ajax({
            url: '/system/import-schedule/upload-preview',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(resp) {
                $status.hide();
                $mapBtn.prop('disabled', false);
                $previewSection.show();
                $rowCount.text(resp.rowCount);
                // Store the preview data for mapping
                previewData = resp.preview;
                console.log('Preview data stored:', previewData);
                // Render preview table
                $previewTable.find('thead,tbody').empty();
                if (resp.preview && resp.preview.length > 0) {
                    var header = resp.preview[0];
                    var thead = '<tr>';
                    for (var i=0; i<header.length; i++) {
                        thead += '<th>' + (header[i] || '&nbsp;') + '</th>';
                    }
                    thead += '</tr>';
                    $previewTable.find('thead').html(thead);
                    var tbody = '';
                    for (var r=1; r<resp.preview.length; r++) {
                        tbody += '<tr>';
                        for (var c=0; c<resp.preview[r].length; c++) {
                            tbody += '<td>' + (resp.preview[r][c] || '&nbsp;') + '</td>';
                        }
                        tbody += '</tr>';
                    }
                    $previewTable.find('tbody').html(tbody);
                }
            },
            error: function(xhr) {
                var msg = xhr.responseJSON && xhr.responseJSON.error ? xhr.responseJSON.error : 'Upload failed.';
                $status.text(msg).show();
                $mapBtn.prop('disabled', true);
                $previewSection.hide();
            }
        });
    });

    // --- Step 2: AI-Assisted Column Mapping ---
    var previewData = null;
    var objectKey = null;

    // Store preview data after successful upload
    $('#importFile').on('change', function() {
        // Patch: also store previewData for mapping
        var origSuccess = $.ajaxSettings.success;
        $.ajaxSettings.success = function(resp) {
            if (resp && resp.preview) previewData = resp.preview;
            if (origSuccess) origSuccess.apply(this, arguments);
        };
    });

    // Global error handler
    window.onerror = function(message, source, lineno, colno, error) {
        console.error('Global error:', message, 'at', source, 'line', lineno, 'column', colno, 'Error object:', error);
    };

    // Function to submit the import
    function submitImport() {
        const mapping = {};
        let hasErrors = false;
        
        // Collect all mappings
        $('.source-col').each(function() {
            const target = $(this).data('target');
            const source = $(this).val();
            const defaultValue = $(`input.default-val[data-target="${target}"]`).val();
            const transform = $(`select.transform[data-target="${target}"]`).val();
            
            if (source) {
                mapping[target] = {
                    source: source,
                    default: defaultValue || null,
                    transform: transform || null
                };
            }
        });
        
        // Validate at least one mapping exists
        if (Object.keys(mapping).length === 0) {
            alert('Please map at least one column before importing.');
            return;
        }
        
        // Show loading state
        $('#importBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Importing...');
        
        // Get template name if saving as template
        const saveTemplate = $('#saveTemplate').is(':checked');
        const templateName = saveTemplate ? $('#templateName').val() : null;
        
        if (saveTemplate && !templateName) {
            alert('Please enter a template name or uncheck "Save as Template"');
            $('#importBtn').prop('disabled', false).text('Start Import');
            return;
        }
        
        // Prepare the data
        const importData = {
            _token: $('meta[name="csrf-token"]').attr('content'),
            object: objectKey,
            mapping: mapping,
            preview: previewData,
            template_name: templateName,
            save_template: saveTemplate
        };
        
        console.log('Submitting import:', importData);
        
        // Submit the import
        $.ajax({
            url: '{{ route("importSchedule.execute") }}',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(importData),
            success: function(response) {
                if (response.success) {
                    // Show success message with link to status
                    const statusUrl = response.status_url;
                    $('#importStatus')
                        .html(`
                            <div class="alert alert-success">
                                <h5>Import Started</h5>
                                <p>Your import has been queued for processing.</p>
                                <p><a href="${statusUrl}" class="btn btn-sm btn-outline-primary">View Status</a></p>
                                <div class="progress mt-2">
                                    <div id="importProgress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                </div>
                                <div id="importDetails" class="mt-2 small"></div>
                            </div>
                        `)
                        .show();
                    
                    // Start polling for status
                    pollImportStatus(response.import_id);
                    
                } else {
                    // Show error message
                    $('#importStatus')
                        .html(`<div class="alert alert-danger">${response.error || 'Failed to start import'}</div>`)
                        .show();
                    
                    $('#importBtn').prop('disabled', false).text('Start Import');
                }
            },
            error: function(xhr) {
                const errorMsg = xhr.responseJSON?.error || 'Failed to start import';
                $('#importStatus')
                    .html(`<div class="alert alert-danger">${errorMsg}</div>`)
                    .show();
                
                $('#importBtn').prop('disabled', false).text('Start Import');
            }
        });
    }

    // Function to poll import status
    function pollImportStatus(importId) {
        const checkStatus = function() {
            $.get(`{{ route('importSchedule.status', '') }}/${importId}`, function(response) {
                console.log('Import status:', response);
                
                // Update progress
                const progress = response.total > 0 ? Math.round((response.processed / response.total) * 100) : 0;
                $('#importProgress').css('width', `${progress}%`);
                
                // Update details
                const details = `
                    Processed: ${response.processed} of ${response.total}<br>
                    Success: ${response.success}<br>
                    Failed: ${response.failed}
                `;
                $('#importDetails').html(details);
                
                // Handle completion
                if (response.status === 'completed' || response.status === 'failed') {
                    $('#importProgress')
                        .removeClass('progress-bar-animated progress-bar-striped')
                        .addClass(response.status === 'completed' ? 'bg-success' : 'bg-danger');
                    
                    if (response.status === 'completed') {
                        const successMsg = `
                            <div class="alert alert-success">
                                <h5>Import Completed Successfully</h5>
                                <p>Total: ${response.total} | Success: ${response.success} | Failed: ${response.failed}</p>
                                ${response.errors && response.errors.length > 0 ? 
                                    `<button class="btn btn-sm btn-outline-danger" onclick="$('#errorDetails').toggle()">Show Errors (${response.errors.length})</button>
                                    <div id="errorDetails" style="display: none; margin-top: 10px; max-height: 200px; overflow-y: auto;">
                                        <table class="table table-sm table-bordered">
                                            <tr><th>Row</th><th>Error</th></tr>
                                            ${response.errors.map(e => `<tr><td>${e.row}</td><td>${e.error}</td></tr>`).join('')}
                                        </table>
                                    </div>` : ''
                                }
                            </div>
                        `;
                        $('#importStatus').append(successMsg);
                    } else {
                        const errorMsg = `
                            <div class="alert alert-danger">
                                <h5>Import Failed</h5>
                                <p>${response.error || 'An unknown error occurred'}</p>
                            </div>
                        `;
                        $('#importStatus').append(errorMsg);
                    }
                    
                    // Re-enable the import button for a new import
                    $('#importBtn').prop('disabled', false).text('Start New Import');
                    
                } else {
                    // Continue polling
                    setTimeout(checkStatus, 2000);
                }
            }).fail(function() {
                // Retry on failure
                setTimeout(checkStatus, 2000);
            });
        };
        
        // Start polling
        checkStatus();
    }

    // Enable Map Columns only if preview loaded
    $('#mapColumnsBtn').off('click').on('click', function() {
        console.log('=== Map Columns Button Clicked ===');
        console.log('Button disabled state:', $(this).prop('disabled'));
        
        // Get and normalize the object key
        objectKey = $('#objectName').val().trim().toUpperCase();
        console.log('objectKey:', objectKey, 'previewData:', previewData);
        
        // Validate input
        if (!objectKey || !previewData) {
            const errorMsg = 'Missing required data. Please select an object type and upload a file.';
            console.error('Missing required data:', {
                hasObjectKey: !!objectKey,
                hasPreviewData: !!previewData
            });
            $('#uploadStatus')
                .html('<div class="alert alert-danger">' + errorMsg + '</div>')
                .show();
            return;
        }
        
        // Validate object key format
        if (!objectKey.match(/^[A-Z0-9_]+$/)) {
            const errorMsg = 'Invalid object format. Object key can only contain uppercase letters, numbers, and underscores.';
            console.error('Invalid object key format:', objectKey);
            $('#uploadStatus')
                .html('<div class="alert alert-danger">' + errorMsg + '</div>')
                .show();
            return;
        }
        // UI: analysing state
        $('#uploadStatus').text('Analysing data...').show();
        $('#objectName').prop('disabled', true);
        $('#importFile').prop('disabled', true);
        $(this).prop('disabled', true);
        // Remove previous mapping UI
        $('#mappingSection').remove();
        // Get CSRF token from meta tag
        var csrfToken = $('meta[name="csrf-token"]').attr('content');
        
        // Log the data being sent
        console.log('Sending data to server:', {
            object: objectKey,
            previewData: previewData,
            previewDataLength: previewData ? previewData.length : 0
        });
        
        // Request mapping analysis
        $.ajax({
            url: '/system/import-schedule/analyse-mapping',
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({ 
                _token: csrfToken,
                object: objectKey, 
                preview: previewData 
            }),
            contentType: 'application/json',
            dataType: 'json',
            success: function(resp) {
                console.log('AJAX success - Response:', resp);
                $('#uploadStatus').hide();
                
                // Check if response has the expected structure
                if (resp && resp.targetColumns && resp.sourceColumns && resp.mapping) {
                    renderMappingUI(resp);
                } else {
                    console.error('Unexpected response format:', resp);
                    var errorMsg = 'Error: Invalid response format from server. ';
                    if (resp && resp.message) {
                        errorMsg += resp.message;
                    }
                    $('#uploadStatus').text(errorMsg).show();
                    
                    // Re-enable controls
                    $('#objectName, #importFile, #mapColumnsBtn').prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', status, error);
                console.error('Response Text:', xhr.responseText);
                
                var errorMsg = 'Error: ';
                if (xhr.status === 419) {
                    errorMsg += 'Session expired. Please refresh the page and try again.';
                } else if (xhr.status === 500) {
                    errorMsg += 'Server error occurred. Please check the console for details.';
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += xhr.responseJSON.message;
                } else {
                    errorMsg += 'Failed to process mapping. Please try again.';
                }
                
                $('#uploadStatus').html(errorMsg + '<br>Status: ' + xhr.status + ' ' + error).show();
                
                // Re-enable controls
                $('#objectName, #importFile, #mapColumnsBtn').prop('disabled', false);
            }
        });
    });

    function renderMappingUI(data) {
        console.log('renderMappingUI called with data:', data);
        
        // Validate response data
        if (!data || !data.targetColumns || !data.sourceColumns || !data.mapping) {
            console.error('Invalid response data:', data);
            $('#uploadStatus').text('Error: Invalid response from server. Please try again.').show();
            return;
        }
        
        // Show the template section after mapping is rendered
        $('#templateSection').show();

        // Build mapping section
        var $section = $('<div id="mappingSection" class="mt-4"></div>');
        $section.append('<h5>Column Mapping</h5>');
        
        // Template controls
        $section.append('<div class="form-row mb-2">'
            + '<div class="col-md-4"><label>Load Template</label>'
            + '<select id="templateDropdown" class="form-control"></select></div>'
            + '<div class="col-md-4"><label><input type="checkbox" id="overwriteTemplate" disabled> Overwrite Template</label></div>'
            + '</div>');
            
        // Log the data structure for debugging
        console.log('Rendering mapping UI with data:', {
            targetColumns: data.targetColumns,
            sourceColumns: data.sourceColumns,
            mapping: data.mapping
        });

        // Mapping table
        var tbl = '<div class="table-responsive"><table class="table table-bordered" id="mappingTable"><thead><tr>'
            + '<th>Target Field</th><th>Required</th><th>Type</th><th>Max Length</th><th>Source Column</th><th>Default Value</th><th>Transformation</th></tr></thead><tbody>';
            
        // First, create a map of target names to their suggested sources for easier lookup
        var suggestionMap = {};
        data.mapping.forEach(function(mapItem) {
            if (mapItem.target && mapItem.target.name) {
                suggestionMap[mapItem.target.name] = mapItem.suggested_source;
            }
        });
        
        // Ensure targetColumns is an array
        var targetColumns = Array.isArray(data.targetColumns) ? data.targetColumns : [];
        var sourceColumns = Array.isArray(data.sourceColumns) ? data.sourceColumns : [];
        
        if (targetColumns.length === 0) {
            console.error('No target columns found in response');
            $('#uploadStatus').text('Error: No target columns defined for this object').show();
            return;
        }
        
        // Now build the table rows
        targetColumns.forEach(function(col) {
            if (!col || !col.name) {
                console.error('Invalid column data:', col);
                return; // Skip invalid columns
            }
            
            var req = col.required ? '*' : '';
            var srcOptions = '<option value="">-- Select --</option>';
            var suggestedSource = suggestionMap[col.name] || '';
            
            // Add source column options
            sourceColumns.forEach(function(src) {
                if (typeof src !== 'string') {
                    console.warn('Skipping invalid source column:', src);
                    return;
                }
                var sel = (suggestedSource === src) ? 'selected' : '';
                srcOptions += '<option value="' + src + '" ' + sel + '>' + src + '</option>';
            });
            
            tbl += '<tr>'
                + '<td>' + (col.display || col.name) + '</td>'
                + '<td>' + req + '</td>'
                + '<td>' + (col.type || 'string') + '</td>'
                + '<td>' + (col.max_length || '') + '</td>'
                + '<td><select class="form-control source-col" data-target="' + col.name + '">' + srcOptions + '</select></td>'
                + '<td><input type="text" class="form-control default-val" data-target="' + col.name + '" value=""></td>'
                + '<td><select class="form-control transform" data-target="' + col.name + '">'
                    + '<option value="">None</option>'
                    + '<option value="TRIM">TRIM</option>'
                    + '<option value="UPPERCASE">UPPERCASE</option>'
                    + '<option value="LOWERCASE">LOWERCASE</option>'
                    + '<option value="CUSTOM">Custom script</option>'
                    + '</select></td>'
                + '</tr>';
        });
        tbl += '</tbody></table></div>';
        $section.append(tbl);
        // Back & Import buttons
        $section.append('<div class="mt-3">'
            + '<button type="button" id="backBtn" class="btn btn-secondary" title="Back to Upload File">Back</button> '
            + '<button type="button" id="importBtn" class="btn btn-success" disabled>Import</button>'
            + '</div>');
        $('#previewSection').after($section);

        // Load templates (AJAX, filtered by object)
        var $templateDropdown = $('#templateDropdown');
        $templateDropdown.empty().append('<option value="">-- No Template --</option>');
        // TODO: AJAX to /system/import-schedule/templates?object=objectKey
        // On template select, fill mapping, enable overwrite checkbox
        $templateDropdown.on('change', function() {
            if ($(this).val()) {
                $('#overwriteTemplate').prop('disabled', false);
            } else {
                $('#overwriteTemplate').prop('disabled', true).prop('checked', false);
            }
            // TODO: Load template mapping and fill fields
        });
        // Enable Import button after analysis
        $('#importBtn').prop('disabled', false);
        // Back button returns to Step 1
        $('#backBtn').on('click', function() {
            $('#mappingSection').remove();
            $('#objectName').prop('disabled', false);
            $('#importFile').prop('disabled', false);
            $('#mapColumnsBtn').prop('disabled', false);
        });
    }

});
</script>
