@extends('layout.app')
@section('content')
<div class="margin-header">
    <div class="card-header-title ">
        <table class="title-header">
            <tr>
                <td class="title-division"><h4 class="title">{{ __('admin.title.print_grn') }}</h4></td>
            </tr>
        </table>
    </div>
    <div>
        <form class="form-group" id="trans_report" name="trans_report" method="POST" action="{{route('transgrnreport.print')}}" target="_blank">
            @csrf
            <div class="default-button">
                <button id="filter" type="button" class="btn btn-primary button-padding" style="background-color:#37BC9B;"><i class="icon-square-plus"></i> {{__('admin.button.generate')}}</button>
                <button id="btn-refresh" class="btn btn-primary button-padding" onclick="document.getElementById('trans_report').reset(); document.getElementById('from_date').value = null; return false; $('#filter').trigger('click');">
                    <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
                </button>
                <button hidden type="submit" id="print" class="btn btn-info button-padding">
                    <i class="icon-print"></i> {{__('admin.button.print')}}
                </button>
            </div>
            <table align="center">
                <tr>
                    <td><label for="whse">{{__('admin.label.whse_num')}}</label></td>
                    <td><input type="text" name="from_whse_num" id="from_whse_num" tabindex="1" class="form-control border-primary" placeholder="From Warehouse" ></td>
                    <td>
                        <button type="button" name="{{__('admin.list.warehouses')}}" onClick="selection('/getWhse','from_whse_num', 'whse_num', 'from_whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </td>
                    <td width='50px'><label>&ensp; To</label></td>
                    <td><input type="text" name="to_whse_num" id="to_whse_num" tabindex="5" class="form-control border-primary" placeholder="To Warehouse" ></td>
                    <td>
                        <button type="button" name="{{__('admin.list.warehouses')}}" onClick="selection('/getWhse','to_whse_num', 'whse_num', 'to_whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </td>
                </tr>
                <tr>
                    <td><label for="grn_num">{{ __('admin.label.grn') }} &nbsp;</label></td>
                    <td><input type="text" name="from_grn_num" id="from_grn_num" tabindex="2"  class="form-control border-primary" placeholder="From GRN"></td>
                    <td>
                        <button type="button" name="{{ __('admin.label.grn') }}" tabindex="-1" onClick="selection('/getGrnNum', 'from_grn_num', 'grn_num', 'from_grn_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        {{-- <input type="hidden" name="whse_num" id="whse_num" tabindex="-1"  class="form-control border-primary" placeholder="From GRN" value="{{auth()->user()->getCurrWhse()}}"> --}}
                    </td>
                    <td width='50px'><label>&ensp; To</label></td>
                    <td><input type="text" name="to_grn_num" id="to_grn_num" tabindex="6"  class="form-control border-primary" placeholder="To GRN"></td>
                    <td>
                        <button type="button" name="{{ __('admin.label.grn') }}" tabindex="-1" onClick="selection('/getGrnNum', 'to_grn_num', 'grn_num', 'to_grn_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        {{-- <input type="hidden" name="whse_num" id="whse_num" tabindex="-1"  class="form-control border-primary" placeholder="To GRN" value="{{auth()->user()->getCurrWhse()}}"> --}}
                    </td>
                </tr>
                <tr>
                    <td><label for="vend_num">{{ __('admin.label.vendor') }} &nbsp;</label></td>
                    <td><input type="text" name="from_vend_num" id="from_vend_num" tabindex="2"  class="form-control border-primary" placeholder="From Vendor"></td>
                    <td>
                        <button type="button" name="{{ __('admin.label.vend_num') }}" tabindex="-1" onClick="selection('/getVendor', 'from_vend_num', 'vend_num', 'from_vend_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        {{-- <input type="hidden" name="whse_num" id="whse_num" tabindex="-1"  class="form-control border-primary" placeholder="From GRN" value="{{auth()->user()->getCurrWhse()}}"> --}}
                    </td>
                    <td width='50px'><label>&ensp; To</label></td>
                    <td><input type="text" name="to_vend_num" id="to_vend_num" tabindex="6"  class="form-control border-primary" placeholder="To Vendor"></td>
                    <td>
                        <button type="button" name="{{ __('admin.label.vend_num') }}" tabindex="-1" onClick="selection('/getVendor', 'to_vend_num', 'vend_num', 'to_vend_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        {{-- <input type="hidden" name="whse_num" id="whse_num" tabindex="-1"  class="form-control border-primary" placeholder="To GRN" value="{{auth()->user()->getCurrWhse()}}"> --}}
                    </td>
                </tr>
                <tr>
                    <td width='100px'><label for="date">{{ __('admin.label.shipped_date') }}</label></td>
                    <td width='300px'><input autocomplete="off" id="from_date" class="form-control border-primary input-group to_due_date" name="from_date" placeholder="From Shipped Date"> <!--  value="{{ old('due_date') }}" required> --></td>
                    <td width='90px'>&nbsp;</td>
                    <td width='50px'><label>&ensp; To</label></td>
                    <td width='300px'><input autocomplete="off" id="to_date" class="form-control border-primary input-group to_due_date" name="to_date" placeholder="To Shipped Date" value="{{ old('due_date') }}"></td>
                </tr>
                <tr>
                    <td><label for="status">{{ __('admin.label.status') }}&nbsp;</label></td>
                    <td colspan="4">
                        <input type="radio" name="status" tabindex="4" value="all" id="all" checked> {{ __('admin.label.all') }} &nbsp;&nbsp;&nbsp;
                        <input type="radio" name="status" value="open" id="open"> {{ __('admin.label.open') }} &nbsp;&nbsp;&nbsp;
                        <!-- <input type="radio" name="status" value="released" id="released"> {{ __('admin.label.released') }} &nbsp;&nbsp; -->
                        <input type="radio" name="status" value="completed" id="completed"> {{ __('admin.label.completed') }}<br>
                    </td>
                </tr>
            </table>
        </form>
        <div class="card-block card-dashboard">
            @include('report.trans.grn.list')
        </div>
    </div>
</div>
<style>
    .title-header{
        width:100%;}
    .title{
        padding-top:2px;
        font-size: 22px !important;
        font-weight: bold;}
    .title-division{
        width:50%;
        float:left;}
    .default-button{
        float:right;
        margin-top:-59.4px;
        padding-right:0px;
        position: relative;}
    .button-padding{
        margin:5px 0.5px;}
    .card-block{
        padding-top: 0.5rem;
        padding-right: 0rem;
        padding-left: 0rem;}
    label{
        margin-bottom: 0.1rem !important;}
    .margin-header input.form-control.border-primary{
        padding-left:10px;
        height: 25px;}
    input#all, input#open, input#released, input#completed{
        padding-left:10px;}
    .form-group{
        margin-top:1rem;}
    form td{
        padding: 2px 1px;}
    .table th, .table td{
        padding: 0.2rem 0.2rem !important;}
    button.ColVis_Button>span{
        font-size: 9pt;}
    /* table header */
    div.card-block>table#TransReport thead>tr:first-child>th, div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        background-color: rgb(220,220,220);}
    div.dataTables_scrollBody>table>thead>tr.row>th{
        background-color: white;}
    table#datatable.table.table-bordered.table-hover.nowrap.datatable.no-footer td>a>button.btn.btn-primary{
        padding: 0.1rem 0.2rem !important;}
    div.dataTables_wrapper div.dataTables_paginate{
        margin-bottom: 3rem !important;}
    li.paginate_button.page-item.active>a {
        font-size: 9pt;}
    .dt-center {
        text-align: center;}
    .dt-right {
        text-align: right;}
    .dt-buttons {
        padding-bottom: 15px;}
    .dataTables_length {
        float:left;
        padding-top: 0.5em;
        padding-bottom: 5px;}
     div.dataTables_info {
        padding-top: 0.85em;
        position:absolute;}
    div.dataTables_paginate {
        padding-top: 0.85em;
        right:0px;}
    .no-sort::after { display: none!important; }
    .no-sort::before { display: none!important; }
    .no-sort { pointer-events: none!important; cursor: default!important; }

</style>
@push('scripts')
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/gh/jeffreydwalter/ColReorderWithResize@9ce30c640e394282c9e0df5787d54e5887bc8ecc/ColReorderWithResize.js"></script>
@endpush

<script type="text/javascript">
    jQuery(function($){
        $("#trans_report").validate({
            onchange:true,
            onblur:true,
            rules:{
                from_whse_num:{
                    remote:{
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_whse_num:{
                    remote: {
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_grn_num:{
                    remote:{
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_grn_num:{
                    remote: {
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_vend_num:{
                    remote:{
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_vend_num:{
                    remote: {
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
            },
            messages:{
                from_whse_num:{
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                },
                to_whse_num:{
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                },
                from_grn_num:{
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.grn') ]) }}"
                },
                to_grn_num:{
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.grn') ]) }}"
                },
                from_vend_num:{
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.grn') ]) }}"
                },
                to_vend_num:{
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.grn') ]) }}"
                },
            },
        });
    });

    $(document).ready(function() {
        $('#filter').click(function(){
            // Show pending datatable
            let table = $('#TransReport').DataTable({
                dom: 'Blrtip',
                scrollX: true, // enable scroll horizontal
                scrollY: 400,
                scrollCollapse: true,
                autoWidth: false,
                destroy: true,
                paging: true,
                processing: true, // loading bar
                serverSide: false,
                responsive: true,
                searching: true,
                stateSave: false,
                bSortCellsTop: true, // set ordering icon at the first thead tr
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                order: [],
                ajax: {
                    url : '{!! route('transgrnreport.data') !!}',
                    method: 'get',
                    data: function (d) {
                        d.from_whse_num = $('#from_whse_num').val();
                        d.to_whse_num = $('#to_whse_num').val();
                        d.from_grn_num = $('#from_grn_num').val();
                        d.to_grn_num = $('#to_grn_num').val();
                        d.from_vend_num = $('#from_vend_num').val();
                        d.to_vend_num = $('#to_vend_num').val();
                        d.from_date = $('#from_date').val();
                        d.to_date = $('#to_date').val();
                        d.status = $("input[name='status']:checked").val();
                    }
                },
                order: [[ 0, "desc" ]],
                columns: [
                    { data: 'whse_num', name: 'whse_num' },
                    { data: 'grn_num', name: 'grn_num' },
                    { data: 'vend_num', name: 'vend_num' },
                    { data: 'vend_name', name: 'vend_name' },
                    { data: 'shipped_date', name: 'shipped_date'},
                    { data: 'status', name: 'status' },
                    { data: 'action', name: 'action' },
                ],

                buttons: ['pdf']
            });

            $('#btn-refresh').click(function(){
                $('input:not([type="radio"]').each(function() {
                    $(this).val('');
                });
                $('.dataTable').DataTable().$('select').each(function() {
                    $(this).val('');
                })
                $('.dataTable').DataTable().columns().search('');
                $('.dataTable').DataTable().clear().draw();
            });

            var colreorder = new $.fn.dataTable.ColReorder(table);

            var colvis = new $.fn.dataTable.ColVis( table );
            $(colvis.button() ).insertAfter('div.dt-buttons');

        });

        $("#TransReport thead input:not(#mass-chk)").on( 'keyup', function (e) {
            if (e.keyCode == 13) {
                $('#TransReport').DataTable().column($(this).parent().index() + ':visible').search(this.value).draw();
             }
        });

        $(window).resize(function (e) {
            $("#TransReport").DataTable().columns.adjust();
        });
    });
</script>

@include('util.selection')
@include('util.datepicker')
@endsection
