@extends('layout.mobile.app')
@section('content')
@section('title', __('Stock Move'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="moveform" name="moveform" action="{{ route('multiStockMoveProcess') }}"
            method="post">
            @csrf
            <div class="form-body">
                <input type="hidden" name="batch_id" value="{{ $batch_id }}">

                @include('components.form.scan_input', ['type' => 'inventory'])
                <div class="form-group row" <?php if (!$tparm) {
                    echo 'colspan="2"';
                } ?>>
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="whse_num">{{ __('mobile.label.whse_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" onchange="clickSelf(this.id)" name="whse_num" id="whse_num"
                                class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.whse_num') }}" <?php if (!$tparm) {
                                    echo 'readonly';
                                } ?>
                                value="{{ old('whse_num', auth()->user()->getCurrWhse()) }}" required>
                        </div>
                    </div>
                    @if ($tparm)
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" tabindex="-1" id="getWhse"
                                name="{{ __('mobile.list.warehouses') }}"
                                onClick="selection('/getWhse','whse_num','whse_num','whse_num');modalheader(this.id,this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" onchange="clickSelf(this.id)" id="item_num"
                                class="form-control border-primary" name="item_num"
                                placeholder="{{ __('mobile.placeholder.item_num') }}" value="{{ old('item_num') }}">
                            <input type="hidden" name="lot_tracked" id="lot_tracked" value="">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" id="getItem" name="{{ __('mobile.list.items') }}"
                            onclick="selection('/getWhseItem','whse_num,item_num','item_num','item_num');modalheader(this.id,this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="item_desc"> &nbsp;</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <textarea type="text" name="item_desc" id="item_desc" class="form-control border-primary clearable"
                                placeholder="{{ __('mobile.placeholder.item_desc') }}" readonly>{{ old('item_desc') }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row" id="itemlocation">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="loc">{{ __('mobile.label.from_loc') }} </label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="hidden" name="checkSubmit" id="checkSubmit"
                                class="form-control border-primary" value="">
                            <input type="text" maxlength="30" onchange="clickSelf(this.id)" name="loc_num"
                                id="loc_num" value="{{ old('loc_num') }}" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.from_loc') }}" required>
                            <span id="checkLoc"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="{{ __('mobile.list.from_locations') }}" id="locbtn"
                            onCLick= "selectionNull('/getItemLocCheckPicking','whse_num,item_num,loc_num','loc_num','loc_num');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="trlot" hidden>
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="fromLoc">{{ __('mobile.label.from_lot') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="lot_num" id="lot_num" onchange="onLotChange()"
                                class="form-control border-primary valid"
                                placeholder="{{ __('mobile.placeholder.lot_num') }}" value="{{ old('lot_num') }}"
                                maxlength="50">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="Lots" id="lotbtn"
                            onclick="selectionMultiLineInput('/getLotLocExpiry','whse_num,loc_num,item_num,sortField,sortBy','lot_num','loc_num,lot_num');modalheader_uom(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="toLoc">{{ __('mobile.label.to_loc') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="toLoc" id="toLoc" value="{{ old('toLoc') }}"
                                class="form-control border-primary" onChange="clickSelf(this.id);showNewLoc()"
                                placeholder="{{ __('mobile.placeholder.to_loc') }}" maxlength="30" required>
                            <span id="loc_info"></span>
                            <span id="checkToLoc"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="{{ __('mobile.list.to_locations') }}"
                            onClick="selectionNull('/getLocNoPicking','whse_num,toLoc','loc_num','toLoc');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="trcontainer3">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="document">{{ __('mobile.label.doc') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="document_num" id="document_num"
                                value="{{ old('document_num') }}" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.doc') }}" maxlength="30">
                        </div>
                    </div>
                </div>

                <div class="form-group row" id="qtyonhand">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_available">{{ __('mobile.label.qty_available') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="text" style="text-align:right" name="qty_available" id="qty_available"
                                value="{{ old('qty_available') }}" class="form-control border-primary"
                                onblur="formatDecimal(this)" placeholder="{{ __('mobile.label.qty_available') }}"
                                readonly>
                            <input type="hidden" name="qty_available_conv" readonly id="qty_available_conv"
                                value="{{ old('qty_available_conv') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input id="base_uom" name="base_uom" class="form-control border-primary"
                            placeholder="Base UOM" readonly value="{{ old('base_uom') }}">
                    </div>
                </div>

                <div class="form-group row" id="trcontainer6">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="qty_to_move">{{ __('mobile.label.qty_to_move') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input inputmode="numeric" type="text" style="text-align:right" name="qty"
                                id="qty" value="{{ old('qty') }}"
                                class="form-control border-primary number-format"
                                placeholder="{{ __('mobile.label.qty_to_move') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" onchange="clickSelf(this.id)" name="uom" id="uom"
                            class="form-control border-primary" value="{{ old('uom') }}" placeholder="Unit">
                        <input type="hidden" name="max_qty_input" id="max_qty_input"
                            value="{{ numberFormatPrecision(old('max_qty_input'), $unit_quantity_format) }}">
                        <input type="hidden" name="conv_factor" id="conv_factor" value="1">
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="{{ __('mobile.list.uoms') }}"
                            onClick="selection('/getItemUOMConv','item_num','uom','uom');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <br>

                <div style="text-align:center;">
                    <input type="hidden" name="single_form" id="single_form" class="form-control border-primary"
                        value=true>
                    <button type="submit" id="process" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.stock_move') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@include('errors.maxchar')

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.15.0/additional-methods.min.js"></script>

<script>
    var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
    var errorMessage = "default error message";
    var errorMessageToLoc = "default error message";

    var errorMessageQty = "{{ __('error.mobile.qty_notmoreorequalqty_available') }}";

    jQuery(function($) {
        console.log(disable_create_new_item_location);
        $.validator.addMethod('minStrict', function(value, el, param) {
            return value > param;
        });
        $.validator.addMethod('QoH', function(value, el, param) {
            qty_available = parseFloat($('#qty_available').val().replace(/,/g, ""));
            return qty_available > 0;
        });

        $("#moveform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $(".submitloader").attr('disabled', false);
            }
        });

        $("#moveform").validate({
            onchange: true,
            //   onfocusout: false,
            rules: {
                whse_num: {
                    required: true,
                    remote: {
                        url: "{{ route('validation2') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val()
                        }
                    }
                },
                item_num: {
                    required: true,
                    remote: {
                        url: "{{ route('validateWarehouseItemV3') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                        }
                    }
                },


                loc_num: {
                    required: true,
                    notEqualTo: "#toLoc",
                    depends: function() {
                        $("#loc_info").html("");
                        $("#checkLoc").html("");
                        $("#locnumnotexist").html("");
                    },
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_status == 0) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage =
                                        "{{ __('error.mobile.inactive_loc2', ['resource' => 'To Loc']) }}";
                                    $(".pageloader").css("display", "none");
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else {
                                    if ($("#trlot").is(":hidden")) {
                                        display('/displayQuantity', 'item_num,whse_num,loc_num',
                                            'qty_available,base_uom,qty_available_conv');
                                    }
                                    return true;
                                }

                            } else {
                                $("#loc_info").html("");
                                $("#locnumnotexist").html('');
                                $("#qty_available").val("0");
                                // errorMessage = "{{ __('error.mobile.loc_not_exists') }}";
                                var $loc = $("#loc_num").val();
                                var msg2 =
                                    "{{ __('error.mobile.notexist3', ['resource' => 'From Loc', 'resource2' => ':item:']) }}";
                                msg2 = msg2.replace(":item:", $loc);
                                errorMessage = msg2;
                                // showNewLoc();
                                // $(".submitloader").attr("disabled", true);
                                return false;
                            }
                        }
                    }
                },
                lot_num: {
                    //  onchange:false,
                    //    onfocusout: false,
                    required: {
                        depends: function(element) {
                            return $("#lot_num").is(":visible");
                        }
                    },
                    remote: {
                        url: "{{ route('ExistingLotValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            loc_num: function() {
                                return $("#loc_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                        }
                    }
                },

                toLoc: {
                    // required: {
                    //     depends: function() {
                    //         if (disable_create_new_item_location == 1) {
                    //             return true;
                    //         }

                    //         return false;
                    //     },
                    // },
                    required: true,
                    notEqualTo: "#loc_num",
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();

                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    // $("#qty").val("");
                                    //  $("#qty_available").val("0");
                                    errorMessageToLoc =
                                        "{{ __('error.mobile.validate_picking') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    //$("#qty").val("");
                                    //$("#qty_available").val("0");
                                    errorMessageToLoc =
                                        "{{ __('error.mobile.validate_transit') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_status == 0) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    // $("#qty_available").val("0");
                                    errorMessageToLoc =
                                        "{{ __('error.mobile.inactive_loc2', ['resource' => 'To Loc']) }}";
                                    $(".pageloader").css("display", "none");
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else {
                                    // if($("#trlot").is(":hidden")){
                                    //     display('/displayQuantity','item_num,whse_num,loc_num','qty_available,base_uom,qty_available_conv');
                                    // }
                                    return true;
                                }

                            } else {
                                $("#toLoc").html("");
                                $("#locnumnotexist").html('');
                                // $("#qty_available").val("0");
                                // errorMessageToLoc = "{{ __('error.mobile.loc_not_exists') }}";
                                // showNewLoc();
                                // $(".submitloader").attr("disabled", true);
                                // return false;

                                if (disable_create_new_item_location == 1) {
                                    var $loc = $("#toLoc").val();
                                    var msg2 =
                                        "{{ __('error.mobile.notexist3', ['resource' => 'To Loc', 'resource2' => ':item:']) }}";
                                    msg2 = msg2.replace(":item:", $loc);
                                    errorMessageToLoc = msg2;
                                    // errorMessageToLoc = "{{ __('error.mobile.loc_not_exists') }}";
                                    return false;
                                }
                                return true;
                            }
                        }
                    }
                },
                qty: {
                    required: true,
                    number: true,
                    number_size: true,
                    QoH: true,
                    minStrict_value: 0,
                    max_value: function() {
                        var qty_receivable = parseFloat($("#max_qty_input").val().replace(/,/g,
                            ""));
                        return Math.max(0, qty_receivable);
                        //return parseFloat($("#qty_available_conv").val().replace(/,/g,"")); //
                    }


                },
                uom: {
                    required: true,
                    uom_validation: function() {
                        return [$('#item_num').val(), null, null, $('#base_uom').val(), $('#uom')
                            .val()
                        ];
                    }
                    // remote:{
                    //         url: "{{ route('MiscValidation') }}",
                    //         type: "post",
                    //         data: { _token : $('input[name="_token"]').val() }
                    //     }
                },
            },
            messages: {
                // whse_num:{
                //     remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]) }}"
                // },
                // item_num:{
                //     remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.item_num')]) }}"
                // },
                loc_num: {
                    remote: function() {
                        return errorMessage;
                    }
                },
                lot_num: {
                    remote: function(val2) {

                        var msg2 =
                            "{{ __('error.mobile.notexist3', ['resource' => __('mobile.label.lot_num'), 'resource2' => ':lot:']) }}";
                        var lot_num = $("#lot_num").val();
                        // var qty2= $("#")
                        msg2 = msg2.replace(":lot:", lot_num);

                        return msg2;
                        // remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}"
                    },
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}"
                },
                toLoc: {
                    notEqualTo: "{{ __('error.mobile.same', ['resource1' => __('mobile.label.from_loc'), 'resource2' => __('mobile.label.to_loc')]) }}",
                    remote: function() {
                        return errorMessageToLoc;
                    }
                },
                qty: {
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.label.qty')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}",
                    QoH: "{{ __('error.mobile.insufficient') }}",
                    minStrict_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty_to_move')]) }} {0}.",
                    // max_value: function(val2) {
                    //     var msg2 =
                    //         "{{ __('error.mobile.exceed', ['resource' => __('mobile.label.qty_to_move'), 'resource2' => ':qty2:']) }}";
                    //     var qty = $("#qty").val();
                    //     // var qty2= $("#")
                    //     msg2 = msg2.replace(":qty:", qty)
                    //     msg2 = msg2.replace(":qty2:", val2)


                    //     return msg2;
                    // }

                    max_value: function(value, element) {
                        var maxQty = parseFloat($('#max_qty_input').val().replace(/,/g, ""));
                        return errorMessageQty.replace(':resource_qty_available', maxQty);
                    }
                    // max_value: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.label.qty')]) }} {0}"
                },
                uom: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                },
            },
            submitHandler: function(form) {
                // ajaxurl ="{{ route('itemlocv', ['item_num', 'toLoc', 'whse_num']) }}";
                // url = ajaxurl.replace('item_num', $("#item_num").val());
                // url = url.replace('toLoc', $("#toLoc").val());
                // url = url.replace('whse_num', $("#whse_num").val());
                $(".submitloader").attr('disabled', true);

                ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
                url = ajaxurl.replace('loc_num', btoa($("#toLoc").val()));
                $.get(url, function(data) {
                    if (data == 'not exist' && disable_create_new_item_location == 0) {
                        let m = '{{ __('admin.message.surecreate') }}';
                        if ($('#lot_num').val() == "" || $('#lot_num').val() == null) {
                            m = m.replace(':resource', "Item Location");
                        } else {
                            m = m.replace(':resource', "Item Lot Location");
                        }
                        Swal.fire({
                            title: 'Warning',
                            text: m,
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Yes'
                        }).then((result) => {
                            if (result.value) {
                                $(".pageloader").css("display", "block");
                                $(".submitloader").attr("disabled", true);
                                form.submit();
                            } else {
                                $(".submitloader").attr('disabled', false);
                                return false;
                            }
                        });
                    } else {
                        $(".pageloader").css("display", "block");
                        $(".submitloader").attr("disabled", true);
                        form.submit();
                    }
                });
            }
        });
    });
</script>
@include('util.validate_uom')
<script type="text/javascript">
    $(document).ready(function() {
        $("#checkLoc").html('');
        $("#checkToLoc").html('');
        $("#loc_info").html('');

        if ($("#trlot").is(":hidden") && $("#lot_num").val()) {
            $("#trlot").attr('hidden', false);
        }

        $("#whse_num").on("change", function() {
            // $("#lot_num,#loc_num,#item_num,#item_desc,#uom,#qty_available,#qty_avail,#lot,#toLoc,#qty_available_conv").val("");
            $("#loc_info").html("");
        });

        $("#qty_available").on("change", function() {
            // $("#max_qty_input").val($(this).val()).change();

            $("#max_qty_input").val($(this).val()).change();
            if ($("#qty_available").val() < 0) {
                Swal.fire({
                    title: 'Error',
                    text: 'Qty On Hand is 0',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                $("#loc_num,#base_uom,#uom,#qty_available,#qty_avail,#lot_num,#toLoc,#qty_available_conv")
                    .val("");
                $("#loc_info").html("");
            }

        });

        $("#item_num").on("change", function() {
            if ($("#item_num").val() == "") {

                $("#item_desc,#uom").val("");
                // $("#lot_num,#loc_num,#item_desc,#qty_available,#qty_avail,#toLoc,#lot,#base_uom,#uom,#loc_info,#unit,#lot_tracked,#qty_available_conv").val("");
                $("#loc_info").html("");
                $("#trlot").attr('hidden', true);
            } else {
                let asynfFunc = [];

                asynfFunc.push(display('/getLocByRankIssuePL', 'whse_num,item_num',
                    'qty_available,base_uom,qty_available_conv'));

                // display('/getLocByRankIssuePL','whse_num,item_num', 'loc_num,qty_available,base_uom,qty_available_conv');
                asynfFunc.push(display('/displayItemDesc', 'item_num', 'item_desc,uom,lot_tracked'));

                Promise.all(asynfFunc).then(function() {
                    $("#max_qty_input").val($("#qty_available_conv").val());
                })


                //display('/displayQuantity','item_num,whse_num,loc_num','qty_available,base_uom,max_qty_input');
                showbaseuom();
            }
        })

        $("#loc_num").on("change", function() {
            $("#loc_info").html("");
            if ($("#loc_num").val() == "") {
                clearLocs();
                $("#toLoc").val("");
                $("#loc_info").html("");
                $("#checkLoc").html("");
            } else {
                // if ($("#trlot").is(":hidden")) {
                display('/displayQuantity', 'item_num,whse_num,loc_num',
                    'qty_available,base_uom,qty_available_conv');
                // }
                return;

                // Send error if manually type object that is transit location
                $.ajax({
                    url: '{{ route('checkLocNotTransitpickLocs') }}',
                    type: "GET",
                    data: {
                        whse_num: $("#whse_num").val(),
                        loc_num: $("#loc_num").val(),
                    },
                    success: function(data) {
                        console.log(data.length);
                        if (data.length > 0) {
                            if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                $("#checkLoc").html('');
                                $("#loc_info").html("");

                                $("#checkLoc").html(
                                    '<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>'
                                );
                                $(".submitloader").attr("disabled", true);
                                exit;

                            }
                        }
                        if (data.length == 0) {

                            $("#checkLoc").html('');
                            $("#loc_info").html("");

                            $("#checkLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>'
                            );
                            $(".submitloader").attr("disabled", true);

                            exit;

                        }
                        if (data[0].loc_type == 'T' && data.length > 0) {
                            $("#loc_info").html("");
                            $("#checkLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.validate_transit') }}</span>'
                            );
                            $(".submitloader").attr("disabled", true);

                            $("#checkSubmit").val('true');

                            $("#toLoc").on("change", function() {
                                // $("#qty_to_move").val('');
                                $("#loc_info").html("");

                                if ($("#toLoc").val() == "") {
                                    $("#loc_info").html("");
                                    $("#checkToLoc").html("");
                                } else {
                                    $("#loc_info").html('');
                                    // Send error if manually type object that is transit location
                                    $.ajax({
                                        url: '{{ route('checkLocNotTransitpickLocs') }}',
                                        type: "GET",
                                        data: {
                                            whse_num: $("#whse_num").val(),
                                            loc_num: $("#toLoc").val(),
                                        },
                                        success: function(data) {
                                            if (data[0].pick_locs ==
                                                1 && data[0]
                                                .pick_locs !== undefined
                                            ) {
                                                $("#loc_info").html("");
                                                $("#checkToLoc").html(
                                                    '<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>'
                                                );
                                                $(".submitloader").attr(
                                                    "disabled", true
                                                );
                                            } else if (data[0]
                                                .loc_type == 'T') {
                                                $("#loc_info").html("");
                                                $("#checkToLoc").html(
                                                    '<span style="color:red;"> {{ __('error.mobile.validate_transit') }}</span>'
                                                );
                                                $(".submitloader").attr(
                                                    "disabled", true
                                                );

                                                $("#checkSubmit").val(
                                                    'true');
                                            } else {

                                                var checksubmit = $(
                                                        "#checkSubmit")
                                                    .val();

                                                console.log(
                                                    checksubmit);

                                                if (checksubmit ==
                                                    true) {

                                                    $("#checkToLoc")
                                                        .html("");
                                                    $("#loc_info").html(
                                                        "");
                                                    $(".submitloader")
                                                        .attr(
                                                            "disabled",
                                                            false);

                                                } else {
                                                    $("#checkToLoc")
                                                        .html("");
                                                    $("#loc_info").html(
                                                        "");
                                                    $(".submitloader")
                                                        .attr(
                                                            "disabled",
                                                            true);
                                                }

                                            }
                                        }
                                    });
                                }
                            });
                        } else if (data[0].loc_status == 0) {
                            $("#loc_info").html("");
                            $("#locnumnotexist").html('');
                            $("#qty").val("");
                            $("#qty_available").val("0");
                            $("#checkLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.inactive_loc') }}</span>'
                            );
                            $("#checkSubmit").val('true');

                            // errorMessage = "{{ __('error.mobile.inactive_loc') }}";
                            $(".pageloader").css("display", "none");
                            $(".submitloader").attr("disabled", false);

                            // $(".submitloader").attr("disabled", true);
                            return false;
                        } else {
                            $("#checkLoc").html("");
                            $(".submitloader").attr("disabled", false);

                            $("#checkSubmit").val('false');
                        }
                    }
                });
            }
        });

        $("#toLoc").on("change", function() {

            // $("#qty_to_move").val('');
            $("#loc_info").html("");

            if ($("#toLoc").val() == "") {
                $("#loc_info").html("");
                $("#checkToLoc").html("");
                $(".submitloader").attr("disabled", true);
            } else {
                $("#loc_info").html('');
                $(".submitloader").attr("disabled", false);
                return;
                //alert('got data');
                // Send error if manually type object that is transit location
                $.ajax({
                    url: '{{ route('checkLocNotTransitpickLocs') }}',
                    type: "GET",
                    data: {
                        whse_num: $("#whse_num").val(),
                        loc_num: $("#toLoc").val(),
                    },
                    success: function(data) {
                        //console.log(data[0].pick_locs);
                        $("#checkToLoc").html("");
                        if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                            $("#loc_info").html("");
                            $("#checkToLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>'
                            );
                            $(".submitloader").attr("disabled", true);
                        } else if (data[0].loc_type == 'T') {
                            $("#loc_info").html("");
                            $("#checkToLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.validate_transit') }}</span>'
                            );
                            $(".submitloader").attr("disabled", true);

                            $("#checkSubmit").val('true');
                        } else if (data[0].loc_status == 0) {
                            $("#loc_info").html("");
                            $("#locnumnotexist").html('');
                            $("#qty").val("");
                            // $("#qty_available").val("0");
                            $("#checkToLoc").html(
                                '<span style="color:red;"> {{ __('error.mobile.inactive_loc') }}</span>'
                            );
                            $("#checkSubmit").val('true');

                            // errorMessage = "{{ __('error.mobile.inactive_loc') }}";
                            $(".pageloader").css("display", "none");
                            $(".submitloader").attr("disabled", true);
                            return false;
                        } else {
                            //                                 var checksubmit = $("#checkSubmit").val();
                            // alert(checksubmit + " >>>"+ "kdk" );
                            //                                 if(checksubmit == true)
                            //                                 {

                            // console.log('kena block');
                            // $("#checkToLoc").html("");
                            // $("#loc_info").html("");
                            // $(".submitloader").attr("disabled", false);
                            //}

                            if (data.length == 0) {
                                $("#loc_info").html("");
                                $("#locnumnotexist").html('');
                                $("#qty_available").val("0");
                                $("#checkToLoc").html(
                                    '<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>'
                                );
                                // showNewLoc();
                                $(".submitloader").attr("disabled", true);
                                return false;
                            } else {
                                $(".submitloader").attr("disabled", false);
                            }

                        }

                    }
                });
            }
        });

        // $("#uom").on("change", function(){
        //     if($("#uom").val() != ""){
        //         if($("#item_num").val() == ""){
        //             $("#qty_available_conv").val("");
        //         } else
        //         {
        //             display('/displayQuantityConverted','base_uom,item_num,qty_available,uom','qty_available_conv');
        //         }
        //     }
        //     else{
        //         $("#qty_to_move").val('');
        //     }
        // });

        $("#uom").on("change", function() {
            var lot_num = "undefined";
            if ($("#lot_num").val() != "") {
                lot_num = $("#lot_num").val();
            }
            $("#input4").val('');
            if ($('#uom').val()) {
                let validate = validateConvUOM($('#item_num').val(), null, null, $('#base_uom').val(),
                    $('#base_uom').val(), $('#uom').val());
                //  let validate = validateConvUOM($('#item_num').val(), null, null, $('#base_uom').val(), $("#base_uom").val(), $('#uom').val());

                validate.then(function(resp) {
                        // true
                    },
                    function(err) {
                        // false
                        $("#qty_available_conv").val("");
                        $("#uom").val($("#uom").val());
                    }).finally(function() {
                        $("#qty").val('');
                        $("#qty-error").hide();

                    calculateQtyLimit($("#base_uom").val(), $("#qty_available_conv").val(), $(
                            "#uom").val(), $("#item_num").val(), $("#whse_num").val(), $(
                            "#toLoc").val(), lot_num, $("#base_uom").val(), "null", "null",
                        "null", "");
                    //calculateQtyLimit($("#uom_need_to_convert").val(), $("#qty_need_to_convert").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), lot_num, $("#base_uom").val(), $("#cust_num").val(), "null", "{{ __('mobile.nav.co_picking') }}", "", "qty_available_conv");
                    //calculateQtyLimit($("#qty_required_uom").val(), $("#qty_required").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), lot_num, $("#base_uom").val(), $("#cust_num").val(), "null", "{{ __('mobile.nav.co_picking') }}", "");
                });
            }
        });









    });

    function displayQty() {
        if (($("#lot_tracked") == '0')) {
            display('/displayQuantity', 'item_num,whse_num,loc_num', 'qty_available,qty_avail');
        }
    }

    function onLotChange() {
        var lotnum = $("#lot_num").val();
        var max_qty = $("#max_qty_input").val($("#qty_available_conv").val());
        alert(lotnum + "  -- " + max_qty)
        if ($("#lot_num").val()) {
            display('/displayLotQuantity', 'item_num,whse_num,loc_num,lot_num', 'qty_available,qty_available_conv');

            //  display('/displayLotQuantity','item_num,whse_num,loc_num,lot_num','qty_on_hand,qty_on_hand_conv');
            $("#max_qty_input").val($("#qty_available").val());
        }
        $("#lot_num").focus();
    }

    function showNewLoc() {

        // Check the From Loc and To Loc
        var toLoc = $("#toLoc").val();
        var fromLoc = $("#loc_num").val();
        //alert(toLoc + " :: " +fromLoc);
        if (toLoc === fromLoc) {
            Swal.fire({
                title: 'Warning',
                text: "From Loc cannot same as To Loc",
                icon: 'warning',
                showCancelButton: false,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Ok'
            }).then((result) => {
                $("#toLoc").val('');
            });
        }



        if ($("#lot_tracked").val() != "1") {
            $("#loc_info").html('');
            // ajaxurl ="{{ route('itemlocv', ['item_num', 'toLoc', 'whse_num']) }}";
            // url = ajaxurl.replace('item_num', $("#item_num").val());
            // url = url.replace('toLoc', $("#toLoc").val());
            // url = url.replace('whse_num', $("#whse_num").val());


            ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
            url = ajaxurl.replace('loc_num', btoa($("#toLoc").val()));
            $.get(url, function(data) {
                // console.log(data);
                if (data == 'not exist' && disable_create_new_item_location == 0) {
                    $("#loc_info").html(
                        '<i class="icon-info"></i> <span>{{ __('mobile.message.new_item_location') }}</span>'
                    );
                }
                if (data == 'not exist' && disable_create_new_item_location == 1) {
                    // $("#checkToLoc").html('<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>');
                    // showNewLoc();
                    //  $(".submitloader").attr("disabled", true);

                }
            });

        } else {
            if (($("#lot_num").val() != "" && $("#toLoc").val() != "") && ($("#loc_num").val() != $("#toLoc").val())) {
                $("#loc_info").html('');

                ajaxurl = "{{ route('lotlocv', ['lot_num', 'item_num', 'toLoc', 'whse_num']) }}";
                url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
                url = url.replace('item_num', btoa($("#item_num").val()));
                url = url.replace('toLoc', btoa($("#toLoc").val()));
                url = url.replace('whse_num', btoa($("#whse_num").val()));

                $.get(url, function(data) {
                    // console.log(data);
                    if (data == 'not exist' && disable_create_new_item_location == 0) {
                        $("#loc_info").html(
                            '<i class="icon-info"></i> <span>{{ __('mobile.message.new_lot_location2') }}</span>'
                        );
                    }
                });
            }
        }
        $("#toLoc").focus();

        //display('/displayQuantityConverted','base_uom,item_num,qty_available,uom','qty_available_conv');
        $("#max_qty_input").val($("#qty_available_conv").val());
    }

    function showbaseuom() {
        if ($("#uom").val() != "") {
            ajaxurl = "{{ route('getbaseuom', ['item', 'um']) }}";
            url = ajaxurl.replace('um', $("#uom").val());
            url = url.replace('item', $("#item_num").val());

            console.log(url);
            $.get(url, function(data) {
                if (data != 'empty') {
                    console.log(data);
                    $("#base_uom").val(data);
                } else {
                    console.log($("#uom").val());
                    return $("#base_uom").val($("#uom").val());
                }
            });
        }
    }
</script>

@include('util.selection')

@include('util.convert_alternade_barcode_to_item')
@include('Pallet.palletMobileValidation')

@endsection
