@extends('layout.app')
@push('custom-head')
    <style>
    html body .content .content-wrapper{
        margin: 0.6rem 1.2rem;}
    .card-header1{
        margin-bottom: -10px;}
    textarea{
        padding:2px;}
    input {
        padding:2px;
        height: 25px;}
    select#conv_type.form-control.border-primary{
        padding-left: 0.5rem !important;
        height: 25px;}
    </style>
@endpush
@section('content')

<div class="card-header1">
    <h4 class="card-title" id="basic-layout-colored-form-control">{{__('admin.title.add_uomcon')}}</h4>
</div>
<br>
<form class="form" id="postform" autocomplete="off" action="{{ route('addUOMConversion') }}" method="POST">
    @csrf
    <div class="form-body">
        <div>
            <ul id="errors"></ul>
        </div>
        <div>
        <div class="form-group">
            <table>
                <tr>
                    <td width='200px'><label for="uom_from" class="required">{{__('admin.label.uom_from')}}</label></td>
                    <td width='300px'><input type="text" id="uom_from" class="form-control border-primary"  placeholder="{{__('admin.label.uom_from')}}" name="uom_from" value="{{old('uom_from')}}" required></td>
                    <td><button type="button" name="{{__('admin.list.uoms')}}" onClick="selection('/getUOM','uom_from','uom','uom_from');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                </tr>
                <tr>
                    <td><label for="uom_to" class="required">{{__('admin.label.uom_to')}}</label></td>
                    <td><input type="text" id="uom_to" class="form-control border-primary" placeholder="{{__('admin.label.uom_to')}}" name="uom_to" value="{{old('uom_to')}}" required></td>
                    <td><button type="button" name="{{__('admin.list.uoms')}}" onClick="selection('/getUOM','uom_to','uom','uom_to');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                </tr>
                <tr>
                    <td><label for="conv_type" class="required">{{__('admin.label.conv_type')}}</label></td>
                    <td>
                        <select class="form-control border-primary" id="conv_type" class="form-control" name="conv_type" required>
                            <option value="G" {{old('conv_type') == "G" ? 'selected' : ''}}>{{__('admin.option.global')}}</option>
                            <option value="I" {{old('conv_type') == "I" ? 'selected' : ''}}>{{__('admin.option.item')}}</option>
                            <option value="V" {{old('conv_type') == "V" ? 'selected' : ''}}>{{__('admin.option.vendor')}}</option>
                            <option value="C" {{old('conv_type') == "C" ? 'selected' : ''}}>{{__('admin.option.customer')}}</option>
                        </select>
                    </td>
                </tr>
                <tr id="itemrow" style="display:none;">
                    <td><label for="item_num" class="required">{{__('admin.label.item_num')}}</label></td>
                    <td><input type="text" id="item_num" class="form-control border-primary" onchange="display('/displayItemDesc','item_num','item_desc');" placeholder="{{__('admin.label.item_num')}}" name="item_num" value="{{old('item_num')}}"></td>
                    {{-- <td><button type="button" name="{{__('admin.list.items')}}"  onClick="selection('/getItemByUom','uom_from','item_num','item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td> --}}
                    <td><button type="button" name="{{__('admin.list.items')}}"  onClick="selection('/getItem','item_num','item_num','item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                </tr>
                <tr  id="item_desc_row" style="display:none;">
                    <td><label for="item_desc"> {{__('admin.label.desc')}}</label></td>
                    <td><textarea type="text" id="item_desc" name="item_desc" class="form-control border-primary" placeholder="{{__('admin.label.item_desc')}}" readonly>{{old('item_desc')}}</textarea></td>
                </tr>
                <tr id="vendrow" style="display:none;">
                    <td><label for="vend_num" class="required">{{__('admin.label.vend_num')}}</label></td>
                    <td><input type="text" id="vend_num" class="form-control border-primary" onchange="display('/displayVendName','vend_num','vend_name');" placeholder="{{__('admin.label.vend_num')}}" name="vend_num" value="{{old('vend_num')}}"></td>
                    <td><button type="button" tabindex="-1" name="{{__('admin.label.vendor')}}" onClick="selection('/getVendor','vend_num','vend_num','vend_num');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                </tr>
                <tr id="vend_name_row" style="display:none;">
                    <td></td>
                    <td width='300px'><input type="text" id="vend_name" name="vend_name" class="form-control border-primary" placeholder="{{__('admin.label.vend_name')}}" value="{{old('vend_name')}}" readonly></td>
                </tr>
                <tr id="custrow" style="display:none;">
                    <td><label for="cust_num" class="required">{{__('admin.label.cust_num')}}</label></td>
                    <td><input type="text" id="cust_num" class="form-control border-primary" name="cust_num" onchange="display('/displayCustName','cust_num','cust_name');" placeholder="{{__('admin.label.cust_num')}}" value="{{ old('cust_num') }}" ></td>
                    <td><button type="button" name="{{__('admin.list.customers')}}"  onClick="selection('/getCust','cust_num','cust_num','cust_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td>
                </tr>
                <tr id="cust_name_row" style="display:none;">
                    <td></td>
                    <td width='300px'><input type="text" id="cust_name" name="cust_name" class="form-control border-primary" placeholder="{{__('admin.label.cust_name')}}" value="{{old('cust_name')}}" readonly></td>
                </tr>
                <tr>
                    <td><label for="conv_factor" class="required">{{__('admin.label.conv_factor')}}</label></td>
                    <td><input type="text" inputmode="numeric" style="text-align: right;" id="conv_factor" class="form-control border-primary number-format" placeholder="Conversion Factor" name="conv_factor" value="{{ numberFormatPrecision(old('conv_factor',1.0000),$unit_quantity_format) }}" required></td>
                </tr>
            </table>
        </div>
    </div>
    </div>

    <div class="form-actions center">
        <a href="{{route('UOMConversion')}}">
            <button type="button" class="btn btn-warning mr-1">
                <i class="icon-cross2"></i> {{__('admin.button.cancel')}}
            </button>
        </a>
        <button type="submit" class="btn btn-primary" id="save_submit">
            <i class="icon-check2"></i> {{__('admin.button.save')}}
        </button>
    </div>
</form>

<script>
   jQuery(function($){
        $.validator.addMethod("notEqualTo", function(value, element, param) {
            return this.optional(element) || value != $(param).val();
        }, "Please specify a different (non-default) value");
        $.validator.addMethod("minValue", function(value, element, param) {
            return value > param;
        }, "Conversion Factor must be greater than 0.");
        $("#postform").validate({
            onchange:true,
            rules:{
                uom_to:{
                    required: true,
                    notEqualTo: "#uom_from",
                    remote:{
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                uom_from:{
                    required: true,
                    remote:{
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                item_num:{
                    required: true,
                    remote:{
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                cust_num:{
                    required: true,
                    remote:{
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                vend_num:{
                    required: true,
                    remote:{
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                conv_factor:{
                    required: true,
                    number: true,
                    number_size: true,
                    min_value: 0.0001,
                }
            },
            messages:{
                uom_from:{
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.uom_from') ]) }}"
                },
                uom_to:{
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.uom_to') ]) }}",
                    notEqualTo: "Converted UOM Must Be Different With Base UOM"
                },
                item_num:{
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                cust_num:{
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                },
                vend_num:{
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.vend_num') ]) }}"
                },
                conv_factor:{
                    number: "{{ __('error.mobile.numbersonly', ['resource' => __('admin.label.conv_factor') ]) }}",
                    number_size: "{{__('error.mobile.max_characters')}}",
                    min_value: "{{ __('error.mobile.morethanequal', ['resource' => __('admin.label.conv_factor')])}} {0}"
                }
            },

            errorPlacement: function(error,element){
            error.insertAfter(element);
            },

            submitHandler: function(form) {
                $(".pageloader").css("display", "block");
                $(".submitloader").attr("disabled", true);
                setTimeout( function () {
                    form.submit();
                }, 300);
            }
        });

        function getConvType()
        {
            if($("#conv_type").val() =="I"){
                $("#itemrow").show();
                $("#item_desc_row").show();
                $("#item_num").prop('required',true);
                $("#vendrow").hide();
                $("#vend_name_row").hide();
                $("#vend_num").prop('required',false);
                $("#custrow").hide();
                $("#cust_name_row").hide();
                $("#cust_num").prop('required',false);
                $("#vend_num,#cust_num").val("");
            }else if($("#conv_type").val() =="V"){
                $("#itemrow").show();
                $("#item_desc_row").show();
                $("#item_num").prop('required',true);
                $("#vendrow").show();
                $("#vend_name_row").show();
                $("#vend_num").prop('required',true);
                $("#custrow").hide();
                $("#cust_name_row").hide();
                $("#cust_num").prop('required',false);
                $("#cust_num").val("");
            }else if($("#conv_type").val() =="C"){
                $("#itemrow").show();
                $("#item_desc_row").show();
                $("#item_num").prop('required',true);
                $("#vendrow").hide();
                $("#vend_name_row").hide();
                $("#vend_num").prop('required',false);
                $("#custrow").show();
                $("#cust_name_row").show();
                $("#cust_num").prop('required',true);
                $("#vend_num").val("");
            }else{
                $("#itemrow").hide();
                $("#item_desc_row").hide();
                $("#item_num").prop('required',false);
                $("#vendrow").hide();
                $("#vend_name_row").hide();
                $("#vend_num").prop('required',false);
                $("#custrow").hide();
                $("#cust_name_row").hide();
                $("#cust_num").prop('required',false);
                $("#vend_num,#cust_num,#item_num").val("");
            }
        }

        //Check Conv. Type
        $("#conv_type").on("change", function(){
            if ($("#conv_type").val() == 'G') {
                ajaxurl ="{{ route('checkuomv', ['uom_from','uom_to'])}}";
                url = ajaxurl.replace('uom_to', $("#uom_to").val());
                url = url.replace('uom_from', $("#uom_from").val());
                $.get(url, function(data){

                    ajaxurl ="{{ route('convtypev', ['uom_from','uom_to', 'conv_type'])}}";
                    url = ajaxurl.replace('conv_type', $("#conv_type").val());
                    url = url.replace('uom_from', $("#uom_from").val());
                    url = url.replace('uom_to', $("#uom_to").val());
                    $.get(url, function(data){
                        if(data == 'exist') {
                            Alert.exists('{{__('Conv. Type')}}', $("#conv_type").val());
                            $("#conv_type, #uom_from, #uom_to").val('');
                        }
                        else{
                            event.preventDefault();
                        }
                    });
                });
            }
        });

        $(document).ready(function() {

            // Call getConvType onpageload (for fail validation)
            getConvType();

            $("#conv_type").on('change',function() {
                getConvType();
            })

            $("#item_num").on("change", function(){
                if ($("#item_num").val()) {
                    // Send error if manually type object that is not exist or inactive
                    $.ajax({
                        url: '{{route('itemv2')}}',
                        type: "GET",
                        data: {
                            item_num: btoa($("#item_num").val()),
                        },
                        success: function(data){
                            if (data == "inactive") {
                                Alert.inactive('{{__('admin.label.item_num')}}',$("#item_num").val());
                                $("#item_num,#item_desc").val('');
                            }
                            else if (data == "not exist") {
                                Alert.notexist('{{__('admin.label.item_num')}}',$("#item_num").val());
                                $("#item_num,#item_desc").val('');
                            }
                            else if (data == "active") {
                                if ($("#conv_type").val() == 'I') {
                                    ajaxurl ="{{ route('checkuomitem', ['uom_from','uom_to', 'conv_type', 'item_num'])}}";
                                    url = ajaxurl.replace('item_num', $("#item_num").val());
                                    url = url.replace('conv_type', $("#conv_type").val());
                                    url = url.replace('uom_from', $("#uom_from").val());
                                    url = url.replace('uom_to', $("#uom_to").val());
                                    $.get(url, function(data){
                                        console.log(data);
                                        if(data == 'exist') {
                                            Alert.exists('{{__('Item')}}', $("#item_num").val());
                                            $("#item_num,#item_desc").val('');
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            });

            $("#cust_num").on('change',function() {
                if ($("#cust_num").val()) {
                    // Send error if manually type object that is not exist or inactive
                    $.ajax({
                        url: '{{route('custv2')}}',
                        type: "GET",
                        data: {
                            cust_num: btoa($("#cust_num").val()),
                        },
                        success: function(data){
                            if (data == "inactive") {
                                Alert.inactive('{{__('admin.label.cust_num')}}',$("#cust_num").val());
                                $("#cust_num,#cust_name").val('');
                            }
                            else if (data == "not exist") {
                                Alert.notexist('{{__('admin.label.cust_num')}}',$("#cust_num").val());
                                $("#cust_num,#cust_name").val('');
                            }
                        }
                    });
                }
            });

            $("#vend_num").on('change',function() {
                if ($("#vend_num").val()) {
                    // Send error if manually type object that is not exist or inactive
                    $.ajax({
                        url: '{{route('vendorv2')}}',
                        type: "GET",
                        data: {
                            vend_num: $("#vend_num").val(),
                        },
                        success: function(data){
                            if (data == "inactive") {
                                Alert.inactive('{{__('admin.label.vend_num')}}',$("#vend_num").val());
                                $("#vend_num,#vend_name").val('');
                            }
                            else if (data == "not exist") {
                                Alert.notexist('{{__('admin.label.vend_num')}}',$("#vend_num").val());
                                $("#vend_num,#vend_name").val('');
                            }
                        }
                    });
                }
            });
        });

        function itemexist() {
            ajaxurl ="{{ route('uomitemv', ['uom_from','uom_to','conv_type', 'item_num', 'cust_num', 'vend_num', 'id'])}}";
            url = ajaxurl.replace('item_num', $("#item_num").val());
            url = url.replace('cust_num', $("#cust_num").val());
            url = url.replace('vend_num', $("#vend_num").val());
            url = url.replace('conv_type', $("#conv_type").val());
            url = url.replace('uom_from', $("#uom_from").val());
            url = url.replace('uom_to', $("#uom_to").val());
            $.get(url, function(data){
                if(data == 'exist'){
                    Alert.exists('{{__('Item')}}', $("#item_num").val());
                    $("#conv_type, #uom_from, #uom_to, #item_num, #cust_num, #vend_num").val('');
                }else{
                    $('#postform').attr('onsubmit','return true;');
                }
            });
        }
    });

    $("#uom_from").on('change',function() {
        $('#item_num').val("");
        if ($("#uom_from").val()) {
            // Send error if manually type object that is not exist or inactive
            $.ajax({
                url: '{{route('uomv2')}}',
                type: "GET",
                data: {
                    uom: $("#uom_from").val(),
                },
                success: function(data){
                    if (data == "not exist") {
                        Alert.notexist('{{__('admin.label.uom')}}',$("#uom_from").val());
                        $("#uom_from").val('');
                    }
                }
            });
        }
    });

    $("#uom_to").on('change',function() {
        if ($("#uom_to").val()) {
            // Send error if manually type object that is not exist or inactive
            $.ajax({
                url: '{{route('uomv2')}}',
                type: "GET",
                data: {
                    uom: $("#uom_to").val(),
                },
                success: function(data){
                    if (data == "not exist") {
                        Alert.notexist('{{__('admin.label.uom')}}',$("#uom_to").val());
                        $("#uom_to").val('');
                    }
                }
            });
        }
    });
</script>
@include('util.selection')
@include('errors.maxchar')
@endsection

