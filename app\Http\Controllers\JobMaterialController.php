<?php

namespace App\Http\Controllers;

use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\Services\SapCallService;
use Illuminate\Http\Request;
use App\Job;
use App\JobMatl;
use App\AlternateBarcode;
use App\Item;
use App\ItemLoc;
use App\Warehouse;
use App\Loc;
use App\UomConv;
use App\Services\GeneralService;
use DB;
use Alert;
use App\Allocation;
use App\Exports\JobOrderExport;
use App\Exports\JobOrderExportV2;
use App\Exports\JobReturn;
use App\Exports\JobReturnV2;
use App\IssuedLot;
use App\Services\LotService;
use App\Services\MatltransService;
use App\Services\UOMService;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use App\View\TparmView;
use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use App\Services\SiteConnectionService;
use App\SiteConnection;
use App\Services\SapApiCallService;
use App\Services\PreassignLotsService;
use App\Services\CatchWeightService;

class JobMaterialController extends Controller
{

    //JOB MATERIAL ISSUE BEGIN
    use \App\Traits\HasDefaultLoc;

    public function index($job_num = "", $oper_num = "", $jobmatldesc = "", $suffix = "")
    {

        if (!\Gate::allows('hasJobMatlIssue')) {
            return view('errors.404')->with('page', 'error');;
        }

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm = $tparm->getTparmValue('JobMaterialIssue', 'enable_warehouse');
        $batch_id = generateBatchId("JobMaterialIssue");

        return view('production.jobmatlissue.index')->with('batch_id', $batch_id)->with('unit_quantity_format', $unit_quantity_format)->with('tparm', $tparm)->with('job_num', $job_num)->with('suffix', $suffix)->with('oper_num', $oper_num)->with('jobmatldesc', $jobmatldesc);
    }
    public function batchIndex($job_num = "", $oper_num = "", $jobmatldesc = "", $suffix = "")
    {
        // $result = SapCallService::postInventoryGenExits("Job Material Issue", "AXA_TEST_JobMaterialIssue_anxfaxee_20240903111526");
        // dd("a");

        // if (!\Gate::allows('hasBatchJobMatlIssue')) {
        //     return view('errors.404')->with('page', 'error');;
        // }

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm = $tparm->getTparmValue('BatchJobMaterialIssue', 'enable_warehouse');
        $batch_id = generateBatchId("BatchJobMaterialIssue");

        return view('production.jobmatlissue.batch.index')->with('batch_id', $batch_id)->with('unit_quantity_format', $unit_quantity_format)->with('tparm', $tparm)->with('job_num', $job_num)->with('suffix', $suffix)->with('oper_num', $oper_num)->with('jobmatldesc', $jobmatldesc);
    }
    public function JobList(Request $request)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');
            $job_num = $request->get('job_num');
            $suffix = $request->get('suffix');
            $oper_num = $request->get('oper_num');
            $matl_item = $request->get('matl_item');
            $whse_num = $request->get('whse_num');

            if ($query != '') {
                $jobmatl = new JobMatl();
                $data = $jobmatl->jobMatlSearchList($job_num, $suffix, $oper_num, $query, $whse_num);
            } else {
                //Removed % from matl_item search
                $data = JobMatl::with('item')
                    ->whereHas('item', function ($q) {
                        $q->where('item_status', 1);
                    })
                    ->where('job_num', $job_num)
                    ->where('suffix', $suffix)
                    ->where('oper_num', $oper_num)
                    ->whereHas('job', function ($quer) use ($whse_num) {
                        return $quer->where('whse_num', $whse_num);
                    })
                    // ->where('whse_num', $whse_num)

                    ->where('qty_balance', '>', 0)
                    ->orderBy('job_num');
                // ->get();
                if ($matl_item != "") {
                    $data = $data->where('matl_item', 'LIKE',   $matl_item);
                }
                $data = $data->get();
                // dd($data);
            }

            $total_row = $data->count();

            if ($total_row > 0) {
                foreach ($data as $row) {
                    $altBarCode = AlternateBarcode::where('item_num', $row->matl_item)->where('site_id', auth()->user()->site_id)->get();
                    $output .= '
                        <div class="container" >
                            <form class="form" autocomplete="off" id="selectjobform" name="selectjobform" action="/home/<USER>/job-material-issue/details"  method="get">
                                <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                                    <div class="col-xs-12">
                                        <table width="100%">
                                            <tr>
                                                <td><label>' . __('mobile.label.sequence') . '</label>
                                                <td colspan="2">
                                                    <span class="form-control border-primary pseudoinput">' . $row->sequence . '</span>
                                                    <input size="5" readonly="" type="hidden" class="form-control border-primary" id="sequence" name="sequence" value="' . $row->sequence . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <input type="hidden" name="_token" value="' . csrf_token() . '">
                                                <input type="hidden" value="' . $row->oper_num . '" name="oper_num" id="oper_num">
                                                <input type="hidden" value="' . $row->suffix . '" name="suffix" id="suffix">
                                                <input type="hidden" value="' . base64_encode($row->job_num) . '" name="job_num" id="job_num">
                                                <input type="hidden" value="' . $row->sequence . '" name="sequence" id="sequence">
                                                <input type="hidden" value="' . $row->whse_num . '" name="whse_num" id="whse_num">

                                                <td width="70px"><label>' . __('mobile.label.item_num') . '</label></td>
                                                <td colspan="2">';
                    foreach ($altBarCode as $barCode) {
                        $output .= '<span style="display: none"> ' . $barCode->alternate_barcode . ' </span>';
                    }
                    $output .= '
                                                    <span class="form-control border-primary pseudoinput">' . $row->matl_item . '</span>
                                                    <input size="10" readonly="" type="hidden" class="form-control border-primary" name="matl_item" value="' . $row->matl_item . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td></td>
                                                <td colspan="2">
                                                    <span class="form-control border-primary pseudoinput" >' . $row->matl_desc . '</span>
                                                    <input size="30" readonly style="text-align:left;"type="hidden"  class="form-control border-primary" name="matl_item_description" value="' . $row->matl_desc . '">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><label>' . __('mobile.label.qty_required') . '</label></td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . numberFormatPrecision($row->qty_required, $unit_quantity_format, '.', '') . '</span>
                                                    <input size="5" readonly="" type="hidden" style="text-align:right" class="form-control border-primary" id="matl_qty_required" name="matl_qty_required" value="' . numberFormatPrecision($row->qty_required, $unit_quantity_format, '.', '') . '">
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . $row->uom . '</span>
                                                    <input type="hidden" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><label>' . __('mobile.label.qty_issued') . '</label></td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . numberFormatPrecision($row->qty_issued, $unit_quantity_format, '.', '') . '</span>
                                                    <input size="10" readonly="" type="hidden" style="text-align:right" class="form-control border-primary" name="qty_issued" value="' . numberFormatPrecision($row->qty_issued, $unit_quantity_format, '.', '') . '">
                                                </td>
                                                <td>
                                                    <span class="form-control border-primary pseudoinput">' . $row->uom . '</span>
                                                    <input type="hidden" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </form>
                        </div>
                    ';
                }
            } else {
                $output = "
                <tr>
                <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );
            echo json_encode($data);
        }
    }

    public function showSelectionList(Request $request)
    {
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'matl_item' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'matl_item.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.matl_items')]),
        ]);
        $job = new JobMatl();
        $job_num = $request->job_num;
        $suffix = $request->suffix;
        $oper_num = $request->oper_num;
        $jobdetails = $job->where('job_num', $job_num)->where('suffix', $suffix)->where('oper_num', $oper_num)->first();


        if ($jobdetails == null) {
            throw ValidationException::withMessages(['job_num' => 'Job Material does not exists.']);
        }
        Session::put('request_data_jobmaterial', $request->all());

        // Send error if job_num's job_status is not released
        $checkJobNum = Job::where('job_num', $request->job_num)->where('whse_num', $request->whse_num)->where('suffix', $request->suffix)->where('job_status', '!=', "R")->exists();
        if ($checkJobNum) {
            throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->job_num . ' cannot be proceed due to status is not released.']);
        }

        $job_num = $request->job_num;
        $suffix = $request->suffix;
        $oper_num = $request->oper_num;
        $matl_item = $request->matl_item;
        $whse_num = $request->whse_num;
        $issued = $request->issued;

        Session::put('matl_item', $matl_item);

        if ($matl_item != null) {
            $matl = JobMatl::where('job_num', $job_num)
                // ->where('whse_num', $whse_num)
                ->where('suffix', $suffix)
                ->where('oper_num', $oper_num)
                ->whereHas('job', function ($quer) use ($whse_num) {
                    return $quer->where('whse_num', $whse_num);
                })
                ->where('matl_item', $matl_item)
                ->get();

            if (count($matl) === 1) {
                $request = new \Illuminate\Http\Request();
                $job_num = base64_encode($job_num);
                $request->replace(['job_num' => $job_num, 'suffix' => $suffix, 'whse_num' => $whse_num, 'oper_num' => $oper_num, 'matl_item' => $matl_item]);
                return $this->jobMatlDetails($request);
            }
        }

        return view('production.jobmatlissue.newjobmatlIssueList')
            ->with('job_num', $job_num)->with('suffix', $suffix)->with('oper_num', $oper_num)->with('matl_item', $matl_item)->with('whse_num', $whse_num)->with('unit_quantity_format', $unit_quantity_format);
    }
    public function showBatchSelectionList(Request $request)
    {
        // dd($request->all());
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $def_location = $tparm->getTparmValue('BatchJobMaterialIssue', 'def_location');
        $tparm_issue_location = $tparm->getTparmValue('BatchJobMaterialIssue', 'issue_location') ? 'BatchJobMaterialIssue' : 0;

        $allow_over_issue = $tparm->getTparmValue('BatchJobMaterialIssue', 'allow_over_issue');
        // dd($def_location);
        $whse_loc = "";
        $def_location = json_decode($def_location);

        if ($def_location) {
            foreach ($def_location as $def_loc) {
                if (strtolower($def_loc->whse_num) == strtolower($request->whse_num))
                    $whse_loc = $def_loc->loc_num;
            }
        }
        // dd($whse_loc);
        // dd($def_location);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            // 'matl_item' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            // 'matl_item.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.matl_items')]),
        ]);

        Session::put('request_data_jobmaterial', $request->all());

        // Send error if job_num's job_status is not released
        $checkJobNum = Job::where('job_num', $request->job_num)->where('whse_num', $request->whse_num)->where('suffix', $request->suffix)->where('job_status', '!=', "R")->exists();
        if ($checkJobNum) {
            throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->job_num . ' cannot be proceed due to status is not released.']);
        }

        $job_num = $request->job_num;
        $suffix = $request->suffix;
        $oper_num = $request->oper_num;
        $whse_num = $request->whse_num;
        $issued = $request->issued;
        // dd($whse_num);

        $matl_items = JobMatl::with('item')
            ->whereHas('item', function ($q) {
                $q->where('item_status', 1);
            })
            ->where('job_num', $job_num)
            ->where('suffix', $suffix)
            // ->where('whse_num', $whse_num)
            ->whereHas('job', function ($quer) use ($whse_num) {
                return $quer->where('whse_num', $whse_num);
            })
            ->where('qty_balance', '>', 0)
            ->orderBy('job_num');
        if ($oper_num != "") {
            $matl_items = $matl_items->where('oper_num', $oper_num);
        }
        $matl_items = $matl_items->get();
        // dd($matl_items);



        if (count($matl_items) == 0) {
            throw ValidationException::withMessages(['job_num' => 'Job Material does not exist.']);
        }
        $joborder = Job::where('job_num', $request->job_num)->where('whse_num', $request->whse_num)->where('suffix', $request->suffix)->first();

        return view('production.jobmatlissue.batch.newjobmatlIssueList')
            ->with('job_num', $job_num)
            ->with('suffix', $suffix)
            ->with('joborder', $joborder)
            ->with('matl_items', $matl_items)
            ->with('oper_num', $oper_num)
            ->with('whse_num', $whse_num)
            ->with('whse_loc', $whse_loc)
            ->with('allow_over_issue', $allow_over_issue)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('tparm_issue_location', $tparm_issue_location);
    }
    public function showSelectionListAfterPrintLabel()
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $job_num = session('job_num');
        $suffix = session('suffix');
        $oper_num = session('oper_num');
        $matl_item = session('matl_item');
        $whse_num = session('whse_num');

        // Send error if job_num's job_status is not released
        if (config('icapt.special_modules.enable_suffix')) {
            $checkJobNum = Job::where('job_num', $job_num)->where('suffix', $suffix)->where('whse_num', $whse_num)->where('job_status', '!=', "R")->exists();
        } else {
            $checkJobNum = Job::where('job_num', $job_num)->where('whse_num', $whse_num)->where('job_status', '!=', "R")->exists();
        }
        if ($checkJobNum) {
            throw ValidationException::withMessages(['job_num' => 'Job Order-' . $job_num . ' cannot be proceed due to status is not released.']);
        }

        if ($matl_item != null) {
            $matl = JobMatl::where('job_num', $job_num)
                // ->where('whse_num', $whse_num)
                ->whereHas('job', function ($quer) use ($whse_num) {
                    return $quer->where('whse_num', $whse_num);
                })
                ->where('oper_num', $oper_num)
                ->where('matl_item', $matl_item)
                ->get();
            if (count($matl) === 1) {
                $request = new \Illuminate\Http\Request();
                $request->replace(['job_num' => base64_encode($job_num), 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'suffix' => $suffix, 'matl_item' => $matl_item]);
                return $this->jobMatlDetails($request);
            }
        }

        return view('production.jobmatlissue.newjobmatlIssueList')
            ->with('job_num', $job_num)
            ->with('suffix', $suffix)
            ->with('oper_num', $oper_num)
            ->with('matl_item', $matl_item)
            ->with('whse_num', $whse_num)
            ->with('unit_quantity_format', $unit_quantity_format);
    }

    public function jobMatlDetails(Request $request)
    {
        //        dd($request->job_num);
        $job_num = base64_decode($request->job_num);
        //        $job_num = $request->job_num;

        $disable_lot_number_selection = 0;
        $defaults = [
            'lot_num' => null,
            'loc_num' => null,
            'qty_available' => null,
            'qty_available_conv' => null,
            'qty_on_hand_conv' => null,
            'qty_on_hand' => null,
            'base_uom' => null,
            'uom_conv' => null,
        ];
        $whse_num = $request->whse_num;
        $joborder = JobMatl::with('item')
            ->where('job_num', $job_num)
            ->where('suffix', $request->suffix)
            // ->where('whse_num', $request->whse_num)
            ->whereHas('job', function ($quer) use ($whse_num) {
                return $quer->where('whse_num', $whse_num);
            })
            ->where('oper_num', $request->oper_num)
            ->where('matl_item', $request->matl_item)
            ->where('sequence', 'like', '%' . $request->sequence . '%')
            ->first();
        //        dd($joborder,$request->all());
        $tparm = new TparmView();
        $allow_over_issue = $tparm->getTparmValue('JobMaterialIssue', 'allow_over_issue');
        $issue_by_item_base_uom = $tparm->getTparmValue('JobMaterialIssue', 'issue_by_item_base_uom');
        $tparm_def_location = $tparm->getTparmValue('JobMaterialIssue', 'def_location');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $tparm_issue_location = $tparm->getTparmValue('JobMaterialIssue', 'issue_location') ? 'JobMaterialIssue' : 0;
        $printLabel = $tparm->getTparmValue('JobMaterialIssue', 'print_label');


        $loc = json_decode($tparm_def_location);

        $defaultLoc = new ItemLoc();
        if ($loc && auth()->user()->getCurrWhse() == $loc->whse_num) {
            $defaultLoc = $defaultLoc->getItemQtyOnHand($loc->whse_num, $loc->loc_num, $request->matl_item);
        }

        $def_loc = "";
        if ($joborder) {
            $defaults = $this->getDefaultIssueLoc($joborder->whse_num, $request->matl_item, $joborder->item->lot_tracked);

            if ($joborder->item->lot_tracked == 1) {
                // Get tparm's disable_lot_number_selection value
                $disable_lot_number_selection = $tparm->getTparmValue('JobMaterialIssue', 'disable_lot_number_selection');

                $lotloc = $this->getDefaultLocLotQtyELNS($joborder, $joborder->item, $disable_lot_number_selection);
                if ($lotloc) {
                    $defaults['lot_num'] = $lotloc->lot_num;
                    $defaults['loc_num'] = $lotloc->loc_num;
                    $defaults['uom_conv'] = $lotloc->uom;
                    $defaults['qty_available_conv'] = $lotloc->qty_available;
                    $defaults['qty_available'] = $lotloc->qty_available;
                    $defaults['qty_on_hand_conv'] = $lotloc->qty_available;
                    $defaults['qty_on_hand'] = $lotloc->qty_available;
                    $def_loc = $lotloc->loc_num;
                } else {
                    $defaults['lot_num'] = null;
                    $defaults['loc_num'] = null;
                    $defaults['uom_conv'] = null;
                    $defaults['qty_available_conv'] = null;
                    $defaults['qty_available'] = null;
                    $defaults['qty_on_hand_conv'] = null;
                    $defaults['qty_on_hand'] = null;
                }
            }

            $total_amount = 0.00;
            $data = [];
            $getItem = Item::where('item_num', $joborder->matl_item)->first();

            // Get Job qty required
            $getQtyRequired = Job::where('job_num', $joborder->job_num)->where('suffix', $joborder->suffix)->first();

            if ($joborder->uom == $getItem->uom) {
                $data = [
                    'total_amount' => $joborder->qty_balance,
                    'uom' => $joborder->uom
                ];
            } else {
                $getConv = UomConv::where('item_num', $joborder->matl_item)->where('uom_to', $joborder->uom)->where('conv_type', 'I')->first();
                if ($getConv) {
                    if ($getItem->issue_by_unit == 1 && $getItem->round_up == 1 && $joborder->qty_per <= $getConv->conv_factor) {
                        $total_amount = (ceil($getQtyRequired->qty_released / ((int)($getConv->conv_factor / $joborder->qty_per)))) / (1 - $joborder->scrap_factor);
                    } else if ($getItem->issue_by_unit == 1 && $joborder->qty_per <= $getConv->conv_factor) {
                        $total_amount = ($getQtyRequired->qty_released / ((int)($getConv->conv_factor / $joborder->qty_per))) / (1 - $joborder->scrap_factor);
                    } else if ($getItem->round_up == 1) {
                        $total_amount = (ceil($getQtyRequired->qty_released / ($getConv->conv_factor / $joborder->qty_per))) / (1 - $joborder->scrap_factor);
                    } else {
                        $total_amount = ($getQtyRequired->qty_released / ($getConv->conv_factor / $joborder->qty_per)) / (1 - $joborder->scrap_factor);
                    }
                } else {

                    $getConvGlobFirst = UomConv::where('uom_to', $joborder->uom)->where('uom_from', $getItem->uom)->where('conv_type', 'G')->first();
                    $getConvGlobSecond = UomConv::where('uom_to', $getItem->uom)->where('uom_from', $joborder->uom)->where('conv_type', 'G')->first();


                    if ($getConvGlobFirst != null) {
                        $getConvGlob = $getConvGlobFirst;
                        $operation = 'divided';
                    } elseif ($getConvGlobSecond != null) {
                        $getConvGlob = $getConvGlobSecond;
                        $operation = 'multiple';
                    }
                    // dd($getQtyRequired);
                    //  dd($joborder->scrap_factor,$getConvGlob->conv_factor ,$getQtyRequired->qty_released,$joborder->qty_per);
                    if ($getConvGlob) {
                        if ($getItem->issue_by_unit == 1 && $getItem->round_up == 1 && $joborder->qty_per <= $getConvGlob->conv_factor) {
                            $total_amount = (ceil($getQtyRequired->qty_released / ((int)($getConvGlob->conv_factor / $joborder->qty_per)))) / (1 - $joborder->scrap_factor);
                        } else if ($getItem->issue_by_unit == 1 && $joborder->qty_per <= $getConvGlob->conv_factor) {
                            $total_amount = ($getQtyRequired->qty_released / ((int)($getConvGlob->conv_factor / $joborder->qty_per))) / (1 - $joborder->scrap_factor);
                        } else if ($getItem->round_up == 1) {
                            $total_amount = (ceil($getQtyRequired->qty_released / ($getConvGlob->conv_factor / $joborder->qty_per))) / (1 - $joborder->scrap_factor);
                        } else {

                            // qty released * qty per /  (1- scrap factor)

                            if ($operation == 'multiple') {

                                $total_amount = $joborder->qty_balance * $getConvGlob->conv_factor;
                            } else {
                                $total_amount = $joborder->qty_balance / $getConvGlob->conv_factor;
                            }
                        }
                    }
                }

                $data = ['total_amount' => $total_amount, 'uom' => $getItem->uom];
            }
        }

        $defaults['indicate'] = 1;
        $url = generateRedirectUrl('JobMaterialIssue', $defaults);

        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $batch_id = generateBatchId("JobMaterialIssue");
        $view =  $getItem->catch_weight ? 'production.jobmatlissue.process_cw' :'production.jobmatlissue.process';

        return view($view)
            ->with('joborder', $joborder)
            ->with('defaults', $defaults)
            ->with('base_uom', $data)
            ->with('allow_over_issue', $allow_over_issue)
            ->with('issue_by_item_base_uom', $issue_by_item_base_uom)
            ->with('disable_lot_number_selection', $disable_lot_number_selection)
            ->with('sap_trans_order_integration', $sap_trans_order_integration)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('tparm_issue_location', $tparm_issue_location)
            // for CW
            ->with('disable_create_new_item_location', 0)
            ->with('batch_id', $batch_id)
            ->with('ItemList', $getItem)
            ->with('printLabel', $printLabel)
            ->with('def_loc', $def_loc)
            ->with('url', $url);
    }

    public function jobMatlProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }
        $request = validateSansentiveValue($request);

        // DB::beginTransaction();
        // try {
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'Job Material Issue', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        $jobmatl = JobMatl::where('job_num', $request->ref_num)
            ->where('suffix', $request->suffix)
            ->where('oper_num', $request->ref_line)
            ->where('sequence', $request->ref_release)
            ->where('matl_item', $request->item_num)
            ->first();

        // Verifying JobMatl exist
        if (!$jobmatl) {
            throw ValidationException::withMessages([__(
                'error.mobile.notexist',
                ['resource' => '[' . $request->ref_num . '-' . $request->suffix . '-' . $request->ref_line . '-' . $request->ref_release . '-' . $request->item_num . ']']
            )]);
        }

        LotService::checkExpiryDate('allow_expired_item_JobMaterialIssue', $request);

        $transDate = Carbon::now()->toDateTimeString();
        Session::put('timestamp', $transDate);

        $job_num = $request->ref_num;
        $oper_num = $request->ref_line;
        $whse_num = $request->whse_num;
        $suffix = $request->suffix;
        $item_num = $request->item_num;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        $sequence = $request->ref_release;
        $qty = $request->qty;
        $uom = $request->uom;
        $baseuom = $request->qty_available_uom;
        $selectuom = $request->uom;
        $lineuom = $request->ref_uom;
        $transType = 'Job Material Issue';
        $request['trans_type'] = $transType;
        // $request['trans_type'] = $transType;

        // Conversion for inventory and material trans - convert to item UOM
        //$request = UOMService::convertRequestItemGlobal($request);

        //check if the uom is same with base uom if not change the value to the same uom
        //if ($request->qty_available_uom != $uom) {
        $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, '', '', $transType);
        // $uom_conv = UomConv::convert($uom, $request->qty, $item_num, null, null, $request->qty_available_uom);
        // $request->merge([
        //     'suffix' => $suffix,
        //     'qty' => -1 * $request->qty,
        //     'qty_conv' => -1 * $getQtyConv['conv_qty_to_base']['qty'],
        //     'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
        //     'trans_uom' => $getQtyConv['conv_qty_to_line']['uom'],
        //     'trans_qty' => -1 * $getQtyConv['conv_qty_to_line']['qty'],
        //     'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
        // ]);
        // $request->merge([
        //     'qty_conv' => $getQtyConv['conv_qty_to_base']['qty'],
        //     'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
        //     'trans_uom' => $getQtyConv['conv_qty_to_line']['uom'],
        //     'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
        // ]);
        $request->merge([
            'suffix' => $suffix,
            'trans_qty' => -1 * $request->qty,
            'trans_uom' => $request->uom,
            'qty_conv' =>  $getQtyConv['conv_qty_to_base']['qty'],
            'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
            'uom' => $getQtyConv['conv_qty_to_line']['uom'],
            'qty' => -1 * $getQtyConv['conv_qty_to_line']['qty'],
            'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
        ]);
        // dd($request->all());
        //  } else {
        //     $request->merge([
        //         'qty_conv' => $request->qty,
        //         'qty_line' => $request->qty,

        //     ]);
        // }
        // dd($lineuom,$selectuom,$request);
        // Update Allocation
        DB::beginTransaction();
        $this->updateAllocations($request);

        $checkJobMatl = GeneralService::checkJobMatl($job_num, $suffix, $oper_num, $sequence, $request->qty_line);

        if (!$checkJobMatl) {
            DB::rollback();
            $request->replace(['job_num' => $job_num, 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'matl_item' => $item_num, 'sequence' => $sequence]);
            throw ValidationException::withMessages(['qty' => 'Quantity Item To Issue more than Qty Open']);
            return $this->jobMatlDetails($request)->withInput();
        }

        $newTrans =  MatltransService::postTransaction($request);

        // $newTrans = GeneralService::newTrans('Job Material Issue', $request, $whse_num, $loc_num, $item_num, -$request->qty_conv, $lot_num, $request->uom_conv);

        if (!$newTrans) {
            DB::rollback();
            throw ValidationException::withMessages(['qty' => 'Quantity On Hand is not enough']);
            $request->replace(['job_num' => $job_num, 'suffix' => $suffix, 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'matl_item' => $item_num, 'sequence' => $sequence]);

            return $this->jobMatlDetails($request);
        }

        $itemToTrack = new Item();

        // Is item lot tracked?
        $lot_tracked = $itemToTrack->select('lot_tracked', 'item_desc')
            ->where('item_num', $request->item_num)
            ->first();

        $lot_track = $lot_tracked->lot_tracked;

        $updateJobMatl = GeneralService::updateJobMatl($job_num, $suffix, $oper_num, $sequence, $request->qty_line);

        $this->updateIssuedLot($request);
        // Future webhook here
        DB::commit();
        Alert::success('Success', __('success.processed', ['process' => __('Job Material Issue')]));

        Session::put('modulename', 'JobMaterialIssue');
        Session::put('job_num', $job_num);
        Session::put('suffix', $suffix);
        Session::put('oper_num', $oper_num);
        Session::put('whse_num', $whse_num);

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $tparm = new TparmView;
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

        $request->merge([
            'sap_single_bin' => $sap_single_bin
        ]);

        // SAP Integration
        if ($sap_trans_order_integration == 1) {
            //$res = SapCallService::postJobMaterialIssue($request);

            if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                // Later Change to read from Matltrans
                if ($sap_single_bin == 1) {
                    $result = SiteConnectionService::postIntergrationTrans("Job Material Issue", $batch_id = 0, $request);
                } else {
                    $result = SapCallService::postInventoryGenExits('Job Material Issue', 0, $request);
                }
            } else {

                if (config('icapt.enable_sap_resync')) {
                    //dd($request);
                    $result = SapCallService::postJobMaterialIssueResync($request);
                } else {
                    $result = SapCallService::postJobMaterialIssue($request);
                }
            }
            if ($result != 200) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
            }
        }

        // DB::commit();

        if ($request->lot_num != null) {
            $check_expiry_date = LotService::getExpiryDate($request);
            // Generate barcode
            // change the qty_conv to qty because uom passed was not converted
            $input = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num, $qty, $uom, $whse_num, $loc_num, $lot_num, $check_expiry_date, $transDate, 'JobMaterialIssue');
        } else {
            // Generate barcode
            // change the qty_conv to qty because uom passed was not converted

            $input = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num, $qty, $uom, $whse_num, $loc_num, $lot_num, null, $transDate, 'JobMaterialIssue');
        }

        $tparm = new TparmView;
        $print_label = $tparm->getTparmValue('JobMaterialIssue', 'print_label');

        if ($print_label == 1) {
            return BarcodeController::showLabelDefinition($input);
        } else {
            return app('App\Http\Controllers\RouteController')->BackButton();
        }
        // } catch (Exception $e) {
        //     DB::rollback();
        //     throw $e;
        // }
    }

    public function runJobMatlIssueCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }
        $request = validateSansentiveValue($request);

        // DB::beginTransaction();
        // try {
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'Job Material Issue', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        $jobmatl = JobMatl::where('job_num', $request->ref_num)
            ->where('suffix', $request->suffix)
            ->where('oper_num', $request->ref_line)
            ->where('sequence', $request->ref_release)
            ->where('matl_item', $request->item_num)
            ->first();

        // Verifying JobMatl exist
        if (!$jobmatl) {
            throw ValidationException::withMessages([__(
                'error.mobile.notexist',
                ['resource' => '[' . $request->ref_num . '-' . $request->suffix . '-' . $request->ref_line . '-' . $request->ref_release . '-' . $request->item_num . ']']
            )]);
        }

        // Get Tolerance and UOM
        $tolerance = $jobmatl->qty_required - $jobmatl->qty_issued + $jobmatl->qty_returned;
        $tolerance_uom = $jobmatl->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);
        $request->merge([
            'qty' => array_sum($request->arr_qty ?? []),
            'base_uom' => $jobmatl->uom
        ]);

        LotService::checkExpiryDate('allow_expired_item_JobMaterialIssue', $request);

        $transDate = Carbon::now()->toDateTimeString();
        Session::put('timestamp', $transDate);

        $job_num = $request->ref_num;
        $oper_num = $request->ref_line;
        $whse_num = $request->whse_num;
        $suffix = $request->suffix;
        $item_num = $request->item_num;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        $sequence = $request->ref_release;
        $qty = $request->qty;
        $uom = $request->uom;
        $baseuom = $request->qty_available_uom;
        $selectuom = $request->uom;
        $lineuom = $request->ref_uom;
        $transType = 'Job Material Issue';
        $request['trans_type'] = $transType;
        // $request['trans_type'] = $transType;

        // Conversion for inventory and material trans - convert to item UOM
        //$request = UOMService::convertRequestItemGlobal($request);

        //check if the uom is same with base uom if not change the value to the same uom
        //if ($request->qty_available_uom != $uom) {
        $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, '', '', $transType);
        // $uom_conv = UomConv::convert($uom, $request->qty, $item_num, null, null, $request->qty_available_uom);
        // $request->merge([
        //     'suffix' => $suffix,
        //     'qty' => -1 * $request->qty,
        //     'qty_conv' => -1 * $getQtyConv['conv_qty_to_base']['qty'],
        //     'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
        //     'trans_uom' => $getQtyConv['conv_qty_to_line']['uom'],
        //     'trans_qty' => -1 * $getQtyConv['conv_qty_to_line']['qty'],
        //     'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
        // ]);
        // $request->merge([
        //     'qty_conv' => $getQtyConv['conv_qty_to_base']['qty'],
        //     'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
        //     'trans_uom' => $getQtyConv['conv_qty_to_line']['uom'],
        //     'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
        // ]);
        $request->merge([
            'suffix' => $suffix,
            'trans_qty' => -1 * $request->qty,
            'trans_uom' => $request->uom,
            'qty_conv' =>  $getQtyConv['conv_qty_to_base']['qty'],
            'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
            'uom' => $getQtyConv['conv_qty_to_line']['uom'],
            'qty' => -1 * $getQtyConv['conv_qty_to_line']['qty'],
            'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
        ]);
        // dd($request->all());
        //  } else {
        //     $request->merge([
        //         'qty_conv' => $request->qty,
        //         'qty_line' => $request->qty,

        //     ]);
        // }
        // dd($lineuom,$selectuom,$request);
        // Update Allocation
        DB::beginTransaction();
        try {
            $this->updateAllocations($request);

            $checkJobMatl = GeneralService::checkJobMatl($job_num, $suffix, $oper_num, $sequence, $request->qty_line);

            if (!$checkJobMatl) {
                DB::rollback();
                $request->replace(['job_num' => $job_num, 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'matl_item' => $item_num, 'sequence' => $sequence]);
                throw ValidationException::withMessages(['qty' => 'Quantity Item To Issue more than Qty Open']);
                return $this->jobMatlDetails($request)->withInput();
            }

            // $newTrans =  MatltransService::postTransaction($request);

            // // $newTrans = GeneralService::newTrans('Job Material Issue', $request, $whse_num, $loc_num, $item_num, -$request->qty_conv, $lot_num, $request->uom_conv);

            // if (!$newTrans) {
            //     DB::rollback();
            //     throw ValidationException::withMessages(['qty' => 'Quantity On Hand is not enough']);
            //     $request->replace(['job_num' => $job_num, 'suffix' => $suffix, 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'matl_item' => $item_num, 'sequence' => $sequence]);

            //     return $this->jobMatlDetails($request);
            // }

            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $request->tolerance_uom, $transType, $transType);

            $itemToTrack = new Item();

            // Is item lot tracked?
            $lot_tracked = $itemToTrack->select('lot_tracked', 'item_desc')
                ->where('item_num', $request->item_num)
                ->first();

            $lot_track = $lot_tracked->lot_tracked;

            $updateJobMatl = GeneralService::updateJobMatl($job_num, $suffix, $oper_num, $sequence, $request->qty_line);

            $this->updateIssuedLot($request);
            // Future webhook here

            Alert::success('Success', __('success.processed', ['process' => __('Job Material Issue')]));

            Session::put('modulename', 'JobMaterialIssue');
            Session::put('job_num', $job_num);
            Session::put('suffix', $suffix);
            Session::put('oper_num', $oper_num);
            Session::put('whse_num', $whse_num);

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

            $tparm = new TparmView;
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

            $request->merge([
                'sap_single_bin' => $sap_single_bin
            ]);

            // SAP Integration
            if ($sap_trans_order_integration == 1) {
                //$res = SapCallService::postJobMaterialIssue($request);

                if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                    // Later Change to read from Matltrans
                    if ($sap_single_bin == 1) {
                        $result = SiteConnectionService::postIntergrationTrans("Job Material Issue", $batch_id = 0, $request);
                    } else {
                        $result = SapCallService::postInventoryGenExits('Job Material Issue', 0, $request);
                    }
                } else {

                    if (config('icapt.enable_sap_resync')) {
                        //dd($request);
                        $result = SapCallService::postJobMaterialIssueResync($request);
                    } else {
                        $result = SapCallService::postJobMaterialIssue($request);
                    }
                }
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            DB::commit();

            // if ($request->lot_num != null) {
            //     $check_expiry_date = LotService::getExpiryDate($request);
            //     // Generate barcode
            //     // change the qty_conv to qty because uom passed was not converted
            //     $input = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num, $qty, $uom, $whse_num, $loc_num, $lot_num, $check_expiry_date, $transDate, 'JobMaterialIssue');
            // } else {
            //     // Generate barcode
            //     // change the qty_conv to qty because uom passed was not converted

            //     $input = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num, $qty, $uom, $whse_num, $loc_num, $lot_num, null, $transDate, 'JobMaterialIssue');
            // }

            // $tparm = new TparmView;
            // $print_label = $tparm->getTparmValue('JobMaterialIssue', 'print_label');

            // if ($print_label == 1) {
            //     return BarcodeController::showLabelDefinition($input);
            // } else {
                return app('App\Http\Controllers\RouteController')->BackButton();
            // }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function BatchJobMatlProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }
        $request = validateSansentiveValue($request);
        $request->validate([
            'whse_loc' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
            // 'matl_item' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'whse_loc.required' => __('error.mobile.required', ['resource' => __('mobile.label.defult_issue_loc')]),

            'whse_loc.exists' => __('error.admin.is_invalid', ['resource' => __('mobile.label.defult_issue_loc')]),
            // 'matl_item.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.matl_items')]),
        ]);
        $sap_ok = true;
        $user = auth()->user();
        $transType = "JobMaterialIssue";
        $batch_id = $user->site_id  . "_" . $transType  . "_" . $user->name . "_" . date("YmdHis");
        // dd($batch_id);
        DB::beginTransaction();
        try {
            // $request->validate([
            //     'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            //     'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            //     'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
            // ], [
            //     'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            //     'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            //     'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
            // ]);

            // LotService::checkExpiryDate('allow_expired_item_JobMaterialIssue', $request);

            $request['batch_id'] = $batch_id;
            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
            $request->merge([
                'sap_single_bin' => $sap_single_bin
            ]);

            $job_matls = JobMatl::whereIn('id', $request->selected)->get();
            // dd($request->matl_items);
            $matl_items = $request->matl_items ?? [];
            $apiController = new \App\Http\Controllers\ApiController();

            // Conversion for inventory and material trans - convert to item UOM
            //$request = UOMService::convertRequestItemGlobal($request);
            $loc_num = $request->whse_loc;
            $lot_num = $request->lot_num;

            // dd($request);
            $arrInput = [];
            //check if the uom is same with base uom if not change the value to the same uom
            foreach ($job_matls as $job_matl) {

                $job_num = $job_matl->job_num;
                $oper_num = $job_matl->oper_num;
                $whse_num = $job_matl->job->whse_num;
                $suffix = $job_matl->suffix;

                $sequence = $job_matl->sequence;
                $item_num = $job_matl->matl_item;
                // $request['qty'] =  $job_matl->qty_balance;
                // dd($matl_items[$job_matl->id]['qty_issued']);
                // dd($matl_items[$job_matl->id]);
                $request['qty'] = $matl_items[$job_matl->id]['qty_issued'] ? parseNumericValue($matl_items[$job_matl->id]['qty_issued']) : 0;

                $qty_avail_obj = $apiController->displayQtyItemAvailable(base64_encode($whse_num), base64_encode($job_matl->matl_item), base64_encode($loc_num));
                // dd($whse_num, $job_matl->matl_item, $loc_num);
                $qty_available = parseNumericValue($qty_avail_obj->qty_available);
                // dd($request['qty'], $qty_available);
                if ($request['qty'] > $qty_available) {
                    // dd($request->all());
                    throw ValidationException::withMessages(['qty' => 'Quantity To Issue more than Qty Required']);
                    return $this->showBatchSelectionList($request)->withInput($request->all());
                }
                // dd($request['qty'], $qty_available);
                // dd($request->qty_available_uom, $qty_avail_obj);
                $request['uom'] = $job_matl->uom;
                $request['qty_available_uom'] = $qty_avail_obj->uom;
                $request['ref_num'] = $job_matl->job_num;
                $request['ref_line'] = $job_matl->oper_num;
                $request['ref_release'] = $job_matl->sequence;
                $request['item_num'] = $job_matl->matl_item;
                $request['loc_num'] = $loc_num;
                $request['trans_type'] = "Job Material Issue";

                $request->merge([
                    'suffix' => $job_matl->suffix,
                    'qty' => -1 * $request->qty,
                    'qty_conv' =>  $request->qty,
                    'trans_qty' => -1 * $request->qty,
                    'uom_conv' => $job_matl->uom,
                    'trans_uom' => $job_matl->uom,
                ]);

                // dd($request->qty_available_uom);
                $this->updateAllocations($request);


                $checkJobMatl = GeneralService::checkJobMatl($job_num, $suffix, $oper_num, $sequence, $request->qty_conv, true);

                if (!$checkJobMatl) {
                    $request->replace(['job_num' => $job_num, 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'matl_item' => $item_num, 'sequence' => $sequence]);
                    throw ValidationException::withMessages(['qty' => 'Qty must be less than or equal to ' . $job_matl->qty_balance]);
                    return $this->showBatchSelectionList($request)->withInput();
                }

                $newTrans =  MatltransService::postTransaction($request);

                // $newTrans = GeneralService::newTrans('Job Material Issue', $request, $whse_num, $loc_num, $item_num, -$request->qty_conv, null, $request->uom_conv);

                if (!$newTrans) {
                    throw ValidationException::withMessages(['qty' => 'Quantity On Hand is not enough']);
                    $request->replace(['job_num' => $job_num, 'suffix' => $suffix, 'oper_num' => $oper_num, 'whse_num' => $whse_num, 'matl_item' => $item_num, 'sequence' => $sequence]);

                    return $this->showBatchSelectionList($request);
                }
                $uniquekey = $job_num . "-" . $suffix . "-" . $oper_num . "-" . $sequence . "-" . $item_num;
                $updateJobMatl = GeneralService::updateJobMatl($job_num, $suffix, $oper_num, $sequence, $request->qty_conv);
                $arrInput[$uniquekey]  = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num,  $request->qty_conv, $job_matl->uom, $whse_num, $loc_num, $lot_num, null, $transDate, 'JobMaterialIssue');
            }

            //dd($request,$uom);
            // Update Allocation



            // dd($arrInput);
            DB::commit();

            // Future webhook here

            // SAP Integration
            if ($sap_trans_order_integration == 1) {
                // $res = SapCallService::postJobMaterialIssue($request);
                if ($sap_single_bin == 1) {
                    $arrData = ['batch_id' => $batch_id];
                    $result = SiteConnectionService::postIntergrationTrans("Batch Job Material Issue", $arrData, $request);
                } else {
                    // $sapresult = SapCallService::postStockTransferFromMaltransBulk('Job Material Issue');
                    $result = SapCallService::postInventoryGenExits("Batch Job Material Issue", $batch_id, $request);
                }

                if ($result != 200) {
                    $sap_ok = false;
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            if ($sap_ok)
                Alert::success('Success', __('success.processed', ['process' => __('Batch Job Material Issue')]));

            Session::put('modulename', 'BatchJobMaterialIssue');
            Session::put('job_num', $job_num);
            Session::put('suffix', $suffix);
            Session::put('oper_num', $oper_num);
            Session::put('whse_num', $whse_num);

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('BatchJobMaterialIssue', 'print_label');

            if ($print_label == 1) {
                //return BarcodeController::showLabelDefinition($input);
                return BarcodeController::showLabelDefinitionMultiple($arrInput, 0);
            }
            // else {

            // return app('App\Http\Controllers\RouteController')->BackButton();
            // }


            return redirect()->route('BatchJobMaterialIssue');

            // return app('App\Http\Controllers\RouteController')->BackButton();
        } catch (Exception $e) {
            DB::rollback();
            // dd("a");
            throw $e;
        }
    }
    //JOB MATERIAL ISSUE END
    //JOB MATERIAL UNISSUE BEGIN

    public function unIssueIndex()
    {
        if (!\Gate::allows('hasJobMatlReturn')) {
            return view('errors.404')->with('page', 'error');
        }

        $tparm = new TparmView();
        $tparm = $tparm->getTparmValue('JobMaterialUnIssue', 'enable_warehouse');
        return view('production.jobmatliunissue.index')->with('tparm', $tparm);
    }

    public function showUnIssueSelectionList(Request $request)
    {
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'matl_item' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'matl_item.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.matl_items')]),
        ]);

        Session::put('request_data_jobmaterial', $request->all());

        // Send error if job_num's job_status is not released
        $checkJobNum = Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('job_status', '!=', "R")->exists();
        if ($checkJobNum) {
            throw ValidationException::withMessages(['job_num' => 'Job Order-' . $request->job_num . '-' . $request->suffix . ' cannot be proceed due to status is not released.']);
        }

        $whse_num = $request->whse_num;
        $job_num = $request->job_num;
        $suffix = $request->suffix;
        $oper_num = $request->oper_num;
        $matl_item = $request->matl_item;
        $backflush = $request->backflush;
        $issued = $request->issued;
        $infoMsg = "";

        if ($matl_item != null) {
            $request = new \Illuminate\Http\Request();
            $request->replace(['whse_num' => $whse_num, 'job_num' => base64_encode($job_num), 'suffix' => $suffix, 'oper_num' => $oper_num, 'matl_item' => $matl_item]);

            return $this->unIssueJobDetails($request);
        }

        $joborder = new JobMatl();

        // Show jobs with "qty_issued = 0" when user choose "Issued = No"
        //Removed % from matl item search for issue 542
        if ($issued == '0') {
            $joborder = $joborder->where('job_num', $job_num)
                // ->where('whse_num', $whse_num)
                ->whereHas('job', function ($quer) use ($whse_num) {
                    return $quer->where('whse_num', $whse_num);
                })
                ->where('oper_num', 'like', '%' . $oper_num . '%')
                ->where('matl_item', 'like', '' . $matl_item . '')
                ->where('qty_issued', '>=', 0)
                ->get();
        }
        // Show jobs with "qty_issued >= 0" when user choose "Issued = Yes"
        elseif ($issued == '1') {
            $joborder = $joborder->where('job', $job_num)
                // ->where('whse_num', $whse_num)
                ->whereHas('job', function ($quer) use ($whse_num) {
                    return $quer->where('whse_num', $whse_num);
                })
                ->where('suffix', $suffix)
                ->where('oper_num', 'like', '%' . $oper_num . '%')
                ->where('matl_item', 'like', '' . $matl_item . '')
                ->where('qty_issued', '>=', 0)
                ->get();
        }

        return view('production.jobmatliunissue.newjobmatlIssueList')
            ->with('joborder', $joborder)->with('job_num', $job_num)->with('suffix', $suffix)->with('oper_num', $oper_num)->with('whse_num', $whse_num)->with('unit_quantity_format', $unit_quantity_format);
    }

    public function JobUnissueList(Request $request)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');
            $job_num = $request->get('job_num');
            $oper_num = $request->get('oper_num');
            $whse_num = $request->get('whse_num');

            if ($query != '') {
                $jobmatl = new JobMatl();

                $data = $jobmatl->jobMatlSearchList($job_num, $oper_num, $query, $whse_num);
            } else {
                $data = JobMatl::with('item')
                    ->whereHas('item', function ($q) {
                        $q->where('item_status', 1);
                    })
                    ->where('job_num', $job_num)
                    ->whereHas('job', function ($quer) use ($whse_num) {
                        return $quer->where('whse_num', $whse_num);
                    })
                    // ->where('whse_num', $whse_num)
                    ->where('oper_num', $oper_num)
                    ->where('qty_issued', '>', 0)
                    ->orderBy('job_num')
                    ->get();
            }
            $total_row = $data->count();

            if ($total_row > 0) {
                foreach ($data as $row) {
                    $altBarCode = AlternateBarcode::where('item_num', $row->matl_item)->where('site_id', auth()->user()->site_id)->get();
                    $output .= '
            <div class="container" >
                <form class="form" autocomplete="off" id="selectjobform" name="selectjobform" action="/home/<USER>/job-material-return/details" method="get">
                    <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                        <div class="col-xs-12">
                            <table style="width: 100%;">
                                <tr>
                                    <td width="50px"><label>' . __('mobile.label.sequence') . '</label>
                                    <td colspan="4">
                                        <span class="form-control border-primary pseudoinput">' . $row->sequence . '</span>
                                        <input size="5" readonly="" type="hidden" class="form-control border-primary" id="sequence" name="sequence" value="' . $row->sequence . '">
                                    </td>
                                </tr>
                                <tr>
                                    <input type="hidden" name="_token" value="' . csrf_token() . '">
                                    <input type="hidden" value="' . $row->oper_num . '" name="oper_num" id="oper_num">
                                    <input type="hidden" value="' . $row->suffix . '" name="suffix" id="suffix">
                                    <input type="hidden" value="' . base64_encode($row->job_num) . '" name="job_num" id="job_num">
                                    <input type="hidden" value="' . $row->whse_num . '" name="whse_num" id="whse_num">
                                    <td width="50px"><label>' . __('mobile.label.item_num') . '</label></td>
                                    <td colspan="4">';
                    foreach ($altBarCode as $barCode) {
                        $output .= '<span style="display: none"> ' . $barCode->alternate_barcode . ' </span>';
                    }
                    $output .= '
                                        <span class="form-control border-primary pseudoinput">' . $row->matl_item . '</span>
                                        <input size="10" readonly="" style="text-align=left;" type="hidden" class="form-control border-primary" name="matl_item" value="' . $row->matl_item . '">
                                    </td>
                                </tr>
                                <tr>
                                    <td width="50px"></td>
                                    <td colspan="4">
                                        <span class="form-control border-primary pseudoinput">' . $row->matl_desc . '</span>
                                        <input size="30" readonly style="text-align:left;" type="hidden" class="form-control border-primary" name="matl_item_description" value="' . $row->matl_desc . '">
                                    </td>
                                </tr>
                                <tr>
                                    <td width="50px"><label>' . __('mobile.label.qty_required') . '</label></td>
                                    <td>
                                        <span class="form-control border-primary pseudoinput">' . numberFormatPrecision($row->qty_required, $unit_quantity_format, '.', '') . '</span>
                                        <input size="10" readonly="" type="hidden" style="text-align:right" class="form-control border-primary" id="matl_qty_required" name="matl_qty_required" value="' . numberFormatPrecision($row->qty_required, $unit_quantity_format, '.', '') . '">
                                    </td>
                                    <td>
                                        <span class="form-control border-primary pseudoinput">' . $row->uom . '</span>
                                        <input type="hidden" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                    </td>
                                </tr>
                                <tr>
                                    <td width="70px"><label>' . __('mobile.label.qty_issued') . '</label></td>
                                    <td width="100px">
                                        <span class="form-control border-primary pseudoinput">' . numberFormatPrecision($row->qty_issued, $unit_quantity_format, '.', '') . '</span>
                                        <input size="10" readonly="" type="hidden" style="text-align:right" class="form-control border-primary" name="qty_issued" value="' . numberFormatPrecision($row->qty_issued, $unit_quantity_format, '.', '') . '">
                                    </td>
                                    <td width="100px">
                                    <span class="form-control border-primary pseudoinput">' . $row->uom . '</span>
                                        <input type="hidden" class="form-control border-primary" name="uom" value="' . $row->uom . '" readonly>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </form>
            </div>
            ';
                }
            } else {
                $output = "
            <tr>
            <td align='center' colspan='4'>Record not found</td>
            </tr>
            ";
            }
            $data = array(
                'table_data' => $output,
                'total_data' => $total_row
            );

            echo json_encode($data);
        }
    }

    public function unIssueJobDetails(Request $request)
    {
        $item = new Item();
        $whse_num = $request->whse_num;
        $suffix = $request->suffix;
        $job_num = base64_decode($request->job_num);
        $oper_num = $request->oper_num;
        $matl_item = $request->matl_item;
        $sequence = $request->sequence;
        $transType = "Job Material Return";
        $batch_id = generateBatchId($transType);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if (isset($sequence)) {
            $jobmatl = new JobMatl();
            $jobmatl = $jobmatl->where('job_num', $job_num)
                ->where('suffix', $suffix)
                // ->where('whse_num', $whse_num)
                ->whereHas('job', function ($quer) use ($whse_num) {
                    return $quer->where('whse_num', $whse_num);
                })
                ->where('oper_num', $oper_num)
                ->where('sequence', $sequence)
                ->where('matl_item', $matl_item)
                ->first();
        } else {
            $jobmatl = new JobMatl();
            $jobmatl = $jobmatl->where('job_num', $job_num)
                ->where('suffix', $suffix)
                // ->where('whse_num', $whse_num)
                ->whereHas('job', function ($quer) use ($whse_num) {
                    return $quer->where('whse_num', $whse_num);
                })
                ->where('oper_num', $oper_num)
                ->where('matl_item', $matl_item)
                ->first();
        }
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        if ($jobmatl) {
            $item_details = $item->select('lot_tracked', 'catch_weight', 'catch_weight_tolerance')->where('item_num', $matl_item)->first();
            $defaults = $this->getLocByRankReceipt($jobmatl->whse_num, $matl_item);

            // Check if item is catch weight enabled
            if ($item_details->catch_weight == 1) {
                // Get additional parameters for catch weight view
                $tparm = new TparmView;
                $disable_create_new_item_location = $tparm->getTparmValue('JobMaterialReturn', 'disable_create_new_item_location');
                $allow_over_return = $tparm->getTparmValue('JobMaterialReturn', 'allow_over_return');
                $printLabel = $tparm->getTparmValue('JobMaterialReturn', 'print_label');

                // Build URL for back navigation
                $url = route('showUnIssueSelectionList') . '?' . http_build_query([
                    'whse_num' => $whse_num,
                    'job_num' => base64_encode($job_num),
                    'oper_num' => $oper_num,
                    'suffix' => $suffix
                ]);

                return view('production.jobmatliunissue.process_cw')
                    ->with('jobmatl', $jobmatl)
                    ->with('batch_id', $batch_id)
                    ->with('disable_create_new_item_location', $disable_create_new_item_location)
                    ->with('allow_over_return', $allow_over_return)
                    ->with('printLabel', $printLabel)
                    ->with('def_loc', $defaults['loc_num'] ?? '')
                    ->with('url', $url)
                    ->with('unit_quantity_format', $unit_quantity_format);
            } else {
                // Regular non-catch weight process
                return view('production.jobmatliunissue.process')
                    ->with('lot_tracked', $item_details->lot_tracked)
                    ->with('jobmatl', $jobmatl)
                    ->with('defaults', $defaults)
                    ->with('batch_id', $batch_id)
                    ->with('sap_trans_order_integration', $sap_trans_order_integration)
                    ->with('unit_quantity_format', $unit_quantity_format);
            }
        } else {
            throw ValidationException::withMessages(['matl_item' => 'Invalid Material Item.']);
        }
    }

    public function unissueJobMatlProcess(Request $request)
    {
        $batch_id = $request->batch_id;
        // dd($batch_id);
        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }
        $request = validateSansentiveValue($request);
        DB::beginTransaction();
        try {
            $request->validate([
                'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
                'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
                'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
            ], [
                'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
                'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
                'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
            ]);

            $jobmatl = JobMatl::where('job_num', $request->job_num)
                ->where('suffix', $request->suffix)
                ->where('oper_num', $request->oper_num)
                ->where('sequence', $request->sequence)
                ->where('matl_item', $request->item_num)
                ->first();

            // Verifying JobMatl exist
            if (!$jobmatl) {
                throw ValidationException::withMessages([__(
                    'error.mobile.notexist',
                    ['resource' => '[' . $request->job_num . '-' . $request->suffix . '-' . $request->oper_num . '-' . $request->sequence . '-' . $request->item_num . ']']
                )]);
            }

            $transDate = Carbon::now()->toDateTimeString();
            Session::put('timestamp', $transDate);

            $request->request->add([
                'suffix' => $request->suffix,
                'ref_num' => $request->job_num,
                'ref_line' => $request->oper_num,
                'ref_release' => $request->sequence,
            ]);
            $executeJobMatlReturn = GeneralService::executeJobMatlReturn($request);
            if (!$executeJobMatlReturn) {
                $request->replace(['job_num' => $request->job_num, 'oper_num' => $request->oper_num, 'whse_num' => $request->whse_num, 'matl_item' => $request->item_num]);
                Alert::error('Error', 'Quantity Return more than Quantity Issued');

                return $this->unIssueJobDetails($request);
            }

            // Minus qty from table: issued_lots
            if ($request->lot_num) {
                $issued_lot = IssuedLot::where('from_module', 'Job Material Issue')
                    ->where('whse_num', $request->whse_num)
                    ->where('ref_num', $request->job_num)
                    ->where('ref_line', $request->oper_num)
                    ->where('ref_release', $request->sequence)
                    ->where('lot_num', $request->lot_num)
                    ->first();

                if ($issued_lot) {
                    $issued_lot->qty = $issued_lot->qty - $request->qty_conv;

                    // If less or equal to 0, delete it
                    if ($issued_lot->qty <= 0) {
                        $issued_lot->delete();
                    } else {
                        $issued_lot->save();
                    }
                }
            }

            // Future webhook here
            Alert::success('Success', __('success.processed', ['process' => __('Job Material Return')]));

            Session::put('modulename', 'JobMatlReturn');
            Session::put('job_num', $request->ref_num);
            Session::put('suffix', $request->suffix);
            Session::put('oper_num', $request->ref_line);
            Session::put('whse_num', $request->whse_num);

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $tparm = new TparmView;
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
            if ($sap_trans_order_integration == 1) {
                //$result = SapCallService::postJobMaterialReturns($request);

                if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                    // Later Change to read from Matltrans
                    //$result = SapCallService::postJobMaterialReturns($request);
                    if ($sap_single_bin == 1) {
                        $result = SiteConnectionService::postIntergrationTrans("Job Material Return", $request);
                    } else {
                        $result = SapCallService::postInventoryGenEntries("Job Material Return", $request);
                    }
                } else {

                    $request->merge([
                        'sap_single_bin' => $sap_single_bin
                    ]);






                    if (config('icapt.enable_sap_resync')) {
                        $result = SapCallService::postJobMaterialReturnsResync($request);
                    } else {
                        $result = SapCallService::postJobMaterialReturns($request);
                    }
                }
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            $job_num = $request->ref_num;
            $suffix = $request->suffix;
            $oper_num = $request->ref_line;
            $whse_num = $request->whse_num;
            $item_num = $request->item_num;
            $loc_num = $request->loc_num;
            $lot_num = $request->lot_num;
            $sequence = $request->ref_release;
            $uom = $request->uom;

            DB::commit();
            $transType = 'JobMaterialReturn';

            if ($request->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($request);

                // Generate barcode
                // change the qty_conv to qty because uom passed was not converted

                $input = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num, $request->qty, $uom, $whse_num, $loc_num, $lot_num, $check_expiry_date, $transDate, $transType);
            } else {
                // Generate barcode
                // change the qty_conv to qty because uom passed was not converted

                $input = BarcodeController::GetJobMatLabelData($job_num, $suffix, $sequence, $oper_num, $item_num, $request->qty, $uom, $whse_num, $loc_num, $lot_num, null, $transDate, $transType);
            }
            // dd($input, $request->all());

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('JobMaterialUnIssue', 'print_label');

            if ($print_label == 1) {
                return BarcodeController::showLabelDefinition($input);
            } else {
                return app('App\Http\Controllers\RouteController')->BackButton();
            }
            // return redirect()->route('unIssueIndex');
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function runJobMatlReturnCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }
        $request = validateSansentiveValue($request);

        // Validate basic requirements
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
            'loc_num' => 'required|exists:locs,loc_num,whse_num,' . $request->whse_num . ',loc_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
            'loc_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.locations')]),
        ]);

        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if ($sap_require_check_online == 1 && $sap_trans_order_integration == 1) {
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id, null, 'Job Material Return', 1);

            if ($checkConnection > 2) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }
        }

        $jobmatl = JobMatl::where('job_num', $request->ref_num)
            ->where('suffix', $request->suffix)
            ->where('oper_num', $request->ref_line)
            ->where('sequence', $request->ref_release)
            ->where('matl_item', $request->item_num)
            ->first();

        // Verifying JobMatl exist
        if (!$jobmatl) {
            throw ValidationException::withMessages([__(
                'error.mobile.notexist',
                ['resource' => '[' . $request->ref_num . '-' . $request->suffix . '-' . $request->ref_line . '-' . $request->ref_release . '-' . $request->item_num . ']']
            )]);
        }

        // Get Tolerance and UOM for job material return
        $tolerance = $jobmatl->qty_issued - $jobmatl->qty_returned;
        $tolerance_uom = $jobmatl->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);
        $request->merge([
            'qty' => array_sum($request->arr_qty ?? []),
            'base_uom' => $jobmatl->uom
        ]);

        LotService::checkExpiryDate('allow_expired_item_JobMaterialReturn', $request);

        $transDate = Carbon::now()->toDateTimeString();
        Session::put('timestamp', $transDate);

        $job_num = $request->ref_num;
        $oper_num = $request->ref_line;
        $whse_num = $request->whse_num;
        $suffix = $request->suffix;
        $item_num = $request->item_num;
        $loc_num = $request->loc_num;
        $lot_num = $request->lot_num;
        $sequence = $request->ref_release;
        $qty = $request->qty;
        $uom = $request->uom;
        $baseuom = $request->qty_available_uom;
        $selectuom = $request->uom;
        $lineuom = $request->ref_uom;
        $transType = 'Job Material Return';
        $request['trans_type'] = $transType;

        // Conversion for inventory and material trans - convert to item UOM
        $getQtyConv = UomConv::convertUOM($baseuom, $selectuom, $lineuom, $request->qty, $request->item_num, '', '', $transType);

        $request->merge([
            'suffix' => $suffix,
            'trans_qty' => $request->qty, // Positive for return
            'trans_uom' => $request->uom,
            'qty_conv' =>  $getQtyConv['conv_qty_to_base']['qty'],
            'base_uom' => $getQtyConv['conv_qty_to_base']['uom'],
            'uom' => $getQtyConv['conv_qty_to_line']['uom'],
            'qty' => $getQtyConv['conv_qty_to_line']['qty'], // Positive for return
            'qty_line' => $getQtyConv['conv_qty_to_line']['qty'],
            'job_num' => $job_num,
            'oper_num' => $oper_num,
        ]);

        // Start database transaction
        DB::beginTransaction();
        try {
            $request->request->add([
                'suffix' => $request->suffix,
                'ref_num' => $request->ref_num,
                'ref_line' => $request->ref_line,
                'ref_release' => $request->ref_release,
            ]);

            $executeJobMatlReturn = GeneralService::executeJobMatlReturn($request);
            if (!$executeJobMatlReturn) {
                DB::rollback();
                throw ValidationException::withMessages(['qty' => 'Quantity Return more than Quantity Issued']);
            }

            // Update inventory using catch weight service
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $request->tolerance_uom, $transType, $transType);

            // Update issued lots for catch weight items
            if ($request->lot_num) {
                $issued_lot = IssuedLot::where('from_module', 'Job Material Issue')
                    ->where('whse_num', $request->whse_num)
                    ->where('ref_num', $request->ref_num)
                    ->where('ref_line', $request->ref_line)
                    ->where('ref_release', $request->ref_release)
                    ->where('lot_num', $request->lot_num)
                    ->first();

                if ($issued_lot) {
                    $issued_lot->qty = $issued_lot->qty - $request->qty_conv;

                    // If less or equal to 0, delete it
                    if ($issued_lot->qty <= 0) {
                        $issued_lot->delete();
                    } else {
                        $issued_lot->save();
                    }
                }
            }

            Alert::success('Success', __('success.processed', ['process' => __('Job Material Return')]));

            Session::put('modulename', 'JobMatlReturn');
            Session::put('job_num', $request->ref_num);
            Session::put('suffix', $request->suffix);
            Session::put('oper_num', $request->ref_line);
            Session::put('whse_num', $request->whse_num);

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $tparm = new TparmView;
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

            $request->merge([
                'sap_single_bin' => $sap_single_bin
            ]);

            // SAP Integration
            if ($sap_trans_order_integration == 1) {
                if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                    if ($sap_single_bin == 1) {
                        $result = SiteConnectionService::postIntergrationTrans("Job Material Return", $request);
                    } else {
                        $result = SapCallService::postInventoryGenEntries("Job Material Return", $request);
                    }
                } else {
                    if (config('icapt.enable_sap_resync')) {
                        $result = SapCallService::postJobMaterialReturnsResync($request);
                    } else {
                        $result = SapCallService::postJobMaterialReturns($request);
                    }
                }
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }

            DB::commit();
            return app('App\Http\Controllers\RouteController')->BackButton();

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    //JOB MATERIAL UNISSUE END

    /**
     * Job Validation
     *
     * <AUTHOR>
     *
     * @return true or false
     */
    public function JobValidation(Request $request)
    {
        // dd($request->is_batch);
        $vinput = $request->all();

        foreach ($vinput as $name => $value) {

            if ($name == "job_num") {
                $model = new Job();
                if ($request->whse_num != "") {
                    $result = $model->where('whse_num', $request->whse_num)->where('job_num', $value)->first();

                    // $result = $model->where('whse_num', $request->whse_num)->exists($value);
                } else {
                    $result = $model->exists($value);
                }
            }
            if ($name == "suffix" || $name == 'from_suffix' || $name == 'to_suffix' || $name == "from_job_suffix" || $name == "to_job_suffix") {
                $model = new Job();
                $result = $model->existsSuffix($value);
            }
            if ($name == "sequence") {
                $model = new JobMatl();
                $result = $model->sqExists($value);
            }
            if ($name == "matl_item") {
                $model = new Item();
                $result = $model->exists($value);
            }
            if ($name == "oper_num") {
                $model = new JobMatl();
                $result = $model->opExists($value);
            }
            if ($name == "loc_num") {
                $model = new Loc();
                $result = $model->exists($value);
            }
            if ($name == "whse_num") {
                $model = new Warehouse();
                $result = $model->exists($value);
            }
            if ($result == true) {
                return "true";
            } else {
                return "false";
            }
        }
    }

    public function checkJobQtyCompletedMoreThan0(Request $request)
    {

        $result = Job::where('item_num', $request->item_num)->where('whse_num', $request->whse_num)->where('job_num', $request->job_num)
            ->exists();
        // ->where('qty_completed','>',0);

        if ($result == true) {
            return "true";
        } else {
            return "false";
        }
    }

    public function checkExpiryDateJobMaterialIssue(Request $request)
    {
        // Send error if lot_num is expired and allow_expired_item = 0
        return LotService::checkExpiryDate('allow_expired_item_JobMaterialIssue', $request);
    }
    public function updateAllocations($request)
    {
        $allocations = Allocation::where('order_type', 'Job Order')
            ->where('ref_num', $request->ref_num)
            ->where('ref_line', $request->ref_line)
            ->where('ref_release', $request->ref_release)
            ->where('item_num', $request->item_num)
            ->get();

        foreach ($allocations as $allocation) {
            $allocation_location = $allocation->allocation_locations()->where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->first();
            if ($allocation_location) {

                // Minus qty allocated in table: allocation_locations
                $allocation_location->qty_manual_allocated = $allocation_location->qty_allocated - $request->qty_conv;
                $allocation_location->save();

                // Minus qty allocated in table: allocations
                $allocation->qty_manual_allocated = $allocation->qty_allocated - $request->qty_conv;
                $allocation->save();
            }
        }
    }
    public function updateIssuedLot($request)
    {
        if ($request->lot_num) {
            $issued_lot = IssuedLot::where('from_module', 'Job Material Issue')
                ->where('whse_num', $request->whse_num)
                ->where('ref_num', $request->ref_num)
                ->where('ref_line', $request->ref_line)
                ->where('ref_release', $request->ref_release)
                ->where('lot_num', $request->lot_num)
                ->first();

            // Update
            if ($issued_lot) {
                $issued_lot->qty = $issued_lot->qty + $request->qty_conv;
            }
            // Store
            else {
                $issued_lot = new IssuedLot;
                $issued_lot->from_module = 'Job Material Issue';
                $issued_lot->whse_num = $request->whse_num;
                $issued_lot->loc_num = $request->loc_num;
                $issued_lot->item_num = $request->item_num;
                $issued_lot->ref_num = $request->ref_num;
                $issued_lot->ref_line = $request->ref_line;
                $issued_lot->ref_release = $request->ref_release;
                $issued_lot->lot_num = $request->lot_num;
                $issued_lot->qty = $request->qty_conv;
            }
            $issued_lot->save();
        }
    }
}
