@extends('layout.mobile.app')

@section('content')
@section('title', __('Purchase Order Return - CW'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$ItemList->whse_num"
            :itemnum="$ItemList->item_num"
            :itemdesc="$ItemList->item_desc"
            :refnum="$ItemList->po_num"
            :refline="$ItemList->po_line"
            :qtybalance="$ItemList->qty_returnable ?? 0"
            :qtybalanceuom="$ItemList->uom"
            :submiturl="route('runPoReturnCWProcess')"
            :catch-weight-tolerance="$ItemList->item->catch_weight_tolerance"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :allow-over="$allow_over_return"
            :line-uom="$ItemList->uom"
            :print-label="$printLabel"
            transtype="po"
            trans-type="POReturn"
            :incoming="false">

            <input type="hidden" name="vend_num" id="vend_num" value="{{ $ItemList->vend_num }}">
            <input type="hidden" name="non_inv" id="non_inv" value="{{ $non_inv }}">
            <input type="hidden" name="ref_release" id="po_rel" class="form-control border-primary" value="{{ $ItemList->po_rel }}">
            <input type="hidden" name="vend_num" id="vend_num" class="form-control border-primary" value="{{ old('vend_num', $ItemList->vend_num) }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num',$def_loc) }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                @if($ItemList->lot_tracked == 1)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control-custom" for="vend_lot">{{ __('mobile.label.vend_lot') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" maxlength="30" id="vend_lot" value="{{old('vend_lot')}}" class="form-control border-primary" placeholder="{{__('mobile.placeholder.vend_lot')}}" name="vend_lot" >
                            </div>
                        </div>
                    </div>
                @endif
                <div class="form-group row" id="reason">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control-custom required"
                        for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" value="{{ old('reason_code') }}"
                                name="reason_code" id="reason_code" class="form-control border-primary"
                                placeholder="Reason" onchange="clickSelf(this.id)">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="getRequest5" name="{{ __('mobile.list.reasons') }}"
                            onClick="selectionwithcheckreasoncode('/getReasonCode/POReturn', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{__('admin.menu.reason_codes')}}');"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.po_num') }} : {{ $ItemList->po_num }} | {{ $ItemList->po_line }}
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack()
    {
        var URLRedirect = "{{$url}}";
        url = URLRedirect.replace(/&amp;/g, "&");
        // if (document.location.href.includes("po-receipt/process"))
        // {
        //     var payload = <?= json_encode(Session::get("request_data_poline")) ?>;
        //     submitForm(payload);
        // }
        // else
        // {
            // window.history.go(-1);
            //window.location.href = '/home/<USER>/po-receipt';
            window.location.href   =  url;
        // }
    }

    $(document).ready(function() {
        $("#lot_num").on('change', function() {
            if ($("#lot_num").val()) {
                $.ajax({
                    url: '{{ route('getVendLot') }}',
                    type: 'GET',
                    data: {
                        lot_num: $("#lot_num").val(),
                    },
                    success: function(data) {
                        display('/displayLotQuantityCoPick',
                            'item_num,whse_num,loc_num,lot_num,qtybalance,qtybalanceuom,cust_num,qtybalanceuom,vend_num',
                            'qty_available,max_qty_input'
                        );
                    }
                });
            }
        });
    });
</script>

@endsection()
