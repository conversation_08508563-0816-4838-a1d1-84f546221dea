<?php

namespace App\Http\Controllers\Report;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Alert;
use DataTables;
use App\SiteSetting;
use DB;
use PDF;
use DateTime;
use App\View\TparmView;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Support\Facades\Session;

class COPickingShippingStatusReportController extends Controller
{

    public function index(){
       
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error');
        }
        
        // Get staging locations for multi-select
        $stagingLocations = DB::table('stage_locs')
            ->where('site_id', auth()->user()->site_id)
            ->select('stage_num')
            ->whereNotNull('stage_num')
            ->distinct()
            ->orderBy('stage_num')
            ->get();

        return view('report.inv.copickingshippingstatus.index')
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('stagingLocations', $stagingLocations)
            ->with('report_module_name', 'CO Picking vs Shipping Status');
    }

    public function getQuery(Request $request) {
        $siteSettings = new SiteSetting();
        $now_date = Carbon::now()->toDateString();
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        
        if($sap_trans_order_integration==1 && config('icapt.enable_cust_name_notfrom_cust_table')==true) {
            $cust_name = "co.cust_name";
        } else {
            $cust_name = "customers.cust_name";
        }

        // Create stage_locs subquery to aggregate quantities per CO/Line/Stage
        $stageLocsSubquery = DB::table('stage_locs')
            ->select(
                'co_num',
                'co_line', 
                'co_rel',
                'stage_num',
                DB::raw('SUM(qty_staged) as total_qty_staged')
            )
            ->where('site_id', auth()->user()->site_id)
            ->groupBy('co_num', 'co_line', 'co_rel', 'stage_num');

        // Base query - get all CO lines and their picking/shipping data
        $query = DB::query()->from('coitems as co')
            ->select(
                'co.cust_num',
                $cust_name . ' as cust_name',
                'co.co_num',
                'co.co_line',
                DB::raw('DATE_FORMAT(co.due_date, "' . $siteSettings->getMySQLDateFormat() . '") as due_date'),
                'co.item_num',
                'co.item_desc',
                'co.qty_ordered as qty_required',
                DB::raw('IFNULL(stage_summary.stage_num, "Not Staged") as stage_location'),
                DB::raw('IFNULL(stage_summary.total_qty_staged, 0) as qty_picked'),
                DB::raw('IFNULL(shipments.total_shipped, 0) as qty_shipped'),
                DB::raw('(IFNULL(stage_summary.total_qty_staged, 0) - IFNULL(shipments.total_shipped, 0)) as outstanding_qty'),
                'co.rel_status as co_line_status',
                'co_header.co_status'
            );

        // Add joins based on SAP integration setting
        if($sap_trans_order_integration==1 && config('icapt.enable_cust_name_notfrom_cust_table')==true) {
            // Don't join customers table if using SAP integration
        } else {
            $query->leftJoin('customers', function($join) {
                $join->on('customers.cust_num', '=', 'co.cust_num')
                     ->on('customers.site_id', '=', 'co.site_id');
            });
        }

        // Create shipment subquery separately
        $shipmentSubquery = DB::table('shipments')
            ->select('co_num', 'co_line', DB::raw('SUM(qty_shipped) as total_shipped'))
            ->where('site_id', auth()->user()->site_id)
            ->groupBy('co_num', 'co_line');

        $query->leftJoin('customer_orders as co_header', function($join) {
                $join->on('co_header.co_num', '=', 'co.co_num')
                     ->on('co_header.site_id', '=', 'co.site_id');
            })
            ->leftJoinSub($stageLocsSubquery, 'stage_summary', function($join) {
                $join->on('stage_summary.co_num', '=', 'co.co_num')
                     ->on('stage_summary.co_line', '=', 'co.co_line')
                     ->on('stage_summary.co_rel', '=', 'co.co_rel');
            })
            ->leftJoinSub($shipmentSubquery, 'shipments', function($join) {
                $join->on('shipments.co_num', '=', 'co.co_num')
                     ->on('shipments.co_line', '=', 'co.co_line');
            })
            ->where('co.site_id', auth()->user()->site_id);

        // Apply filters
        if(request('from_co_num') && request('to_co_num')){
            $query->whereBetween('co.co_num', [request('from_co_num'), request('to_co_num')]);
        }
        else if(request('from_co_num')){
            $query->where('co.co_num', '>=', request('from_co_num'));
        }
        else if(request('to_co_num')){
            $query->where('co.co_num', '<=', request('to_co_num'));
        }

        if(request('from_cust_num') && request('to_cust_num')){
            $query->whereBetween('co.cust_num', [request('from_cust_num'), request('to_cust_num')]);
        }
        else if(request('from_cust_num')){
            $query->where('co.cust_num', '>=', request('from_cust_num'));
        }
        else if(request('to_cust_num')){
            $query->where('co.cust_num', '<=', request('to_cust_num'));
        }

        if(request('from_due_date') && request('to_due_date')){
            $from_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_due_date'))->format('Y-m-d');
            $to_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_due_date'))->format('Y-m-d');
            $query->whereBetween('co.due_date', [$from_due_date, $to_due_date.' 23:59:59']);
        }
        else if(request('from_due_date')){
            $from_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_due_date'))->format('Y-m-d');
            $query->where('co.due_date', '>=', $from_due_date);
        }
        else if(request('to_due_date')){
            $to_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_due_date'))->format('Y-m-d');
            $query->where('co.due_date', '<=', $to_due_date.' 23:59:59');
        }

        if(request('from_item_num') && request('to_item_num')){
            $query->whereBetween('co.item_num', [request('from_item_num'), request('to_item_num')]);
        }
        else if(request('from_item_num')){
            $query->where('co.item_num', '>=', request('from_item_num'));
        }
        else if(request('to_item_num')){
            $query->where('co.item_num', '<=', request('to_item_num'));
        }

        if(request('co_status') && request('co_status') != 'All'){
            $query->where('co_header.co_status', request('co_status'));
        }

        if(request('co_line_status') && request('co_line_status') != 'All'){
            $query->where('co.rel_status', request('co_line_status'));
        }

        if(request('stage_locations') && is_array(request('stage_locations'))){
            $query->whereIn('stage_summary.stage_num', request('stage_locations'));
        }

        if(request('outstanding_only') == 'on'){
            $query->whereRaw('(IFNULL(stage_summary.total_qty_staged, 0) - IFNULL(shipments.total_shipped, 0)) > 0');
        }

        $query->orderBy('co.co_num', 'asc')
            ->orderBy('co.co_line', 'asc')
            ->orderBy('co.cust_num', 'asc')
            ->orderBy('co.due_date', 'asc')
            ->orderBy('co.item_num', 'asc');

        return $query;
    }

    public function data(Request $request) {
        $query = $this->getQuery($request);
        $dataTable = Datatables::of($query);
        $response = $dataTable->make(true);
        return $response;
    }

    // Print function
    public function print(Request $request) {
        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        $query = $this->getQuery($request);
        $query1 = $query->get();

        // Site
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        if($site->logo != "") {
            $url = $site->getFileFullPath();
            $image = file_get_contents($url);
            $site->logoazure = "data:image/png+jpeg+jpg;base64,".base64_encode($image);
        }
        else
            $site->logoazure = "";

        if($site->company_info) {
            $sitedecode = json_decode($site->company_info);
            $site->company_name = strtoupper($sitedecode->company_name);
        }

        $prev_data = [
            'co_num' => '',
        ];
        
        $index = 1;

        $now = Carbon::now()->toDateTimeString();
        $date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, auth()->user()->timezone, 'H:i:s');

        $headerHtml = view()->make('report.trans.transactionReport')->with('siteLogo', $site->logo)->with('siteLogoAzure', $site->logoazure)->with('companyName', $site->company_name)->render();
        $footerHtml = view()->make('report.trans.footer')->with('date',$date)->with('time',$time)->render();

        $pdf = PDF::loadView('report.inv.copickingshippingstatus.print', compact('total_quantity_format', 'query1', 'request', 'prev_data', 'index', 'site'))
            ->setPaper('A4', 'landscape')
            ->setOption('header-html', $headerHtml)
            ->setOption('footer-html', $footerHtml)
            ->setOption('margin-bottom', 10)
            ->setOption('margin-top', 5)
            ->setOption('margin-right', 10)
            ->setOption('margin-left', 10)
            ->setOption("footer-right", "Page [page] of [topage]")
            ->setOption("footer-font-size", 8);

        return $pdf->stream('CO Picking vs Shipping Status.pdf');
    }
}
