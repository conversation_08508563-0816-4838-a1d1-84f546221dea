<?php return array (
  'app' => 
  array (
    'name' => 'AXACUTE - Local Dev',
    'env' => 'local',
    'debug' => true,
    'debug_blacklist' => 
    array (
      '_COOKIE' => 
      array (
      ),
      '_SERVER' => 
      array (
        0 => 'ALLUSERSPROFILE',
        1 => 'APPDATA',
        2 => 'ChocolateyInstall',
        3 => 'ChocolateyLastPathUpdate',
        4 => 'CHROME_CRASHPAD_PIPE_NAME',
        5 => 'CommonProgramFiles',
        6 => 'CommonProgramFiles(x86)',
        7 => 'CommonProgramW6432',
        8 => 'COMPUTERNAME',
        9 => 'ComSpec',
        10 => 'configsetroot',
        11 => 'DriverData',
        12 => 'FPS_BROWSER_APP_PROFILE_STRING',
        13 => 'FPS_BROWSER_USER_PROFILE_STRING',
        14 => 'HOMEDRIVE',
        15 => 'HOMEPATH',
        16 => 'LOCALAPPDATA',
        17 => 'LOGONSERVER',
        18 => 'MOSQUITTO_DIR',
        19 => 'MOZ_PLUGIN_PATH',
        20 => 'NUMBER_OF_PROCESSORS',
        21 => 'NVM_HOME',
        22 => 'NVM_SYMLINK',
        23 => 'OneDrive',
        24 => 'OneDriveCommercial',
        25 => 'OPENSSL_CONF',
        26 => 'ORIGINAL_XDG_CURRENT_DESKTOP',
        27 => 'OS',
        28 => 'Path',
        29 => 'PATHEXT',
        30 => 'PROCESSOR_ARCHITECTURE',
        31 => 'PROCESSOR_IDENTIFIER',
        32 => 'PROCESSOR_LEVEL',
        33 => 'PROCESSOR_REVISION',
        34 => 'ProgramData',
        35 => 'ProgramFiles',
        36 => 'ProgramFiles(x86)',
        37 => 'ProgramW6432',
        38 => 'PSModulePath',
        39 => 'PUBLIC',
        40 => 'SESSIONNAME',
        41 => 'SystemDrive',
        42 => 'SystemRoot',
        43 => 'TEMP',
        44 => 'TMP',
        45 => 'USERDNSDOMAIN',
        46 => 'USERDOMAIN',
        47 => 'USERDOMAIN_ROAMINGPROFILE',
        48 => 'USERNAME',
        49 => 'USERPROFILE',
        50 => 'VBOX_MSI_INSTALL_PATH',
        51 => 'windir',
        52 => 'ZES_ENABLE_SYSMAN',
        53 => '__PSLockDownPolicy',
        54 => 'TERM_PROGRAM',
        55 => 'TERM_PROGRAM_VERSION',
        56 => 'LANG',
        57 => 'COLORTERM',
        58 => 'GIT_ASKPASS',
        59 => 'VSCODE_GIT_ASKPASS_NODE',
        60 => 'VSCODE_GIT_ASKPASS_EXTRA_ARGS',
        61 => 'VSCODE_GIT_ASKPASS_MAIN',
        62 => 'VSCODE_GIT_IPC_HANDLE',
        63 => 'VSCODE_INJECTION',
        64 => 'PHP_SELF',
        65 => 'SCRIPT_NAME',
        66 => 'SCRIPT_FILENAME',
        67 => 'PATH_TRANSLATED',
        68 => 'DOCUMENT_ROOT',
        69 => 'REQUEST_TIME_FLOAT',
        70 => 'REQUEST_TIME',
        71 => 'argv',
        72 => 'argc',
        73 => 'SHELL_VERBOSITY',
        74 => 'APP_NAME',
        75 => 'APP_ENV',
        76 => 'APP_KEY',
        77 => 'APP_DEBUG',
        78 => 'APP_URL',
        79 => 'LOG_CHANNEL',
        80 => 'TELESCOPE_ENABLED',
        81 => 'DB_CONNECTION',
        82 => 'DB_HOST',
        83 => 'DB_PORT',
        84 => 'DB_DATABASE',
        85 => 'DB_USERNAME',
        86 => 'DB_PASSWORD',
        87 => 'DB_CONNECTION_SECOND',
        88 => 'DB_HOST_SECOND',
        89 => 'DB_PORT_SECOND',
        90 => 'DB_DATABASE_SECOND',
        91 => 'DB_USERNAME_SECOND',
        92 => 'DB_PASSWORD_SECOND',
        93 => 'BROADCAST_DRIVER',
        94 => 'CACHE_DRIVER',
        95 => 'QUEUE_CONNECTION',
        96 => 'SESSION_DRIVER',
        97 => 'SESSION_LIFETIME',
        98 => 'REDIS_HOST',
        99 => 'REDIS_PASSWORD',
        100 => 'REDIS_PORT',
        101 => 'MAIL_MAILER',
        102 => 'MAIL_HOST',
        103 => 'MAIL_PORT',
        104 => 'MAIL_USERNAME',
        105 => 'MAIL_PASSWORD',
        106 => 'MAIL_ENCRYPTION',
        107 => 'MAIL_FROM_NAME',
        108 => 'MAIL_FROM_ADDRESS',
        109 => 'AWS_ACCESS_KEY_ID',
        110 => 'AWS_SECRET_ACCESS_KEY',
        111 => 'AWS_DEFAULT_REGION',
        112 => 'AWS_BUCKET',
        113 => 'AZURE_STORAGE_NAME',
        114 => 'AZURE_STORAGE_KEY',
        115 => 'AZURE_STORAGE_CONTAINER',
        116 => 'AZURE_STORAGE_URL',
        117 => 'PUSHER_APP_ID',
        118 => 'PUSHER_APP_KEY',
        119 => 'PUSHER_APP_SECRET',
        120 => 'PUSHER_APP_CLUSTER',
        121 => 'MIX_PUSHER_APP_KEY',
        122 => 'MIX_PUSHER_APP_CLUSTER',
        123 => 'ICAPT_VERSION',
        124 => 'ICAPT_ENV',
        125 => 'ICAPT_LOGO',
        126 => 'BARTENDER',
        127 => 'ENABLE_GRN',
        128 => 'WKHTMLTOPDF_BINARY_PATH',
        129 => 'WKHTMLTOIMAGE_BINARY_PATH',
        130 => 'DB_CONNECTION_MIDDLEWARE',
        131 => 'DB_HOST_MIDDLEWARE',
        132 => 'DB_PORT_MIDDLEWARE',
        133 => 'DB_DATABASE_MIDDLEWARE',
        134 => 'DB_USERNAME_MIDDLEWARE',
        135 => 'DB_PASSWORD_MIDDLEWARE',
        136 => 'QDB_CONNECTION',
        137 => 'QDB_HOST',
        138 => 'QDB_PORT',
        139 => 'QDB_DATABASE',
        140 => 'QDB_USERNAME',
        141 => 'QDB_PASSWORD',
        142 => 'QDB_LABEL_MODE',
      ),
      '_ENV' => 
      array (
        0 => 'SHELL_VERBOSITY',
        1 => 'APP_NAME',
        2 => 'APP_ENV',
        3 => 'APP_KEY',
        4 => 'APP_DEBUG',
        5 => 'APP_URL',
        6 => 'LOG_CHANNEL',
        7 => 'TELESCOPE_ENABLED',
        8 => 'DB_CONNECTION',
        9 => 'DB_HOST',
        10 => 'DB_PORT',
        11 => 'DB_DATABASE',
        12 => 'DB_USERNAME',
        13 => 'DB_PASSWORD',
        14 => 'DB_CONNECTION_SECOND',
        15 => 'DB_HOST_SECOND',
        16 => 'DB_PORT_SECOND',
        17 => 'DB_DATABASE_SECOND',
        18 => 'DB_USERNAME_SECOND',
        19 => 'DB_PASSWORD_SECOND',
        20 => 'BROADCAST_DRIVER',
        21 => 'CACHE_DRIVER',
        22 => 'QUEUE_CONNECTION',
        23 => 'SESSION_DRIVER',
        24 => 'SESSION_LIFETIME',
        25 => 'REDIS_HOST',
        26 => 'REDIS_PASSWORD',
        27 => 'REDIS_PORT',
        28 => 'MAIL_MAILER',
        29 => 'MAIL_HOST',
        30 => 'MAIL_PORT',
        31 => 'MAIL_USERNAME',
        32 => 'MAIL_PASSWORD',
        33 => 'MAIL_ENCRYPTION',
        34 => 'MAIL_FROM_NAME',
        35 => 'MAIL_FROM_ADDRESS',
        36 => 'AWS_ACCESS_KEY_ID',
        37 => 'AWS_SECRET_ACCESS_KEY',
        38 => 'AWS_DEFAULT_REGION',
        39 => 'AWS_BUCKET',
        40 => 'AZURE_STORAGE_NAME',
        41 => 'AZURE_STORAGE_KEY',
        42 => 'AZURE_STORAGE_CONTAINER',
        43 => 'AZURE_STORAGE_URL',
        44 => 'PUSHER_APP_ID',
        45 => 'PUSHER_APP_KEY',
        46 => 'PUSHER_APP_SECRET',
        47 => 'PUSHER_APP_CLUSTER',
        48 => 'MIX_PUSHER_APP_KEY',
        49 => 'MIX_PUSHER_APP_CLUSTER',
        50 => 'ICAPT_VERSION',
        51 => 'ICAPT_ENV',
        52 => 'ICAPT_LOGO',
        53 => 'BARTENDER',
        54 => 'ENABLE_GRN',
        55 => 'WKHTMLTOPDF_BINARY_PATH',
        56 => 'WKHTMLTOIMAGE_BINARY_PATH',
        57 => 'DB_CONNECTION_MIDDLEWARE',
        58 => 'DB_HOST_MIDDLEWARE',
        59 => 'DB_PORT_MIDDLEWARE',
        60 => 'DB_DATABASE_MIDDLEWARE',
        61 => 'DB_USERNAME_MIDDLEWARE',
        62 => 'DB_PASSWORD_MIDDLEWARE',
        63 => 'QDB_CONNECTION',
        64 => 'QDB_HOST',
        65 => 'QDB_PORT',
        66 => 'QDB_DATABASE',
        67 => 'QDB_USERNAME',
        68 => 'QDB_PASSWORD',
        69 => 'QDB_LABEL_MODE',
      ),
    ),
    'url' => 'http://localhost:8000',
    'asset_url' => NULL,
    'marketing_site_url' => 'https://axacute.com/plans',
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'faker_locale' => 'en_US',
    'key' => 'base64:1wspCqMvzN7tbBidgwqhZhvIxA4hXwBT69f+Q71/e4c=',
    'cipher' => 'AES-256-CBC',
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'App\\Providers\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
      23 => 'Milon\\Barcode\\BarcodeServiceProvider',
      24 => 'Camroncade\\Timezone\\TimezoneServiceProvider',
      25 => 'Intervention\\Image\\ImageServiceProvider',
      26 => 'Barryvdh\\DomPDF\\ServiceProvider',
      27 => 'App\\Providers\\AppServiceProvider',
      28 => 'App\\Providers\\AuthServiceProvider',
      29 => 'App\\Providers\\EventServiceProvider',
      30 => 'App\\Providers\\TelescopeServiceProvider',
      31 => 'App\\Providers\\RouteServiceProvider',
      32 => 'Yajra\\DataTables\\DataTablesServiceProvider',
      33 => 'RealRashid\\SweetAlert\\SweetAlertServiceProvider',
      34 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Arr' => 'Illuminate\\Support\\Arr',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Broadcast' => 'Illuminate\\Support\\Facades\\Broadcast',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Image' => 'Intervention\\Image\\Facades\\Image',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Redis' => 'Illuminate\\Support\\Facades\\Redis',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'Str' => 'Illuminate\\Support\\Str',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Alert' => 'RealRashid\\SweetAlert\\Facades\\Alert',
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
      'DNS1D' => 'Milon\\Barcode\\Facades\\DNS1DFacade',
      'DNS2D' => 'Milon\\Barcode\\Facades\\DNS2DFacade',
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
      'PDF' => 'Barryvdh\\DomPDF\\Facade',
      'Debugbar' => 'Barryvdh\\Debugbar\\Facade',
    ),
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'passport',
        'provider' => 'users',
        'hash' => false,
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 30,
        'throttle' => 1,
      ),
    ),
  ),
  'barcode' => 
  array (
    'store_path' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\public\\/',
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => '',
        'app_id' => '',
        'options' => 
        array (
          'cluster' => 'mt1',
          'encrypted' => true,
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'file',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\framework/cache/data',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'cache',
      ),
      'dynamodb' => 
      array (
        'driver' => 'dynamodb',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'table' => 'cache',
      ),
    ),
    'prefix' => 'axacute_local_dev_cache',
  ),
  'constants' => 
  array (
    'modules' => 
    array (
      'Item Inquiry' => 'Inventory',
      'Location Inquiry' => 'Inventory',
      'Job Inquiry' => 'Inventory',
      'Stock Move' => 'Inventory',
      'Misc Receipt' => 'Inventory',
      'Misc Issue' => 'Inventory',
      'Inventory Count' => 'Inventory',
      'PO Receipt' => 'Inventory',
      'PO Return' => 'Inventory',
      'Putaway' => 'Inventory',
      'Transfer Order Receipt' => 'Inventory',
      'Pick N Ship' => 'Inventory',
      'CO Picking' => 'Inventory',
      'CO Return' => 'Inventory',
      'CO Shipping' => 'Inventory',
      'Transfer Order Ship' => 'Inventory',
      'Job Matl Issue' => 'Inventory',
      'Job Matl Return' => 'Inventory',
      'Job Receipt' => 'Inventory',
      'Job Labour' => 'Production',
      'Machine Run' => 'Production',
      'WIP Move' => 'Production',
      'Master Maintenance' => 'API',
      'PO Maintenance' => 'API',
      'CO Maintenance' => 'API',
      'TO Maintenance' => 'API',
      'Job Maintenance' => 'API',
      'Inventory Count Maintenance' => 'API',
      'OverridesQtyFields' => 'API',
      'History Maintenance' => 'API',
      'Job Transaction' => 'API',
      'Machine Transaction' => 'API',
      'Sysadmin' => 'Admin',
      'Barcode' => 'Admin',
      'Report' => 'Admin',
    ),
    'businesstype' => 
    array (
      1 => 'Manufacturing or Assembly',
      2 => 'E-commerce',
      3 => 'Wholesale or Distribution',
      4 => 'Retail',
      5 => 'Others',
    ),
    'companysize' => 
    array (
      1 => '< 5',
      2 => '5 - 20',
      3 => '21 - 50',
      4 => '51 - 250',
      5 => '> 250',
    ),
    'primaryinterest' => 
    array (
      1 => 'To solve inventory accuracy issue',
      2 => 'To simplify warehouse activities',
      3 => 'To optimize picking process',
      4 => 'To track production status',
      5 => 'To track labor & machine',
      6 => 'Others',
    ),
    'industry' => 
    array (
      1 => 'Airlines/Aviation',
      2 => 'Apparel/Fashion',
      3 => 'Automotive',
      4 => 'Biotechnology/Greentech',
      5 => 'Building Materials',
      6 => 'Business Supplies/Equipment',
      7 => 'Chemicals',
      8 => 'Computer Hardware',
      9 => 'Construction',
      10 => 'Consumer Electronics',
      11 => 'Consumer Goods',
      12 => 'Cosmetics',
      13 => 'Dairy',
      14 => 'Electrical/Electronic Manufacturing',
      15 => 'Food/Beverages',
      16 => 'Furniture',
      17 => 'Glass/Ceramics/Concrete',
      18 => 'Industrial Automation',
      19 => 'Logistics/Procurement',
      20 => 'Luxury Goods/Jewelry',
      21 => 'Machinery',
      22 => 'Medical Equipment',
      23 => 'Other Industry',
      24 => 'Packaging/Containers',
      25 => 'Paper/Forest Products',
      26 => 'Plastics',
      27 => 'Semiconductors',
      28 => 'Sporting Goods',
      29 => 'Telecommunications',
      31 => 'Textiles',
      32 => 'Tobacco',
      33 => 'Warehousing',
    ),
  ),
  'database' => 
  array (
    'default' => 'mysql',
    'connections' => 
    array (
      'queue' => 
      array (
        'driver' => 'mysql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'axacute_test',
        'username' => 'root',
        'password' => 'root',
      ),
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'database' => 'axacute_test',
        'prefix' => '',
        'foreign_key_constraints' => true,
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'axacute_test',
        'username' => 'root',
        'password' => 'root',
        'unix_socket' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => false,
        'engine' => NULL,
        'options' => 
        array (
          20 => true,
        ),
      ),
      'mysql2' => 
      array (
        'driver' => 'mysql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'axacute_local_client',
        'username' => 'root',
        'password' => 'root',
      ),
      'mysql3' => 
      array (
        'driver' => 'mysql',
        'host' => 'axacute-testdb.mysql.database.azure.com',
        'port' => '3306',
        'database' => 'middleware_app_test',
        'username' => 'axacuteadmin',
        'password' => 'Harapan5500!',
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'axacute_test',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
        'schema' => 'public',
        'sslmode' => 'prefer',
      ),
      'sqlsrv' => 
      array (
        'driver' => 'sqlsrv',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'axacute_test',
        'username' => 'root',
        'password' => 'root',
        'charset' => 'utf8',
        'prefix' => '',
        'prefix_indexes' => true,
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'client' => 'predis',
      'options' => 
      array (
        'cluster' => 'predis',
        'prefix' => 'axacute_local_dev_database_',
      ),
      'default' => 
      array (
        'host' => '127.0.0.1',
        'password' => NULL,
        'port' => '6379',
        'database' => 0,
      ),
      'cache' => 
      array (
        'host' => '127.0.0.1',
        'password' => NULL,
        'port' => '6379',
        'database' => 1,
      ),
    ),
  ),
  'datatables-buttons' => 
  array (
    'namespace' => 
    array (
      'base' => 'DataTables',
      'model' => '',
    ),
    'pdf_generator' => 'snappy',
    'snappy' => 
    array (
      'options' => 
      array (
        'no-outline' => true,
        'margin-left' => '0',
        'margin-right' => '0',
        'margin-top' => '10mm',
        'margin-bottom' => '10mm',
      ),
      'orientation' => 'landscape',
    ),
    'parameters' => 
    array (
      'dom' => 'Bfrtip',
      'order' => 
      array (
        0 => 
        array (
          0 => 0,
          1 => 'desc',
        ),
      ),
      'buttons' => 
      array (
        0 => 'create',
        1 => 'export',
        2 => 'print',
        3 => 'reset',
        4 => 'reload',
      ),
    ),
    'generator' => 
    array (
      'columns' => 'id,add your columns,created_at,updated_at',
      'buttons' => 'create,export,print,reset,reload',
      'dom' => 'Bfrtip',
    ),
  ),
  'debug-server' => 
  array (
    'host' => 'tcp://127.0.0.1:9912',
  ),
  'debugbar' => 
  array (
    'enabled' => NULL,
    'hide_empty_tabs' => false,
    'except' => 
    array (
      0 => 'telescope*',
      1 => 'horizon*',
    ),
    'storage' => 
    array (
      'enabled' => true,
      'driver' => 'file',
      'path' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\debugbar',
      'connection' => NULL,
      'provider' => '',
      'hostname' => '127.0.0.1',
      'port' => 2304,
    ),
    'editor' => 'phpstorm',
    'remote_sites_path' => NULL,
    'local_sites_path' => NULL,
    'include_vendors' => true,
    'capture_ajax' => true,
    'add_ajax_timing' => false,
    'ajax_handler_auto_show' => true,
    'ajax_handler_enable_tab' => true,
    'error_handler' => false,
    'clockwork' => false,
    'collectors' => 
    array (
      'phpinfo' => true,
      'messages' => true,
      'time' => true,
      'memory' => true,
      'exceptions' => true,
      'log' => true,
      'db' => true,
      'views' => true,
      'route' => true,
      'auth' => false,
      'gate' => true,
      'session' => true,
      'symfony_request' => true,
      'mail' => true,
      'laravel' => false,
      'events' => false,
      'default_request' => false,
      'logs' => false,
      'files' => false,
      'config' => false,
      'cache' => false,
      'models' => true,
      'livewire' => true,
    ),
    'options' => 
    array (
      'auth' => 
      array (
        'show_name' => true,
      ),
      'db' => 
      array (
        'with_params' => true,
        'backtrace' => true,
        'backtrace_exclude_paths' => 
        array (
        ),
        'timeline' => false,
        'explain' => 
        array (
          'enabled' => false,
          'types' => 
          array (
            0 => 'SELECT',
          ),
        ),
        'hints' => false,
        'show_copy' => false,
      ),
      'mail' => 
      array (
        'full_log' => false,
      ),
      'views' => 
      array (
        'data' => false,
      ),
      'route' => 
      array (
        'label' => true,
      ),
      'logs' => 
      array (
        'file' => NULL,
      ),
      'cache' => 
      array (
        'values' => true,
      ),
    ),
    'inject' => true,
    'route_prefix' => '_debugbar',
    'route_middleware' => 
    array (
    ),
    'route_domain' => NULL,
    'theme' => 'auto',
    'debug_backtrace_limit' => 50,
  ),
  'dompdf' => 
  array (
    'show_warnings' => false,
    'public_path' => NULL,
    'convert_entities' => true,
    'options' => 
    array (
      'font_dir' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\fonts',
      'font_cache' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\fonts',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase',
      'allowed_protocols' => 
      array (
        'file://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'http://' => 
        array (
          'rules' => 
          array (
          ),
        ),
        'https://' => 
        array (
          'rules' => 
          array (
          ),
        ),
      ),
      'log_output_file' => NULL,
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_paper_orientation' => 'portrait',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => true,
    ),
    'orientation' => 'portrait',
    'defines' => 
    array (
      'font_dir' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\fonts/',
      'font_cache' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\fonts/',
      'temp_dir' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'chroot' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase',
      'enable_font_subsetting' => false,
      'pdf_backend' => 'CPDF',
      'default_media_type' => 'screen',
      'default_paper_size' => 'a4',
      'default_font' => 'serif',
      'dpi' => 96,
      'enable_php' => false,
      'enable_javascript' => true,
      'enable_remote' => true,
      'font_height_ratio' => 1.1,
      'enable_html5_parser' => false,
      'isHtml5ParserEnabled' => true,
      'isRemoteEnabled' => true,
      'DOMPDF_ENABLE_PHP' => true,
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'UTF-8',
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
      'default_ttl' => 10800,
    ),
    'transactions' => 
    array (
      'handler' => 'db',
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'remote_disk' => NULL,
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'local',
    'cloud' => 's3',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\app',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\app/public',
        'url' => 'http://localhost:8000/storage',
        'visibility' => 'public',
      ),
      'd-drive' => 
      array (
        'driver' => 'local',
        'root' => 'D:\\icapterrorlog\\',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
        'bucket' => '',
        'url' => NULL,
      ),
      'azure' => 
      array (
        'driver' => 'azure',
        'name' => 'icaptstorage',
        'key' => '2nsfOY2dNEE7Xrbg+kkMsPbieAtDemGmtyEyw9gnFYgi1ob1pDkpK8lzUzytZNx1I5ypllW/MuItKM725mf/1g==',
        'container' => 'public',
        'url' => 'https://icaptstorage.blob.core.windows.net/',
        'prefix' => NULL,
      ),
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
    'bcrypt' => 
    array (
      'rounds' => 10,
    ),
    'argon' => 
    array (
      'memory' => 1024,
      'threads' => 2,
      'time' => 2,
    ),
  ),
  'health' => 
  array (
    'result_stores' => 
    array (
      'Spatie\\Health\\ResultStores\\JsonFileHealthResultStore' => 
      array (
        'disk' => 'local',
        'path' => 'health.json',
      ),
    ),
    'notifications' => 
    array (
      'enabled' => true,
      'notifications' => 
      array (
        'Spatie\\Health\\Notifications\\CheckFailedNotification' => 
        array (
          0 => 'mail',
        ),
      ),
      'notifiable' => 'Spatie\\Health\\Notifications\\Notifiable',
      'throttle_notifications_for_minutes' => 60,
      'throttle_notifications_key' => 'health:latestNotificationSentAt:',
      'mail' => 
      array (
        'to' => 
        array (
          0 => '<EMAIL>',
          1 => '<EMAIL>',
          2 => '<EMAIL>',
          3 => '<EMAIL>',
        ),
        'from' => 
        array (
          'address' => '<EMAIL>',
          'name' => 'Axacute Support',
        ),
      ),
      'slack' => 
      array (
        'webhook_url' => '',
        'channel' => NULL,
        'username' => NULL,
        'icon' => NULL,
      ),
    ),
    'oh_dear_endpoint' => 
    array (
      'enabled' => false,
      'always_send_fresh_results' => true,
      'secret' => NULL,
      'url' => '/oh-dear-health-check-results',
    ),
    'theme' => 'dark',
    'silence_health_queue_job' => true,
    'json_results_failure_status' => 200,
  ),
  'icapt' => 
  array (
    'transtype' => 
    array (
      'misc_issue' => 'Misc Issue',
      'misc_receipt' => 'Misc Receipt',
      'stock_move_from' => 'Stock Move From',
      'stock_move_to' => 'Stock Move To',
      'put_away_from' => 'Put Away From',
      'put_away_to' => 'Put Away To',
      'pick_from' => 'Pick From',
      'pick_to' => 'Pick To',
      'po_receipt' => 'PO Receipt',
      'po_return' => 'PO Return',
      'co_pick_from' => 'CO Pick From',
      'co_pick_to' => 'CO Pick To',
      'co_unpick_from' => 'CO Unpick From',
      'co_unpick_to' => 'CO Unpick To',
      'co_shipping' => 'CO Ship',
      'pick_n_ship' => 'Pick N Ship',
      'co_return' => 'CO Return',
      'customer_return' => 'Customer Return',
      'to_lost' => 'TO Lost',
      'to_receipt' => 'TO Receipt',
      'to_receipt_from' => 'TO Receipt From',
      'to_receipt_to' => 'TO Receipt To',
      'to_ship' => 'TO Ship',
      'to_shipping_to' => 'TO Ship To',
      'to_shipping_from' => 'TO Ship From',
      'inv_count' => 'Inventory Count',
      'job_matl_issue' => 'Job Material Issue',
      'job_matl_unissue' => 'Job Material Return',
      'job_receipt' => 'Job Receipt',
      'job_return' => 'Job Return',
      'downtime' => 'Down Time',
      'machine_run' => 'Machine Run',
      'pallet_build_from' => 'Pallet Build From',
      'pallet_move_from' => 'Pallet Move From',
      'pallet_letdown_from' => 'Pallet Letdown From',
      'pallet_transfer_from' => 'Pallet Transfer From',
      'pallet_destruct_from' => 'Pallet Destruct From',
      'pallet_build_to' => 'Pallet Build To',
      'pallet_move_to' => 'Pallet Move To',
      'pallet_letdown_to' => 'Pallet Letdown To',
      'pallet_transfer_to' => 'Pallet Transfer To',
      'pallet_destruct_to' => 'Pallet Destruct To',
    ),
    'thirdparty_process_name' => 
    array (
      'Pick N Ship' => 'Pick N Ship',
      'PO Receipt' => 'PO Receipt',
      'PO Receipt - Batch Sync' => 'PO Receipt - Batch Sync',
      'PO Return : Misc Issue' => 'PO Return : Misc Issue',
      'PO Return' => 'PO Return',
      'CO Return' => 'Co Return',
      'CO Return : Misc Receipt' => 'Co Return : Misc Receipt',
      'Inventory Count' => 'Inventory Count',
      'Inventory Count Setup' => 'Inventory Count Setup',
      'Inventory Count Update' => 'Inventory Count Update',
      'Inventory Posting Count' => 'Inventory Posting Count',
      'Second Counted Inventory Count - Bulk Update' => 'Second Counted Inventory Count - Bulk Update',
      'Counted Inventory Count - Bulk Update' => 'Counted Inventory Count - Bulk Update',
      'Stock Move' => 'Stock Move',
      'Multi Stock Move' => 'Multi Stock Move',
      'Putaway' => 'Putaway',
      'CO Shipping' => 'Co Shipping',
      'Co Pick' => 'Co Pick',
      'Co UnPick' => 'Co UnPick',
      'TO Ship' => 'TO Ship',
      'Bulk TO Ship' => 'Bulk TO Ship',
      'TO Receipt' => 'TO Receipt',
      'Bulk TO Receipt' => 'Bulk TO Receipt',
      'Job Receipt' => 'Job Receipt',
      'Job Material Issue' => 'Job Material Issue',
      'Batch Job Material Issue' => 'Batch Job Material Issue',
      'Job Material Return' => 'Job Material Return',
      'Close Job Order' => 'Close Job Order',
      'Close Transer Order' => 'Close Transer Order',
      'Machine Run' => 'Machine Run',
      'Job Run' => 'Job Run',
      'WIP Move' => 'WIP Move',
      'CO Pick List Pick' => 'CO Pick List Pick',
      'CO Pick List Unpick' => 'CO Pick List Unpick',
      'TO Receipt : Add Qty Loss' => 'TO Receipt : Add Qty Loss',
      'Job Return' => 'Job Return',
      'Misc Issue' => 'Misc Issue',
      'Misc Receipt' => 'Misc Receipt',
      'Pallet Builder' => 'Pallet Builder',
      'Ceate Item' => 'Ceate Item',
      'Update Item' => 'Update Item',
      'Ceate Item Category' => 'Ceate Item Category',
      'Sync Customer' => 'Sync Customer',
      'Sync Vendor' => 'Sync Vendor',
      'syncPurchaseOrder' => 'syncPurchaseOrder',
    ),
    'module' => 
    array (
      'CustOrdPicking' => 'CO Picking',
      'CustOrdShipping' => 'CO Shipping',
      'CustomerReturn' => 'Customer Return',
      'Inventory Label' => 'Inventory Label',
      'JobMaterialIssue' => 'Job Material Issue',
      'JobMaterialReturn' => 'Job Material Return',
      'JobReceipt' => 'Job Receipt',
      'MiscIssue' => 'Misc Issue',
      'MiscReceipt' => 'Misc Receipt',
      'Picklist' => 'Pick List',
      'PickList' => 'Pick List',
      'PickNShip' => 'Pick and Ship',
      'Pallet' => 'Pallet',
      'POReceipt' => 'PO Receipt',
      'Putaway' => 'Put Away',
      'StockMove' => 'Stock Move',
      'TranOrderShipping' => 'TO Shipping',
      'TransferOrderReceipt' => 'TO Receipt',
      'WIPMove' => 'WIP Move',
      'JobLabour' => 'Job Labor Hours',
      'MachineRun' => 'Machine Run Hours',
    ),
    'form' => 
    array (
      'job_order' => 'Job Order',
      'co' => 'Customer Order',
      'grn' => 'GRN',
      'job_matl' => 'Job Material',
      'to_line' => 'TO Line',
      'po_item' => 'Purchase Order',
      'po' => 'Purchase Order',
      'uom_conversion' => 'UOM Conversion',
    ),
    'version' => '1.2.3',
    'locale' => false,
    'midApis' => false,
    'environment' => 'TestEnvironment',
    'debug' => true,
    'client_prefix' => NULL,
    'is_saas' => false,
    'icapt_logo' => false,
    'migrator' => false,
    'bartender' => false,
    'enable_packing' => false,
    'enable_grn' => true,
    'enable_google_tag' => false,
    'enable_jobclose_sap' => false,
    'enable_cust_name_notfrom_cust_table' => false,
    'enable_sap_poreceive_to_drafts' => false,
    'enable_sap_miscissue_to_drafts' => false,
    'enable_sap_miscreceive_to_drafts' => false,
    'enable_sap_miscreceive_baseentry_baseline' => false,
    'enable_sap_miscissue_baseentry_baseline' => false,
    'enable_connected_apps' => true,
    'enable_sap_ap_readfrom_maltrans' => false,
    'enable_sap_resync' => false,
    'enable_customer_returns' => false,
    'enable_scan_inv' => false,
    'auto_delete_integration_logs' => '90',
    'btadebug' => '0',
    'btwdatafolder' => NULL,
    'btwtemplatepath' => NULL,
    'mapbtwdatafolder' => NULL,
    'mapbtwtemplatepath' => NULL,
    'outgoingTrans' => 
    array (
      0 => 'Misc Issue',
      1 => 'Stock Move From',
      2 => 'Put Away From',
      3 => 'PO Return',
      4 => 'CO Ship',
      5 => 'CO Pick From',
      6 => 'CO Unpick From',
      7 => 'Pick N Ship',
      8 => 'TO Ship',
      9 => 'TO Ship From',
      10 => 'TO Receipt From',
      11 => 'Job Material Issue',
      12 => 'Job Return',
      13 => 'Pick From',
      'pallet_build_from' => 'Pallet Build From',
      'pallet_move_from' => 'Pallet Move From',
      'pallet_letdown_from' => 'Pallet Letdown From',
      'pallet_transfer_from' => 'Pallet Transfer From',
      'pallet_destruct_from' => 'Pallet Destruct From',
    ),
    'zoho_apisetting' => 
    array (
      'client_id' => '1000.EIBML85AD3FZ03JKZJ4KQSRX8QCCWV',
      'client_secret' => '125dc6852f4e785528dcc466be6f04ec7149e935c6',
      'scope' => 'ZohoSubscriptions.fullaccess.all',
      'state' => 'my',
      'response_type' => 'code',
      'authorization_code' => 'authorization_code',
      'refresh_token' => 'refresh_token',
      'access_type' => 'offline',
      'organizationId' => '*********',
      'refeshtoken' => '**********************************************************************',
      'paymentgateway' => 'stripe',
      'product_id' => '2794855000000074278',
      'apiUrl' => 'https://accounts.zoho.com/oauth/v2/token?',
      'subscriptionBillingUrl' => 'www.zohoapis.com/billing',
      'subscriptionApiUrl' => 'subscriptions.zoho.com/api',
      'open_subscription' => '1',
    ),
    'special_modules' => 
    array (
      'alternate_barcode' => false,
      'enable_suffix' => true,
      'enable_erp_integeration' => false,
      'enable_packing' => true,
      'enable_pallet' => true,
    ),
    'app_url' => 
    array (
      'real_url' => 'ssss',
    ),
    'recaptcha' => 
    array (
      'key' => NULL,
      'secret' => NULL,
    ),
    'path' => 
    array (
      'process' => NULL,
      'failed' => NULL,
      'sucess' => NULL,
    ),
    'process_site' => 
    array (
      'site' => NULL,
    ),
    'license' => 
    array (
      'product_key' => '!PhiiCaptYunicmeh20220114$$)&',
    ),
    'quickbook' => 
    array (
      'client_id' => NULL,
      'client_secret' => NULL,
      'is_sandbox ' => NULL,
    ),
    'bartendercloud' => 
    array (
      'client_id' => NULL,
      'client_secret' => NULL,
    ),
  ),
  'languages' => 
  array (
    'en' => 'English',
    'bm' => 'Bahasa Melayu',
    'cn' => 'Chinese',
  ),
  'logging' => 
  array (
    'default' => 'stack',
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'daily',
        ),
        'ignore_exceptions' => false,
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\logs/laravel.log',
        'level' => 'debug',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 14,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => NULL,
          'port' => NULL,
        ),
      ),
      'stderr' => 
      array (
        'driver' => 'monolog',
        'handler' => 'Monolog\\Handler\\StreamHandler',
        'formatter' => NULL,
        'with' => 
        array (
          'stream' => 'php://stderr',
        ),
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
      'zoholog' => 
      array (
        'driver' => 'custom',
        'via' => 'App\\Logging\\ZohoLogger',
        'path' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\logs/zoho_log.log',
        'level' => 'debug',
        'days' => 14,
      ),
    ),
  ),
  'mail' => 
  array (
    'driver' => 'smtp',
    'host' => 'mail.axacute.com',
    'port' => '465',
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'Axacute Support',
    ),
    'encryption' => 'ssl',
    'username' => '<EMAIL>',
    'password' => 'Ndq*v9029',
    'sendmail' => '/usr/sbin/sendmail -bs',
    'markdown' => 
    array (
      'theme' => 'default',
      'paths' => 
      array (
        0 => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\resources\\views/vendor/mail',
      ),
    ),
    'log_channel' => NULL,
  ),
  'queue' => 
  array (
    'default' => 'sync',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'qimports',
        'queue' => 'default',
        'retry_after' => 300,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => 0,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => '',
        'secret' => '',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'your-queue-name',
        'region' => 'us-east-1',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 90,
        'block_for' => NULL,
      ),
    ),
    'failed' => 
    array (
      'database' => 'mysql',
      'table' => 'failed_qimports',
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => NULL,
      'secret' => NULL,
      'endpoint' => 'api.mailgun.net',
    ),
    'postmark' => 
    array (
      'token' => NULL,
    ),
    'ses' => 
    array (
      'key' => '',
      'secret' => '',
      'region' => 'us-east-1',
    ),
    'sparkpost' => 
    array (
      'secret' => NULL,
    ),
    'stripe' => 
    array (
      'model' => 'App\\User',
      'key' => NULL,
      'secret' => NULL,
      'webhook' => 
      array (
        'secret' => NULL,
        'tolerance' => 300,
      ),
    ),
  ),
  'session' => 
  array (
    'driver' => 'file',
    'lifetime' => '120',
    'expire_on_close' => true,
    'encrypt' => false,
    'files' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'axacute_local_dev_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => false,
    'http_only' => true,
    'same_site' => NULL,
  ),
  'snappy' => 
  array (
    'pdf' => 
    array (
      'enabled' => true,
      'binary' => 'D:\\wkhtmltopdf\\bin\\wkhtmltopdf.exe',
      'timeout' => false,
      'options' => 
      array (
        'margin-top' => 3,
        'margin-bottom' => 3,
        'margin-left' => 3,
        'margin-right' => 3,
        'print-media-type' => true,
      ),
      'env' => 
      array (
      ),
    ),
    'image' => 
    array (
      'enabled' => true,
      'binary' => 'D:\\wkhtmltopdf\\bin\\wkhtmltoimage.exe',
      'timeout' => false,
      'options' => 
      array (
      ),
      'env' => 
      array (
      ),
    ),
  ),
  'sweetalert' => 
  array (
    'cdn' => NULL,
    'alwaysLoadJS' => false,
    'neverLoadJS' => false,
    'timer' => 5000,
    'width' => '32rem',
    'height_auto' => true,
    'padding' => '1.25rem',
    'animation' => 
    array (
      'enable' => false,
    ),
    'animatecss' => 'https://cdn.jsdelivr.net/npm/animate.css',
    'show_confirm_button' => true,
    'show_close_button' => false,
    'toast_position' => 'top-end',
    'timer_progress_bar' => false,
    'middleware' => 
    array (
      'toast_position' => 'top-end',
      'toast_close_button' => true,
      'alert_auto_close' => 5000,
      'auto_display_error_messages' => false,
    ),
    'customClass' => 
    array (
      'container' => NULL,
      'popup' => NULL,
      'header' => NULL,
      'title' => NULL,
      'closeButton' => NULL,
      'icon' => NULL,
      'image' => NULL,
      'content' => NULL,
      'input' => NULL,
      'actions' => NULL,
      'confirmButton' => NULL,
      'cancelButton' => NULL,
      'footer' => NULL,
    ),
  ),
  'telescope' => 
  array (
    'domain' => NULL,
    'path' => 'telescope',
    'driver' => 'database',
    'storage' => 
    array (
      'database' => 
      array (
        'connection' => 'mysql',
        'chunk' => 1000,
      ),
    ),
    'enabled' => false,
    'middleware' => 
    array (
      0 => 'web',
      1 => 'Laravel\\Telescope\\Http\\Middleware\\Authorize',
    ),
    'only_paths' => 
    array (
    ),
    'ignore_paths' => 
    array (
      0 => 'api/*',
      1 => 'api*',
    ),
    'ignore_commands' => 
    array (
    ),
    'watchers' => 
    array (
      'Laravel\\Telescope\\Watchers\\CacheWatcher' => true,
      'Laravel\\Telescope\\Watchers\\CommandWatcher' => 
      array (
        'enabled' => true,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\DumpWatcher' => true,
      'Laravel\\Telescope\\Watchers\\EventWatcher' => 
      array (
        'enabled' => true,
        'ignore' => 
        array (
        ),
      ),
      'Laravel\\Telescope\\Watchers\\ExceptionWatcher' => true,
      'Laravel\\Telescope\\Watchers\\JobWatcher' => true,
      'Laravel\\Telescope\\Watchers\\LogWatcher' => true,
      'Laravel\\Telescope\\Watchers\\MailWatcher' => true,
      'Laravel\\Telescope\\Watchers\\ModelWatcher' => 
      array (
        'enabled' => true,
        'events' => 
        array (
          0 => 'eloquent.*',
        ),
      ),
      'Laravel\\Telescope\\Watchers\\NotificationWatcher' => true,
      'Laravel\\Telescope\\Watchers\\QueryWatcher' => 
      array (
        'enabled' => true,
        'ignore_packages' => true,
        'slow' => 100,
      ),
      'Laravel\\Telescope\\Watchers\\RedisWatcher' => true,
      'Laravel\\Telescope\\Watchers\\RequestWatcher' => 
      array (
        'enabled' => true,
        'size_limit' => 64,
      ),
      'Laravel\\Telescope\\Watchers\\GateWatcher' => 
      array (
        'enabled' => true,
        'ignore_abilities' => 
        array (
        ),
        'ignore_packages' => true,
      ),
      'Laravel\\Telescope\\Watchers\\ScheduleWatcher' => true,
      'Laravel\\Telescope\\Watchers\\ViewWatcher' => true,
    ),
  ),
  'test' => 
  array (
    'test_pass' => false,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\resources\\views',
    ),
    'compiled' => 'D:\\Git ICAPT\\icapt-saas\\icapt-saas-codebase\\storage\\framework\\views',
  ),
  'image' => 
  array (
    'driver' => 'gd',
  ),
  'passport' => 
  array (
    'private_key' => NULL,
    'public_key' => NULL,
    'client_uuids' => false,
    'personal_access_client' => 
    array (
      'id' => NULL,
      'secret' => NULL,
    ),
    'storage' => 
    array (
      'database' => 
      array (
        'connection' => 'mysql',
      ),
    ),
  ),
  'datatables-html' => 
  array (
    'namespace' => 'LaravelDataTables',
    'table' => 
    array (
      'class' => 'table',
      'id' => 'dataTableBuilder',
    ),
    'callback' => 
    array (
      0 => '$',
      1 => '$.',
      2 => 'function',
    ),
    'script' => 'datatables::script',
    'editor' => 'datatables::editor',
  ),
  'datatables' => 
  array (
    'search' => 
    array (
      'smart' => true,
      'multi_term' => true,
      'case_insensitive' => true,
      'use_wildcards' => false,
      'starts_with' => false,
    ),
    'index_column' => 'DT_RowIndex',
    'engines' => 
    array (
      'eloquent' => 'Yajra\\DataTables\\EloquentDataTable',
      'query' => 'Yajra\\DataTables\\QueryDataTable',
      'collection' => 'Yajra\\DataTables\\CollectionDataTable',
      'resource' => 'Yajra\\DataTables\\ApiResourceDataTable',
    ),
    'builders' => 
    array (
    ),
    'nulls_last_sql' => ':column :direction NULLS LAST',
    'error' => NULL,
    'columns' => 
    array (
      'excess' => 
      array (
        0 => 'rn',
        1 => 'row_num',
      ),
      'escape' => '*',
      'raw' => 
      array (
        0 => 'action',
      ),
      'blacklist' => 
      array (
        0 => 'password',
        1 => 'remember_token',
      ),
      'whitelist' => '*',
    ),
    'json' => 
    array (
      'header' => 
      array (
      ),
      'options' => 0,
    ),
  ),
  'trustedproxy' => 
  array (
    'proxies' => NULL,
    'headers' => 30,
  ),
  'horizon' => 
  array (
    'silenced' => 
    array (
      0 => 'Spatie\\Health\\Jobs\\HealthQueueJob',
    ),
  ),
  'datatables-fractal' => 
  array (
    'includes' => 'include',
    'serializer' => 'League\\Fractal\\Serializer\\DataArraySerializer',
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'alias' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
