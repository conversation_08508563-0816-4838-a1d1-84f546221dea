/*================================================================================
	Item Name: Robust - Responsive Admin Theme
	Version: 1.0
	Author: PIXINVENT
	Author URL: http://www.themeforest.net/user/pixinvent
================================================================================

NOTE:
------
PLACE HERE YOUR OWN SCSS CODES AND IF NEEDED, OVERRIDE THE STYLES FROM THE OTHER STYLESHEETS.
WE WILL RELEASE FUTURE UPDATES SO IN ORDER TO NOT OVERWRITE YOUR STYLES IT'S BETTER LIKE THIS.  */

*[data-href] {
    cursor: pointer;
}

select.custom-select {
    padding-left: 11px !important;
}

.input-group_custom {
    position: relative;
}

.input-group_custom .form-control {
    height: 30px;
}

.input-group_custom button {
    position: absolute;
    right: 0px;
    top: 0px;
    height: 30px;
    line-height: 15px;
}

span.form-control.border-primary.pseudoinput {
    width: 100%;
    word-break: break-all;
    word-wrap: break-word;
    height: auto;
    min-height: 20px;
}

div.modal-content {
    border-radius: 5px !important;
    padding: 10px;
}

.desc_text {
    white-space: pre-wrap;
    white-space: -moz-pre-wrap;
    white-space: -pre-wrap;
    white-space: -o-pre-wrap;
    word-wrap: break-word;
    word-break: break-word;
}

.btn {
    min-width: 100px;
}

.card-block-custom {
    padding-left: 10px !important;
    padding-right: 10px !important;
}

.label-control-custom {
    padding-left: 6px !important;
    padding-right: 6px !important;
}

@media screen and (max-width: 767px) {
    .card-block {

        padding-right: 1.0rem;
        padding-left: 1.0rem;
    }


    .btn_sm .btn {
        /* background-color: #ccc !important; */
        min-width: 75px !important;
    }
}

form label.error {
    word-break: break-word;
}

.show-std-form {
    display: none;
}