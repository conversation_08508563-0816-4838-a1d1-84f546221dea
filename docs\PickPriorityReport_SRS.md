# Software Requirements Specification (SRS)
## Pick Priority Report – ICAPT SaaS WMS

_Last updated: 08-Jul-2025_

---

## 1. Introduction
### 1.1 Purpose
This SRS defines the complete set of software requirements for the **Pick Priority Report** module within the ICAPT SaaS Warehouse-Management-System (WMS).  It is intended for developers, QA analysts, product owners, and stakeholders involved in design, implementation, testing and maintenance.

### 1.2 Scope
The Pick Priority Report presents a real-time, prioritised list of customer-order lines awaiting picking.  It helps supervisors allocate pickers efficiently by ranking lines according to due-date urgency, stock availability and warehouse zone.  The module is delivered as a responsive web page inside the existing WMS portal, consuming data from the production PostgreSQL database via Laravel Eloquent.

### 1.3 Definitions, Acronyms & Abbreviations
| Term | Meaning |
|------|---------|
| CO | Customer Order |
| WMS | Warehouse Management System |
| Zone | Logical grouping of locations within a warehouse |
| DataTables | jQuery plug-in used for interactive tables |
| SRS | Software Requirements Specification |

### 1.4 References
1. _IEEE Std 830-1998 – Recommended Practice for Software Requirements Specifications_
2. ICAPT SaaS WMS Coding Standards v2.3
3. Functional Spec: `docs/PickPriorityReport_Functional.md`

### 1.5 Overview
Section 2 provides an overall product description.  Section 3 enumerates functional and non-functional requirements.  Section 4 defines external interfaces.  Section 5 lists other constraints, assumptions and dependencies.

---

## 2. Overall Description
### 2.1 Product Perspective
The Pick Priority Report is a **sub-module** of the existing _Reports_ component in the WMS.  It leverages:
* Laravel 8 backend using existing authentication, policy and site-scope mechanisms.
* jQuery DataTables frontend (already included globally).
* Existing database tables: `coitems`, `item_warehouses`, `item_locs`, `locs`, `zones`.

```
[Browser] ⇆ [Laravel Controller] ⇆ [Eloquent/DB] ⇆ [PostgreSQL]
```

### 2.2 Product Functions (high-level)
1. Retrieve open CO lines with outstanding quantities.
2. Merge stock availability, location and zone information.
3. Compute shortage metric.
4. Sort records using priority rules.
5. Render paginated, searchable, exportable table.
6. Provide optional filters (warehouse, zone, due-date range).
7. Export current view to CSV/Excel/PDF.

### 2.3 User Classes & Characteristics
| User Class | Description | Technical Skill |
|------------|-------------|-----------------|
| Picker Supervisor | Assigns pick tasks; requires real-time data. | Moderate |
| Warehouse Manager | Reviews workload; export data for KPI. | Moderate |
| IT Support | Troubleshoots performance & data issues. | Advanced |

### 2.4 Operating Environment
* **Server**: Ubuntu 22.04 LTS, PHP 8.1, PostgreSQL 14
* **Client**: Chrome >= 110, Edge >= 110, Safari iPadOS >= 16
* **Network**: HTTPS over TLS 1.2+

### 2.5 Design & Implementation Constraints
* Must not introduce breaking schema changes.
* Use existing global CSS classes; no additional frameworks.
* Query response ≤ 2 s for 10 k records.

### 2.6 Assumptions & Dependencies
* Stock quantities are updated by background jobs every ≤ 5 minutes.
* Each item has at most one default location per warehouse in `item_locs`.
* Zones are properly mapped in `locs.zone_id`.

---

## 3. Specific Requirements
### 3.1 Functional Requirements (F-#)
| ID | Description | Priority |
|----|-------------|----------|
| F-1 | System shall fetch CO lines where `rel_status = 'O'` and `qty_released > qty_shipped`. | High |
| F-2 | System shall calculate `qty_required = qty_released − qty_shipped`. | High |
| F-3 | System shall left-join `item_warehouses` to obtain `qty_available`. | High |
| F-4 | System shall left-join `item_locs → locs → zones` to obtain `loc_num` and `zone_num`. | Medium |
| F-5 | System shall compute `shortage = qty_required − qty_available`, floor at 0. | High |
| F-6 | System shall sort results by `due_date`, `zone_num` (nulls last), `shortage`. | High |
| F-7 | System shall render the list via DataTables with server-side processing. | High |
| F-8 | User shall be able to search, paginate and sort columns client-side. | High |
| F-9 | User shall export current view to Copy, CSV, Excel, PDF. | Medium |
| F-10 | System shall apply policy `hasReport` to restrict access. | High |
| F-11 | (Phase-2) User shall filter by Warehouse, Zone, Due-date range. | Low |
| F-12 | (Phase-2) User shall assign a picker to a selected line. | Low |

### 3.2 External Interface Requirements
#### 3.2.1 User Interfaces
* Page URL: `/report/pick-priority`
* Follows _layout.app_ base template.
* Bootstrap card with `bg-info` header.
* Column definitions per Functional Spec (see Section 4.2).

#### 3.2.2 Hardware Interfaces
* None.

#### 3.2.3 Software Interfaces
| Interface | Direction | Protocol | Description |
|-----------|-----------|----------|-------------|
| WMS DB | R/W | SQL via Laravel Eloquent | Query data tables listed in 2.1 |
| Authn Service | R | Laravel Passport Cookie | reuse existing session |

#### 3.2.4 Communications Interfaces
HTTPS 443; no additional ports.

### 3.3 Performance Requirements
* **PR-1**: Generate JSON payload < 500 ms (app server benchmark).
* **PR-2**: Frontend first meaningful paint within 2 s on 50-Mb/s link.

### 3.4 Security Requirements
* **SR-1**: Enforce HTTPS (Laravel `URL::forceScheme('https')`).
* **SR-2**: Authorize via `hasReport` policy before data access.
* **SR-3**: Escape all user input in DataTables to prevent XSS.

### 3.5 Safety Requirements
Not applicable – purely informational module.

### 3.6 Software Quality Attributes
| Attribute | Target |
|-----------|--------|
| Reliability | 99.5 % uptime. |
| Maintainability | PSR-12 compliant code; unit tests covering query builder. |
| Usability | ≤ 2 clicks to export; keyboard accessible. |
| Scalability | Performs up to 25 k open lines with ≤ 5 s response (degraded). |

---

## 4. External Interface Detail
### 4.1 Database Schema Impact
No new tables; relies on existing.

### 4.2 UI Mock-up
```text
+-----------------------------------------------------------+
|  Pick Priority Report                                     |
+-----------------------------------------------------------+
| CO Num | Line | Rel | Item | Customer | Zone | Shortage ...
+-----------------------------------------------------------+
| 35109  | 10   | 1   | RM-A | ACME     | A1   | 0         |
| ...                                                     |
+-----------------------------------------------------------+
```

### 4.3 API Endpoint (internal)
`GET /report/pick-priority/data` → JSON (DataTables format)

---

## 5. Other Requirements
* **Backup**: Daily DB backup covers all referenced tables.
* **Logging**: Queries logged via Laravel `DB::listen` in the `report` channel.
* **Internationalisation**: Headers and dates localised via existing lang files.

---

## 6. Appendices
A. SQL Optimisation Plan  
B. Test-Case Matrix (to be maintained in `tests/Feature/Report/PickPriorityReportTest.php`).

---
**Author:** Dev Team – ICAPT SaaS WMS
