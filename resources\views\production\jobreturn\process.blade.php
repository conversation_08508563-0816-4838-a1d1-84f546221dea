@extends('layout.mobile.app')
@section('title', __('mobile.nav.job_return'))
@section('head')
    <style>
        .card {
            box-shadow: 0px 0px 0px transparent;
            border: 1px solid transparent;
        }

        div.col-xs-2.col-md-1.col-lg-1,
        div.col-xs-2.col-md-1.col-lg-2 {
            margin-top: 5px;
        }

        form .form-group {
            margin-bottom: 0.2rem;
        }
    </style>
@endsection
@section('content')
    <div class="card-body collapse in">
        <div class="card-block ">
            <form class="form" autocomplete="off" id="postform" method="POST" action="{{ route('JobReturn.process') }}">
                @csrf
                <div class="form-body">
                    @include('components.form.scan_input', ['type' => 'job', 'merge' => true])
                    @if (isset($batch_id))
                        <input type="hidden" name="batch_id" id="batch_id" value="{{ $batch_id }}" />
                    @endif
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="whse_num">{{ __('mobile.label.whse_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" onchange="clickSelf(this.id)" id="whse_num" required
                                    class="form-control border-primary" name="whse_num"
                                    placeholder="{{ __('mobile.placeholder.whse_num') }}"
                                    value="{{ old('whse_num', auth()->user()->getCurrWhse()) }}">
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" tabindex="-1" name="{{ __('mobile.list.warehouses') }}"
                                onClick="selection('/getWhse','whse_num','whse_num','whse_num');modalheader(this.id,this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                    class="icon-search"></i></button>
                        </div>
                    </div>

                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="item_num">{{ __('mobile.label.item_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" onchange="clickSelf(this.id)" id="item_num"
                                    class="form-control border-primary valid" name="item_num"
                                    placeholder="{{ __('mobile.placeholder.item_num') }}" required>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" tabindex="-1" name="{{ __('mobile.list.items') }}"
                                onclick="selection('/getWhseItem','whse_num,item_num','item_num','item_num');modalheader(this.id,this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                    class="icon-search"></i></button>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="from_loc">{{ __('mobile.label.from_loc') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" onchange="clickSelf(this.id)" name="loc_num" id="loc_num"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.from_loc') }}" required>
                                <span id="checkLoc"></span>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.locations') }}"
                                onclick="selectionNull('/getItemLocCheckPicking','whse_num,item_num,loc_num','loc_num','loc_num');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                    class="icon-search"></i></button>
                        </div>
                    </div>

                    <div class="form-group row show-std-form" id="trlot" hidden>
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" onchange="clickSelf(this.id)" name="lot_num" id="lot_num"
                                    class="form-control border-primary valid"
                                    placeholder="{{ __('mobile.placeholder.lot_num') }}" maxlength="50">
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.label.lot_num') }}"
                                onclick="selectionMultiLineInput('/getLotLocExpiry','whse_num,loc_num,item_num,sortField,sortBy','lot_num','loc_num,lot_num');modalheader_uom(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                    class="icon-search"></i></button>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="job_num">{{ __('mobile.label.job_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="job_num" id="job_num" class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.job_num') }}" required>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" tabindex="-1" name="{{ __('mobile.list.jobs') }}"
                                onClick="selection('/getJobQtyCompletedMoreThan0','whse_num,item_num','job_num','job_num');modalheader(this.id,this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="suffix">{{ __('mobile.label.suffix') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input inputmode="numeric" type="text" name="suffix" id="suffix"
                                    class="form-control border-primary"
                                    onchange="display('/displayJobMatlItemSuffix','whse_num,job_num,suffix','item_num,item_desc,qty_required');clickSelf(this.id)"
                                    placeholder="Suffix" required>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" tabindex="-1" name="{{ __('admin.list.suffix') }}"
                                onClick="selection('/getSuffix','job_num','suffix','suffix');modalheader(this.id,'Suffix');"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                            for="oper_num">{{ __('mobile.label.oper_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" readonly onchange="clickSelf(this.id)" name="oper_num"
                                    id="oper_num" class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.oper_num') }}" required>
                            </div>
                        </div>
                        {{-- <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="Operations" onclick="selection('/getOper','job_num','oper_num','oper_num');modalheader(this.id, this.name);" class="btn btn-icon btn-outline-secondary btn-square bg-primary white" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div> --}}
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                            for="qty_available">{{ __('mobile.label.qty_available') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="qty_available" id="qty_available" style="text-align:right"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.label.qty_available') }}" required readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control"
                            for="qty_returnable">{{ __('mobile.label.qty_returnable') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="qty_returnable" id="qty_returnable" style="text-align:right"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.label.qty_returnable') }}" required readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                            for="qty_to_return">{{ __('mobile.label.qty_to_return') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input inputmode="numeric" type="number" onchange="clickSelf(this.id)"
                                    style="text-align:right" name="qty_to_return" min="0" step="any"
                                    id="qty_to_return" class="form-control border-primary"
                                    placeholder="{{ __('mobile.label.qty_to_return') }}" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row show-std-form">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                            for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" onchange="clickSelf(this.id)" name="reason_code" id="reason_code"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.reason_code') }}" required>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.label.reason_code') }}"
                                onclick="selection('/getReasonCode/JobReturn', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{ __('admin.menu.reason_codes') }}');"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    </div>

                    <input hidden name="uom" id="uom">

                    <div class="text-center mt-1 show-std-form">
                        <button type="submit" class="btn btn-primary submitloader">
                            <i class="icon-check2"></i> {{ __('mobile.button.process') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $("#whse_num").on('change', function() {
                // $("#item_num,#loc_num,#lot_num,#job_num,#suffix,#oper_num,#qty_available,#qty_returnable,#qty_to_return,#reason_code").val('');

                // $("#item_num,#job_num,#suffix,#oper_num,#qty_available,#qty_returnable,#qty_to_return,#reason_code").val('');
                $("#qty_available-error,#qty_returnable-error").remove();
            });

            $("#item_num").on('change', function() {
                // $("#loc_num,#lot_num,#job_num,#suffix,#oper_num,#qty_available,#qty_returnable,#qty_to_return,#reason_code").val('');
                $("#qty_available-error,#qty_returnable-error").remove();

                if ($("#item_num").val()) {
                    // Show Lot Number if Item is Lot-tracked
                    $.ajax({
                        url: "{{ route('validateItemLot') }}",
                        type: 'GET',
                        data: {
                            item_num: $("#item_num").val(),
                        },
                        success: function(data) {
                            if (data == "true") {
                                $("#trlot").prop('hidden', false);
                            } else {
                                $("#trlot").prop('hidden', true);
                                $("#lot_num").val('');
                            }
                        }
                    });
                }
            });

            $("#loc_num").on('change', function() {
                // $("#job_num,#suffix,#oper_num,#qty_available,#qty_returnable,#qty_to_return,#reason_code").val('');
                $("#qty_available-error,#qty_returnable-error").remove();

                if ($("#loc_num").val() && $("#trlot").prop('hidden') == true) {
                    // Predefined qty_available
                    $.ajax({
                        url: '{{ route('getItemLocOrLotLoc') }}',
                        type: 'GET',
                        data: {
                            whse_num: $("#whse_num").val(),
                            loc_num: $("#loc_num").val(),
                            item_num: $("#item_num").val(),
                        },
                        success: function(data) {
                            $("#uom").val(data['uom']);
                            $("#qty_available").val(data['qty_available']);
                        },
                    });
                }
            });

            $("#lot_num").on('change', function() {
                // $("#job_num,#suffix,#oper_num,#qty_available,#qty_returnable,#qty_to_return,#reason_code").val('');
                $("#qty_available-error,#qty_returnable-error").remove();

                if ($("#lot_num").val()) {
                    // Predefined qty_available
                    $.ajax({
                        url: '{{ route('getItemLocOrLotLoc') }}',
                        type: 'GET',
                        data: {
                            whse_num: $("#whse_num").val(),
                            loc_num: $("#loc_num").val(),
                            item_num: $("#item_num").val(),
                            lot_num: $("#lot_num").val(),
                        },
                        success: function(data) {
                            $("#uom").val(data['uom']);
                            $("#qty_available").val(data['qty_available']);
                        },
                    });
                }
            });

            $("#suffix").on('change', function() {
                $("#oper_num,#qty_returnable,#qty_to_return,#reason_code").val('');
                $("#qty_available-error,#qty_returnable-error,#oper_num-error").remove();

                if ($("#suffix").val()) {
                    // Predefined oper_num to last operation and qty_returnable
                    $.ajax({
                        url: '{{ route('getSuffixData') }}',
                        type: 'GET',
                        data: {
                            suffix: $("#suffix").val(),
                            job_num: $("#job_num").val(),
                        },
                        success: function(data) {
                            $("#oper_num").val(data.job_routes[0].oper_num);
                            $("#qty_returnable").val(data.qty_returnable);
                        },
                    });
                }
            });

            $("#oper_num").on('change', function() {
                $("#qty_to_return,#reason_code").val('');
                $("#qty_available-error,#qty_returnable-error").remove();

                // Check for catch weight when all required fields are filled
                if ($("#item_num").val() && $("#job_num").val() && $("#suffix").val() && $("#oper_num").val()) {
                    checkCatchWeightJobReturn();
                }
            });

            $("#qty_to_return").on('change', function() {
                $("#qty_available-error,#qty_returnable-error").remove();

                // Set max to which is smaller: qty_available or qty_returnable
                $(this).attr('max', Math.min($("#qty_available").val(), $("#qty_returnable").val()));
            });

            // cannot type -,+,e in input number
            $("#qty_to_return").on('input', function() {
                $(this)[0].validity.valid || ($(this).val($(this).val()));
            });

            /* $("#loc_num").on("change", function() {
                 $("#loc_info").html("");
                 $("#checkLoc").html("");
                 $("#locnumnotexist").html('');

                 if ($("#loc_num").val() == "") {
                     $("#loc_info").html("");
                     $("#checkLoc").html("");
                     $("#locnumnotexist").html('');
                 }
             });*/
        })

        // Function to check catch weight for Job Return
        function checkCatchWeightJobReturn() {
            var item_num = $("#item_num").val();
            var job_num = $("#job_num").val();
            var suffix = $("#suffix").val();
            var oper_num = $("#oper_num").val();

            if (!item_num || !job_num || !suffix || !oper_num) {
                return;
            }

            // AJAX call to check for catch weight
            $.ajax({
                url: '/check-catch-weight',
                type: 'GET',
                data: {
                    item_num: item_num,
                },
                success: function (response) {
                    console.log('Catch weight response:', response);
                    if (response == 1) {
                        // Show loader with message
                        showLoader('Loading catch weight form...');
                        $('.show-std-form').hide();

                        // Redirect to catch weight form with parameters
                        var params = new URLSearchParams({
                            item_num: item_num,
                            job_num: job_num,
                            suffix: suffix,
                            oper_num: oper_num
                        });

                        window.location.href = window.location.pathname + '?' + params.toString();
                    } else {
                        // Hide loader when no catch weight found
                        hideLoader();
                        $('.show-std-form').show();
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error('Error checking catch weight:', textStatus, errorThrown);
                    // Hide loader on error
                    hideLoader();
                }
            });
        }

        var errorMessage = "{{ __('error.mobile.loc_not_exists') }}";

        $("#postform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $(".submitloader").attr('disabled', false);
            }
        });
        $("#postform").submit(function() {
            $(".submitloader").attr('disabled', true);
        })
        $("#postform").validate({
            onchange: true,

            rules: {
                whse_num: {
                    required: true,
                    remote: {
                        url: "{{ route('JobReceiptValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                        }
                    }
                },
                item_num: {
                    required: true,
                    remote: {
                        url: "{{ route('WarehouseItemValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                        }
                    }
                },
                /*loc_num:{
                    required: true,
                    remote:{
                        url: "{{ route('WarehouseItemValidation') }}",
                        type: "post",
                        data: {
                            _token : $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            }
                        },
                        dataFilter: function(data){
                            if(data == 'true'){
                                var result = 'true';
                                $.ajax({
                                    url: '{{ route('checkLocNotTransit') }}',
                                    type: "GET",
                                    async: false,
                                    data: {
                                        whse_num: $("#whse_num").val(),
                                        loc_num: $("#loc_num").val(),
                                    },
                                    dataFilter: function(data){
                                        if (data == "transit") {
                                            result = 'false';
                                            errorMessage = "{{ __('error.mobile.transit_loc') }}";
                                        }
                                        else{
                                            showNewLoc();
                                        }
                                    }
                                });

                                return result;
                            }

                            errorMessage = "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.loc_num')]) }}";
                            return false;
                        }
                    }
                },*/

                loc_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            console.log(data.length);
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#checkLoc").html("");
                                    // $("#locnumnotexist").html('');
                                    //  $("#qty").val("");
                                    //  $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#checkLoc").html("");
                                    errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    return false;
                                } else {
                                    // showNewLoc();
                                    return true;
                                }
                            } else {
                                $("#checkLoc").html("");
                                errorMessage = "{{ __('error.mobile.loc_not_exists') }}";

                                return false;
                            }
                        }
                    }
                },




                lot_num: {
                    required: function() {
                        if ($("#lot_num").is(":visible")) {
                            return true;
                        }
                    },
                    remote: {
                        url: "{{ route('ExistingLotValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                            loc_num: function() {
                                return $("#loc_num").val();
                            }
                        }
                    }
                },
                job_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkJobQtyCompletedMoreThan0') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                        }
                    }
                },
                suffix: {
                    required: true,
                    remote: {
                        url: "{{ route('JobReceiptValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                        }
                    }
                },
                oper_num: {
                    required: true,
                    remote: {
                        url: "{{ route('JobRouteValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            job_num: function() {
                                return $("#job_num").val();
                            },
                        }
                    }
                },
                reason_code: {
                    required: true,
                    remote: {
                        url: "{{ route('reasonValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            reason_class: function() {
                                return "JobReturn";
                            },
                            reason_num: function() {
                                return $("#reason_code").val();
                            },
                        }
                    }
                },
            },
            messages: {
                whse_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]) }}"
                },
                item_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.item_num')]) }}"
                },
                loc_num: {
                    remote: function() {
                        return errorMessage;
                    }
                },
                lot_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}",
                },
                job_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.job_num')]) }}"
                },
                suffix: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.suffix')]) }}"
                },
                oper_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.oper_num')]) }}"
                },
                qty_available: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.qty_available')]) }}"
                },
                qty_returnable: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.qty_returnable')]) }}"
                },
                qty_to_return: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.qty_to_return')]) }}"
                },
                reason_code: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.reason_code')]) }}"
                },
            },
            submitHandler: function(form) {
                $(".pageloader").css("display", "block");
                $(".submitloader").attr("disabled", true);
                form.submit();
            }
        });
    </script>

    @include('errors.maxchar')
    @include('util.selection')
    @include('util.convert_alternade_barcode_to_item')
@endsection
