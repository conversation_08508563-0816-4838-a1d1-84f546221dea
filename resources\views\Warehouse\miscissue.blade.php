@extends('layout.mobile.app')
@section('content')
@section('title', __('Misc Issue'))
<style>
    .align-left {
        text-align: left;
        font-size: 12pt;
        font-family: inherit;
    }

    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="postform" name="postform" action="{{ route('issueprocess') }}"
            method="post">
            <input type="hidden" name="batch_id" value="{{ $batch_id }}">
            <input type="hidden" readonly value="MISC ISSUE" name="class" class="form-control border-primary">
            <input type="hidden" readonly id="enable_sap_miscissue_baseentry_baseline"
                name="enable_sap_miscissue_baseentry_baseline" value="{{ $enable_sap_miscissue_baseentry_baseline }}">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                <div class="form-group row">
                    <label
                        class="col-xs-3 col-md-2 col-lg-3 label-control required">{{ __('mobile.label.whse_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group" <?php if (!$tparm) {
                            echo 'colspan="2"';
                        } ?>>
                            <input type="text" onchange="clickSelf(this.id)" id="whse_num" autocomplete="off"
                                name="whse_num" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.whse_num') }}" <?php if (!$tparm) {
                                    echo 'readonly';
                                } ?>
                                value="{{ old('whse_num') ?? auth()->user()->getCurrWhse() }}" required>
                        </div>
                    </div>
                    @if ($tparm)
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.warehouses') }}" tabindex="-1"
                                id="getRequest1"
                                onClick="selection('/getWhse','whse_num','whse_num','whse_num');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif
                </div>

                <div class="form-group row" id="tritem">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" onchange="clickSelf(this.id)" name="item_num" autocomplete="off"
                                id="item_num" onchange="" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.item_num') }}"
                                value="{{ old('item_num') }}"required>
                            <input type="hidden" name="lot_tracked" id="lot_tracked" value="">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" id="getItem" name="{{ __('mobile.list.items') }}"
                            onClick="selection('/getExistingItemNoCheck','whse_num,item_num','item_num','item_num');modalheader(this.id,this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="trdesc">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"></label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <textarea type="text" class="form-control border-primary" name="item_desc" autocomplete="off" id="item_desc"
                                placeholder="{{ __('mobile.placeholder.item_desc') }}" readonly>{{ old('item_desc') }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="loc_num">{{ __('mobile.label.loc_num') }} </label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="loc_num" autocomplete="off" id="loc_num"
                                class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.loc_num') }}"
                                value="{{ old('loc_num') }}"  required>


                            <span id="locnumnotexist"></span>
                            <span id="checkLoc"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="{{ __('mobile.list.locations') }}" tabindex="-1"
                            onClick="selectionNull('/getItemLocCheckPicking','whse_num,item_num,loc_num','loc_num','loc_num');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="trlot" hidden>
                    <label
                        class="col-xs-3 col-md-2 col-lg-3 label-control required">{{ __('mobile.label.lot_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="lot_num" id="lot_num" maxlength="50"
                                onchange="display('/displayLotQuantity','item_num,whse_num,loc_num,lot_num','qty_on_hand');clickSelf(this.id)"
                                class="form-control border-primary valid"
                                placeholder="{{ __('mobile.placeholder.lot_num') }}" <?php if ($disable_lot_number_selection == 1) {
                                    echo 'readonly';
                                } ?>
                                value="{{ old('lot_num') }}">
                        </div>
                    </div>
                    @if ($disable_lot_number_selection == 0)
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.lot_locations') }}" id="lotbtn"
                                onclick="selectionMultiLineInput('/getLotLocExpiry','whse_num,loc_num,item_num,sortField,sortBy','lot_num','loc_num,lot_num');modalheader_uom(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif
                </div>

                <div class="form-group row">
                    <label
                        class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" onchange="clickSelf(this.id)" autocomplete="off"
                                name="reason_code" id="reason_code" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.reason_code') }}"
                                value="{{ old('reason_code') }}" required>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="getRequest5" name="{{ __('mobile.list.reasons') }}"
                            onClick="selection('/getReasonCode/MiscIssue', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{ __('admin.menu.reason_codes') }}');"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                @include('components.form._qty_on_hand', [
                    'qty_available' => old('qty_available'),
                    'base_uom' => old('base_uom'),
                    'qty_available_conv' => old('qty_available_conv'),
                ])

                <div class="form-group row" id="trtoissue">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="qty_to_issue">{{ __('mobile.label.qty_to_issue') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="number" inputmode="numeric" pattern="\d+" style="text-align:right"
                                value="{{ old('qty') }}" onblur="formatDecimal(this)" onKeyup="umchange();"
                                autocomplete="off" id="qty" name="qty" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.qty_to_issue') }}" size="4" required>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" id="uom" onchange="clickSelf(this.id)" name="uom"
                            class="form-control border-primary" placeholder="{{ __('mobile.placeholder.uom') }}"
                            value="{{ old('uom') }}">
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="{{ __('mobile.list.uoms') }}"
                            onClick="selection('/getItemUOMConv','item_num','uom','uom');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="document_num">{{ __('mobile.label.doc') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="document_num" id="document_num" autocomplete="off"
                                class="form-control border-primary" placeholder="{{ __('mobile.placeholder.doc') }}"
                                value="{{ old('document_num') }}" name="doc" maxlength="30">
                        </div>
                    </div>
                </div>


                @if ($sap_integration == 1)
                    <div class="form-group row" id="sap_base_entry_div" style="display:none">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required" for="document_num">Base
                            Entry</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" required name="sap_base_entry" min=0 id="sap_base_entry"
                                    autocomplete="off" class="form-control border-primary" placeholder="Base Entry"
                                    value="{{ old('sap_base_entry') }}" maxlength="9">
                            </div>
                        </div>
                    </div>

                    <div class="form-group row" id="sap_base_line_div" style="display:none">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required" for="document_num">Base
                            Line</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" required name="sap_base_line" min=0 id="sap_base_line"
                                    autocomplete="off" class="form-control border-primary" placeholder="Base Line"
                                    value="{{ old('sap_base_line') }}" maxlength="9">
                            </div>
                        </div>
                    </div>
                @endif
                <br>

                <div style="text-align:center;">
                    <button type="submit" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.process') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@include('errors.maxchar')

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<script type="text/javascript">
    jQuery(function($) {
        $.validator.addMethod('minStrict', function(value, el, param) {
            return value > param;
        });
        $.validator.addMethod('QoH', function(value, el, param) {
            qty_available = parseFloat($('#qty_available').val());
            return qty_available > 0;
        });

        var errorMessage = 'Default error message';
        var errorMessageQty = "{{ __('error.mobile.qty_notmoreorequalqty_available') }}";

        $("#postform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $(".submitloader").attr('disabled', false);
            }
        });

        uom_error_message = "";
        $("#postform").validate({
            onchange: true,
            rules: {
                whse_num: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val()
                        }
                    }
                },
                item_num: {
                    required: true,
                    remote: {
                        url: "{{ route('WarehouseItemValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                        }
                    }
                },
                loc_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    return false;
                                } else {
                                    // if($("#trlot").is(":hidden")){
                                    //     display('/displayQuantity','item_num,whse_num,loc_num','qty_available,base_uom,qty_available_conv');
                                    // }
                                    return true;
                                }

                            } else {
                                $("#loc_info").html("");
                                $("#locnumnotexist").html('');
                                $("#qty_available").val("0");
                                errorMessage = "{{ __('error.mobile.loc_not_exists') }}";
                                // showNewLoc();
                                // $(".submitloader").attr("disabled", true);
                                return false;
                            }
                        }
                    }
                },
                lot_num: {
                    required: {
                        depends: function(element) {
                            return $("#lot_num").is(":visible");
                        }
                    },
                    remote: {
                        url: "{{ route('ExistingLotValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            loc_num: function() {
                                return $("#loc_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                        }
                    }
                },
                // qty_available:{
                //     minStrict: 0,
                // },
                qty: {
                    required: true,
                    number: true,
                    QoH: true,
                    minStrict: 0,
                    max: function() {
                        return parseFloat($("#qty_available_conv").val().replace(/,/g, "")); //
                    }
                },
                reason_code: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            class: "MISC ISSUE"
                        }
                    }
                },
                uom: {
                    required: true,
                    remote: {
                        url: "{{ route('validateConvUOM') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            selected_uom: function() {
                                return $('#uom').val()
                            },
                            item_num: function() {
                                return $('#item_num').val()
                            },
                        },
                        dataFilter: function(data) {
                            data = JSON.parse(data);

                            if (!data.result) {
                                uom_error_message = data.msg;
                                result = false;
                            }

                            return data.result;
                        }
                    }
                },
            },
            messages: {
                whse_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]) }}"
                },
                item_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.item_num')]) }}"
                },
                loc_num: {
                    remote: function() {
                        return errorMessage;
                    }
                },
                lot_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}"
                },
                // qty_available: {
                // minStrict: "{{ __('error.mobile.insufficient') }}",
                // },
                qty: {
                    number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.label.qty')]) }}",
                    QoH: "{{ __('error.mobile.insufficient') }}",
                    minStrict: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty')]) }} {0}",
                    //max: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.label.qty')]) }} {0}"

                    max: function(value, element) {
                        var maxQty = parseFloat($('#qty_available_conv').val().replace(/,/g, ""));
                        return errorMessageQty.replace(':resource_qty_available', maxQty);
                    }
                },
                reason_code: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.reason_code')]) }}"
                },
                uom: {
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                    remote: function() {
                        return uom_error_message
                    }
                },
            },
            submitHandler: function(form) {
                $(".submitloader").attr('disabled', true);
                var sap_integration = '{{ $sap_integration }}';
                var reason_code = $('#reason_code').val();
                var item_num = $("#item_num").val();
                if (sap_integration == 1) {
                    $.ajax({
                        url: '{{ route('checkReasonCodeValidation') }}',
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            reason_num: btoa($("#reason_code").val()),
                            reason_class: btoa('MiscIssue')
                        },
                        success: function(data) {

                            //var data = JSON.parse(data);
                            //console.log(data[0].sync_status);
                            if (data.length > 0) {
                                if (data[0].sync_status == "N") {
                                    Swal.fire({
                                        title: '',
                                        html: '<div class="align-left">{{ __('mobile.title.list_reason_code_title') }}</div>',
                                        input: 'text',
                                        inputAttributes: {
                                            autocapitalize: 'off',
                                            style: 'font-size: 16px;'
                                        },

                                        showCancelButton: true,
                                        confirmButtonText: 'Confirm',
                                        showLoaderOnConfirm: true,
                                        preConfirm: (inputItem) => {
                                            var original_item = $(
                                                '#item_num').val();
                                            if (original_item !==
                                                inputItem) {
                                                Swal.showValidationMessage(
                                                    `{{ __('error.admin.invalid_item_num') }} `
                                                );

                                                $(".submitloader").attr(
                                                    'disabled', false);
                                            } else {
                                                $(".pageloader").css(
                                                    "display", "block");
                                                $(".submitloader").attr(
                                                    "disabled", true);
                                                setTimeout(function() {
                                                    form.submit();
                                                }, 300);
                                            }

                                            // return fetch(`//api.github.com/users/${login}`)
                                            // .then(response => {
                                            //     if (!response.ok) {
                                            //     throw new Error(response.statusText)
                                            //     }
                                            //     return response.json()
                                            // })
                                            // .catch(error => {
                                            //     Swal.showValidationMessage(
                                            //     `Request failed: ${error}`
                                            //     )
                                            // })
                                        },
                                        allowOutsideClick: () => !Swal
                                            .isLoading()
                                    }).then((result) => {
                                        if (result.isConfirmed) {
                                            $(".pageloader").css("display",
                                                "block");
                                            $(".submitloader").attr("disabled",
                                                true);
                                            setTimeout(function() {
                                                form.submit();
                                            }, 300);
                                        } else {
                                            $(".submitloader").attr('disabled',
                                                false);
                                            return false;
                                        }
                                    });
                                } else {
                                    $(".pageloader").css("display", "block");
                                    $(".submitloader").attr("disabled", true);
                                    setTimeout(function() {
                                        form.submit();
                                    }, 300);
                                }

                            } else {
                                $(".submitloader").attr('disabled', false);
                                errorMessageReasonCode =
                                    "{{ __('error.mobile.reason_code_sync') }}";
                                return false;
                            }

                        }
                    });
                } else {

                    $(".pageloader").css("display", "block");
                    $(".submitloader").attr("disabled", true);
                    setTimeout(function() {
                        form.submit();
                    }, 300);
                }
            }
        });
    });
</script>

<script type="text/javascript">
    $(document).ready(function() {

        if ($("#trlot").is(":hidden") && $("#lot_num").val()) {
            $("#trlot").attr('hidden', false);
        }
        $("#loc_num").on("change", function() {
            if ($("#trlot").is(":hidden")) {
                display('/displayQuantity', 'item_num,whse_num,loc_num',
                    'qty_available,base_uom,qty_available_conv');
            }
        });
        $("#whse_num").on("change", function() {
            // $("#item_num, #item_desc, #loc_num, #lot_num, #reason_code, #qty_available, #qty, #document_num").val("");
            if ($("#whse_num").val() == "") {
                clearItems();
                clearLocs();
                $("#item_num, #loc_num, #reason_code, #qty, #document_num").val("");
                $("#trlot").attr('hidden', true);
            } else {
                if ($("#item_num").val() != "") {

                    display('/displayItemDesc', 'item_num', 'item_desc,uom,lot_tracked');
                    if ($("#loc_num").val() != "") {
                        display('/getLocByRankIssuePL', 'whse_num,item_num',
                            'qty_available,base_uom,qty_available_conv');

                        // display('/getLocByRankIssuePL', 'whse_num,item_num', 'loc_num,qty_available,base_uom,qty_available_conv');
                    }
                }
            }
        });

        $("#item_num").on("change", function() {
                clearItems();
                clearLocs();
            if ($("#item_num").val() == "") {
                $("#loc_num-error").remove()
                $("#loc_num, #lot_num, #qty_available, #reason_code, #qty, #document_num").val("");
                $("#trlot").attr('hidden', true);
                $("#loc_num").prop('readonly', false);
                $("button[name='{{ __('mobile.list.locations') }}']").prop("hidden", false);
            } else {
                display('/getLocByRankIssuePL', 'whse_num,item_num',
                    'qty_available,base_uom,qty_available_conv');
                display_custom('/displayItemDesc', 'item_num', 'item_desc,uom,lot_tracked');
            }
        });

        // $("#loc_num").on("change", function(){
        //     $("#lot_num").val("");
        //     if($("#loc_num").val() == ""){
        //         clearLocs();
        //         $("#loc_num-error").remove()
        //     } else
        //     {
        //         if($("#trlot").is(":hidden")){
        //             display('/displayQuantity','item_num,whse_num,loc_num','qty_available,base_uom,qty_available_conv');
        //         }
        //     }
        // });

        $("#reason_code").on("change", function() {
            var reasoncode = $("#reason_code").val();
            var enablebaseentryline = $("#enable_sap_miscissue_baseentry_baseline").val();
            var sap_integration = "{{ $sap_integration }}";

            if (sap_integration == 1) {
                if (reasoncode == "SupplierCN" && enablebaseentryline == 1) {
                    document.getElementById("sap_base_entry_div").style.display = "block";
                    document.getElementById("sap_base_line_div").style.display = "block";
                } else {
                    document.getElementById("sap_base_entry_div").style.display = "none";
                    document.getElementById("sap_base_line_div").style.display = "none";

                }
            }

        });

        $("#lot_num").on("change", function() {
            $("#reason_code").val("");
            if ($("#lot_num").val() == "") {
                clearLocs();
            } else {
                if ($("#loc_num").val() != '' && $("#lot_num").val() != '') {
                    var urlparm = buildurl('/displayLotQuantity', 'item_num,whse_num,loc_num,lot_num',
                        'qty_available,base_uom,qty_available_conv');
                    $.get(urlparm, function(data, status) {
                        $("#qty_available").val(data.qty_available);
                        $("#qty_available_conv").val(data.qty_available_conv);
                    });
                }
            }
        });

        $("#uom").on("change", function() {
            if ($("#item_num").val() == "") {
                $("#qty_available_conv").val("");
            } else {
                $("#qty").val('');
                        $("#qty-error").hide();
                display('/displayQuantityConverted', 'base_uom,item_num,qty_available,uom',
                    'qty_available_conv');
            }
        });

    });

    function display_custom(route, input_string, fieldname) {

        // Enable location selection for non-lot tracked item
        $("#loc_num").prop('readonly', false);
        $("button[name='{{ __('mobile.list.locations') }}']").prop("hidden", false);

        var disable_lot_number_selection = '{{ $disable_lot_number_selection }}';

        var urlparm = buildurl(route, input_string);

        $.get(urlparm, function(data, status) {
            var array = fieldname.split(',');

            for (var i = 0; i < array.length; i++) {
                var element = array[i];
                document.getElementById(element).value = data[element];

                if (element == "lot_tracked" && data[element] == 0) {
                    $("#trlot").prop('hidden', true);
                } else if (element == "lot_tracked" && data[element] == 1) {
                    $("#trlot").prop('hidden', false);

                    // Predefined value for loc_num, lot_num, qty_available, qty_available_conv
                    $.ajax({
                        url: '{{ route('getLocLotQtyELNSPL') }}',
                        type: "GET",
                        data: {
                            disable_lot_number_selection: disable_lot_number_selection,
                            whse_num: $("#whse_num").val(),
                            item_num: $("#item_num").val(),
                        },
                        success: function(data) {
                            if (disable_lot_number_selection == 1) {
                                $("#lot_num").prop('readonly', true);
                                $("#loc_num").prop('readonly', true);
                                $("button[name='{{ __('mobile.list.lot_locations') }}']").prop(
                                    'hidden', true);
                                $("button[name='{{ __('mobile.list.locations') }}']").prop(
                                    'hidden', true);
                                $("#lot_num").val(data['lot_num']);
                                $("#loc_num").val(data['loc_num']);

                            }
                            //

                            $("#qty_available").val(data['qty_available']);
                            $("#qty_available_conv").val(data['qty_available']);
                        }
                    });
                }
            }
        });
    }

    function umchange() {
        $("#u_m1").val($("#uom").val());
    };
</script>
@include('util.selection')
@include('components.prevent2submit')
@include('util.convert_alternade_barcode_to_item')
@endsection()
