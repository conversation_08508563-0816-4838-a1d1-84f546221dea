<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\ImportTemplate;
use Exception;

class ProcessImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $importId;
    protected $fileName;
    protected $objectKey;
    protected $userId;
    protected $templateName;
    protected $saveTemplate;

    public $tries = 3;
    public $timeout = 3600; // 1 hour

    public function __construct($importId, $fileName, $objectKey, $userId, $templateName = null, $saveTemplate = false)
    {
        $this->importId = $importId;
        $this->fileName = $fileName;
        $this->objectKey = $objectKey;
        $this->userId = $userId;
        $this->templateName = $templateName;
        $this->saveTemplate = $saveTemplate;
    }

    public function handle()
    {
        try {
            // Load the import data
            $importData = json_decode(Storage::disk('local')->get($this->fileName), true);
            $rows = $importData['data'] ?? [];
            $mapping = $importData['mapping'] ?? [];
            
            // Initialize status
            $total = count($rows);
            $success = 0;
            $errors = [];
            
            $this->updateStatus([
                'status' => 'processing',
                'total' => $total,
                'processed' => 0,
                'success' => 0,
                'failed' => 0,
                'errors' => []
            ]);
            
            // Process each row
            foreach ($rows as $index => $row) {
                try {
                    $mappedData = $this->mapRowData($row, $mapping);
                    
                    // Validate the mapped data
                    $validator = $this->validateRow($mappedData);
                    
                    if ($validator->fails()) {
                        throw new Exception($validator->errors()->first());
                    }
                    
                    // Save the record
                    $this->saveRecord($this->objectKey, $mappedData);
                    $success++;
                    
                    $this->updateStatus([
                        'processed' => $index + 1,
                        'success' => $success,
                        'failed' => $index + 1 - $success
                    ]);
                    
                } catch (Exception $e) {
                    $errors[] = [
                        'row' => $index + 1,
                        'error' => $e->getMessage()
                    ];
                    
                    $this->updateStatus([
                        'processed' => $index + 1,
                        'failed' => count($errors),
                        'errors' => array_slice($errors, -100) // Keep last 100 errors
                    ]);
                }
            }
            
            // Save template if requested
            if ($this->saveTemplate && $this->templateName) {
                $this->saveImportTemplate($mapping);
            }
            
            // Clean up
            Storage::disk('local')->delete($this->fileName);
            
            // Final status update
            $this->updateStatus([
                'status' => 'completed',
                'processed' => $total,
                'success' => $success,
                'failed' => $total - $success,
                'completed_at' => now()->toDateTimeString()
            ]);
            
        } catch (Exception $e) {
            Log::error("Import {$this->importId} failed: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->updateStatus([
                'status' => 'failed',
                'error' => $e->getMessage(),
                'failed_at' => now()->toDateTimeString()
            ]);
            
            // Re-throw to allow for job retries
            throw $e;
        }
    }
    
    protected function mapRowData($row, $mapping)
    {
        $mappedData = [];
        
        foreach ($mapping as $targetField => $sourceField) {
            if (isset($row[$sourceField])) {
                $mappedData[$targetField] = $row[$sourceField];
            } else {
                $mappedData[$targetField] = null;
            }
        }
        
        return $mappedData;
    }
    
    protected function validateRow($data)
    {
        // Get validation rules based on object type
        $rules = $this->getValidationRules($this->objectKey);
        
        return validator($data, $rules);
    }
    
    protected function getValidationRules($objectKey)
    {
        // Define validation rules for each object type
        $rules = [
            'MAINTENANCE_ITEM' => [
                'item_num' => 'required|string|max:50',
                'item_desc' => 'required|string|max:255',
                'uom' => 'nullable|string|max:10',
            ],
            'MAINTENANCE_VENDOR' => [
                'vendor_num' => 'required|string|max:50',
                'vendor_name' => 'required|string|max:255',
            ],
            // Add more validation rules for other object types
        ];
        
        return $rules[$objectKey] ?? [];
    }
    
    protected function saveRecord($objectKey, $data)
    {
        // Determine the model class based on the object key
        $modelClass = $this->getModelClass($objectKey);
        
        if (!$modelClass) {
            throw new Exception("No model found for object type: {$objectKey}");
        }
        
        // Check for existing record (update if exists, create if not)
        $uniqueKey = $this->getUniqueKey($objectKey);
        
        if (isset($data[$uniqueKey])) {
            $model = $modelClass::updateOrCreate(
                [$uniqueKey => $data[$uniqueKey]],
                $data
            );
        } else {
            $model = $modelClass::create($data);
        }
        
        return $model;
    }
    
    protected function getModelClass($objectKey)
    {
        $modelMap = [
            'MAINTENANCE_ITEM' => \App\Models\Item::class,
            'MAINTENANCE_VENDOR' => \App\Models\Vendor::class,
            // Add more model mappings as needed
        ];
        
        return $modelMap[$objectKey] ?? null;
    }
    
    protected function getUniqueKey($objectKey)
    {
        $uniqueKeys = [
            'MAINTENANCE_ITEM' => 'item_num',
            'MAINTENANCE_VENDOR' => 'vendor_num',
            // Add more unique keys as needed
        ];
        
        return $uniqueKeys[$objectKey] ?? 'id';
    }
    
    protected function saveImportTemplate($mapping)
    {
        ImportTemplate::updateOrCreate(
            [
                'name' => $this->templateName,
                'object_type' => $this->objectKey,
                'created_by' => $this->userId
            ],
            [
                'mapping' => $mapping,
                'is_active' => true
            ]
        );
    }
    
    protected function updateStatus($updates)
    {
        $status = Cache::get("import_status_{$this->importId}", [
            'status' => 'pending',
            'processed' => 0,
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'errors' => [],
            'started_at' => now()->toDateTimeString(),
        ]);
        
        $status = array_merge($status, $updates);
        
        // Store status in cache for 24 hours
        Cache::put("import_status_{$this->importId}", $status, now()->addDay());
    }
    
    public function failed($exception = null)
    {
        $this->updateStatus([
            'status' => 'failed',
            'error' => $exception ? $exception->getMessage() : 'Unknown error',
            'failed_at' => now()->toDateTimeString()
        ]);
    }
}
