<?php

namespace App\Http\Controllers\MasterMaintenance;

use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\View\TparmView;

use App\Services\SapCallService;

use App;
use App\Bom;
use App\BomItem;
use App\BomRoute;
use App\BomMatl;
use App\BomSample;
use App\CustomField;
use App\JobMatl;
use App\JobRoute;
use App\JobFinishedParent;
use App\MatlTrans;
use App\JobTrans;
use App\MachineTrans;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use PDF;
use Gate;
use Alert;
use App\Allocation;
use Excel;
use App\Job;
use App\Item;
use App\ItemWarehouse;
use App\UomConv;
use Throwable;
use App\events;
use App\Customer;
use Carbon\Carbon;
use App\WorkCenter;
use App\SiteSetting;
use App\ImportExportLog;
use App\Imports\JobImport;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\ImportService;
use App\DataTables\Master\JobDataTable;
use App\DocNote;
use App\Services\GeneralService;
use App\Http\Controllers\Controller;
use App\Http\Controllers\ImportController;
use App\Services\OverrideQtyService;
use App\Http\Requests\JobOrderRequest;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\FileImportRequest;
use App\Jobs\ImportBackground;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Validation\ValidationException;
use Yajra\DataTables\Facades\DataTables;

use App\OrderNumberDefinition;
use App\Services\OrderNumberDefinitionService;
use App\helpers;

use Illuminate\Support\Facades\Event;
use App\Events\JobEvent;
use App\Services\ExportService;
use Illuminate\Support\Str;
use App\Services\PreassignLotsService;


use Illuminate\Support\Facades\Lang;
use App\Traits\GetAddLineButton;

class JobController extends Controller
{
    use \App\Traits\Importable;

    private $name = 'MAINTENANCE_JOB'; // 'Job';
    private $table = 'jobs';

    use \App\Traits\HasCustomFields;
    use GetAddLineButton;

    public function __construct()
    {
        $this->middleware('auth', ['except' => ['exist', 'jobexist', 'getQtyReleased', 'scopeActive']]);
        $this->middleware('can:hasJobMaintenance', ['except' => ['exist', 'jobexist', 'getQtyReleased', 'scopeActive']]);
    }

    public function import(FileImportRequest $request)
    {
        // $file = Storage::put('temp_file',$request->file);
        // ImportBackground::dispatch('JobImport',$file,$request->file->getClientOriginalName(),$this->name,auth()->user(),'Job Orders');
        // return back()->with('successmsg',__('admin.message.import_submitted'));
        return ImportController::pushData('JobImport', $request, $this->name, auth()->user(), 'Job Orders');
    }

    public function export(JobDataTable $jobDataTable)
    {
        $export = new ExportService($jobDataTable, $this->name, "Job Orders");
        return $export->handleExport();
    }

    public function index(JobDataTable $jobDataTable)
    {
        if (!Gate::allows('hasJobMaintenance')) {
            return view('errors.404v2')->with('page', 'error');
        }
        //return view('MasterMaintenance.job.index');

        $tparm = new TparmView;
        $sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        return $jobDataTable->render('MasterMaintenance.job.table', compact('sap_integration'));
    }


    public function create(Request $request)
    {
        $site_id = auth()->user()->site_id;
        $order_type = 'JO';
        $co_num_generate = OrderNumberDefinitionService::getGenerateCode($order_type, $site_id);


        $prefix = OrderNumberDefinition::select('prefix_define')->where('status', 'Y')->where('order_type', 'JO')->first();

        if ($prefix == NULL) {
            $prefix['prefix_define'] = "";
        } else {
            @$prefix = $prefix->toArray();
        }

        $referer = $request->referer;
        $bom = null;
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if ($request->bom) {
            $bom = Bom::find($request->bom);
            if (config('icapt.special_modules.enable_suffix')) {
                $bomItem = BomItem::where('bom_id', $bom->id)->first();
                $bomRoute = BomRoute::where('bom_item_id', $bomItem->id)->first();
            }
            $customFields = $this->getCustomFields();
            if (config('icapt.special_modules.enable_suffix')) {
                return view('MasterMaintenance.job-new.add')->with('customFields', $customFields)->with('bom', $bom)->with('bomRoute', $bomRoute)->with('JONum', $co_num_generate)->with('unit_quantity_format', $unit_quantity_format)->with('prefix_define', @$prefix['prefix_define']);
            } else {
                return view('MasterMaintenance.job.add')->with('customFields', $customFields)->with('bom', $bom)->with('JONum', $co_num_generate)->with('unit_quantity_format', $unit_quantity_format)->with('prefix_define', @$prefix['prefix_define']);
            }
        } else {
            $customFields = $this->getCustomFields();
            if (config('icapt.special_modules.enable_suffix')) {
                return view('MasterMaintenance.job-new.add')->with('customFields', $customFields)->with('bom', $bom)->with('JONum', $co_num_generate)->with('unit_quantity_format', $unit_quantity_format)->with('prefix_define', @$prefix['prefix_define']);
            } else {
                return view('MasterMaintenance.job.add')->with('customFields', $customFields)->with('bom', $bom)->with('JONum', $co_num_generate)->with('unit_quantity_format', $unit_quantity_format)->with('prefix_define', @$prefix['prefix_define']);
            }
        }
    }

    public function store(JobOrderRequest $request)
    {
        $request = validateSansentiveValue($request);
        $record = $request->validated();
        DB::beginTransaction();

        try {
            $suff1 = $request->suffix;
            if ($suff1 > 9999) {
                throw ValidationException::withMessages([__('error.admin.error_max_suffix')]);
            }


            //dd($request);

            // // Validate item warehouse
            // if (!ItemWarehouse::where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->exists()) {
            //     throw ValidationException::withMessages([__('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $request->whse_num, 'resource2' => __('admin.label.whse_num')])]);
            // }

            $record = $request->except('_token', 'prefix_define', 'action', 'finished_job', 'parent_job', 'job_item', 'cust_name', 'item_desc', 'qty_returned');
            // $strNote = Str::of( $record['notes'])->limit(200);
            //  $record['notes'] =   $strNote->value;

            // dd(config('icapt.special_modules.enable_suffix'));
            if ($request->bom) {
                if (config('icapt.special_modules.enable_suffix')) {

                    $job = Job::create($record);

                    $request_item_num = $request->item_num;
                    $bomItems = BomItem::with('bom_attachments')->where('bom_id', $request->bom)->get();

                    if (count($bomItems) > 0) {
                        foreach ($bomItems as $bomItem) {
                            // Add Job Route
                            // dd($bomItem->bom_content->job_matls);
                            $job_route = JobRoute::create([
                                'job_num' => $job->job_num,
                                'suffix' => $job->suffix,
                                'oper_num' => $bomItem->bom_content->oper_num,
                                'wc_num' => $bomItem->bom_content->wc_num,
                                'oper_status' => 1,
                                'machine_hours_per_piece' => $bomItem->bom_content->machine_hours_per_piece,
                                'labour_hours_per_piece' => $bomItem->bom_content->labour_hours_per_piece,
                                'notes' => $bomItem->bom_content->notes,
                            ]);

                            foreach ($bomItem->bom_content->job_matls as $content) {
                                $content_item_num = $content->item_num;

                                // Get total qty_released
                                $total_released = $request->qty_released * $content->qty_per;

                                // Add Doc
                                if (count($bomItem->bom_attachments) > 0) {
                                    foreach ($bomItem->bom_attachments as $bom_attachment) {
                                        DocNote::create([
                                            'type' => $bom_attachment->type,
                                            'ref_num' => $job_route->job_num,
                                            'ref_line' => $job_route->oper_num,
                                            'ref_type' => 'JobRoute',
                                            'doc_filename' => $bom_attachment->path,
                                            'file_ori_name' => $bom_attachment->name,
                                        ]);
                                    }
                                }
                            }
                            // Add Job Material
                            if ($bomItem->bom_content->job_matls) {
                                foreach ($bomItem->bom_content->job_matls as $matl) {
                                    if ($matl->item_num != null) {
                                        $item = Item::select('uom')->where('item_num', $matl->item_num)->first();
                                        JobMatl::create([
                                            'whse_num' => $job->whse_num,
                                            'sequence' => $matl->sequence,
                                            'job_num' => $job->job_num,
                                            'suffix' => $job->suffix,
                                            'oper_num' => $bomItem->bom_content->oper_num,
                                            'matl_item' => $matl->item_num,
                                            'matl_desc' => $matl->item_desc,
                                            'qty_per' => $matl->qty_per,
                                            'scrap_factor' => $matl->scrap_factor,
                                            'qty_required' => $job->qty_released * $matl->qty_per * (1 / (1 - $matl->scrap_factor)),
                                            'uom' => $matl->uom,
                                        ]);
                                    }
                                }
                            }

                            $finishedParentJob = JobFinishedParent::create([
                                'job_num' => $request->job_num,
                                'suffix' => $job->suffix,
                                'parent_job' => $request->job_num . ' - ' . $request->suffix,
                                'finished_job' => $request->job_num . ' - ' . $request->suffix,
                                'site_id' => auth()->user()->site_id,
                                'created_by' => auth()->user()->name,
                            ]);
                        }
                        $suff1++;

                        // Recursive of Bom Item
                        $checkChild = DB::select(DB::raw(
                            "with recursive cte (id, bom_route_id, uom, qty_per, item_num, sequence, scrap_factor, site_id, bom_item_flag, parent_id, created_by, modified_by, created_date, modified_date) as (
                                select     *
                                from       bom_matls
                                where      parent_id = '$request_item_num'  and bom_item_flag = 1
                                union
                                select     p.*
                                from       bom_matls p
                                inner join cte
                                        on p.parent_id = cte.item_num
                                        where p.bom_item_flag = '1'
                            )
                            select MAX(id) as id, MAX(bom_route_id) as bom_route_id, MAX(uom) as uom, MAX(qty_per) as qty_per,
                            item_num, MAX(sequence) as sequence, MAX(scrap_factor) as scrap_factor, MAX(site_id) as site_id,
                            MAX(bom_item_flag) as bom_item_flag, MAX(parent_id) as parent_id, MAX(created_by) as created_by, MAX(modified_by) as modified_by,
                            MAX(created_date) as created_date, MAX(modified_date) as modified_date
                            from cte where cte.bom_item_flag = 1
                            and site_id = '" . auth()->user()->site_id . "'
                            group by cte.item_num"
                        ));

                        foreach ($checkChild as $children) {

                            $getBoms = Bom::where('item_num', $children->item_num)->where('bom_status', 'A')->where('site_id', auth()->user()->site_id)->get();
                            foreach ($getBoms as $getBom) {
                                // Get qty_released from parents
                                $totalQtyRelease = 0;
                                // dd($request_item_num, $children->parent_id);
                                if ($children->parent_id == $request_item_num) {
                                    $getBomQtys = Bom::where('item_num', $request_item_num)->where('bom_status', 'A')->where('site_id', auth()->user()->site_id)->get();
                                    foreach ($getBomQtys as $getBomQty) {
                                        $eachQtyReleases = BomItem::where('bom_id', $getBomQty->id)->get();
                                        foreach ($eachQtyReleases as $eachQtyRelease) {
                                            $eachRoutes = BomRoute::where('bom_item_id', $eachQtyRelease->id)->get();
                                            foreach ($eachRoutes as $eachRoute) {
                                                $eachMatls = BomMatl::where('bom_route_id', $eachRoute->id)->get();
                                                foreach ($eachMatls as $eachMatl) {
                                                    if ($children->item_num == $eachMatl->item_num) {
                                                        $getQtyRelease = Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('site_id', auth()->user()->site_id)->first();
                                                        $totalQtyRelease = $totalQtyRelease + ($getQtyRelease->qty_released * $eachMatl->qty_per * (1 / (1 - $eachMatl->scrap_factor)));
                                                        $parentJob = $getQtyRelease->job_num . ' - ' . $getQtyRelease->suffix;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    $getBomQtys = Bom::where('item_num', $children->parent_id)->where('bom_status', 'A')->where('site_id', auth()->user()->site_id)->get();
                                    foreach ($getBomQtys as $getBomQty) {
                                        $eachQtyReleases = BomItem::where('bom_id', $getBomQty->id)->get();
                                        foreach ($eachQtyReleases as $eachQtyRelease) {
                                            $eachRoutes = BomRoute::where('bom_item_id', $eachQtyRelease->id)->get();
                                            foreach ($eachRoutes as $eachRoute) {
                                                $eachMatls = BomMatl::where('bom_route_id', $eachRoute->id)->get();
                                                foreach ($eachMatls as $eachMatl) {
                                                    if ($children->item_num == $eachMatl->item_num) {
                                                        $getQtyRelease = JobMatl::where('job_num', $request->job_num)->where('matl_item', $children->item_num)->where('sequence', $children->sequence)->where('site_id', auth()->user()->site_id)->first();
                                                        if ($getQtyRelease) {
                                                            $totalQtyRelease = $totalQtyRelease + ($getQtyRelease->qty_required * $eachMatl->qty_per * (1 / (1 - $eachMatl->scrap_factor)));
                                                            $parentJob = $getQtyRelease->job_num . ' - ' . $getQtyRelease->suffix;
                                                        } else {
                                                            $totalQtyRelease = 0;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                $finishedJob = $request->job_num . ' - ' . $request->suffix;



                                $suff3 = str_pad($suff1, 4, "0", STR_PAD_LEFT);
                                // Get Item_num UOM
                                $itemUom = Item::where('item_num', $getBom->item_num)->first();

                                // Add Jobs
                                // dd($children->qty_per, $totalQtyRelease);
                                $recursive_job = Job::create([
                                    'job_num' => $request->job_num,
                                    'suffix' => $suff3,
                                    'job_status' => 'O',
                                    'whse_num' => $record['whse_num'],
                                    'cust_num' => $record['cust_num'],
                                    'item_num' => $children->item_num,
                                    'qty_released' => $totalQtyRelease,
                                    // 'qty_released' => $children->qty_per,
                                    'uom' => $children->uom,
                                    'job_date' => $record['job_date'],
                                    'start_date_plan' => $record['start_date_plan'],
                                    'end_date_plan' => $record['end_date_plan'],
                                    'notes' => $record['notes'],
                                    'bom' => $children->bom_route_id,
                                ]);

                                $finishedParentJob = JobFinishedParent::create([
                                    'job_num' => $request->job_num,
                                    'suffix' => $suff3,
                                    'parent_job' => $parentJob,
                                    'finished_job' => $finishedJob,
                                    'site_id' => auth()->user()->site_id,
                                    'created_by' => auth()->user()->name,
                                ]);

                                $getBomItems = BomItem::where('bom_id', $getBom->id)->get();
                                foreach ($getBomItems as $getBomItem) {
                                    $getBomRoutes = BomRoute::where('bom_item_id', $getBomItem->id)->get();
                                    foreach ($getBomRoutes as $getBomRoute) {

                                        // Add Job Route
                                        $recursive_job_route = JobRoute::create([
                                            'job_num' => $recursive_job->job_num,
                                            'suffix' => $recursive_job->suffix,
                                            'oper_num' => $getBomRoute->oper_num,
                                            'wc_num' => $getBomRoute->wc_num,
                                            'oper_status' => 1,
                                            'machine_hours_per_piece' => $getBomRoute->machine_hours,
                                            'labour_hours_per_piece' => $getBomRoute->labour_hours,
                                            'notes' => $getBomRoute->notes,
                                            // 'qty_received' => $total_received;
                                        ]);

                                        // Add Doc
                                        if (count($bomItem->bom_attachments) > 0) {
                                            foreach ($bomItem->bom_attachments as $bom_attachment) {
                                                DocNote::create([
                                                    'type' => $bom_attachment->type,
                                                    'ref_num' => $recursive_job_route->job_num,
                                                    'ref_line' => $recursive_job_route->oper_num,
                                                    'ref_type' => 'JobRoute',
                                                    'doc_filename' => $bom_attachment->path,
                                                    'file_ori_name' => $bom_attachment->name,
                                                ]);
                                            }
                                        }

                                        $getBomMatls = BomMatl::where('bom_route_id', $getBomRoute->id)->get();
                                        foreach ($getBomMatls as $getBomMatl) {
                                            // Get Item Desc
                                            $getDesc = Item::where('item_num', $getBomMatl->item_num)->first();

                                            // Add Job Materials
                                            JobMatl::create([
                                                'whse_num' => $recursive_job->whse_num,
                                                'sequence' => $getBomMatl->sequence,
                                                'job_num' => $recursive_job->job_num,
                                                'suffix' => $recursive_job->suffix,
                                                'oper_num' => $recursive_job_route->oper_num,
                                                'matl_item' => $getBomMatl->item_num,
                                                'matl_desc' => $getDesc->item_desc,
                                                'qty_per' => $getBomMatl->qty_per,
                                                'scrap_factor' => $getBomMatl->scrap_factor,
                                                'qty_required' => $recursive_job->qty_released * $getBomMatl->qty_per * (1 / (1 - $getBomMatl->scrap_factor)),
                                                'uom' => $getBomMatl->uom,
                                            ]);
                                        }
                                    }
                                }
                                $suff1++;
                            }
                        }
                    }
                } else {
                    $job = Job::create($record);

                    $request_item_num = $request->item_num;
                    $bomItems = BomItem::with('bom_attachments')->where('bom_id', $request->bom)->get();
                    if (count($bomItems) > 0) {
                        foreach ($bomItems as $bomItem) {

                            // Add Job Route
                            $job_route = JobRoute::create([
                                'job_num' => $job->job_num,
                                'suffix' => '0000',
                                'oper_num' => $bomItem->bom_content->oper_num,
                                'wc_num' => $bomItem->bom_content->wc_num,
                                'oper_status' => 1,
                                'machine_hours_per_piece' => $bomItem->bom_content->machine_hours_per_piece,
                                'labour_hours_per_piece' => $bomItem->bom_content->labour_hours_per_piece,
                                'notes' => $bomItem->bom_content->notes,
                            ]);

                            $count = 0;
                            foreach ($bomItem->bom_content->job_matls as $content) {
                                // Get total qty_released
                                $total_released = $request->qty_released * $content->qty_per;

                                // Add Doc
                                if (count($bomItem->bom_attachments) > 0) {
                                    foreach ($bomItem->bom_attachments as $bom_attachment) {
                                        DocNote::create([
                                            'type' => $bom_attachment->type,
                                            'ref_num' => $job_route->job_num,
                                            'ref_line' => $job_route->oper_num,
                                            'ref_type' => 'JobRoute',
                                            'doc_filename' => $bom_attachment->path,
                                            'file_ori_name' => $bom_attachment->name,
                                        ]);
                                    }
                                }
                            }

                            // Add Job Material
                            if ($bomItem->bom_content->job_matls) {
                                foreach ($bomItem->bom_content->job_matls as $matl) {
                                    $item = Item::select('uom')->where('item_num', $matl->item_num)->first();
                                    JobMatl::create([
                                        'whse_num' => $job->whse_num,
                                        'sequence' => $matl->sequence,
                                        'job_num' => $job->job_num,
                                        'suffix' => '0000',
                                        'oper_num' => $bomItem->bom_content->oper_num,
                                        'matl_item' => $matl->item_num,
                                        'matl_desc' => $matl->item_desc,
                                        'qty_per' => $matl->qty_per,
                                        'scrap_factor' => $matl->scrap_factor,
                                        'qty_required' => $job->qty_released * $matl->qty_per * (1 / (1 - $matl->scrap_factor)),
                                        'uom' => $item->uom,
                                    ]);
                                }
                            }
                        }
                    }
                }
            } else {
                // Check Job Num + Suffix is exist

                if (config('icapt.special_modules.enable_suffix')) {
                    $checkJob = Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->first();
                    if ($checkJob) {
                        throw ValidationException::withMessages([__('error.admin.exists2', ['resource' => __('admin.label.job_num'), 'name' => $request->job_num . '-' . $request->suffix])]);
                    } else {
                        $job = Job::create($record);
                    }
                } else {
                    $checkJob = Job::where('job_num', $request->job_num)->first();
                    if ($checkJob) {
                        throw ValidationException::withMessages([__('error.admin.exists2', ['resource' => __('admin.label.job_num'), 'name' => $request->job_num])]);
                    } else {
                        $record = $request->except('suffix');
                        $job = Job::create($record);
                    }
                }
            }

            // validate and auto create item whse
            $autoCreateItemWhse = GeneralService::autoCreateItemWhse($request, ['whse_num']);
            if ($autoCreateItemWhse !== "true") {
                throw ValidationException::withMessages([$autoCreateItemWhse]);
            }

        } catch (Throwable $e) {
            DB::rollback();

            return $request->bom ? redirect()->route('addJob', ['bom' => $request->bom])->withInput()->withErrors($e->getMessage())
                                 : redirect()->route('addJob')->withInput()->withErrors($e->getMessage());
        }

        DB::commit();

        $site_id = auth()->user()->site_id;
        $prefix = $request->prefix_define;
        $conum  = $request->job_num;
        $order_type = 'JO';
        $co_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type, $site_id, $prefix, $conum);

        if (config('icapt.special_modules.enable_suffix')) {
            $newJobCreated = Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('site_id', $site_id)->first();
        } else {
            $newJobCreated = Job::where('job_num', $request->job_num)->where('site_id', $site_id)->first();
        }

        return redirect()->route('viewJob', ['job' => $newJobCreated->id])
            ->with('successmsg', __(
                'success.addedv2',
                ['resource' => __('Job Order'), 'name' => $request->job_num]
            ));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show(Job $job)
    {
       // dd($job->suffix);
        $tparm = new TparmView;
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting_quantity');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if (request()->ajax()) {
            if (config('icapt.special_modules.enable_suffix')) {
                $job = JobRoute::where('job_num', $job->job_num)->where('suffix', $job->suffix)->orderby('job_num', 'asc')->orderByRaw('cast(oper_num as unsigned) ASC')->get();
            } else {
                $job = JobRoute::where('job_num', $job->job_num)->orderby('job_num', 'asc')->orderByRaw('cast(oper_num as unsigned) ASC')->get();
            }
            $result = DataTables::of($job)->make(true);
            return $result;
        }

        $cust_name = Customer::select('cust_name')->where('cust_num', $job->cust_num)->where('site_id', auth()->user()->site_id)->value('cust_name');

        $customFields = $this->getCustomFields();

        $finished_parent = JobFinishedParent::where('job_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->first();
        if ($finished_parent) {
            $parent_job = $finished_parent->parent_job;
            $finished_job = $finished_parent->finished_job;
        } else {
            $parent_job = $job->job_num . ' - ' . $job->suffix;
            // $parent_job="";

            $finished_job = $job->job_num . ' - ' . $job->suffix;
            // $finished_job="";
        }


        $tparmSAP = new TparmView;
        $sap_trans_order_integration = $tparmSAP->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly = "";
        if ($sap_trans_order_integration == 1 &&  auth()->user()->name != "sa") {
            $sap_readonly = "disabled";
        }

         if($job->job_status == 'C')
        {
             $status_disabled  = "disabled";
            $tooltips = "tooltip";
            $title = Lang::get('admin.tooltip.showaddline');
            $tooltipsdeletemsg = __('admin.tooltip.deleteaddline');
            $tooltipseditmsg = __('admin.tooltip.editaddline');
            $isDisabled = "disabled";

        }
        else{
             $status_disabled  = "";
            $tooltips = null;
            $title = null;
            $tooltipsdeletemsg =null;
            $tooltipseditmsg ="";
            $isDisabled = "";
        }
        $buttonName = Lang::get('admin.button.add_job_route');


        //add comment
        if (config('icapt.special_modules.enable_suffix')) {
          $route =  route('addJobRouteNew', ['job_num' => base64_encode($job->job_num), 'suffix' => base64_encode($job->suffix), base64_encode($job->whse_num)]);
          $AddNewLineButton = $this->getAddLineButton($sap_readonly, $route,$status_disabled,$tooltips,$buttonName,$title);



            return view('MasterMaintenance.job-new.view', compact('AddNewLineButton'))->with('customFields', $customFields)
                ->with('sap_readonly', $sap_readonly)
                ->with('cust_name', $cust_name)
                ->with('details', $job)
                ->with('parent_job', $parent_job)
                ->with('finished_job', $finished_job)
                ->with('isDisabled',$isDisabled)
                ->with('tooltipsdeletemsg',$tooltipsdeletemsg)
                ->with('tooltipseditmsg',$tooltipseditmsg)
                ->with('status_disabled',$status_disabled)
                ->with('tooltips',$tooltips)
                ->with('status_disabled',$status_disabled)
                ->with('quantity_per_format', $quantity_per_format)
                ->with('unit_quantity_format', $unit_quantity_format);
        } else {

   $route =  route('addJobRoute', ['job_num' => base64_encode($job->job_num), $job->whse_num]);
          $AddNewLineButton = $this->getAddLineButton($sap_readonly, $route,$status_disabled,$tooltips,$buttonName,$title);

            return view('MasterMaintenance.job.view',compact('AddNewLineButton'))->with('customFields', $customFields)
                ->with('sap_readonly', $sap_readonly)
                ->with('cust_name', $cust_name)
                ->with('details', $job)
                ->with('parent_job', $parent_job)
                ->with('finished_job', $finished_job)
                ->with('isDisabled',$isDisabled)
                ->with('tooltipsdeletemsg',$tooltipsdeletemsg)
                ->with('tooltipseditmsg',$tooltipseditmsg)
                ->with('status_disabled',$status_disabled)
                ->with('tooltips',$tooltips)
                ->with('status_disabled',$status_disabled)
                ->with('quantity_per_format', $quantity_per_format)
                ->with('unit_quantity_format', $unit_quantity_format);
        }
    }

    public function  viewconvertJob($job,$suffix)
    {
        // $job = base64_decode($job);
        // $jobdetails = Job::where('job_num', $job)->where('site_id', auth()->user()->site_id)->first();
        // if($jobdetails){
        //     return redirect()->route('viewJob', ['job' => $jobdetails->id]);
        // }
        // else{
        //     Alert.error('Error', 'Job does not exist.');
        //     return redirect()->back();
        // }

        $job = base64_decode($job);
       //$suffix = base64_decode($suffix);
        $jobdetails = Job::where('job_num', $job)->where('site_id', auth()->user()->site_id)->first();
        //dd($lpn_num);
        if(@$jobdetails == null)
        {
            Alert::error('Error', 'Job does not exist.');
            return redirect()->back();
        }

        if(@$suffix=="null")
        {
            //dd($lpn_line,'aaaaa');
       return redirect()->route('viewJob', ['job' => $jobdetails->id]);
        }
        else{
$jobdetails = Job::where('job_num', $job)->where('suffix', $suffix)->where('site_id', auth()->user()->site_id)->first();
               // dd($jobdetails,$suffix,$job);
return redirect()->route('viewJobNew', ['job' => $jobdetails->id,'suffix'=>$suffix]);



        }















    }

    public function shownew(Job $job, $suffix)
    {
        //dd($job->suffix);
        $tparm = new TparmView;
        $quantity_per_format = $tparm->getTparmValue('System', 'decimal_setting_quantity');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        if (request()->ajax()) {
            if (config('icapt.special_modules.enable_suffix')) {
                $job = JobRoute::where('job_num', $job->job_num)->where('suffix', $suffix)->orderby('job_num', 'asc')->orderby('oper_num', 'asc')->get();
            } else {
                $job = JobRoute::where('job_num', $job->job_num)->orderby('job_num', 'asc')->orderby('oper_num', 'asc')->get();
            }
            $result = DataTables::of($job)->make(true);
            return $result;
        }

        $cust_name = Customer::select('cust_name')->where('cust_num', $job->cust_num)->where('site_id', auth()->user()->site_id)->value('cust_name');

        $customFields = $this->getCustomFields();

        $finished_parent = JobFinishedParent::where('job_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->first();
        if ($finished_parent) {
            $parent_job = $finished_parent->parent_job;
            $finished_job = $finished_parent->finished_job;
        } else {
            $parent_job = $job->job_num . ' - ' . $job->suffix;
            $finished_job = $job->job_num . ' - ' . $job->suffix;
        }
        $tparmSAP = new TparmView;
        $sap_trans_order_integration = $tparmSAP->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly = "";
        if ($sap_trans_order_integration == 1 &&  auth()->user()->name != "sa") {
            $sap_readonly = "disabled";
        }


          if($job->job_status == 'C')
        {
            $status_show_add_line = "disabled";
            $tooltips = "tooltip";
           $title = Lang::get('admin.tooltip.showaddline');

        }
        else{
            $status_show_add_line = "";
             $tooltips = null;
             $title = null;
        }
        $buttonName = Lang::get('admin.button.add_job_route');






        if (config('icapt.special_modules.enable_suffix')) {

          $route =  route('addJobRouteNew', ['job_num' => base64_encode($job->job_num), 'suffix' => base64_encode($job->suffix), base64_encode($job->whse_num)]);
          $AddNewLineButton = $this->getAddLineButton($sap_readonly, $route,$status_show_add_line,$tooltips,$buttonName,$title);



            return view('MasterMaintenance.job-new.view',compact('AddNewLineButton'))->with('customFields', $customFields)
                ->with('cust_name', $cust_name)
                ->with('details', $job)
                ->with('parent_job', $parent_job)
                ->with('finished_job', $finished_job)
                ->with('quantity_per_format', $quantity_per_format)
                ->with('sap_readonly', $sap_readonly)
                ->with('unit_quantity_format', $unit_quantity_format);
        } else {

   $route =  route('addJobRoute', ['job_num' => base64_encode($job->job_num), $job->whse_num]);
          $AddNewLineButton = $this->getAddLineButton($sap_readonly, $route,$status_show_add_line,$tooltips,$buttonName,$title);

            return view('MasterMaintenance.job.view',compact('AddNewLineButton'))->with('customFields', $customFields)
                ->with('cust_name', $cust_name)
                ->with('details', $job)
                ->with('parent_job', $parent_job)
                ->with('finished_job', $finished_job)
                ->with('quantity_per_format', $quantity_per_format)
                ->with('sap_readonly', $sap_readonly)
                ->with('unit_quantity_format', $unit_quantity_format);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit(Job $job)
    {

        if (Gate::allows('canOverrideQtyField') && Gate::allows('hasJobMaintenance')) {
            $canoverrideqty = true;
        } else {
            $canoverrideqty = false;
        }

        $cust_name = Customer::select('cust_name')->where('cust_num', $job->cust_num)->where('site_id', auth()->user()->site_id)->value('cust_name');

        $siteSettings = SiteSetting::first();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $customFields = $this->getCustomFields();

        $finished_parent = JobFinishedParent::where('job_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->first();
        if ($finished_parent) {
            $parent_job = $finished_parent->parent_job;
            $finished_job = $finished_parent->finished_job;
        } else {
            $parent_job = $job->job_num . ' - ' . $job->suffix;
            $finished_job = $job->job_num . ' - ' . $job->suffix;
        }
        $allow_update_qty_release = 1;
        // Checking Job Trans and Matlran
        $checkJobTrans = JobTrans::where('job_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->count();
        $checkMaltTrans =  MatlTrans::where('ref_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->count();
        if ($job->job_status == "O" && $checkJobTrans == 0 && $checkMaltTrans == 0) {
            $allow_update_qty_release = 0;
        } else {
            $allow_update_qty_release = 1;
        }

        //dd($checkJobTrans,$checkMaltTrans,$job->job_status);

        if ('icapt.special_modules.enable_suffix') {
            return view('MasterMaintenance.job-new.edit')->with('customFields', $customFields)
                ->with('details', $job)
                ->with('parent_job', $parent_job)
                ->with('finished_job', $finished_job)
                ->with('cust_name', $cust_name)
                ->with('canoverrideqty', $canoverrideqty)
                ->with('allow_update_qty_release', $allow_update_qty_release)
                ->with('unit_quantity_format', $unit_quantity_format);
        } else {
            return view('MasterMaintenance.job.edit')->with('customFields', $customFields)
                ->with('details', $job)
                ->with('parent_job', $parent_job)
                ->with('finished_job', $finished_job)
                ->with('cust_name', $cust_name)
                ->with('canoverrideqty', $canoverrideqty)
                ->with('allow_update_qty_release', $allow_update_qty_release)
                ->with('unit_quantity_format', $unit_quantity_format);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param  int  $id
     * @return Response
     */
    public function update(JobOrderRequest $request, $id)
    {
        $job = Job::where('id', $id)->first();



        if (!$job) {
            return redirect(route('Job'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.job_num'), 'resource1' => __('admin.button.update'), 'resource2' => $id]));
        }

        // dd($request);

        // if($request->notes!=NULL)
        // {
        //     $strNote = Str::of( $request->notes)->limit(2);
        //     $request->merge([
        //         'notes' =>   $strNote->value,

        //     ]);
        // }

        // // Validate item warehouse
        // if (!ItemWarehouse::where('whse_num', $request->whse_num)->where('item_num', $request->item_num)->exists()) {
        //     throw ValidationException::withMessages([__('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $request->whse_num, 'resource2' => __('admin.label.whse_num')])]);
        // }

        // Event::dispatch(new JobEvent($request,'update'));
        if ($request->job_status == "C") {
            // Throw error if data is in Allocation and allocation's status is Open / Generated
            if (Allocation::where('ref_num', $request->job_num)->whereIn('status', ['Open', 'Generated'])->where('order_type', 'Job Order')->exists()) {
                throw ValidationException::withMessages([__('error.admin.error_exists_in_with_status', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.label.allocation'), 'resource3' => 'Open or Generated'])]);
            }
        }

        $checkJobTrans = JobTrans::where('job_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->count();
        $checkMaltTrans =  MatlTrans::where('ref_num', $job->job_num)->where('suffix', $job->suffix)->where('site_id', auth()->user()->site_id)->count();

        if ($request->job_status != 'O' && ($checkJobTrans != 0 || $checkMaltTrans != 0) && ($request->qty_released < $job->qty_released)) {

            // Throw error if data is in Allocation and allocation's status is Open / Generated
            throw ValidationException::withMessages([__('error.admin.qty_released_min', ['resource' => $job->qty_released])]);
        }
        // Check the job order in Material Transaction, Job Transaction and Machine Transaction
        if ($request->job_status == "O") {
            if (config('icapt.special_modules.enable_suffix')) {
                //Throw error if the Job Num + Suffix is exist
                if (MatlTrans::where('ref_num', $request->job_num)->where('suffix', $request->suffix)->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.title.mat_tran')])]);
                }

                if (JobTrans::where('job_num', $request->job_num)->where('suffix', $request->suffix)->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.title.job_trans')])]);
                }

                if (MachineTrans::where('job_num', $request->job_num)->where('suffix', $request->suffix)->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.title.mac_tran')])]);
                }
            } else {
                //Throw error if the Job Num is exist
                if (MatlTrans::where('ref_num', $request->job_num)->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in_with_status', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.label.allocation'), 'resource3' => 'Open or Generated'])]);
                }

                if (JobTrans::where('job_num', $request->job_num)->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in_with_status', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.label.allocation'), 'resource3' => 'Open or Generated'])]);
                }

                if (MachineTrans::where('job_num', $request->job_num)->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in_with_status', ['resource1' => __('admin.label.job_num'), 'resource2' => __('admin.label.allocation'), 'resource3' => 'Open or Generated'])]);
                }
            }
        }


        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');


        $record = $request->except(
            '_token',
            '_method',
            'prefix_define',
            'action',
            'finished_job',
            'parent_job',
            'job_item',
            'cust_name',
            'item_desc',
            'qty_returned',
            'suffix',
            'job_num',
            'whse_num',
            'item_num',
            'qty_completed',
            'qty_scrapped',
            'uom'
        );

        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();

        $request['start_date_plan'] = $record['start_date_plan'] = Carbon::createFromFormat($site->date_format, $record['start_date_plan'])->format('Y-m-d H:i:s');
        $request['job_date'] = $record['job_date'] = Carbon::createFromFormat($site->date_format, $record['job_date'])->format('Y-m-d H:i:s');
        if (isset($record['end_date_plan'])) {
            $request['end_date_plan'] = $record['end_date_plan'] = Carbon::createFromFormat($site->date_format, $record['end_date_plan'])->format('Y-m-d H:i:s');
        }

        // Keep previous Job Order Status before update.
        $prev_job_status = $job->job_status;
        $prev_qty_released = $job->qty_released;

        $jobDetails = $job->where('job_num', $request->job_num)->where('suffix', $request->suffix)->first();
        if (!$jobDetails) {
            return redirect(route('Job'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.job_order'), 'resource1' => __('admin.button.update'), 'resource2' => "$request->job_num"]));
        }
        // dd($jobDetails);
        if ($prev_job_status == 'O') {
            $job->where('job_num', $job->job_num)->where('suffix', $job->suffix)->update($record); // Validation at JobObserver.
        } else if ($prev_job_status == 'C') {
            // only update job_status and custom fields
            $record = $request->except(
                '_token',
                '_method',
                'prefix_define',
                'action',
                'finished_job',
                'parent_job',
                'job_item',
                'cust_name',
                'item_desc',
                'qty_returned',
                'suffix',
                'job_num',
                'whse_num',
                'item_num',
                'qty_completed',
                'qty_scrapped',
                'job_date',
                'cust_num',
                'uom',
                'start_date_plan',
                'qty_released',
                'end_date_plan'
            );

            $job->where('job_num', $job->job_num)->where('suffix', $job->suffix)->update($record); // Validation at JobObserver.
        } else if ($prev_job_status == 'R') {
            // only update job_status, qty_released, job_date, start_date_plan,  custom fields
            $record = $request->except(
                '_token',
                '_method',
                'prefix_define',
                'action',
                'finished_job',
                'parent_job',
                'job_item',
                'cust_name',
                'item_desc',
                'qty_returned',
                'suffix',
                'job_num',
                'whse_num',
                'item_num',
                'qty_completed',
                'qty_scrapped',
                'cust_num',
                'uom'
            );

            $job->where('job_num', $job->job_num)->where('suffix', $job->suffix)->update($record); // Validation at JobObserver.
        }

        $getJobRoute = JobRoute::where('job_num', $job->job_num)->where('suffix', $job->suffix)->first();
        // dd($getJobRoute);
        //Update Job Routes
        if ($getJobRoute != null && $request->job_status == "R") {
            $getJobRoute->update(['qty_received' => $request->qty_released]); // Validation at JobObserver.
        }
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $tparameter = new TparmView;
        $number_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        $getJobMatls = JobMatl::where('job_num', $job->job_num)->where('suffix', $job->suffix)->get();

        //dd($getJobMatls);
        if ($sap_trans_order_integration == 0 && $prev_job_status != 'C') {
            foreach ($getJobMatls as $getJobMatl) {
                $qtyPer = $getJobMatl->qty_per;
                //$qtyRequired = $request->qty_released * $qtyPer;
                $qtyRequired = getQtyRequiredJob($request->qty_released, $qtyPer, $getJobMatl->scrap_factor, $number_format);
                JobMatl::where('id', $getJobMatl->id)->update(['qty_required' => $qtyRequired]); // Validation at JobObserver.
            }
        }

        // Record status change to value change log
        if ($prev_job_status != $request->job_status) {
            OverrideQtyService::newOverRideHistorySuffix(
                __('admin.label.job_order'),
                'Status',
                $prev_job_status,
                $request->job_status,
                $job->job_num,
                $job->suffix,
                null,
                null
            );
        }

        if ($prev_qty_released != $request->qty_released && $prev_job_status != 'C') {
            OverrideQtyService::newOverRideHistorySuffix(
                __('admin.label.job_order'),
                'Qty Released',
                $prev_qty_released,
                $request->qty_released,
                $job->job_num,
                $job->suffix,
                null,
                null
            );
        }

        // SAP Intergration #
        if (config('icapt.enable_jobclose_sap') == TRUE) {
            if ($request->job_status == 'C' && $sap_trans_order_integration == 1) {
                $docEntry = explode("|", $request->job_num);
                $id = (int)$docEntry[0];
                //$res = SapCallService::putCloseJobOrder($id);

                // if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                //     // Later Change to read from Matltrans
                //     $result = SapCallService::putCloseJobOrder($id);
                // } else {
                //     $result = SapCallService::putCloseJobOrder($id);
                // }


                 $type = "JO";
            $arrConfigType   = getPostNotificationToSAP($type);
            $U_DocType       = $arrConfigType['U_DocType'];
            $post_from       = $arrConfigType['post_from'];
            $sap_service     = $arrConfigType['sap_service'];
            $process_name    = $arrConfigType['process_name'];
            $service_name    = $arrConfigType['service_name'];            
            
            $arrData = array(
                "U_DocType" => $U_DocType,
                "U_DocEntry" => $job->erp_ID ?? null,
                "U_DocNum" => $job->job_num ?? null ,
                "U_ItemCode" => @$job->item_num ?? null,
                "U_LineNum" =>@$job->suffix ?? null,
                "U_Qty" => @$job->qty_completed ?? null,
                "U_WhsCode" => @$job->whse_num ?? null,
                "U_ToWhsCode" => @$job->to_whse ?? null,
                "U_ManualClosed" =>'Y',
                "id" => @$job->id ?? null,
                "process_name" => $process_name,
                "post_from" => $post_from,
                "service_name" => $service_name
             );

             //dd($job,$id,$arrData);
             
             // 1 = Send Close API ; 0= Just Send Notification
             $result = SapCallService::postNotificationAndCloseAction($arrData,1);








                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }
        }

        if (!$request->preassign_lots) {
            PreassignLotsService::deleteExistingLots('job', $job->job_num, $job->suffix, $job->item_num, $job->site_id);
        }



        return redirect()->route('Job')
            ->with('successmsg', __(
                'success.updated',
                ['resource' => __('Job Order'), 'name' => $request->job_num]
            ));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($job_num)
    {
        $job = new Job();
        $details = $job->where('job_num', $job_num)->delete();  // Validation at JobObserver.

        return $this->index();
    }

    public function jobapi(Job $job)
    {
        //get all
        $job = $job->orderby('job_date', 'desc')->get();
        ini_set('memory_limit', '-1');
        set_time_limit(0);
        return json_encode($job);
    }

    public function ap_delete(Request $request)
    {
        $myString = $request->id;
        $myArray = explode(',', $myString);
        $jobDetails = Job::whereIn('id', $myArray)->get();
        // Event::dispatch(new JobEvent($jobDetails,'delete'));
        Job::destroy($myArray);

        //if no row selected or request is null
        if ($myString == "null" || $myString == "") {
            return redirect(route('Job'))->with('errormsg', __('error.admin.selectone'));
        }

        //if >1 selected and successful
        else {
            return redirect(route('Job'))->with('successmsg', __('success.deleted', ['resource' => __('admin.label.job_num')]));
        }
    }

    public function validateJobOrder(Request $request)
    {
        $request->except('_token');

        $result = Job::where('job_num', $request->job_num);

        // for job with suffix
        if (isset($request->suffix)) {
            if ($request->suffix)
                $result = $result->where('suffix', $request->suffix);
        }

        $result = $result->first();
        return $result ? "false" : "true"; // for add form, cannot use same job
    }

    public function existSuffix($id, $suffix = "")
    {
        $data = Job::where('job_num', $id)->where('suffix', $suffix)->first();

        if ($data != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function exist($id = "")
    {
        $data = Job::where('job_num', $id)->first();

        if ($data != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }
    public function jobdetail($job_num, $suffix = "")
    {
        $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($job_num)));
        $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));

        $data = Job::where('job_num', $job_num)->where('suffix', $whse_num)->first();

        return response()->json(['job' => $data]);
    }
    public function jobexist($job_num, $whse_num = "")
    {
        $job_num = utf8_encode(htmlspecialchars_decode(base64_decode($job_num)));
        $whse_num = utf8_encode(htmlspecialchars_decode(base64_decode($whse_num)));

        $data = Job::where('job_num', $job_num)->where('whse_num', $whse_num)->first();

        if ($data != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function getQtyReleased(Request $request)
    {
        $qty_released = Job::where('job_num', $request->job_num)->value('qty_released');
        return $qty_released;
    }

    public function print($suffix, $job_num)
    {

        $tparm = new TparmView;
        $issue_by_item_base_uom = $tparm->getTparmValue('JobMaterialIssue', 'issue_by_item_base_uom');

        $job_num = base64_decode($job_num);
        // Site
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        if ($site->logo != "") {
            $url = $site->getFileFullPath();
            $image = file_get_contents($url);
            $site->logoazure = "data:image/png+jpeg+jpg;base64," . base64_encode($image);
        } else
            $site->logoazure = "";

        if ($site->company_info) {
            $sitedecode = json_decode($site->company_info);
            $site->company_name = strtoupper($sitedecode->company_name);
        }

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        // Job
        $data = Job::where('job_num', $job_num)->where('suffix', $suffix)->with('job_routes.workCenter')->with(['job_routes' => function ($query_jobroutes) {
            $query_jobroutes->orderByRaw('cast(oper_num as unsigned) ASC');
        }])->with(['job_matls' => function ($query) {
            $query->orderByRaw('cast(oper_num as unsigned) ASC');
        }])->with('item')->with('customer')->first();

        $now = Carbon::now()->toDateTimeString();
        $date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, auth()->user()->timezone, 'H:i:s');
        $index = 1;

        // assign each base uom for the item
        foreach ($data->job_matls as $matl) {
            $total_amount = 0.00;
            $getItem = Item::where('item_num', $matl->matl_item)->first();

            // Get Job qty required
            $getQtyRequired = Job::where('job_num', $matl->job_num)->where('suffix', $matl->suffix)->first();

            if ($matl->uom == $getItem->uom) {
                $matl['total_amount'] = $matl->qty_required;
                $matl['base_uom'] = $getItem->uom;
            } else {
                $getConv = UomConv::where('item_num', $matl->matl_item)->where('uom_to', $matl->uom)->where('conv_type', 'I')->first();
                if ($getConv) {
                    if ($getItem->issue_by_unit == 1 && $getItem->round_up == 1 && $matl->qty_per <= $getConv->conv_factor) {
                        $total_amount = (ceil($getQtyRequired->qty_released / ((int)($getConv->conv_factor / $matl->qty_per)))) / (1 - $matl->scrap_factor);
                    } else if ($getItem->issue_by_unit == 1 && $matl->qty_per <= $getConv->conv_factor) {
                        $total_amount = ($getQtyRequired->qty_released / ((int)($getConv->conv_factor / $matl->qty_per))) / (1 - $matl->scrap_factor);
                    } else if ($getItem->round_up == 1) {
                        $total_amount = (ceil($getQtyRequired->qty_released / ($getConv->conv_factor / $matl->qty_per))) / (1 - $matl->scrap_factor);
                    } else {
                        $total_amount = ($getQtyRequired->qty_released / ($getConv->conv_factor / $matl->qty_per)) / (1 - $matl->scrap_factor);
                    }
                } else {
                    $getConvGlob = UomConv::where('item_num', $matl->matl_item)->where('uom_to', $matl->uom)->where('conv_type', 'G')->first();
                    if ($getConvGlob) {
                        if ($getItem->issue_by_unit == 1 && $getItem->round_up == 1 && $matl->qty_per <= $getConvGlob->conv_factor) {
                            $total_amount = (ceil($getQtyRequired->qty_released / ((int)($getConvGlob->conv_factor / $matl->qty_per)))) / (1 - $matl->scrap_factor);
                        } else if ($getItem->issue_by_unit == 1 && $matl->qty_per <= $getConvGlob->conv_factor) {
                            $total_amount = ($getQtyRequired->qty_released / ((int)($getConvGlob->conv_factor / $matl->qty_per))) / (1 - $matl->scrap_factor);
                        } else if ($getItem->round_up == 1) {
                            $total_amount = (ceil($getQtyRequired->qty_released / ($getConvGlob->conv_factor / $matl->qty_per))) / (1 - $matl->scrap_factor);
                        } else {
                            $total_amount = ($getQtyRequired->qty_released / ($getConvGlob->conv_factor / $matl->qty_per)) / (1 - $matl->scrap_factor);
                        }
                    }
                }

                $matl['total_amount'] = $total_amount;
                $matl['base_uom'] = $getItem->uom;
            }
        }

        $headerHtml = view()->make('report.trans.jobHeader')->with('siteLogo', $site->logo)->with('siteLogoAzure', $site->logoazure)->with('companyName', $site->company_name)->with('jobNum', $data->job_num)->with('suffix', $data->suffix)->with('startDatePlan', $data->start_date_plan)->with('jobDate', $data->job_date)->with('endDatePlan', $data->end_date_plan)->with('itemNum', $data->item_num)->with('qtyReleased', $data->qty_released)->with('unit_quantity_format', $unit_quantity_format)->with('uom', $data->uom)->with('itemDesc', $data->item->item_desc)->with('qtyCompleted', $data->qty_completed)->with('custName', $data->customer->cust_name ?? null)->with('qtyScrapped', $data->qty_scrapped)->with('notes', $data->notes)->render();
        $footerHtml = view()->make('report.trans.footer')->with('date', $date)->with('time', $time)->render();

        $pdf = PDF::loadView('MasterMaintenance.job.print', compact('data', 'suffix', 'site', 'unit_quantity_format', 'index', 'issue_by_item_base_uom'))
            ->setPaper('A4', 'portrait')
            ->setOption('header-html', $headerHtml)
            ->setOption('footer-html', $footerHtml)
            ->setOption('margin-bottom', 10)
            ->setOption('margin-top', 100)
            ->setOption('margin-right', 10)
            ->setOption('margin-left', 10)
            ->setOption("footer-right", "Page [page] of [topage] | $job_num")
            ->setOption("footer-font-size", 8);

        $filename = $data->job_num;
        return $pdf->stream($filename . '.pdf');
    }

    public function scopeActive($query)
    {
        return $query->where('oper_status', 1);
    }


    /**
     * Get id by ref_num, suffix
     *
     */
    public function getJob($ref_num, $suffix)
    {
        $ref_num = base64_decode($ref_num);
        if (config('icapt.special_modules.enable_suffix') && $suffix) {
            $job = Job::select('id')
                ->where('job_num', $ref_num)
                ->where('suffix', $suffix)
                ->where('site_id', auth()->user()->site_id)
                ->value('id');
        } else {
            $job = Job::select('id')
                ->where('job_num', $ref_num)
                ->where('site_id', auth()->user()->site_id)
                ->value('id');
        }
        return redirect()->route('viewJob', ['job' => $job]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function jobStatusUpdater(Job $job)
    {
        // if (Gate::allows('canOverrideQtyField') && Gate::allows('hasJobMaintenance')) {
        //     $canoverrideqty = true;
        // } else {
        //     $canoverrideqty = false;
        // }


        $siteSettings = SiteSetting::first();

        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');



        return view('MasterMaintenance.job.status_update');
    }
    public function jobStatusUpdaterData(Request $request)
    {


        $query = DB::query()->from('jobs')
            ->select(
                'jobs.id',
                'jobs.job_num',
                'jobs.suffix',
                'jobs.item_num',
                'jobs.job_status',
                'items.item_desc',
                'items.product_code',
                'jobs.start_date_plan as start_date',
                'jobs.end_date_plan as end_date',
            )


            ->leftJoin('items', function ($join) {
                $join->on('items.item_num', '=', 'jobs.item_num')->on('items.site_id', '=', 'jobs.site_id');
            })
            // ->where('jobs.job_status', $request->status)
            ->where('jobs.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id);

        $siteSettings = SiteSetting::first();
        if (request('from_start_date') && request('to_start_date')) {
            $from_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_start_date'))->format('Y-m-d');
            $to_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_start_date'))->format('Y-m-d');
            $query->whereBetween('jobs.start_date_plan', [$from_date, $to_date]);
        } else if (request('from_start_date')) {
            $from_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_start_date'))->format('Y-m-d');
            $query->where('jobs.start_date_plan', '>=', $from_date);
        } else if (request('to_start_date')) {
            $to_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_start_date'))->format('Y-m-d');
            $query->where('jobs.start_date_plan', '<=', $to_date);
        }

        if (request('from_date') && request('to_date')) {
            $from_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_date'))->format('Y-m-d');
            $to_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_date'))->format('Y-m-d');
            $query->whereBetween('jobs.end_date_plan', [$from_date, $to_date]);
        } else if (request('from_date')) {
            $from_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('from_date'))->format('Y-m-d');
            $query->where('jobs.end_date_plan', '>=', $from_date);
        } else if (request('to_date')) {
            $to_date = Carbon::createFromFormat('' . $siteSettings->getInputDateFormat() . '', request('to_date'))->format('Y-m-d');
            $query->where('jobs.end_date_plan', '<=', $to_date);
        }

        if (request('from_job_num') && request('to_job_num')) {
            $query->whereBetween('jobs.job_num', [request('from_job_num'), request('to_job_num')]);
        } else if (request('from_job_num')) {
            $query->where('jobs.job_num', '>=', request('from_job_num'));
        } else if (request('to_job_num')) {
            $query->where('jobs.job_num', '<=', request('to_job_num'));
        }
        if (request('from_suffix') && request('to_suffix')) {
            $query->whereBetween('jobs.suffix', [request('from_suffix'), request('to_suffix')]);
        } else if (request('from_suffix')) {
            $query->where('jobs.suffix', '>=', request('from_suffix'));
        } else if (request('to_suffix')) {
            $query->where('jobs.suffix', '<=', request('to_suffix'));
        }
        if (request('status') == "Completed") {
            $query->where('job_status', '=', 'C');
        } else if (request('status') == "Open") {
            $query->where('job_status', '=', 'O');
        } else if (request('status') == "Released") {
            $query->where('job_status', '=', 'R');
        }

        if (request('from_item_num') && request('to_item_num')) {
            $query->whereBetween('items.item_num', [request('from_item_num'), request('to_item_num')]);
        } else if (request('from_item_num')) {
            $query->where('items.item_num', '>=', request('from_item_num'));
        } else if (request('to_item_num')) {
            $query->where('items.item_num', '<=', request('to_item_num'));
        }

        if (request('from_product_code') && request('to_product_code')) {
            $query->whereBetween('items.product_code', [request('from_product'), request('to_product_code')]);
        } else if (request('from_product_code')) {
            $query->where('items.product_code', '>=', request('from_product_code'));
        } else if (request('to_product_code')) {
            $query->where('items.product_code', '<=', request('to_product_code'));
        }

        // $query->whereNotNull('jobs.job_num')->whereNotNull('jobs.suffix')->where('jobs.site_id', auth()->user()->site_id);
        // $query->groupBy('job_num')->groupBy('suffix');


        $dataTable = Datatables::of($query);
        $dataTable->addColumn('error_status', function ($row) use ($request) {
            //  $leftJoin4 = JobRoute::select('trans_num as job_trans_num'))->groupBy('job_num', 'suffix');

            //       $leftJoin4 = JobTrans::select('trans_num as job_trans_num'))->groupBy('job_num', 'suffix');
            //       $leftJoin4 = MachineTrans::select('trans_num as machine_trans_num'))->groupBy('job_num', 'suffix');

            // $leftJoin4 = MatlTrans::select('trans_num as matl_trans_num'))->groupBy('job_num', 'suffix');
            if ($row->job_status == "O" && $request->new_status == "Released") {
                $job_route = JobRoute::where('job_num', $row->job_num)
                    ->where('suffix', $row->suffix)
                    ->first();
                // dd($job_route,$row);
                $job_matl = JobMatl::where('job_num', $row->job_num)->where('suffix', $row->suffix)->first();
                //  dd($job_route,$row, $job_matl);
                if (!$job_route && !$job_matl)
                    return 1;
            }
            if (($row->job_status == "C" || $row->job_status == "R") && $request->new_status == "Open") {

                // $job_route = JobRoute::where('job_num', $row->job_num)
                //     ->where(function ($query) {
                //         $query->where('qty_moved', ">", 0)
                //             ->orWhere('qty_completed', ">", 0)
                //             ->orWhere('qty_scrapped', ">", 0)
                //             ->orWhere('qty_received', ">", 0);
                //     })
                //     ->where('suffix', $row->suffix)
                //     ->first();
                // $job_matl = JobMatl::where('job_num', $row->job_num)
                //     ->where('qty_issued', ">", 0)
                //     ->where('suffix', $row->suffix)
                //     ->first();
                // $JobTrans = JobTrans::where('job_num', $row->job_num)->where('suffix', $row->suffix)->first();
                // $MachineTrans = MachineTrans::where('job_num', $row->job_num)->where('suffix', $row->suffix)->first();
                // $MatlTrans = MatlTrans::where('job_num', $row->job_num)->where('suffix', $row->suffix)->first();
                // if ($MatlTrans)
                //     return 2;
                // if ($job_route || $job_matl)
                //     return 2;
                // if ($JobTrans)
                //     return 3;
                $newStatus = "O";
                $valid = self::jobStatusValidation($row, $newStatus);
                if (!$valid)
                    return 2;
            }
            return 0;
        });
        $dataTable->editColumn('job_num', function ($job) {
            return "<a href='" . route('viewJob', ['job' => $job->id]) . "'  title='$job->job_num'>$job->job_num</a>";
        });
        $dataTable->editColumn('job_status', function ($job) {
            return $job->job_status == "O" ? "Open" : ($job->job_status == "C" ? "Completed" : "Released");
        });
        $dataTable->editColumn('start_date', function ($item) {
            $datFormat =   SiteSetting::select('date_format')->where('site_id', auth()->user()->site_id)->value('date_format');
            return date($datFormat, strtotime($item->start_date));
        });
        $dataTable->editColumn('end_date', function ($item) {
            if ($item->end_date == "")
                return "";
            $datFormat =   SiteSetting::select('date_format')->where('site_id', auth()->user()->site_id)->value('date_format');
            return date($datFormat, strtotime($item->end_date));
        });
        // $dataTable->addColumn('message', function ($row) {
        //     return $row->error_status . " message";
        // });
        $dataTable->setRowId('id');
        $dataTable->rawColumns(['job_num']);
        $response  = $dataTable->make(true);
        return $response;
    }
    public function processJobStatusUpdater(Request $request)
    {
        $statuses = [
            'Released' => "R",
            "Open" => "O",
            "Completed" => "C",
        ];
        $count = 0;
        $status = $statuses[$request->status];
        $new_status = $statuses[$request->new_status];
        $jobs = Job::whereIn('id', $request->jobs)->where('job_status', $status)->where('site_id', auth()->user()->site_id)->get();
        foreach ($jobs as $job) {
            if ($job->job_status == "O" && $request->new_status == "Released") {
                $job_route = JobRoute::where('job_num', $job->job_num)->where('suffix', $job->suffix)->first();
                // dd($job_route,$row);
                $job_matl = JobMatl::where('job_num', $job->job_num)->where('suffix', $job->suffix)->first();
                if (!$job_route && !$job_matl)
                    continue;
            }
            if (($job->job_status == "C" || $job->job_status == "R") && $request->new_status == "Open") {
                // $JobTrans = JobTrans::where('job_num', $job->job_num)->where('suffix', $job->suffix)->first();
                // $MachineTrans = MachineTrans::where('job_num', $job->job_num)->where('suffix', $job->suffix)->first();
                // $MatlTrans = MatlTrans::where('job_num', $job->job_num)->where('suffix', $row->suffix)->first();
                // dd($MachineTrans, $JobTrans);
                $newStatus = "O";
                $valid = self::jobStatusValidation($job, $newStatus);
                if (!$valid)
                    continue;
            }

            $job->job_status = $new_status;
            $job->save();
            $count++;
        }
        return response()->json(["status" => "success", "count" => $count]);
        // dd($jobs);
    }
    public static function jobStatusValidation($job, $newStatus)
    {
        // $job_route = JobRoute::where('job_num', $job->job_num)
        //     ->where(function ($query) {
        //         $query->where('qty_moved', ">", 0)
        //             ->orWhere('qty_completed', ">", 0)
        //             ->orWhere('qty_scrapped', ">", 0)
        //             ->orWhere('qty_received', ">", 0);
        //     })
        //     ->where('suffix', $job->suffix)
        //     ->first();
        // $job_matl = JobMatl::where('job_num', $job->job_num)
        //     ->where('qty_issued', ">", 0)
        //     ->where('suffix', $job->suffix)
        //     ->first();

        // Check the job order in Material Transaction, Job Transaction and Machine Transaction
        if ($newStatus == "O") {
            if (config('icapt.special_modules.enable_suffix')) {
                //Throw error if the Job Num + Suffix is exist
                $matl_trans = MatlTrans::where('ref_num', $job->job_num)->where('suffix', $job->suffix)->exists();
                $job_trans = JobTrans::where('job_num', $job->job_num)->where('suffix', $job->suffix)->exists();
                $machine_trans =  MachineTrans::where('job_num', $job->job_num)->where('suffix', $job->suffix)->exists();
            } else {
                //Throw error if the Job Num is exist
                $matl_trans = MatlTrans::where('ref_num', $job->job_num)->exists();
                $job_trans = JobTrans::where('job_num', $job->job_num)->exists();
                $machine_trans = MachineTrans::where('job_num', $job->job_num)->exists();
            }
        }

        if ($machine_trans || $matl_trans || $job_trans) {
            return false;
        }
        return true;

        // if ($job_route || $job_matl || $job->qty_completed>0 ||$job->qty_scrapped > 0||$job->qty_returned > 0)
        //     return false;
        // return true;
    }
}
