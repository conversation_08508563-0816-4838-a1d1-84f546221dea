<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\User;
use App\Item;
use App\JobMatl;
use App\Job;
use App\Warehouse;
use App\Loc;
use App\JobRoute;

class JobMaterialCatchWeightReturnTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $item;
    protected $job;
    protected $jobMatl;
    protected $warehouse;
    protected $location;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'site_id' => 1,
            'email' => '<EMAIL>'
        ]);

        // Create test warehouse
        $this->warehouse = Warehouse::factory()->create([
            'whse_num' => 'TEST_WH',
            'whse_status' => 1,
            'site_id' => 1
        ]);

        // Create test location
        $this->location = Loc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'loc_status' => 1,
            'site_id' => 1
        ]);

        // Create catch weight enabled item
        $this->item = Item::factory()->create([
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'item_status' => 1,
            'catch_weight' => 1,
            'catch_weight_tolerance' => 5.0,
            'lot_tracked' => 1,
            'site_id' => 1
        ]);

        // Create test job
        $this->job = Job::factory()->create([
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'whse_num' => 'TEST_WH',
            'job_status' => 'R', // Released
            'site_id' => 1
        ]);

        // Create job route
        JobRoute::factory()->create([
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10',
            'site_id' => 1
        ]);

        // Create job material with some issued quantity
        $this->jobMatl = JobMatl::factory()->create([
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10',
            'sequence' => '1',
            'matl_item' => 'CW_ITEM_001',
            'matl_desc' => 'Catch Weight Test Item',
            'qty_required' => 100.0,
            'qty_issued' => 50.0,
            'qty_returned' => 0.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);
    }

    /** @test */
    public function it_shows_catch_weight_view_for_catch_weight_items()
    {
        $this->actingAs($this->user);

        $response = $this->get(route('unIssueJobDetails', [
            'whse_num' => 'TEST_WH',
            'job_num' => base64_encode('TEST_JOB_001'),
            'suffix' => '001',
            'oper_num' => '10',
            'matl_item' => 'CW_ITEM_001',
            'sequence' => '1'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('production.jobmatliunissue.process_cw');
        $response->assertViewHas('jobmatl');
        $response->assertViewHas('batch_id');
    }

    /** @test */
    public function it_shows_regular_view_for_non_catch_weight_items()
    {
        // Create non-catch weight item
        $regularItem = Item::factory()->create([
            'item_num' => 'REG_ITEM_001',
            'item_desc' => 'Regular Test Item',
            'item_status' => 1,
            'catch_weight' => 0,
            'lot_tracked' => 1,
            'site_id' => 1
        ]);

        // Create job material for regular item
        JobMatl::factory()->create([
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10',
            'sequence' => '2',
            'matl_item' => 'REG_ITEM_001',
            'matl_desc' => 'Regular Test Item',
            'qty_required' => 100.0,
            'qty_issued' => 50.0,
            'qty_returned' => 0.0,
            'uom' => 'EA',
            'site_id' => 1
        ]);

        $this->actingAs($this->user);

        $response = $this->get(route('unIssueJobDetails', [
            'whse_num' => 'TEST_WH',
            'job_num' => base64_encode('TEST_JOB_001'),
            'suffix' => '001',
            'oper_num' => '10',
            'matl_item' => 'REG_ITEM_001',
            'sequence' => '2'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('production.jobmatliunissue.process');
        $response->assertViewHas('jobmatl');
        $response->assertViewHas('lot_tracked');
    }

    /** @test */
    public function it_validates_catch_weight_route_exists()
    {
        $this->assertTrue(
            collect(\Route::getRoutes())->contains(function ($route) {
                return $route->getName() === 'runJobMatlReturnCWProcess';
            })
        );
    }
}
