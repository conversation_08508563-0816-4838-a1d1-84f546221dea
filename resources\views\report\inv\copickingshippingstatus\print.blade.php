@extends('report.layout')

@section('content')
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>CO Picking vs Shipping Status Report</h5>
            </div>
            
            <div class="card-body">
                <div class="report-filter-info">
                    <div class="row">
                        <div class="col-md-6">
                            @if(request('from_co_num') || request('to_co_num'))
                                <p><strong>CO Number:</strong> 
                                    @if(request('from_co_num') && request('to_co_num'))
                                        {{request('from_co_num')}} to {{request('to_co_num')}}
                                    @elseif(request('from_co_num'))
                                        From {{request('from_co_num')}}
                                    @elseif(request('to_co_num'))
                                        To {{request('to_co_num')}}
                                    @endif
                                </p>
                            @endif
                            
                            @if(request('from_cust_num') || request('to_cust_num'))
                                <p><strong>Customer:</strong> 
                                    @if(request('from_cust_num') && request('to_cust_num'))
                                        {{request('from_cust_num')}} to {{request('to_cust_num')}}
                                    @elseif(request('from_cust_num'))
                                        From {{request('from_cust_num')}}
                                    @elseif(request('to_cust_num'))
                                        To {{request('to_cust_num')}}
                                    @endif
                                </p>
                            @endif
                            
                            @if(request('from_due_date') || request('to_due_date'))
                                <p><strong>Due Date:</strong> 
                                    @if(request('from_due_date') && request('to_due_date'))
                                        {{request('from_due_date')}} to {{request('to_due_date')}}
                                    @elseif(request('from_due_date'))
                                        From {{request('from_due_date')}}
                                    @elseif(request('to_due_date'))
                                        To {{request('to_due_date')}}
                                    @endif
                                </p>
                            @endif
                        </div>
                        <div class="col-md-6">
                            @if(request('from_item_num') || request('to_item_num'))
                                <p><strong>Item:</strong> 
                                    @if(request('from_item_num') && request('to_item_num'))
                                        {{request('from_item_num')}} to {{request('to_item_num')}}
                                    @elseif(request('from_item_num'))
                                        From {{request('from_item_num')}}
                                    @elseif(request('to_item_num'))
                                        To {{request('to_item_num')}}
                                    @endif
                                </p>
                            @endif
                            
                            @if(request('co_status') && request('co_status') != 'All')
                                <p><strong>CO Status:</strong> {{request('co_status') == 'O' ? 'Open' : 'Completed'}}</p>
                            @endif
                            
                            @if(request('co_line_status') && request('co_line_status') != 'All')
                                <p><strong>CO Line Status:</strong> {{request('co_line_status') == 'O' ? 'Open' : 'Completed'}}</p>
                            @endif
                            
                            @if(request('stage_locations') && is_array(request('stage_locations')))
                                <p><strong>Stage Locations:</strong> {{implode(', ', request('stage_locations'))}}</p>
                            @endif
                            
                            @if(request('outstanding_only') == 'on')
                                <p><strong>Outstanding Only:</strong> Yes</p>
                            @endif
                        </div>
                    </div>
                </div>
                
                <table class="table table-bordered table-sm">
                    <thead>
                        <tr>
                            <th>Customer Code</th>
                            <th>Customer Name</th>
                            <th>CO Number</th>
                            <th>Line</th>
                            <th>Due Date</th>
                            <th>Item</th>
                            <th>Description</th>
                            <th class="text-right">Qty Required</th>
                            <th>Stage Location</th>
                            <th class="text-right">Qty Picked</th>
                            <th class="text-right">Qty Shipped</th>
                            <th class="text-right">Outstanding Qty</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($query1 as $row)
                        <tr>
                            <td>{{$row->cust_num}}</td>
                            <td>{{$row->cust_name}}</td>
                            <td>{{$row->co_num}}</td>
                            <td>{{$row->co_line}}</td>
                            <td>{{$row->due_date}}</td>
                            <td>{{$row->item_num}}</td>
                            <td>{{$row->item_desc}}</td>
                            <td class="text-right">{{number_format($row->qty_required, $total_quantity_format)}}</td>
                            <td>{{$row->stage_location}}</td>
                            <td class="text-right">{{number_format($row->qty_picked, $total_quantity_format)}}</td>
                            <td class="text-right">{{number_format($row->qty_shipped, $total_quantity_format)}}</td>
                            <td class="text-right">{{number_format($row->outstanding_qty, $total_quantity_format)}}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                
                @if(count($query1) == 0)
                    <div class="alert alert-info">
                        <strong>No records found</strong> matching the selected criteria.
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
