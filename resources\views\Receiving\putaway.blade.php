@extends('layout.mobile.app')
@section('content')
@section('title', __('Put Away'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="putawayform" action="{{ route('putawayprocess') }}" method="post">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                @if (isset($batch_id))
                    <input type="hidden" name="batch_id" id="batch_id" value="{{ $batch_id }}" />
                @endif

                <div class="form-group row">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control required"
                        for="whse_num">{{ __('mobile.label.whse_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group" <?php if (!$enable_warehouse) {
                            echo 'colspan="2"';
                        } ?>>
                            <input type="text" onchange="clickSelf(this.id)" name="whse_num" id="whse_num"
                                class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.whse_num') }}" <?php if (!$enable_warehouse) {
                                    echo 'readonly';
                                } ?>
                                value="{{ old('whse_num') ?? auth()->user()->getCurrWhse() }}" required>
                        </div>
                    </div>
                    @if ($enable_warehouse)
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.warehouses') }}"
                                onClick="selection('/getWhse','whse_num','whse_num','whse_num');
                                    modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif
                </div>

                <div class="form-group row" id="receiptlocrow">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control required"
                        for="loc">{{ __('mobile.label.from_loc') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="hidden" name="checkSubmit" id="checkSubmit"
                                class="form-control border-primary" value="">
                            <input type="text" onchange="clickSelf(this.id)" name="loc_num" id="loc_num"
                                class="form-control border-primary" placeholder="{{ __('mobile.placeholder.loc_num') }}"
                                value="{{ old('loc_num') }}"required>
                            <span id="locnumnotexist"></span>
                            <span id="checkLoc"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="locbtn" name="{{ __('mobile.list.locations') }}"
                            onClick="selection('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="itemrow">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control required"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" value="{{ old('item_num') }}" name="item_num" id="item_num"
                                onChange="clickSelf(this.id)" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.item_num') }}" required>
                            <input type="hidden" name="lot_tracked" id="lot_tracked" value="">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="{{ __('mobile.list.items') }}"
                            onClick="selection('/getLocItem','whse_num,loc_num,item_num','item_num','item_num');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i
                                class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="descrow">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control" for="item_desc"></label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <textarea type="text" name="item_desc" id="item_desc" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.item_desc') }}" readonly>{{ old('item_desc') }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row" id="trlot" hidden>
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control required"
                        for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="lot_num" id="lot_num" onchange="onLotChangePutaway()"
                                class="form-control border-primary valid"
                                placeholder="{{ __('mobile.placeholder.lot_num') }}" value="{{ old('lot_num') }}"
                                maxlength="50">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="Lot" id="lotbtn"
                            onclick="selectionMultiLineInput('/getLotLocExpiry','whse_num,loc_num,item_num,sortField,sortBy','lot_num','lot_num');modalheader_uom(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="itemtoloc">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control pr-0 required"
                        for="toLoc">{{ __('mobile.label.to_loc') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="toLoc" id="toLoc" value="{{ old('toLoc') }}"
                                class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.to_loc') }}" maxlength="30">
                            <span id="checkToLoc"></span>
                            <span id="loc_info"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="{{ __('mobile.list.to_locations') }}"
                            onClick="selection('/getLocNoPicking','whse_num,toLoc','loc_num','toLoc');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                {{-- <div class="form-group row" id="trlocinfo">
                        <label class="col-xs-3 col-md-3 col-lg-3 label-control"></label>
                        <div class="col-xs-9 col-md-9 col-lg-8">
                            <div class="input-group">
                                <span id="loc_info"></span>
                            </div>
                        </div>
                    </div> --}}

                <div class="form-group row" id="availrow">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control"
                        for="qty_available">{{ __('mobile.label.qty_available') }} </label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="text" style="text-align:right" name="qty_on_hand" id="qty_available"
                                value="{{ old('qty_on_hand') ? numberFormatPrecision(old('qty_on_hand'), $unit_quantity_format) : '' }}"
                                class="form-control border-primary" onblur="formatDecimal(this)"
                                placeholder="{{ __('mobile.label.qty_available') }}" readonly>
                            <input type="hidden" name="qty_on_hand_conv" readonly id="qty_available_conv"
                                value="{{ old('qty_on_hand_conv') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input id="base_uom" name="base_uom" class="form-control border-primary"
                            placeholder="{{ __('mobile.placeholder.uom') }}" readonly value="{{ old('base_uom') }}">
                    </div>
                </div>

                <div class="form-group row" id="qtyrow">
                    <label class="col-xs-3 col-md-3 col-lg-3 label-control pr-0 required"
                        for="qty_to_putaway">{{ __('mobile.label.qty_to_putaway') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input inputmode="numeric" type="text" style="text-align:right" name="qty"
                                id="qty" value="{{ old('qty') }}"
                                class="form-control border-primary number-format" onblur="formatDecimal(this)"
                                placeholder="{{ __('mobile.placeholder.qty_to_putaway') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" name="uom" id="uom" onchange="clickSelf(this.id)"
                            value="{{ old('uom') }}" class="form-control border-primary"
                            placeholder="{{ __('mobile.placeholder.uom') }}">
                        <input type="hidden" name="max_qty_input" id="max_qty_input"
                            value="{{ numberFormatPrecision(old('max_qty_input'), $unit_quantity_format) }}">
                        <input type="hidden" name="conv_factor" id="conv_factor" value="1">
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="{{ __('mobile.list.uoms') }}"
                            onClick="selection('/getItemUOMConv','item_num','uom','uom');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>
            </div><br>

            <div style="text-align:center;">
                <button type="submit" id="process" class="btn btn-primary submitloader">
                    <i class="icon-check2"></i> {{ __('mobile.button.putaway') }}
                </button>
            </div>
        </form>
    </div>
</div>

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.15.0/additional-methods.min.js"></script>

<script>
    jQuery(function($) {
        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
        $.validator.addMethod('minStrict', function(value, el, param) {
            return value > param;
        });

        var errorMessage = 'Default error message';
        var errorMessageToLoc = 'Default error message';
        var errorMessageQty = "{{ __('error.mobile.qty_notmoreorequalqty_available') }}";

        $("#putawayform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $(".submitloader").attr('disabled', false);
            }
        });

        $("#putawayform").validate({
            onchange: true,
            // onfocusout: false,
            rules: {
                whse_num: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val()
                        }
                    }
                },
                item_num: {
                    required: true,
                    remote: {
                        url: "{{ route('validateWarehouseItemV3') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                        }
                    }
                },
                loc_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_status == 0) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");
                                    $("#qty_available").val("0");
                                    errorMessage =
                                        "{{ __('error.mobile.inactive_loc2', ['resource' => 'To Loc']) }}";
                                    $(".pageloader").css("display", "none");
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else {
                                    // if($("#trlot").is(":hidden")){
                                    //     display('/displayQuantity','item_num,whse_num,loc_num','qty_available,base_uom,qty_available_conv');
                                    // }
                                    // showNewLoc();
                                    if ($("#trlot").is(":hidden")) {
                                        display('/displayQuantity', 'item_num,whse_num,loc_num',
                                            'qty_available,base_uom,qty_available_conv');
                                    }
                                    return true;
                                }

                            } else {
                                $("#loc_info").html("");
                                $("#locnumnotexist").html('');
                                $("#qty_available").val("0");
                                // errorMessage = "{{ __('error.mobile.loc_not_exists') }}";
                                var $loc = $("#loc_num").val();
                                var msg2 =
                                    "{{ __('error.mobile.notexist3', ['resource' => 'From Loc', 'resource2' => ':item:']) }}";
                                msg2 = msg2.replace(":item:", $loc);
                                errorMessage = msg2;
                                // showNewLoc();
                                // $(".submitloader").attr("disabled", true);
                                return false;
                            }
                        }
                    }
                },

                toLoc: {
                    required: {
                        depends: function() {
                            if (disable_create_new_item_location == 1) {
                                return true;
                            }

                            return false;
                        },
                    },
                    notEqualTo: "#loc_num",
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();

                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            $("#loc_info").html('');
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');

                                    errorMessageToLoc =
                                        "{{ __('error.mobile.validate_picking') }}";

                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    errorMessageToLoc =
                                        "{{ __('error.mobile.validate_transit') }}";

                                    return false;
                                } else if (data[0].loc_status == 0) {
                                    $("#loc_info").html("");
                                    $("#locnumnotexist").html('');
                                    $("#qty").val("");

                                    errorMessageToLoc =
                                        "{{ __('error.mobile.inactive_loc2', ['resource' => 'To Loc']) }}";
                                    $(".pageloader").css("display", "none");
                                    return false;
                                } else {
                                    return true;
                                }

                            } else {
                                $("#toLoc").html("");
                                $("#locnumnotexist").html('');

                                if (disable_create_new_item_location == 1) {
                                    var $loc = $("#toLoc").val();
                                    var msg2 =
                                        "{{ __('error.mobile.notexist3', ['resource' => 'To Loc', 'resource2' => ':item:']) }}";
                                    msg2 = msg2.replace(":item:", $loc);
                                    errorMessageToLoc = msg2;

                                    return false;
                                }
                                showNewLoc();
                                return true;
                            }
                        }
                    }
                },

                lot_num: {
                    required: {
                        depends: function(element) {
                            return $("#lot_num").is(":visible");
                        }
                    },
                    remote: {
                        url: "{{ route('ExistingLotValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            },
                            loc_num: function() {
                                return $("#loc_num").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                        }
                    }
                },
                qty: {
                    required: true,
                    number: true,
                    number_size: true,
                    minStrict_value: 0,
                    max_value: function() {
                        //  return parseFloat($("#qty_available").val().replace(/,/g,"")); //
                        var qty_receivable = parseFloat($("#max_qty_input").val().replace(/,/g,
                            ""));
                        return Math.max(0, qty_receivable);
                        // if($("#qty_available_conv").val("")){
                        //     return parseFloat($("#qty_available_conv").val().replace(/,/g,""));
                        // }
                        // else {
                        //                            return parseFloat($("#qty_available").val());
                        //return parseFloat($("#qty_available").val().replace(/,/g,""));
                        // }
                    }
                },
                uom: {
                    required: true,
                    uom_validation: function() {
                        return [$('#item_num').val(), null, null, $('#base_uom').val(), $('#uom')
                            .val()
                        ];
                    }
                    // remote:{
                    //         url: "{{ route('MiscValidation') }}",
                    //         type: "post",
                    //         data: { _token : $('input[name="_token"]').val() }
                    //     }
                },
            },
            messages: {
                whse_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.whse_num')]) }}"
                },
                loc_num: {
                    remote: function() {
                        return errorMessage;
                    }
                },
                // item_num:{
                //     remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.item_num')]) }}"
                // },
                lot_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}"
                },
                toLoc: {
                    notEqualTo: "{{ __('error.mobile.same', ['resource1' => __('mobile.label.from_loc'), 'resource2' => __('mobile.label.to_loc')]) }}",
                    remote: function() {
                        return errorMessageToLoc;
                    }
                },
                qty: {
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.label.qty')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}",
                    minStrict_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty')]) }} {0}",
                    // max_value: function(val2) {
                    //     // var msg2=  "{{ __('error.mobile.exceed', ['resource' => __('mobile.label.qty_to_putaway'), 'resource2' => ':qty2:']) }}";

                    //     var msg2 = "{{ __('error.mobile.qty_putaway_more_qty_available') }}";
                    //     var qty = $("#qty").val();
                    //     msg2 = msg2.replace(":qty:", qty)
                    //     msg2 = msg2.replace(":qty2:", val2)


                    //     return msg2;
                    // }

                    max_value: function(value, element) {
                        var maxQty = parseFloat($('#max_qty_input').val().replace(/,/g, ""));
                        return errorMessageQty.replace(':resource_qty_available', maxQty);
                    },
                    // "{{ __('error.mobile.lessthan', ['resource' => __('mobile.label.qty')]) }} {0}"
                },
                uom: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                },
            },
            submitHandler: function(form) {
                // $(".pageloader").css("display", "block");
                $(".submitloader").attr("disabled", true);
                // ajaxurl ="{{ route('itemlocv', ['item_num', 'loc_num', 'whse_num']) }}";
                // url = ajaxurl.replace('item_num', $("#item_num").val());
                // url = url.replace('loc_num', $("#loc_num").val());
                // url = url.replace('whse_num', $("#whse_num").val());

                ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
                url = ajaxurl.replace('loc_num', btoa($("#toLoc").val()));
                $.get(url, function(data) {
                    if (data == 'not exist') {
                        let m = '{{ __('admin.message.surecreate') }}';
                        if ($('#lot_num').val() == "" || $('#lot_num').val() == null) {
                            m = m.replace(':resource', "Item Location");
                        } else {
                            m = m.replace(':resource', "Item Lot Location");
                        }
                        Swal.fire({
                            title: 'Warning',
                            text: m,
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Yes'
                        }).then((result) => {
                            if (result.value) {
                                $(".pageloader").css("display", "block");
                                $(".submitloader").attr("disabled", true);
                                form.submit();
                            } else {
                                $(".submitloader").attr('disabled', false);
                                return false;
                            }
                        });
                    } else {
                        $(".pageloader").css("display", "block");
                        $(".submitloader").attr("disabled", true);
                        form.submit();



                    }
                });

                // $(".pageloader").css("display", "block");
                //         $(".submitloader").attr("disabled", true);
                //         form.submit();
            }
        });
    });
</script>
@include('util.validate_uom')
<script type="text/javascript">
    $(document).ready(function() {
        $("#checkLoc").html('');
        $("#checkToLoc").html('');
        $("#loc_info").html('');

        $("#qty_available").on("change", function() {
            // console.log($(this).val());
            $("#max_qty_input").val($(this).val()).change();
        });
        if ($("#trlot").is(":hidden") && $("#lot_num").val()) {
            $("#trlot").attr('hidden', false);
        }

        $("#whse_num").on("change", function() {
            // $("#lot_num,#loc_num,#from_loc,#item_num,#item_desc,#uom,#base_uom,#qty_available,#qty,#qty_available_conv").val("");
            $("#loc_info").html("");
        });

        $("#loc_num").on("change", function() {
            $("#checkLoc").html("");
            // $("#lot_num,#loc_num,#item_num,#item_desc,#uom,#base_uom,#qty_available,#qty,#qty_available_conv").val("");

            if ($("#loc_num").val() == "") {
                $("#lot_num,#toLoc,#item_num,#item_desc,#uom,#base_uom,#qty_available,#qty,#qty_available_conv")
                    .val("");
                $("#loc_info").html("");
                $("#checkLoc").html("");
                $("#trlot").attr('hidden', true);
            }
        });

        $("#toLoc").on("change", function() {
            $("#process").attr("disabled", false);
            $("#qty").val('');
            if ($("#toLoc").val() == "") {
                $("#loc_info").html("");
                $("#checkToLoc").html("");
            }
        });

        $("#item_num").on("change", function() {
            if ($("#item_num").val() == "") {
                $("#lot_num,#toLoc,#item_desc,#uom,#base_uom,#qty_available,#qty,#qty_available_conv")
                    .val("");
                $("#loc_info").html("");
                $("#trlot").attr('hidden', true);

            } else {
                console.log('item num not empty');
                display('/displayItemDesc', 'item_num', 'item_desc,uom,lot_tracked');
                display('/displayQuantity', 'item_num,whse_num,loc_num',
                    'qty_available,base_uom,max_qty_input');

                // qtyonhand = $("#qty_available").val();
                // alert('hello' + qtyonhand)
                // $("#max_qty_input").val(qtyonhand);
            }
            $("#item_num").focus();
        });


        $("#uom").on("change", function() {
            var lot_num = "undefined";
            if ($("#lot_num").val() != "") {
                lot_num = $("#lot_num").val();
            }
            $("#input4").val('');
            if ($('#uom').val()) {

                //calculateQtyLimit($("#base_uom").val(), $("#qty_available").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#toLoc").val(), lot_num, $("#base_uom").val(), "null", "null", "null", "");
                let validate = validateConvUOM($("#item_num").val(), null, null, $("#base_uom").val(),
                    $("#base_uom").val(), $("#uom").val());

                validate.then(function(resp) {
                        // true
                    },
                    function(err) {
                        // false
                        $("#qty_input").val('');
                        $("#uom").val($("#base_uom").val());
                    }).finally(function() {
                        $("#qty").val('');
                        $("#qty-error").hide();
                    calculateQtyLimit($("#base_uom").val(), $("#qty_available").val(), $("#uom")
                        .val(), $("#item_num").val(), $("#whse_num").val(), $("#toLoc")
                        .val(), lot_num, $("#base_uom").val(), "null", "null", "null", "");
                    // calculateQtyLimit($("#uom_need_to_convert").val(), $("#qty_need_to_convert").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), lot_num, $("#base_uom").val(), $("#cust_num").val(), "null", "{{ __('mobile.nav.co_picking') }}", "", "qty_available_conv");
                    // calculateQtyLimit($("#qty_required_uom").val(), $("#qty_required").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), lot_num, $("#base_uom").val(), $("#cust_num").val(), "null", "{{ __('mobile.nav.co_picking') }}", "");
                });
            }
        });





        /* $("#uom").on("change", function(){
              var cust_num = "null";
              var vend_num = "null";
              var lot_num = "undefined";
              if($("#lot_num").val() != ""){
                  lot_num = $("#lot_num").val();
              }
              ajaxurl ="{{ route('getAllUOMConv', ['item_num', 'base_uom', 'cust_num', 'vend_num']) }}";
              url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
              url = url.replace('base_uom', btoa($("#base_uom").val()));
              url = url.replace('cust_num', btoa(cust_num));
              url = url.replace('vend_num', btoa(vend_num));
              var selectuom = $("#uom").val();
              var uomConv = [];
              if(selectuom!=""){
                  $.get(url, function(data){
                      data.forEach(element => {
                          uomConv.push(element['uom']);
                      });
                      console.log(JSON.stringify(data), JSON.stringify(uomConv));
                      if(jQuery.inArray(selectuom, uomConv) != -1){
                          calculateQtyLimit($("#base_uom").val(), $("#qty_available").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#toLoc").val(), lot_num, $("#base_uom").val(), "null", "null", "null", "");
                      }
                      else{

                          $("#qty_input").val('');
                          $("#uom").val($("#base_uom").val());
                          Alert.notexist('{{ __('admin.label.uom') }}', $("#uom").val());
                      }
                  });
              }
              // if($("#item_num").val() == ""){
              //     $("#qty_available_conv").val("");
              // } else
              // {
              //     display('/displayQuantityConverted','base_uom,item_num,qty_on_hand,uom','qty_on_hand_conv');
              // }
          });*/
    });

    function onLotChangePutaway() {
        if ($("#lot_num").val()) {
            display('/displayLotQuantity', 'item_num,whse_num,loc_num,lot_num', 'qty_available,qty_available_conv');

            //  display('/displayLotQuantity','item_num,whse_num,loc_num,lot_num','qty_on_hand,qty_on_hand_conv');
            $("#max_qty_input").val($("#qty_available").val());
        }
        $("#lot_num").focus();
    }

    function showNewLoc() {

        // Check the From Loc and To Loc
        var toLoc = $("#toLoc").val();
        var fromLoc = $("#loc_num").val();
        //alert(toLoc + " :: " +fromLoc);
        if (toLoc === fromLoc) {
            Swal.fire({
                title: 'Warning',
                text: "From Loc cannot same as To Loc",
                icon: 'warning',
                showCancelButton: false,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Ok'
            }).then((result) => {
                $("#toLoc").val('');
            });
        }
        /* var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
        $("#loc_info").html('');
        ajaxurl ="{{ route('checklocmaster', ['loc_num']) }}";
        url = ajaxurl.replace('loc_num', btoa($("#toLoc").val()));
        // url = url.replace('loc_num', btoa($("#loc_num").val()));
        // url = url.replace('whse_num', btoa($("#whse_num").val()));

        $.get(url, function(data){

        if(data == 'not exist')
        {
            // Disable create new loc = Yes = 1
            if(disable_create_new_item_location==1)
            {
                $(".submitloader").attr("disabled", true);
                $("#loc_info").html('<span style="color:red;">{{ __('mobile.message.notallow_new_item_location') }}</span>');

            }
            // Disable create new loc = No = 0
            if(disable_create_new_item_location==0)
            {
                $(".submitloader").attr("disabled", false);
                $("#loc_info").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_item_location') }}</small>');

            }

        }
        else
        {
            $(".submitloader").attr("disabled", false);
        }
    });*/
        // var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
        // $("#loc_info").html('');
        // ajaxurl ="{{ route('itemlocv', ['item_num', 'loc_num', 'whse_num']) }}";
        // url = ajaxurl.replace('item_num', $("#item_num").val());
        // url = url.replace('loc_num', $("#loc_num").val());
        // url = url.replace('whse_num', $("#whse_num").val());

        // $.get(url, function(data){

        //     if(data == 'not exist' && $("#lot_num").is(":visible") == false && disable_create_new_item_location == 0) {
        //         console.log('hell');
        //         $("#loc_info").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_item_location') }}</small>');
        //     }

        //     if(data == 'not exist' && $("#lot_num").is(":visible") == false && disable_create_new_item_location == 1) {

        //         $("#loc_info").html('<i class="icon-info"></i> <small>{{ __('error.mobile.notexistLoc') }}</small>');
        //         $("#process").attr("disabled", true);

        //     }

        //     if(data == 'not exist' && $("#lot_num").is(":visible") == true && disable_create_new_item_location == 0) {
        //         console.log('hell2');
        //         $("#loc_info").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_lot_location2') }}</small>');
        //     }
        // });


        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';

        if ($("#lot_tracked").val() != "1") {
            $("#loc_info").html('');
            // ajaxurl ="{{ route('itemlocv', ['item_num', 'toLoc', 'whse_num']) }}";
            // url = ajaxurl.replace('item_num', $("#item_num").val());
            // url = url.replace('toLoc', $("#toLoc").val());
            // url = url.replace('whse_num', $("#whse_num").val());


            ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
            url = ajaxurl.replace('loc_num', btoa($("#toLoc").val()));
            $.get(url, function(data) {
                // console.log(data);
                if (data == 'not exist' && disable_create_new_item_location == 0) {
                    $("#loc_info").html(
                        '<i class="icon-info"></i> <span>{{ __('mobile.message.new_item_location') }}</span>'
                    );
                }
                if (data == 'not exist' && disable_create_new_item_location == 1) {
                    // $("#checkToLoc").html('<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>');
                    //         // showNewLoc();
                    //          $(".submitloader").attr("disabled", true);

                }
            });

        } else {
            if (($("#lot_num").val() != "" && $("#toLoc").val() != "") && ($("#loc_num").val() != $("#toLoc").val())) {
                $("#loc_info").html('');

                ajaxurl = "{{ route('lotlocv', ['lot_num', 'item_num', 'toLoc', 'whse_num']) }}";
                url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
                url = url.replace('item_num', btoa($("#item_num").val()));
                url = url.replace('toLoc', btoa($("#toLoc").val()));
                url = url.replace('whse_num', btoa($("#whse_num").val()));

                $.get(url, function(data) {
                    if (data == 'not exist' && disable_create_new_item_location == 0) {
                        $("#loc_info").html(
                            '<i class="icon-info"></i> <span>{{ __('mobile.message.new_lot_location2') }}</span>'
                        );
                    }
                });
            }
        }






















        $("#toLoc").focus();

        display('/displayQuantityConverted', 'base_uom,item_num,qty_available,uom', 'qty_available_conv');
        $("#max_qty_input").val($("#qty_available").val());
    }
</script>
@include('errors.maxchar')
@include('util.selection')
@include('util.convert_alternade_barcode_to_item')
@include('Pallet.palletMobileValidation')
@endsection
