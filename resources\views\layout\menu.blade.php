{{--
    plan_id: 1,'AX-MT-STR-M','Starter'
    plan_id: 2,'AX-MT-PRO-M','Professional'
    plan_id: 3,'AX-MT-ENT-M','Enterprise'
    plan_id: 4,'AX-MT-STR-A','Starter'
    plan_id: 5,'AX-MT-PRO-A','Professional'
    plan_id: 6,'AX-MT-ENT-A','Enterprise'
    plan_id: 7,'AX-MT-FREE-M','Free'
--}}

@extends('layout.app')
@push('custom-head')
    <style>
        html body .content .content-wrapper {
            margin: 0.8rem 1.2rem;
        }

        .card-block {
            padding-right: 1.0rem !important;
            padding-left: 1.0rem !important;
        }

        .card-header1 {
            margin-bottom: -10px;
        }
    </style>
@endpush
@section('content')
    <div class="card-header1">
        <h4 class="card-title" style="margin-left:14px">{{ __('admin.title.homepage') }} </h4>
    </div>
    <br>
    <div class="card-block">
        <div class="row">
            <div class="col-sm-4 small-screen" style="text-align:left; display:none">
                <a href="{{ route('mobile') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-dark">
                    <i class="icon-mobile"></i> <br /><span class="text-center"> {{ __('admin.menu.switch_to_mobile') }}
                    </span> </a>
                <a href="{{ route('logout') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-dark">
                    <i class="icon-power3"></i> <br /><span class="text-center"> {{ __('admin.menu.logout') }} </span> </a>
            </div>
        </div>
        <div class="row">
            {{-- Administration --}}
            <div class="col-sm-4" style="text-align:left">
                <a href="#"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-primary btn-darken-1">
                    <i class="icon-user-md"></i> <br /><span class="text-center"> {{ __('admin.menu.admins') }} </span> </a>
                <a href="{{ route('userlist') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.users') }} </span> </a>
                <a href="{{ route('usergrouplist.index') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.user_groups') }} </span> </a>
                <a href="{{ route('grouplist') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.objects') }} </span> </a>
                <a href="{{ route('transparm') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.transparam') }} </span> </a>
                <a href="{{ route('customfields') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.custom_fields') }} </span> </a>
                @if (auth()->user()->type == 'site_owner' || auth()->user()->type == 'site_support')
                    <a href="{{ route('editsite') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.site_settings') }} </span> </a>
                @endif
                <a href="{{ route('numberdefinitionmenu') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.number_definition') }} </span> </a>
                {{-- <a href="{{route('lot_number_definition.index')}}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                <span class="text-left"> {{__('admin.menu.lot_number_definition')}} </span> </a>
            <a href="{{route('order_number_definition.index')}}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                <span class="text-left"> {{__('admin.menu.order_number_definition')}} </span> </a> --}}
                {{-- Maintenance --}}
                <a href="#"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-primary btn-darken-1">
                    <i class="icon-settings"></i> <br /><span class="text-center"> {{ __('admin.menu.maintenance') }}
                    </span> </a>
                <a href="{{ route('mastermenu') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.master_files') }} </span> </a>
                <a href="{{ route('transmenu') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.transactions') }} </span> </a>
                {{-- <a href="{{ route('utilitiesmenu') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.utilities') }} </span> </a> --}}
            </div>
            <div class="col-sm-4" align="left">
                {{-- Barcode Admin --}}
                <a href=""
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-primary btn-darken-1">
                    <i class="icon-barcode"></i> <br /><span class="text-center"> {{ __('admin.menu.barcode_admin') }}
                    </span> </a>
                <a href="{{ route('labellist') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.labels') }} </span> </a>
                    @if( \Gate::allows('hasObjectLabel'))
                    <a href="{{ route('objectLabel') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.object_label') }} </span> </a>
                        @endif
                @if (config('icapt.bartender') == false)
                    <a href="{{ route('qrcodes.index') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.qrcodes') }} </span> </a>
                @endif


                {{-- Reports --}}
                <a href="#"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-primary btn-darken-1">
                    <i class="icon-bar-chart"></i> <br /><span class="text-center"> {{ __('admin.menu.reports') }}
                    </span> </a>

                {{-- System --}}
                <a href="#"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-primary btn-darken-1">
                    <i class="icon-cog"></i> <br /><span class="text-center"> System </span> </a>
                <a href="{{ route('importSchedule.index') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> Import Schedule </span> </a>
                <a href="{{ route('inventoryMenu') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.inventory') }} </span> </a>
                @if ($plan != 1 && $plan != 4)
                    <a href="{{ route('productionMenu') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.production') }} </span> </a>
                @endif
                <a href="{{ route('transactionMenu') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.transaction') }} </span> </a>








            </div>
            <div class="col-sm-4" align="left">
                {{-- History --}}
                <a href="#"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block btn-primary btn-darken-1">
                    <i class="icon-history"></i> <br /><span class="text-center"> {{ __('admin.menu.history') }}
                    </span></a>
                <a href="{{ route('MaterialTransaction') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.matl_trans') }} </span> </a>
                @if ($plan != 1 && $plan != 4)
                    <a href="{{ route('JobTransaction') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.job_trans') }} </span> </a>
                @endif
                @if (config('icapt.client_prefix') == 'OceanCash')
                    <a href="{{ route('ProductionTransaction') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block"> <span
                            class="text-left">{{ __('admin.menu.production_trans') }}</span></a></li>
                @endif


                @if ($plan != 1 && $plan != 2 && $plan != 4 && $plan != 5 && $plan != 7)
                    <a href="{{ route('MachineTransaction') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.machine_trans') }} </span> </a>
                @endif
                @if ($plan != 1 && $plan != 4)
                    <a href="{{ route('Labor') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.active_labor_runs') }} </span> </a>
                @endif
                @if ($plan == 3 || $plan == 6)
                    <a href="{{ route('ActiveMachineRuns') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.active_machine_runs') }} </span> </a>
                @endif
                <a href="{{ route('OverrideQtyHistory') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.value_change_log') }} </span> </a>
                <a href="{{ route('ImportExportLog') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.import_log') }} </span> </a>
                <a href="{{ route('ExportLog') }}" class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.export_log') }} </span> </a>
                @if ($sap_integration == 1)
                @endif
                @if (\Gate::allows('hasApiLog'))
                    <a href="{{ route('ApiIntegrationLog') }}"
                        class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                        <span class="text-left"> {{ __('admin.menu.integration_log') }} </span> </a>
                @endif
                <a href="{{ route('background_tasks') }}"
                    class="mr-1 mb-1 btn btn-outline-secondary btn-min-width btn-block">
                    <span class="text-left"> {{ __('admin.menu.background_tasks') }} </span> </a>

            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // This will fire when document is ready:
            $(window).resize(function() {
                // This will fire each time the window is resized:
                if ($(window).width() >= 768) {
                    // if larger or equal
                    $('.small-screen').hide();
                } else {
                    // if smaller
                    $('.small-screen').show();
                }
            }).resize(); // This will simulate a resize to trigger the initial run.
        });
    </script>
@endsection
