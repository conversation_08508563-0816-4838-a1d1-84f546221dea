@extends('layout.mobile.app')
@section('content')
@section('title', __('Customer Return'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<script>
    function historyBack() {

        var URLRedirect = "{{ $url }}";
        url = URLRedirect.replace(/&amp;/g, "&");

        window.location.href = url;

    }
</script>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="picknshipform" method="POST" action="{{ route('ReturnCustomer') }}">
            <input type="hidden" id="reason_class" value="CustomerReturn" name="reason_class">
            <input type="hidden" name="batch_id" value="{{ $batch_id }}">
            @csrf
            <div class="form-body">
                {{-- @include('components.form.scan_input', ['type' => 'inventory']) --}}

                <div class="form-group row">
                    <input type="hidden" name="whse_num" id="whse_num"
                        value="{{ old('whse_num', $Cust_Return->whse_num) }}">


                    <input type="hidden" name="trans_type" id="trans_type" value="Customer Return">


                    @foreach (@$lot_num_cobatch as $lot => $qtycobatch)
                        <input type="hidden" name="lot_co_batch_{{ $lot }}"
                            id="lot_co_batch_{{ $lot }}" value="{{ $qtycobatch }}">
                    @endforeach


                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="return_num">{{ __('mobile.label.return_num') }}</label>
                    <div class="col-xs-7 col-md-5 col-lg-5">
                        <div class="input-group">
                            <input size="9" style="text-align:left;" readonly type="text" id="return_num"
                                name="return_num" class="form-control border-primary"
                                value="{{ old('return_num', $Cust_Return->return_num) }}">


                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input style="text-align:left;" readonly type="text" id="return_line" name="return_line"
                            class="form-control border-primary"
                            value="{{ old('return_line', $Cust_Return->return_line) }}">
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="co_line">{{ __('mobile.label.cust_num') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input style="text-align:left;" readonly type="text" id="cust_num" name="cust_num"
                                class="form-control border-primary" value="{{ old('cust_num', $cust_num) }}">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="cust_name"></label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <textarea readonly type="text" id="cust_name" class="form-control border-primary">{{ old('cust_name', $cust_name) }}</textarea>
                        </div>
                    </div>
                </div>



                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input style="text-align:left;" id="item_num" name="item_num" readonly type="text"
                                class="form-control border-primary"
                                value="{{ old('item_num', $Cust_Return->item_num) }}">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="item_desc"></label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <textarea readonly type="text" id="item_desc" class="form-control border-primary">{{ old('item_desc', $Cust_Return->item_desc) }}</textarea>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_required">{{ __('mobile.label.qty_required') }}</label>
                    <div class="col-xs-7 col-md-5 col-lg-5">
                        <div class="input-group">
                            <input type="hidden" id="qty_balance" name="qty_balance"
                                class="form-control border-primary" style="text-align: right"
                                value="{{ numberFormatPrecision($Cust_Return->qty_required, $unit_quantity_format) }}"
                                readonly>
                            <input type="hidden" id="qty_balance_conv" name="qty_balance_conv"
                                value="{{ numberFormatPrecision($Cust_Return->qty_required, $unit_quantity_format) }}">
                            <input size="1" style="text-align:right;" id="qty_required" name="qty_required"
                                readonly type="text" class="form-control border-primary"
                                value="{{ numberFormatPrecision($Cust_Return->qty_required, $unit_quantity_format) }}">

                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input size="1" style="text-align:left;" id="base_uom" name="base_uom" readonly
                            type="text" class="form-control border-primary"
                            value="{{ old('uom', $Cust_Return->uom) }}">
                    </div>
                </div>

                @if ($Cust_Return->item_num != 'NON-INV')
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" id="loc_num" required class="form-control border-primary"
                                    name="loc_num" placeholder="{{ __('mobile.placeholder.loc_num') }}"
                                    value="{{ old('loc_num') ?? $def_loc }}" data-value="{{ $def_loc }}"
                                    required>
                                <span id="locnumnotexist"></span>
                                <span id="checkLoc"></span>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.locations') }}" tabindex="-1"
                                onclick="selectionNull('/getLocNoPicking','whse_num','loc_num','loc_num');modalheader(this.id,this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    </div>
                @else
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                            for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" id="loc_num" required class="form-control border-primary"
                                    name="loc_num" placeholder="{{ __('mobile.placeholder.loc_num') }}"
                                    value="{{ old('loc_num') }}" readonly>

                            </div>
                        </div>

                    </div>
                @endif

                @if ($lot_tracked == 1)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" class="form-control border-primary" onchange="showNewLot()"
                                    maxlength="50" name="lot_num" id="lot_num" value="{{ old('lot_num') }}"
                                    placeholder="{{ __('mobile.placeholder.lot_num') }}" required>
                                <span id="loc_info"></span>
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <input hidden id="no_recomendation" value="null">
                            <button type="button" name="{{ __('mobile.list.lots') }}"
                                onClick= "selectionNull('/getLot','whse_num,item_num,lot_num,no_recomendation','lot_num','lot_num');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" required data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required" id="expired_track"
                            for="expiry_date">{{ __('mobile.label.expiry_date') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" value="{{ old('expiry_date') }}" id="expiry_date"
                                    name="expiry_date"
                                    class="form-control border-primary date-picker-only min-today-date"
                                    placeholder="{{ __('admin.label.expiry_date') }}" readonly>
                                {{-- <input type="text" class="form-control border-primary min-today-date" name="expiry_date" id="expiry_date" placeholder="{{__('admin.label.expiry_date')}}" required> --}}
                            </div>
                            <span id="expiry_date_info"></span>
                        </div>
                    </div>
                @endif

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="qty_to_return">{{ __('mobile.label.qty_to_return') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input inputmode="numeric" type="text" style="text-align:right"
                                value="{{ old('qty') }}" id="qty" required
                                class="form-control border-primary number-format" name="qty"
                                placeholder="{{ __('mobile.label.qty_to_return') }}">
                            <input type="hidden" id="qty_conv" name="qty_conv" value="{{ old('qty_conv') }}">

                            <input type="hidden" name="max_qty_input" id="max_qty_input"
                                value="{{ numberFormatPrecision($Cust_Return->qty_required, $unit_quantity_format) }}">

                            <input type="hidden" name="conv_factor" id="conv_factor"
                                value="{{ old('conv_factor', 1) }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input style="text-align:left;" type="text" class="form-control border-primary"
                            id="uom" name="uom" value="{{ old('uom', $Cust_Return->uom) }}">
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="UOMs"
                            onclick="selection('/getCOUOMConv','item_num,cust_num','uom','uom');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="reason">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required"
                        for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" name="reason_code" id="reason_code"
                                value="{{ old('reason_code') }}" onchange="clickSelf(this.id)"
                                class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.reason_code') }}" required>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="getRequest5" name="{{ __('mobile.list.reasons') }}"
                            onClick="selectionwithcheckreasoncode('/getReasonCode/CustReturn', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{ __('admin.menu.reason_codes') }}');"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="document_num">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="document_num">{{ __('mobile.label.doc') }}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="document_num" id="document_num" autocomplete="off"
                                class="form-control border-primary" placeholder="{{ __('mobile.placeholder.doc') }}"
                                value="{{ old('document_num') }}" maxlength="30">
                        </div>
                    </div>
                </div>

                @if ($sap_intergation == 1)

                    <div class="form-group row" id="sap_base_entry">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" required
                            for="sap_base_entry">{{ __('mobile.label.sap_base_entry') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" autocomplete="off" value="{{ old('sap_base_entry') }}"
                                    name="sap_base_entry" id="sap_base_entry" class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.sap_base_entry') }}" required>
                            </div>
                        </div>

                    </div>

                    <div class="form-group row" id="sap_base_line">
                        <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" required
                            for="sap_base_entry">{{ __('mobile.label.sap_base_line') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" autocomplete="off" value="{{ old('sap_base_line') }}"
                                    name="sap_base_line" id="sap_base_line" class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.sap_base_line') }}" required>
                            </div>
                        </div>

                    </div>

                    {{-- Document num --}}
                    @if ($sap_intergation == 1)
                        <div class="form-group row " id="document_num">
                            <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                                for="document_num">{{ __('mobile.label.doc') }}</label>
                            <div class="col-xs-7 col-md-7 col-lg-7">
                                <div class="input-group">
                                    <input type="text" name="document_num" required id="document_num"
                                        autocomplete="off" class="form-control border-primary"
                                        placeholder="{{ __('mobile.placeholder.doc') }}"
                                        value="{{ old('document_num') }}" maxlength="30">
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="form-group row" id="document_num">
                            <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                                for="document_num">{{ __('mobile.label.doc') }}</label>
                            <div class="col-xs-7 col-md-7 col-lg-7">
                                <div class="input-group">
                                    <input type="text" name="document_num" id="document_num" autocomplete="off"
                                        class="form-control border-primary"
                                        placeholder="{{ __('mobile.placeholder.doc') }}"
                                        value="{{ old('document_num') }}" maxlength="30">
                                </div>
                            </div>
                        </div>
                    @endif


                    {{-- Last Receive? --}}
                    <div class="form-group row" id="last_return_pair">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                            for="last_return">{{ __('mobile.label.last_return') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="radio" name="last_return" value="Yes" id="last_return">
                                {{ __('mobile.option.yes') }} &emsp;&emsp;&emsp;
                                <input type="radio" name="last_return" value="No" id="last_return_no" checked>
                                {{ __('mobile.option.no') }}
                            </div>
                        </div>
                    </div>

                @endif


                <br>

                <div class="form-actions center">
                    <button type="button" class="btn btn-warning mr-1" onclick="historyBack()">

                        <i class="icon-cross2"></i> {{ __('mobile.button.cancel') }}
                    </button>







                    <button type="submit" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.process') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<!-- <script src="https://ajax.aspnetcdn.com/ajax/jquery.validate/1.19.0/jquery.validate.min.js"></script> -->
@include('util.validate_uom')

<script>
    //sap_base_entry sap_base_line document_num last_return_pair

    // add required class if expired istracked
    if ({{ $expired_tracked }} == 0) {
        $("#expired_track").removeClass("required");
    }




    $("#loc_info").html('');
    $("#checkLoc").html('');
    $("#locnumnotexist").html('');

    jQuery(function($) {
        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
        $.validator.addMethod('minStrict', function(value, el, param) {
            return value > param;
        });


        $.validator.addMethod('locvalidate', function(value, el, param) {
            item = $('#item_num').val();
            if (item == "NON-INV") {
                return true;
            }
            return value ? true : false;
        });

        var errorMessage = 'Default error message';
        var errlotMessge = 'Lot does not exist';

        $("#picknshipform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $(".submitloader").attr('disabled', false);
            }
        });

        $("#picknshipform").validate({
            onchange: true,
            // onfocusout:false,
            rules: {
                loc_num: {
                    required: function(element) {
                        item = $('#item_num').val();

                        if (item == "NON-INV") {
                            return false;
                        }
                        return true;
                    },
                    depends: function(element) {
                        $("#locnumnotexist").html('');
                    }
                    // remote: {
                    //     param: {
                    //         url: "{{ route('LocValidation') }}",
                    //         type: "post",
                    //         data: {
                    //             _token: $('input[name="_token"]').val(),
                    //             whse_num:
                    //                 function () {
                    //                     return $("#whse_num").val();
                    //                 }
                    //         },
                    //         dataFilter: function(data){
                    //             if(data == 'true'){
                    //                 var result = 'true';
                    //                 $.ajax({
                    //                     url: '{{ route('checkLocNotTransit') }}',
                    //                     type: "GET",
                    //                     async: false,
                    //                     data: {
                    //                         whse_num: $("#whse_num").val(),
                    //                         loc_num: $("#loc_num").val(),
                    //                     },
                    //                     dataFilter: function(data){
                    //                         if (data == "transit") {
                    //                             result = 'false';
                    //                             errorMessage = "{{ __('error.mobile.transit_loc') }}";
                    //                         }
                    //                         else{
                    //                             showNewLoc();
                    //                         }
                    //                     }
                    //                 });

                    //                 return result;
                    //             }
                    //             if (disable_create_new_item_location == 0) { // user are allowed to create new location
                    //                 return true;
                    //             }
                    //             errorMessage = "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.loc_num')]) }}";
                    //             return false;
                    //         }
                    //     }
                    //},
                },
                uom: {
                    required: true,
                    uom_validation: [$('#item_num').val(), $('#cust_num').val(), null, null, $(
                        '#co_uom').val()]
                    // remote:{
                    //     url: "{{ route('MiscValidation') }}",
                    //     type: "post",
                    //     data: { _token : $('input[name="_token"]').val() }
                    // }
                },
                qty: {
                    required: true,
                    number: true,
                    number_size: true,

                    minStrict_value: 0,
                    max_value: function() {
                        var qty_required = parseFloat($("#max_qty_input").val().replace(/,/g,
                            ""));
                        return Math.max(0, qty_required);
                        // return parseFloat($("#qty_returnable_conv").val().replace(/,/g,""));
                    }
                },
                reason_code: {
                    required: true,
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val()
                        }
                    }
                },
                /* lot_num: {
                     required: {
                         depends: function (element) {
                             return $("#lot_num").is(":visible");
                         }
                     },
                     remote: {
                         url: "{{ route('checkIssuedLot') }}",
                         type: "GET",
                         data: {
                             from_module:
                                 function () {
                                     return $("#from_module").val();
                                 },
                             whse_num:
                                 function() {
                                     return $("#whse_num").val();
                                 },
                             ref_num:
                                 function() {
                                     return $("#co_num").val();
                                 },
                             ref_line:
                                 function() {
                                     return $("#co_line").val();
                                 },
                             ref_release:
                                 function() {
                                     return $("#co_rel").val();
                                 },
                             lot_num:
                                 function() {
                                     var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';

                                     if(disable_create_new_item_location==0){
                                        var errlotMessge = 'ok';
                                     }else{
                                        var errlotMessge = $("#lot_num").val();
                                     }
                                 },
                         }
                     }
                 },*/
            },
            messages: {
                // loc_num:{
                //     remote: function(){ return errorMessage; }
                // },
                reason_code: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.reason_code')]) }}"
                },
                uom: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                },
                qty: {
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.label.qty')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}",

                    minStrict_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty')]) }} {0}",
                    max_value: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.label.qty')]) }} {0}"
                },
                {{-- lot_num: {
                   remote: function(){ return errlotMessge; }
                }, --}}
            },
            submitHandler: function(form) {
                $(".submitloader").attr('disabled', true);

                $.ajax({
                    url: '{{ route('checkExpiryDateCOPicking') }}',
                    type: "GET",
                    data: {
                        item_num: $("#item_num").val(),
                        lot_num: $("#lot_num").val(),
                        prompt: 'true',
                    },
                    success: function(data) {
                        // If false, submit form
                        if (data == "false") {
                            $(".pageloader").css("display", "block");
                            $(".submitloader").attr("disabled", true);
                            setTimeout(function() {
                                form.submit();
                            }, 300);
                        }
                        // else, prompt confirmation
                        else {
                            console.log(data);
                            var m = '{{ __('mobile.message.lot_expired_prompt') }}';
                            m = m.replace(':resource', $("#lot_num").val());
                            m = m.replace(':resource2', data['expiry_date']);
                            Swal.fire({
                                title: 'Warning',
                                text: m,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes'
                            }).then((result) => {
                                if (result.value) {
                                    $(".pageloader").css("display", "block");
                                    $(".submitloader").attr("disabled", true);
                                    setTimeout(function() {
                                        form.submit();
                                    }, 300);
                                } else {
                                    $(".submitloader").attr('disabled', false);
                                    return false;
                                }
                            });
                        }
                    }
                });

















                ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
                url = ajaxurl.replace('loc_num', btoa($("#loc_num").val()));
                $.get(url, function(data) {
                    if (data == 'not exist') {
                        // Disable create new loc = Yes = 1
                        if (disable_create_new_item_location == 1) {
                            //$(".submitloader").attr("disabled", true);
                            //$("#locnumnotexist").html('<span style="color:red;">{{ __('mobile.message.notallow_new_item_location') }}</span>');

                        }
                        // Disable create new loc = No = 0
                        if (disable_create_new_item_location == 0) {
                            var m = '{{ __('admin.message.surecreate') }}';
                            m = m.replace(':resource', "Item Location");
                            Swal.fire({
                                title: 'Warning',
                                text: m,
                                icon: 'warning',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: 'Yes'
                            }).then((result) => {
                                if (result.value) {
                                    $(".pageloader").css("display", "block");
                                    $(".submitloader").attr("disabled", true);
                                    form.submit();
                                } else {
                                    $(".submitloader").attr("disabled", false);
                                    return false;
                                }
                            });

                        }

                    } else {
                        //$(".pageloader").css("display", "block");
                        // $(".submitloader").attr("disabled", false);
                        // form.submit();
                    }

                });
                // ajaxurl ="{{ route('itemlocv', ['item_num', 'loc_num', 'whse_num']) }}";
                // url = ajaxurl.replace('item_num', $("#item_num").val());
                // url = url.replace('loc_num', $("#loc_num").val());
                // url = url.replace('whse_num', $("#whse_num").val());
                // $.get(url, function(data){
                //     if(data == 'not exist') {
                //         var m = '{{ __('admin.message.surecreate') }}';
                //         m = m.replace(':resource', "Item Location");
                //         Swal.fire({
                //             title: 'Warning',
                //             text: m,
                //             icon: 'warning',
                //             showCancelButton: true,
                //             confirmButtonColor: '#3085d6',
                //             cancelButtonColor: '#d33',
                //             confirmButtonText: 'Yes'
                //         }).then((result) => {
                //             if (result.value) {
                //                 $(".pageloader").css("display", "block");
                //                 $(".submitloader").attr("disabled", true);
                //                 form.submit();
                //             }
                //         });
                //     }
                //     else {
                //         $(".pageloader").css("display", "block");
                //         $(".submitloader").attr("disabled", true);
                //         form.submit();
                //     }
                // });
                // $(".pageloader").css("display", "block");
                // form.submit();
            }
        });

        $("input:radio[name='last_return']").change(function() {
            var _val = $(this).val();
            if (_val == "Yes") {

                Swal.fire({
                    title: 'Warning',
                    icon: 'warning',
                    html: '{!! __('admin.message.confirm_last_return_co') !!}',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes'
                }).then((result) => {
                    if (result.value) {
                        return false;
                    } else {
                        $("#last_return_no").prop("checked", true);
                    }
                });
            }
        });


    });

    function showNewLoc() {

        /* var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';

         $("#loc_info").html('');
         $("#checkLoc").html('');
         $("#locnumnotexist").html('');

         ajaxurl ="{{ route('itemlocv', ['item_num', 'loc_num', 'whse_num']) }}";
         url = ajaxurl.replace('item_num', $("#item_num").val());
         url = url.replace('loc_num', $("#loc_num").val());
         url = url.replace('whse_num', $("#whse_num").val());
         $.get(url, function(data){
             if(data == 'not exist' && $("#lot_num").is(":visible") == false && disable_create_new_item_location == 0) {
                 $("#locnumnotexist").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_item_location') }}</small>');
             }
             if(data == 'not exist' && $("#lot_num").is(":visible") == true && disable_create_new_item_location == 0) {
                 $("#locnumnotexist").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_lot_location2') }}</small>');
             }
         });*/

        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
        $("#locnumnotexist").html('');
        ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
        url = ajaxurl.replace('loc_num', btoa($("#loc_num").val()));

        $.get(url, function(data) {

            if (data == 'not exist') {
                // Disable create new loc = Yes = 1
                if (disable_create_new_item_location == 1) {
                    $(".submitloader").attr("disabled", true);
                    $("#locnumnotexist").html(
                        '<span style="color:red;">{{ __('mobile.message.notallow_new_item_location') }}</span>'
                    );

                }
                // Disable create new loc = No = 0
                if (disable_create_new_item_location == 0) {
                    $(".submitloader").attr("disabled", false);
                    $("#locnumnotexist").html(
                        '<i class="icon-info"></i> <small>{{ __('mobile.message.new_item_location') }}</small>'
                    );

                }

            } else {
                $(".submitloader").attr("disabled", false);
            }

        });



    }


    function showNewLot() {
        $("#loc_info").html('');
        $("#expiry_date_info").html("");
        $("#expiry_date").prop('readonly', false);
        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';

        if ($("#lot_num").val() || $("#loc_num").val()) {

            ajaxurl = "{{ route('lotitemv', ['lot_num', 'item_num', 'whse_num']) }}";
            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
            url = url.replace('item_num', btoa($("#item_num").val()));
            url = url.replace('whse_num', btoa($("#whse_num").val()));

            $.get(url, function(data) {
                if (data == 'not exist') {
                    if (disable_create_new_item_location == 1) {

                        $("#loc_info").html('<span style="color:red;"> Lot does not exist.</span>');
                    } else {
                        $("#loc_info").html(
                            '<i class="icon-info"></i> <small>{{ __('mobile.message.new_lot_location') }}</small>'
                        );
                    }
                }

            });

            $.ajax({
                url: '{{ route('lot_item_expiry_date_exist') }}',
                type: "GET",
                data: {
                    lot_num: $("#lot_num").val(),
                    item_num: $("#item_num").val(),
                },
                success: function(data) {
                    if (data) {
                        $("#expiry_date").val(data);
                        if ($("#expiry_date").val() != "") {
                            $("#expiry_date").removeClass('date-picker-only');
                            $("#expiry_date").prop('readonly', true);
                        } else {
                            $("#expiry_date").addClass('date-picker-only');

                            $("#expiry_date").prop('readonly', false);
                            reodeDatePicke("#expiry_date");
                        }
                    } else {
                        $("#expiry_date").addClass('date-picker-only');
                        $("#expiry_date").prop('readonly', false);
                        $("#expiry_date").val($("#expiry_date").data('date-default-shelf-life'));
                        reodeDatePicke("#expiry_date");

                    }
                }
            });
        } else {
            $("#expiry_date").removeClass('date-picker-only');
            $("#expiry_date").prop('readonly', true);
            $("#item_num").trigger('change');
        }
        $("#lot_num").focus();
    }

    $(document).ready(function() {
        $("#loc_info").html('');
        $("#checkLoc").html('');
        $("#locnumnotexist").html('');
        $("#max_qty_input").val($("#qty_required").val());
        $("#expiry_date").removeClass('date-picker-only');

        if ($("#loc_num").val() == '') {
            $("#loc_info").html('');
            $("#checkLoc").html('');
            $("#locnumnotexist").html('');
        }

        $("#item_num").on('change', function() {
            ajaxurl = "{{ route('item.expiry_tracked', 'item_num') }}";
            url = ajaxurl.replace('item_num', btoa($("#item_num").val()));

            $.get(url, function(data) {
                $("#expiry_date").prop('required', false);
                $("label[for='expiry_date']").removeClass('required');

                if (data['expiry_tracked'] == 1) {
                    // Set expiry_date's required to true
                    $("#expiry_date").prop('required', true);
                    $("label[for='expiry_date']").addClass('required');
                }
                if (data['default_shelf_life']) {
                    // Predefined expiry_date based on default_shelf_life. User can change it if they want to.
                    $('.min-today-date').datepicker('setDate', '+' + data[
                        'default_shelf_life'] + 'd');

                    // Default shelf life is put back when change to new lot that has no due date
                    $("#expiry_date").attr('data-date-default-shelf-life', $("#expiry_date")
                        .val());
                }
            });
        });

        $("#item_num").trigger('change');

        $("#uom").on("change", function() {

            var vend_num = "null";
            var lot_num = "undefined";
            if ($("#lot_num").val() != "") {
                lot_num = $("#lot_num").val();
            }

            var selectuom = $("#uom").val();
            if (selectuom != "") {
                let validate = validateConvUOM($('#item_num').val(), $('#cust_num').val(), null, null,
                    $('#base_uom').val(), selectuom);

                validate.then(function(resp) {
                        $("#qty").val('');
                    },
                    function(err) {
                        // false
                        $("#qty").val('');
                        $("#uom").val($("#base_uom").val());
                    }).finally(function() {
 $("#qty").val('');
  $("#qty-error").hide();
                    calculateQtyLimit($("#base_uom").val(), $("#qty_balance").val(), $("#uom")
                        .val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num")
                        .val(), lot_num, $("#base_uom").val(), $("#cust_num").val(), "null",
                        '{{ __('mobile.nav.co_return') }}', "");
                });
            }

            // displayConvQty('/displayQuantityConverted','co_uom,item_num,qty_returnable,uom','qty_returnable_conv');
            // displayConvQty('/displayQuantityConverted','uom,item_num,qty,co_uom','qty_conv');
        });

        // $("#qty").on("change", function() {
        //     displayConvQty('/displayQuantityConverted','uom,item_num,qty,co_uom','qty_conv');
        // });

        // $("#loc_num").on("change", function() {
        //     // $("#lot_num, #reason_code, #qty").val('');
        //     $("#loc_info").html("");
        //     $("#checkLoc").html("");
        //     $("#locnumnotexist").html('');

        //     if ($("#loc_num").val() == "") {
        //         $("#loc_info").html("");
        //         $("#checkLoc").html("");
        //         $("#locnumnotexist").html('');
        //     }
        // });

        $("#loc_num").on("change", function() {
            // $("#lot_num, #reason_code, #qty").val('');
            $("#checkLoc").html("");
            $("#locnumnotexist").html('');

            if ($("#loc_num").val() != "") {

                // Send error if manually type object that is transit location
                $.ajax({
                    url: '{{ route('checkLocNotTransitpickLocs') }}',
                    type: "GET",
                    data: {
                        whse_num: $("#whse_num").val(),
                        loc_num: $("#loc_num").val(),
                    },
                    success: function(data) {

                        // var data = JSON.parse(data);
                        console.log(data);
                        if (data.length > 0) {

                            if (data[0].pick_locs == 1) {

                                //  $("#loc_info").html("");
                                // $("#locnumnotexist").html('');
                                //  $("#qty").val("");
                                //  $("#qty_available").val("0");
                                //errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                $("#checkLoc").html(
                                    '<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>'
                                );
                                $(".submitloader").attr("disabled", true);
                                //return false;
                            } else if (data[0].loc_type == 'T') {
                                //$("#loc_info").html("");
                                //$("#locnumnotexist").html('');
                                // $("#qty").val("");
                                // $("#qty_available").val("0");
                                //errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                $("#checkLoc").html(
                                    '<span style="color:red;"> {{ __('error.mobile.validate_transit') }}</span>'
                                );
                                $(".submitloader").attr("disabled", true);
                                //return false;
                            } else {
                                if (data[0].loc_status == 0) {


                                    $("#checkLoc").html(
                                        '<span style="color:red;"> {{ __('error.mobile.inactive_loc') }}</span>'
                                    );
                                    $(".submitloader").attr("disabled", true);
                                } else {
                                    showNewLoc();
                                    $("#checkLoc").html("");
                                    $(".submitloader").attr("disabled", false);
                                }
                            }

                            // if (data == "transit") {
                            //     $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.transit_loc') }}</span>');
                            //     $(".submitloader").attr("disabled", true);
                            // }
                            // else {
                            //     showNewLoc();
                            //     $("#checkLoc").html("");
                            //     $(".submitloader").attr("disabled", false);
                            // }
                        } else {
                            showNewLoc();
                            //   $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>');
                            $(".submitloader").attr("disabled", true);


                        }
                    }
                });
            }
        });

        $("#lot_num").on('change', function() {
            // Change qty returnable based on selected Lot
            $.ajax({
                url: '{{ route('getIssuedLotQty') }}',
                type: 'GET',
                data: {
                    from_module: $("#from_module").val(),
                    whse_num: $("#whse_num").val(),
                    ref_num: $("#co_num").val(),
                    ref_line: $("#co_line").val(),
                    ref_release: $("#co_rel").val(),
                    lot_num: $("#lot_num").val(),
                },
                success: function(data) {
                    // var qty_co_batch = $("#qty_co_batch").val();
                    // var dataDB = parseFloat(data).toFixed({{ $unit_quantity_format }});
                    // var balanceqty = dataDB - qty_co_batch;
                    var lot_num = $("#lot_num").val();
                    var lot_num_cobatch = $("#lot_co_batch_" + lot_num + "").val();
                    var qty_co_batch = $("#lot_co_batch").val();
                    var dataDB = numberFormatPrecision(data, {{ $unit_quantity_format }});
                    var sap_intergation = {{ $sap_intergation }}
                    if (sap_intergation == 1 && (lot_num_cobatch > 0 || lot_num_cobatch !=
                            undefined)) {
                        var balanceqty = dataDB - lot_num_cobatch;

                    } else {
                        var balanceqty = dataDB;

                    }
                    var disable_create_new_item_location =
                        '{{ $disable_create_new_item_location }}';
                    if (disable_create_new_item_location == 1) {

                        $("#qty_returnable").val(balanceqty);
                        $("#qty_returnable_conv").val(balanceqty);

                        displayConvQty('/displayQuantityConverted',
                            'co_uom,item_num,qty_returnable,uom', 'qty_returnable_conv');
                        displayConvQty('/displayQuantityConverted',
                            'uom,item_num,qty,co_uom', 'qty_conv');
                    }
                }
            });
        });



        $("#expiry_date").on('change', function() {
            if ($("#expiry_date").val() != "") {

                var ExpiryDate = $("#expiry_date").val();
                var dateFormat = '{{ App\SiteSetting::first()->getJsInputDateFormat() }}';
                validatedateformat(ExpiryDate, dateFormat, 'expiry_date');
            } else {
                $("#expiry_date").addClass('date-picker-only');
                $("#expiry_date").prop('readonly', false);
            }
        });
    });

    function submitForm(payload) {
        var form = document.createElement('form');
        form.style.visibility = 'hidden';
        form.method = 'POST';
        form.action = "{{ route('CustomerReturnDetails') }}";

        payload._token = $("[name=_token]").val();

        if (payload.butt_name) {
            delete payload.butt_name;
        }

        $.each(payload, function(i, v) {
            var input = document.createElement('input');
            input.name = i;
            input.value = v;
            form.appendChild(input); // add key/value pair to form
        });

        document.body.appendChild(form); // forms cannot be submitted outside of body
        form.submit(); // send the payload and navigate
    }



    function validatedate(inputText, DateFormat) {
        // format dd/mm/yyyy or in any order of (dd or mm or yyyy) you can write dd or mm or yyyy in first or second or third position ... or can be slash"/" or dot"." or dash"-" in the dates formats
        var invalid = "";
        var dt = "";
        var mn = "";
        var yr = "";
        var k;
        var delm = DateFormat.includes("/") ? "/" : (DateFormat.includes("-") ? "-" : (DateFormat.includes(".") ? "." :
            ""));
        var f1 = inputText.split(delm);
        var f2 = DateFormat.split(delm);
        for (k = 0; k <= 2; k++) {
            dt = dt + (f2[parseInt(k)] == "dd" ? f1[parseInt(k)] : "");
            mn = mn + (f2[parseInt(k)] == "mm" ? f1[parseInt(k)] : "");
            yr = yr + (f2[parseInt(k)] == "yyyy" ? f1[parseInt(k)] : "");
        }
        var mn_days = "0-31-" + (yr % 4 == 0 ? 29 : 28) + "-31-30-31-30-31-31-30-31-30-31";
        var days = mn_days.split("-");
        if (f1.length != 3 ||
            mn.length > 2 ||
            dt.length > 2 ||
            yr.length != 4 ||
            !(parseInt(mn) >= 1 && parseInt(mn) <= 12) ||
            !(parseInt(yr) >= parseInt(1900) && parseInt(yr) <= parseInt(2100)) ||
            !(parseInt(dt) >= 1 && parseInt(dt) <= parseInt(days[parseInt(mn)]))) {
            invalid = "true";
        }

        if (invalid == "true") {
            document.getElementById('expiry_date').value = "";
            $("#expiry_date_info").html('<span style="color:red;">Invalid Date Format</span>');
        } else {
            $("#expiry_date_info").html("");
        }
    }
</script>

@include('errors.maxchar')
@include('util.selection')
@include('util.datepicker')
@include('Pallet.palletMobileValidation')
@endsection()
