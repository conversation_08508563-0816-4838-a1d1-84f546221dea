<?php

namespace App\DataTables\Reports;

use App\CustomerReturnLine;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Html\Editor\Editor;
use App\SiteSetting;
use DB;
use Carbon\Carbon;
use App\View\TparmView;
use App\DataTables\BaseDataTable;

class CoPickingvsShippingStatusDataTable extends BaseDataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        
        if(request('status')){
            $query->where('customer_return_lines.status', request('status'));
        }

        if(request('from_return_num')){
            $query->where('customer_return_lines.return_num', '>=', request('from_return_num'));
        }
        if(request('to_return_num')){
            $query->where('customer_return_lines.return_num', '<=', request('to_return_num'));
        }

        if(request('from_cust_num')){
            $query->where('customer_returns.cust_num', '>=', request('from_cust_num'));
        }
        if(request('to_cust_num')){
            $query->where('customer_returns.cust_num', '<=', request('to_cust_num'));
        }

        if(request('from_whse_num')){
            $query->where('customer_return_lines.whse_num', '>=', request('from_whse_num'));
        }
        if(request('to_whse_num')){
            $query->where('customer_return_lines.whse_num', '<=', request('to_whse_num'));
        }

        if(request('from_item_num')){
            $query->where('customer_return_lines.item_num', '>=', request('from_item_num'));
        }
        if(request('to_item_num')){
            $query->where('customer_return_lines.item_num', '<=', request('to_item_num'));
        }

        if(request('from_doc_date')){
            $date_input = Carbon::createFromFormat(SiteSetting::getInputDateFormat(), request('from_doc_date'));
            $start_date = $date_input->format('Y-m-d');
            
            $query->whereDate('customer_returns.doc_date', '>=', $start_date);
        }
        if(request('to_doc_date')){
            $date_input = Carbon::createFromFormat(SiteSetting::getInputDateFormat(), request('to_doc_date'));
            $end_date = $date_input->format('Y-m-d');
            
            $query->whereDate('customer_returns.doc_date', '<=', $end_date);
        }

        $datatables = datatables()
            ->of($query)
            ->editColumn('status', function ($q) {
                
                if ($q->status == 'O')
                {
                    return "Open";
                }
                else if ($q->status == 'C')
                {
                    return 'Completed';
                }
                else
                {
                    return null;
                }
            })
            ->editColumn('qty_to_return', function ($q) use ($unit_quantity_format) {
                $intNumber = $q->qty_to_return;
                $qty = numberFormatPrecision($intNumber, $unit_quantity_format);

                return $qty ?? null;
            })
            ->editColumn('qty_returned', function ($q) use ($unit_quantity_format) {
                $intNumber = $q->qty_returned;
                $qty = numberFormatPrecision($intNumber, $unit_quantity_format);

                return $qty ?? null;
            })
            ->editColumn('qty_required', function ($q) use ($unit_quantity_format) {
                $intNumber = $q->qty_required;
                $qty = numberFormatPrecision($intNumber, $unit_quantity_format);

                return $qty ?? null;
            })
            ->editColumn('doc_date', function ($q) {
                if ($q->doc_date)
                {
                    $value = Carbon::parse($q->doc_date)->format(SiteSetting::getInputDateFormat());
                    return getDateTimeConverted($value, true, true);
                }

                return $q->doc_date;
            })
            ->filterColumn('doc_date', function ($q, $value) {
                $dataValue = getDateFitlerValue($value);

                $q->where('doc_date', 'like', $dataValue . '%');
            })
            ->filterColumn('cust_num', function ($q, $value) {
                $q->where('customer_returns.cust_num', 'like', $value . '%');
            })
            ->filterColumn('cust_name', function ($q, $value) {
                $q->where('customer_returns.cust_name', 'like', $value . '%');
            })
        ;

        return $datatables;
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\CustomerReturnLine $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(CustomerReturnLine $model)
    {
        // $query = $model->newQuery()->whereIn('trans_type', ['Stock Move From', 'Stock Move To']);
        $query = $model->newQuery()
            ->select(
                'customer_return_lines.return_num', 'customer_return_lines.return_line', 'customer_returns.doc_date', 
                'customer_returns.cust_num', 'customer_returns.cust_name', 'customer_return_lines.status', 
                'customer_return_lines.whse_num', 'customer_return_lines.item_num', 'customer_return_lines.item_desc', 
                'customer_return_lines.qty_to_return', 'customer_return_lines.qty_returned', 
                'customer_return_lines.qty_required', 'customer_return_lines.uom'
            )
            ->leftJoin('customer_returns', function($join) {
                $join->on('customer_returns.return_num', '=', 'customer_return_lines.return_num');
                $join->on('customer_returns.site_id', '=', 'customer_return_lines.site_id');
            })
        ;
        return $query;
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('co_return_status')
            ->columns($this->getColumns())
            ->addTableClass('nowrap table-bordered table-xs')
            ->parameters([
                'responsive' => true,
                'autoWidth' => false,
                'scrollY' => 400,
                'scrollX' => true,
                'scrollCollapse' => true,
            ])
            ->ajax([
                'type' => 'post',
                'data' => 'function(d) {
                                
                                d.status = () => $("#status").val();
                                
                                d.from_return_num = () => $("#from_return_num").val();
                                d.to_return_num = () => $("#to_return_num").val();

                                d.from_cust_num = () => $("#from_cust_num").val();
                                d.to_cust_num = () => $("#to_cust_num").val();

                                d.from_whse_num = () => $("#from_whse_num").val();
                                d.to_whse_num = () => $("#to_whse_num").val();

                                d.from_item_num = () => $("#from_item_num").val();
                                d.to_item_num = () => $("#to_item_num").val();
                                
                                d.from_doc_date = () => $("#from_doc_date").val();
                                d.to_doc_date = () => $("#to_doc_date").val();
                                

                                d._token =  $("meta[name=\'csrf-token\']").attr("content");
                            }',
            ])
            ->dom('Blrtip')
            ->orderBy(0)
            ->orderCellsTop(true)
            ->fixedHeader(true)
            ->deferLoading(false)
            ->buttons(
                // Button::make('export')->className('hidden btn buttons-excel buttons-html5 btn-info')
                Button::make('postExcel')->text('Export to Excel')->className('btn buttons-excel buttons-html5 btn-success'),
                Button::make('postCsv')->text('Export to CSV')->className('btn btn-info')
            );
    }


    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('return_num')->title(__('admin.label.return_num'))->footer(__('admin.label.return_num')),
            Column::make('return_line')->title(__('admin.label.trn_line'))->footer(__('admin.label.trn_line')),
            Column::make('doc_date')->title(__('admin.label.doc_date'))->footer(__('admin.label.doc_date')),
            Column::make('cust_num')->title(__('admin.label.cust_num'))->footer(__('admin.label.cust_num')),
            Column::make('cust_name')->title(__('admin.label.cust_name'))->footer(__('admin.label.cust_name')),
            Column::make('status')->title(__('admin.label.status'))->footer(__('admin.label.status')),
            Column::make('whse_num')->title(__('admin.label.whse_num'))->footer(__('admin.label.whse_num')),
            Column::make('item_num')->title(__('admin.label.item_num'))->footer(__('admin.label.item_num')),
            Column::make('item_desc')->title(__('admin.label.desc'))->footer(__('admin.label.desc')),
            Column::make('qty_to_return')->title(__('admin.label.qty_to_return'))->footer(__('admin.label.qty_to_return')),
            Column::make('qty_returned')->title(__('admin.label.qty_returned'))->footer(__('admin.label.qty_returned')),
            Column::make('qty_required')->title(__('admin.label.qty_required'))->footer(__('admin.label.qty_required')),
            Column::make('uom')->title(__('admin.label.uom'))->footer(__('admin.label.uom')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'CustomerReturnStatus_' . date('YmdHis');
    }

    public function getDataFromQuery()
    {
        return $this->getAjaxResponseData();
    }
}
