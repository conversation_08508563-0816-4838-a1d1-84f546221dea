<?php

namespace App\Http\Controllers\Report;

use App\JobTrans;
use App\MachineTrans;
use App\Job;
use App\JobRoute;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Item;
use App\ItemLoc;
use App\Lotloc;
use Alert;
use PDF;
use DataTables;
use App\SiteSetting;
use DB;
use App\View\TparmView;
use Camroncade\Timezone\Facades\Timezone;

class JobOperHourReportController extends Controller
{
    public function index(){

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error');
        }

        return view('report.prod.joboperhours.index')->with('unit_quantity_format', $unit_quantity_format)->with('report_module_name', 'Job Operation Hours');
    }

    public function getQuery(){
        $leftJoin1 = JobTrans::select(
            'job_num',
            'suffix',
            DB::raw('IFNULL(SUM(hour), 0.00) as setup_time')
        )
        ->where('job_type', 'Job Setup');

        $leftJoin2 = JobTrans::select(
                'job_num',
                'suffix',
                DB::raw('IFNULL(SUM(hour), 0.00) as run_time')
            )
            ->where('job_type', 'Job Run');

        $leftJoin3 = MachineTrans::select(
                'job_num',
                'suffix',
                DB::raw('IFNULL(SUM(hour), 0.00) as machine_time')
            )
            ->where('trans_type', 'Machine Run');

        $leftJoin4 = Job::select(
                'job_num',
                'suffix',
                'qty_released',
                'qty_completed',
                'qty_scrapped',
                'uom'
            );

        $leftJoin5 = JobRoute::select(
                'job_num',
                'suffix',
                DB::raw('IFNULL(SUM(machine_hours_per_piece), 0.00) as machine_hours_per_piece'),
                DB::raw('IFNULL(SUM(labour_hours_per_piece), 0.00) as labour_hours_per_piece')
            );

        $leftJoin1->groupBy('job_num', 'suffix');
        $leftJoin2->groupBy('job_num', 'suffix');
        $leftJoin3->groupBy('job_num', 'suffix');
        $leftJoin4->groupBy('job_num', 'suffix');
        $leftJoin5->groupBy('job_num', 'suffix');

        $query = DB::query()->from('job_trans')
            ->select(
                'job_trans.job_num',
                'job_trans.suffix',
                DB::raw('(CASE WHEN jobs.job_status = "O" THEN "Open" WHEN jobs.job_status = "R" THEN "Released" ELSE "Completed" END) as job_status'),
                'jobs.item_num',
                'items.item_desc',
                'jobs.qty_released',
                'jobs.qty_completed',
                'jobs.qty_scrapped',
                'jobs.uom',
                DB::raw('IFNULL(leftjoin1.setup_time,0.00) as setup_time'),
                DB::raw('IFNULL(leftjoin2.run_time,0.00) as run_time'),
                DB::raw('IFNULL(leftjoin3.machine_time,0.00) as machine_time'),
                DB::raw('IFNULL(TRUNCATE((IFNULL(setup_time, 0.00) + IFNULL(run_time, 0.00) + IFNULL(machine_time, 0.00)),5),0.00) as tot'),
                DB::raw('IFNULL(IFNULL(run_time, 0.00) / IFNULL(leftjoin4.qty_completed, 0.00), 0.00) as ave_labour_run_time_per_piece'),
                DB::raw('IFNULL(IFNULL(machine_time, 0.00) / IFNULL(leftjoin4.qty_completed, 0.00), 0.00) as ave_machine_run_time_per_piece'),
                DB::raw('IFNULL((IFNULL(setup_time, 0.00) + IFNULL(run_time, 0.00) + IFNULL(machine_time, 0.00)) / IFNULL(leftjoin4.qty_completed, 0.00), 0.00) as ave_total_run_per_piece'),
                'leftJoin5.machine_hours_per_piece',
                'leftJoin5.labour_hours_per_piece'
            )
            ->leftJoinSub($leftJoin1, 'leftjoin1', function($join){
                $join->on('leftjoin1.job_num', '=', 'job_trans.job_num')->on('leftjoin1.suffix', '=', 'job_trans.suffix');
            })
            ->leftJoinSub($leftJoin2, 'leftjoin2', function($join){
                $join->on('leftjoin2.job_num', '=', 'job_trans.job_num')->on('leftjoin2.suffix', '=', 'job_trans.suffix');
            })
            ->leftJoinSub($leftJoin3, 'leftjoin3', function($join){
                $join->on('leftjoin3.job_num', '=', 'job_trans.job_num')->on('leftjoin3.suffix', '=', 'job_trans.suffix');
            })
            ->leftJoin('jobs',function($join){
                $join->on('jobs.job_num', '=', 'job_trans.job_num')->on('jobs.suffix','=','job_trans.suffix');
            })
            ->leftJoin('job_routes',function($join){
                $join->on('job_routes.job_num', '=', 'job_trans.job_num')->on('job_routes.suffix','=','job_trans.suffix');
            })
            ->leftJoinSub($leftJoin4, 'leftJoin4', function($join){
                $join->on('leftJoin4.job_num', '=', 'job_trans.job_num')->on('leftJoin4.suffix','=','job_trans.suffix');
            })
            ->leftJoinSub($leftJoin5, 'leftJoin5', function($join){
                $join->on('leftJoin5.job_num', '=', 'job_trans.job_num')->on('leftJoin5.suffix','=','job_trans.suffix');
            })
            ->leftJoin('items',function($join){
                $join->on('items.item_num', '=', 'jobs.item_num')->on('items.site_id', '=', 'jobs.site_id');
            })
            ->where('job_trans.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id);

        $siteSettings = SiteSetting::first();
        if(request('from_start_date') && request('to_start_date')){
            $from_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_start_date'))->format('Y-m-d');
            $to_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_start_date'))->format('Y-m-d');
            $query->whereBetween('jobs.start_date_plan', [$from_date, $to_date]);
        }
        else if(request('from_start_date')){
            $from_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_start_date'))->format('Y-m-d');
            $query->where('jobs.start_date_plan', '>=', $from_date);
        }
        else if(request('to_start_date')){
            $to_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_start_date'))->format('Y-m-d');
            $query->where('jobs.start_date_plan', '<=', $to_date);
        }

        if(request('from_date') && request('to_date')){
            $from_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_date'))->format('Y-m-d');
            $to_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_date'))->format('Y-m-d');
            $query->whereBetween('job_trans.end_datetime', [$from_date, $to_date]);
        }
        else if(request('from_date')){
            $from_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_date'))->format('Y-m-d');
            $query->where('job_trans.end_datetime', '>=', $from_date);
        }
        else if(request('to_date')){
            $to_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_date'))->format('Y-m-d');
            $query->where('job_trans.end_datetime', '<=', $to_date);
        }

        if(request('from_job_num') && request('to_job_num')){
            $query->whereBetween('job_trans.job_num', [request('from_job_num'), request('to_job_num')]);
        }
        else if(request('from_job_num')){
            $query->where('job_trans.job_num', '>=', request('from_job_num'));
        }
        else if(request('to_job_num')){
            $query->where('job_trans.job_num', '<=', request('to_job_num'));
        }

        if(request('job_status') == "Completed"){
            $query->where('job_status', '=', 'C');
        }
        else if(request('job_status') == "Open"){
            $query->where('job_status', '=', 'O');
        }
        else if(request('job_status') == "Released"){
            $query->where('job_status', '=', 'R');
        }

        if(request('from_item_num') && request('to_item_num')){
            $query->whereBetween('items.item_num', [request('from_item_num'), request('to_item_num')]);
        }
        else if(request('from_item_num')){
            $query->where('items.item_num', '>=', request('from_item_num'));
        }
        else if(request('to_item_num')){
            $query->where('items.item_num', '<=', request('to_item_num'));
        }

        if(request('from_product_code') && request('to_product_code')){
            $query->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
        }
        else if(request('from_product_code')){
            $query->where('items.product_code', '>=', request('from_product_code'));
        }
        else if(request('to_product_code')){
            $query->where('items.product_code', '<=', request('to_product_code'));
        }

        $query->whereNotNull('job_trans.job_num')->whereNotNull('job_trans.suffix')->where('job_trans.site_id', auth()->user()->site_id);
        $query->groupBy('job_num')->groupBy('suffix');

        return $query;
    }

    public function data(Request $request) {

        $query = $this->getQuery($request);

        $dataTable = Datatables::of($query)->filterColumn('job_status', function($q, $value) {
            $q->where('jobs.job_status', $value);
        });
        $response  = $dataTable->make(true);
        return $response;

    }

    // Print function
    public function print(Request $request) {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $query = $this->getQuery($request);
        $query1 = $query->get();

        // Site
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        if($site->logo != "") {
            $url = $site->getFileFullPath();
            $image = file_get_contents($url);
            $site->logoazure = "data:image/png+jpeg+jpg;base64,".base64_encode($image);
        }
        else
            $site->logoazure = "";

        if($site->company_info) {
            $sitedecode = json_decode($site->company_info);
            $site->company_name = strtoupper($sitedecode->company_name);
        }

        foreach($query1 as $q) {
            $q->qty_released = numberFormatPrecision($q->qty_released, $unit_quantity_format);
            $q->qty_completed = numberFormatPrecision($q->qty_completed, $unit_quantity_format);
            $q->qty_scrapped = numberFormatPrecision($q->qty_scrapped, $unit_quantity_format);

            $q->setup_time = numberFormatPrecision($q->setup_time, 5);
            $q->run_time = numberFormatPrecision($q->run_time, 5);
            $q->machine_time = numberFormatPrecision($q->machine_time, 5);
            $q->tot = numberFormatPrecision($q->tot, 5);
            $q->ave_labour_run_time_per_piece = numberFormatPrecision($q->ave_labour_run_time_per_piece, 5);
            $q->ave_machine_run_time_per_piece = numberFormatPrecision($q->ave_machine_run_time_per_piece, 5);
            $q->ave_total_run_per_piece = numberFormatPrecision($q->ave_total_run_per_piece, 5);
            $q->labour_hours_per_piece = numberFormatPrecision($q->labour_hours_per_piece, 5);
            $q->machine_hours_per_piece = numberFormatPrecision($q->machine_hours_per_piece, 5);
        }

        $prev_data = [
            'job_num' => '',
        ];

        $index = 1;

        $now = Carbon::now()->toDateTimeString();
        $date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, auth()->user()->timezone, 'H:i:s');

        $siteLogo = $site->logo;
        $siteLogoAzure = $site->logoazure;
        $companyName = $site->company_name;

        $headerHtml = view()->make('report.prod.joboperhours.header')->with('siteLogo', $site->logo)->with('siteLogoAzure', $site->logoazure)->with('companyName', $site->company_name)->with('request', $request)->render();
        $footerHtml = view()->make('report.trans.footer')->with('date',$date)->with('time',$time)->render();

        $pdf = PDF::loadView('report.prod.joboperhours.print', compact('query1', 'request', 'prev_data', 'index', 'site', 'siteLogo', 'siteLogoAzure', 'companyName'))
            ->setPaper('A4', 'landscape')
            ->setOption('header-html', $headerHtml)
            ->setOption('footer-html', $footerHtml)
            ->setOption('margin-bottom', 10)
            ->setOption('margin-top', 30)
            ->setOption('margin-right', 10)
            ->setOption('margin-left', 10)
            ->setOption("footer-right", "Page [page] of [topage]")
            ->setOption("footer-font-size", 8);

        return $pdf->stream('Job Operation Hours.pdf');
    }
}
