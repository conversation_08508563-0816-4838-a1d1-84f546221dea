# CO Picking vs Shipping Status Report - Technical Specifications

## Document Information
- **Document Version**: 1.0
- **Created Date**: January 7, 2025
- **Last Updated**: January 7, 2025
- **Author**: Development Team
- **Status**: Active

---

## 1. Executive Summary

### 1.1 Purpose
The CO Picking vs Shipping Status Report provides real-time visibility into customer order fulfillment by comparing quantities picked to staging locations versus quantities shipped. This report enables warehouse managers to identify bottlenecks in the pick-to-ship process and optimize order fulfillment workflows.

### 1.2 Key Features
- **Real-time Data**: Live aggregation of picking and shipping transactions
- **Multi-criteria Filtering**: Advanced filtering by CO, customer, dates, items, and locations
- **Outstanding Analysis**: Identifies items picked but not yet shipped
- **Export Capabilities**: PDF and Excel export functionality
- **Responsive Design**: Mobile-friendly interface with DataTables integration

---

## 2. System Architecture

### 2.1 Technology Stack
- **Backend**: Laravel 8.x PHP Framework
- **Frontend**: Blade Templates with jQuery/JavaScript
- **Database**: MySQL 8.0+
- **UI Components**: Bootstrap 4, DataTables, Select2
- **PDF Generation**: wkhtmltopdf via Laravel-PDF
- **Excel Export**: DataTables Export Plugin

### 2.2 File Structure
```
codebase/
├── app/Http/Controllers/Report/
│   └── COPickingShippingStatusReportController.php
├── resources/views/report/inv/copickingshippingstatus/
│   ├── index.blade.php
│   ├── list.blade.php
│   └── print.blade.php
├── routes/custom/
│   └── reports.php
└── resources/lang/
    ├── en/admin.php
    ├── cn/admin.php
    └── bm/admin.php
```

### 2.3 Component Dependencies
- **Models**: CustomerOrderItem, StageLoc, Shipment, Customer
- **Services**: SiteSetting, TparmView, DataTables
- **Libraries**: jQuery Validate, Select2, ColReorder, ColVis

---

## 3. Database Schema

### 3.1 Primary Tables

#### 3.1.1 coitems (Customer Order Items)
```sql
Key Fields:
- co_num (VARCHAR): Customer Order Number
- co_line (VARCHAR): Order Line Number  
- co_rel (VARCHAR): Release Number
- cust_num (VARCHAR): Customer Number
- item_num (VARCHAR): Item Number
- qty_ordered (DECIMAL): Quantity Ordered
- due_date (DATE): Due Date
- rel_status (CHAR): Line Status (O=Open, C=Completed)
- site_id (VARCHAR): Site Identifier
```

#### 3.1.2 stage_locs (Staging Locations)
```sql
Key Fields:
- co_num (VARCHAR): Customer Order Number
- co_line (VARCHAR): Order Line Number
- co_rel (VARCHAR): Release Number
- stage_num (VARCHAR): Stage Location Number
- qty_staged (DECIMAL): Quantity Staged/Picked
- item_num (VARCHAR): Item Number
- site_id (VARCHAR): Site Identifier
```

#### 3.1.3 shipments (Shipment Records)
```sql
Key Fields:
- co_num (VARCHAR): Customer Order Number
- co_line (VARCHAR): Order Line Number
- qty_shipped (DECIMAL): Quantity Shipped
- shipment_date (DATETIME): Shipment Date
- site_id (VARCHAR): Site Identifier
```

#### 3.1.4 customers (Customer Master)
```sql
Key Fields:
- cust_num (VARCHAR): Customer Number
- cust_name (VARCHAR): Customer Name
- site_id (VARCHAR): Site Identifier
```

#### 3.1.5 customer_orders (Order Headers)
```sql
Key Fields:
- co_num (VARCHAR): Customer Order Number
- co_status (CHAR): Order Status (O=Open, C=Completed)
- site_id (VARCHAR): Site Identifier
```

### 3.2 Data Relationships
```mermaid
erDiagram
    coitems ||--o{ stage_locs : "co_num, co_line, co_rel"
    coitems ||--o{ shipments : "co_num, co_line"
    coitems }o--|| customers : "cust_num"
    coitems }o--|| customer_orders : "co_num"
```

---

## 4. API Endpoints

### 4.1 Route Definitions
```php
// Report Routes (routes/custom/reports.php)
Route::get('/reports/inventory/co-picking-shipping-status', 'COPickingShippingStatusReportController@index')
    ->name('copickingshippingstatusrep');

Route::get('/reports/inventory/copickingshippingstatusrep-data', 'COPickingShippingStatusReportController@data')
    ->name('copickingshippingstatusrep.data');

Route::post('/reports/inventory/co-picking-shipping-status', 'COPickingShippingStatusReportController@print')
    ->name('printcopickingshippingstatusrep');
```

### 4.2 Controller Methods

#### 4.2.1 index()
- **Purpose**: Display report interface
- **HTTP Method**: GET
- **Parameters**: None
- **Returns**: Blade view with form and filters
- **Permissions**: hasReport gate

#### 4.2.2 data()
- **Purpose**: Provide JSON data for DataTables
- **HTTP Method**: GET
- **Parameters**: Filter parameters via query string
- **Returns**: DataTables JSON response
- **Rate Limiting**: Standard Laravel throttling

#### 4.2.3 print()
- **Purpose**: Generate PDF report
- **HTTP Method**: POST
- **Parameters**: Filter parameters via form data
- **Returns**: PDF stream
- **Paper Size**: A4 Landscape

---

## 5. Business Logic

### 5.1 Core Query Logic
```sql
-- Main query aggregates data from multiple sources
SELECT 
    co.cust_num,
    customers.cust_name,
    co.co_num,
    co.co_line,
    co.due_date,
    co.item_num,
    co.item_desc,
    co.qty_ordered as qty_required,
    IFNULL(stage_summary.stage_num, 'Not Staged') as stage_location,
    IFNULL(stage_summary.total_qty_staged, 0) as qty_picked,
    IFNULL(shipments.total_shipped, 0) as qty_shipped,
    (IFNULL(stage_summary.total_qty_staged, 0) - IFNULL(shipments.total_shipped, 0)) as outstanding_qty
FROM coitems co
LEFT JOIN customers ON customers.cust_num = co.cust_num
LEFT JOIN customer_orders co_header ON co_header.co_num = co.co_num
LEFT JOIN stage_summary ON stage_summary.co_num = co.co_num
LEFT JOIN shipments ON shipments.co_num = co.co_num
WHERE co.site_id = :site_id
```

### 5.2 Aggregation Subqueries

#### 5.2.1 Stage Locations Aggregation
```sql
-- Sums quantities by CO/Line/Stage combination
SELECT 
    co_num, co_line, co_rel, stage_num,
    SUM(qty_staged) as total_qty_staged
FROM stage_locs
WHERE site_id = :site_id
GROUP BY co_num, co_line, co_rel, stage_num
```

#### 5.2.2 Shipments Aggregation
```sql
-- Sums shipped quantities by CO/Line
SELECT 
    co_num, co_line,
    SUM(qty_shipped) as total_shipped
FROM shipments
WHERE site_id = :site_id
GROUP BY co_num, co_line
```

### 5.3 Filter Logic

#### 5.3.1 Range Filters
- **CO Number**: `BETWEEN from_co_num AND to_co_num`
- **Customer**: `BETWEEN from_cust_num AND to_cust_num`
- **Item Number**: `BETWEEN from_item_num AND to_item_num`
- **Due Date**: `BETWEEN from_due_date AND to_due_date`

#### 5.3.2 Status Filters
- **CO Status**: `co_header.co_status = :status`
- **CO Line Status**: `co.rel_status = :status`

#### 5.3.3 Multi-Select Filters
- **Stage Locations**: `stage_summary.stage_num IN (:locations)`

#### 5.3.4 Outstanding Filter
- **Outstanding Only**: `(qty_picked - qty_shipped) > 0`

---

## 6. User Interface Specifications

### 6.1 Form Controls

#### 6.1.1 Filter Panel
```html
<!-- CO Number Range -->
<input type="text" name="from_co_num" class="form-control border-primary">
<input type="text" name="to_co_num" class="form-control border-primary">

<!-- Customer Range -->
<input type="text" name="from_cust_num" class="form-control border-primary">
<input type="text" name="to_cust_num" class="form-control border-primary">

<!-- Due Date Range -->
<input type="text" name="from_due_date" class="form-control border-primary from_due_date">
<input type="text" name="to_due_date" class="form-control border-primary to_due_date">

<!-- Item Number Range -->
<input type="text" name="from_item_num" class="form-control border-primary">
<input type="text" name="to_item_num" class="form-control border-primary">

<!-- Status Dropdowns -->
<select name="co_status" class="form-control border-primary">
    <option value="All">All</option>
    <option value="O">Open</option>
    <option value="C">Completed</option>
</select>

<select name="co_line_status" class="form-control border-primary">
    <option value="All">All</option>
    <option value="O">Open</option>
    <option value="C">Completed</option>
</select>

<!-- Stage Location Multi-Select -->
<select name="stage_locations[]" class="form-control border-primary select2" multiple>
    <!-- Options populated from database -->
</select>

<!-- Outstanding Only Checkbox -->
<input type="checkbox" name="outstanding_only" checked>
```

#### 6.1.2 Action Buttons
```html
<!-- Generate Report -->
<button id="filter" type="button" class="btn btn-primary">
    <i class="icon-square-plus"></i> Generate
</button>

<!-- Reset Form -->
<button id="btn-refresh" type="button" class="btn btn-primary">
    <i class="icon-refresh2"></i> Reset
</button>

<!-- Print PDF -->
<button type="submit" id="print" class="btn btn-info">
    <i class="icon-print"></i> Print
</button>
```

### 6.2 DataTable Configuration

#### 6.2.1 Column Definitions
```javascript
var defaultColumns = [
    'cust_num', 'cust_name', 'co_num', 'co_line', 'due_date',
    'item_num', 'item_desc', 'qty_required', 'stage_location',
    'qty_picked', 'qty_shipped', 'outstanding_qty'
];
```

#### 6.2.2 DataTable Options
```javascript
{
    dom: 'Blrtip',
    scrollX: true,
    scrollY: 400,
    scrollCollapse: true,
    processing: true,
    paging: true,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    serverSide: false,
    searching: false,
    ordering: false,
    responsive: true,
    buttons: ['excel', 'csv']
}
```

### 6.3 Validation Rules

#### 6.3.1 jQuery Validation
```javascript
$("#co_picking_shipping_report").validate({
    onchange: true,
    rules: {
        from_co_num: {
            remote: {
                url: "/validation",
                type: "post",
                data: { _token: $('input[name="_token"]').val() }
            }
        },
        to_co_num: {
            remote: {
                url: "/validation",
                type: "post",
                data: { _token: $('input[name="_token"]').val() }
            }
        }
        // Similar rules for other fields
    }
});
```

---

## 7. Security & Permissions

### 7.1 Authentication
- **Middleware**: `auth` middleware required for all routes
- **Session Management**: Laravel session handling
- **CSRF Protection**: CSRF tokens on all forms

### 7.2 Authorization
- **Gate**: `hasReport` gate check in controller
- **Site Isolation**: All queries filtered by `auth()->user()->site_id`
- **Data Access**: Users can only access their site's data

### 7.3 Input Validation
- **SQL Injection**: Laravel Query Builder prevents SQL injection
- **XSS Protection**: Blade templating auto-escapes output
- **Form Validation**: Server-side validation for all inputs

---

## 8. Performance Considerations

### 8.1 Database Optimization

#### 8.1.1 Recommended Indexes
```sql
-- Primary performance indexes
CREATE INDEX idx_coitems_site_co_line ON coitems(site_id, co_num, co_line);
CREATE INDEX idx_stage_locs_site_co_line ON stage_locs(site_id, co_num, co_line, co_rel);
CREATE INDEX idx_shipments_site_co_line ON shipments(site_id, co_num, co_line);
CREATE INDEX idx_customers_site_cust ON customers(site_id, cust_num);
CREATE INDEX idx_customer_orders_site_co ON customer_orders(site_id, co_num);

-- Filter-specific indexes
CREATE INDEX idx_coitems_due_date ON coitems(site_id, due_date);
CREATE INDEX idx_coitems_item_num ON coitems(site_id, item_num);
CREATE INDEX idx_coitems_cust_num ON coitems(site_id, cust_num);
CREATE INDEX idx_stage_locs_stage_num ON stage_locs(site_id, stage_num);
```

#### 8.1.2 Query Optimization
- **Subquery Aggregation**: Pre-aggregates stage_locs and shipments data
- **Efficient Joins**: Uses proper join conditions and indexes
- **Selective Filtering**: Applies filters early in query execution

### 8.2 Frontend Performance
- **DataTables**: Client-side processing for better user experience
- **Pagination**: Configurable page sizes to manage large datasets
- **Lazy Loading**: Select2 dropdowns load data on demand

### 8.3 Caching Strategy
- **Query Caching**: Laravel query caching for static lookups
- **Session Caching**: User preferences stored in session
- **Browser Caching**: Static assets cached by browser

---

## 9. Error Handling

### 9.1 Database Errors
```php
try {
    $query = $this->getQuery($request);
    $dataTable = Datatables::of($query);
    return $dataTable->make(true);
} catch (Exception $e) {
    Log::error('CO Picking Shipping Report Error: ' . $e->getMessage());
    return response()->json(['error' => 'Database error occurred'], 500);
}
```

### 9.2 Validation Errors
```javascript
// jQuery Validation Error Messages
messages: {
    from_co_num: {
        remote: "CO Number does not exist"
    },
    to_co_num: {
        remote: "CO Number does not exist"
    }
}
```

### 9.3 PDF Generation Errors
```php
try {
    $pdf = PDF::loadView('report.inv.copickingshippingstatus.print', $data);
    return $pdf->stream('CO Picking vs Shipping Status.pdf');
} catch (Exception $e) {
    Log::error('PDF Generation Error: ' . $e->getMessage());
    return redirect()->back()->with('error', 'PDF generation failed');
}
```

---

## 10. Testing Requirements

### 10.1 Unit Tests
```php
// Test Cases Required
class COPickingShippingStatusReportTest extends TestCase
{
    public function testIndexPageLoads()
    public function testDataEndpointReturnsJson()
    public function testFilteringWorksCorrectly()
    public function testPdfGenerationSucceeds()
    public function testPermissionDeniedForUnauthorizedUsers()
    public function testSiteIsolationWorksCorrectly()
}
```

### 10.2 Integration Tests
- **Database Integration**: Test with sample data
- **Filter Combinations**: Test various filter combinations
- **Large Dataset**: Test with large datasets (10,000+ records)
- **Export Functions**: Test PDF and Excel export

### 10.3 Browser Tests
- **Cross-Browser**: Chrome, Firefox, Safari, Edge
- **Responsive Design**: Mobile, tablet, desktop
- **DataTable Functions**: Sorting, searching, pagination
- **Form Validation**: All validation rules

---

## 11. Deployment Instructions

### 11.1 Prerequisites
- Laravel 8.x application
- MySQL 8.0+ database
- PHP 7.4+ with extensions: pdo, mbstring, tokenizer
- wkhtmltopdf binary installed

### 11.2 Deployment Steps

#### 11.2.1 File Deployment
```bash
# Copy files to application
cp COPickingShippingStatusReportController.php app/Http/Controllers/Report/
cp -r copickingshippingstatus/ resources/views/report/inv/
```

#### 11.2.2 Database Changes
```bash
# Run migrations if any
php artisan migrate

# Create indexes
mysql -u user -p database < indexes.sql
```

#### 11.2.3 Route Registration
```php
// Add to routes/custom/reports.php
Route::get('/reports/inventory/co-picking-shipping-status', 'Report\COPickingShippingStatusReportController@index')->name('copickingshippingstatusrep');
```

#### 11.2.4 Menu Integration
```php
// Add to MenuController.php
array('title' => 'CO Picking vs Shipping Status', 'route' => route('copickingshippingstatusrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
```

#### 11.2.5 Language Files
```php
// Add to resources/lang/*/admin.php
'co_status' => 'Co Status',
```

### 11.3 Post-Deployment Testing
- [ ] Verify report page loads
- [ ] Test all filters
- [ ] Verify PDF generation
- [ ] Check permissions
- [ ] Test with sample data

---

## 12. Maintenance & Support

### 12.1 Monitoring
- **Performance Metrics**: Monitor query execution times
- **Error Logs**: Check Laravel logs for errors
- **Usage Statistics**: Track report usage patterns

### 12.2 Regular Maintenance
- **Index Optimization**: Review and optimize database indexes
- **Data Cleanup**: Clean up old staging and shipment records
- **Performance Tuning**: Monitor and optimize slow queries

### 12.3 Support Procedures
- **Issue Escalation**: Database issues → DBA, UI issues → Frontend team
- **Log Analysis**: Check `storage/logs/laravel.log` for errors
- **Debug Mode**: Enable Laravel debug mode for detailed error info

---

## 13. Future Enhancements

### 13.1 Planned Features
- **Real-time Updates**: WebSocket integration for live updates
- **Advanced Analytics**: Trend analysis and forecasting
- **Mobile App**: Native mobile application
- **API Endpoints**: REST API for external integrations

### 13.2 Performance Improvements
- **Query Optimization**: Further optimize complex queries
- **Caching Layer**: Implement Redis caching
- **Background Processing**: Move heavy operations to queues

### 13.3 User Experience
- **Dashboard Integration**: Embed in main dashboard
- **Customizable Views**: User-defined column layouts
- **Automated Reports**: Scheduled report generation

---

## 14. Appendices

### 14.1 Sample Data
```sql
-- Sample coitems data
INSERT INTO coitems (co_num, co_line, co_rel, cust_num, item_num, qty_ordered, due_date, rel_status, site_id)
VALUES ('CO00001', '1', '1', 'CUST001', 'ITEM001', 100, '2025-01-15', 'O', 'SITE001');

-- Sample stage_locs data
INSERT INTO stage_locs (co_num, co_line, co_rel, stage_num, qty_staged, item_num, site_id)
VALUES ('CO00001', '1', '1', 'STAGE-A', 50, 'ITEM001', 'SITE001');

-- Sample shipments data
INSERT INTO shipments (co_num, co_line, qty_shipped, shipment_date, site_id)
VALUES ('CO00001', '1', 30, '2025-01-10 10:00:00', 'SITE001');
```

### 14.2 Configuration Files
```php
// config/datatables.php
'export' => [
    'excel' => [
        'enabled' => true,
        'extension' => 'xlsx'
    ],
    'csv' => [
        'enabled' => true,
        'delimiter' => ','
    ]
]
```

### 14.3 Environment Variables
```bash
# .env additions
WKHTMLTOPDF_PATH=/usr/local/bin/wkhtmltopdf
REPORT_CACHE_TTL=3600
MAX_EXPORT_ROWS=10000
```

---

**Document Control**
- Version: 1.0
- Classification: Internal Use
- Distribution: Development Team, System Administrators
- Review Cycle: Quarterly
- Next Review: April 2025

---

*This document is maintained by the Development Team and should be updated whenever changes are made to the CO Picking vs Shipping Status Report system.*
