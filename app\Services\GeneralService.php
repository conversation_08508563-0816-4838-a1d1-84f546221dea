<?php

namespace App\Services;

use App\Allocation;
use App\coitem;
use App\Services\LotLocationService;
use App\Services\TOService;
use App\Item;
use App\ItemLoc;
use App\LotLoc;
use App\matl_trans;
use App\MatlTrans;
use App\TransferOrder;
use App\View\TparmView;
use DB;
use Carbon\Carbon;
use App\Job;
use App\JobRoute;
use App\JobMatl;
use App\TransferLine;
use App\TransferOrderLinesSubline;
use App\UomConv;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Event;
use App\Events\MatlTransInsertEvent;
use App\Jobs\DeleteIntegrationLog;
use App\PurchaseOrderItem;
use Illuminate\Support\Facades\Storage;
use App\Services\MatltransService;
use App\StagingLine;
use Illuminate\Http\Request;
use App\Services\CatchWeightService;

class GeneralService
{

    // USE FOR UPDATE ITEM LOCATION QTY

    public static function newTrans($transtype, $request, $whse_num, $loc_num, $item_num, $qty, $lot_num = null, $uom = null)
    {
        //dd($request,'sss',config('icapt.midApis'));
        DB::beginTransaction();

        if ($transtype != 'Pallet Misc Receipt') {
            $updateitemlocation = self::updateItemLocationQty($whse_num, $loc_num, $item_num, $qty, $lot_num, $uom, 0, null, null, null);
            if ($updateitemlocation == false) {
                return false;
            }
        }

        $newMatlTrans = self::newMatlTrans($transtype, $request);
        // if($request->sap_connect==1)
        // {
        if (config('icapt.midApis') == true) {
            $record = $newMatlTrans;
            Event::dispatch(new MatlTransInsertEvent($record, 'add', $request->post_from, $request->sap_service));
        }
        // }


        DB::commit();

        return true;
    }

    public static function updateItemLocationQty($whse_num, $loc_num, $item_num, $qty, $lot_num = null, $uom = null, $non_inv = 0, $picklistallocation_CheckpicklistId = null, $line = null, $lpn = null)
    {
        $qty = floatval(str_replace(',', '', $qty));

        //dd($qty);

        // No need to update/create Item Location or Item Lot Location if it is non-inventory
        if ($non_inv == 1) {
            return true;
        }
        // Reject request if lot_tracked item doesnt have lot number
        $item = Item::where('item_num', $item_num)->first();
        if ($item && $item->lot_tracked && empty($lot_num)) {
            throw ValidationException::withMessages(['details' => 'Item [' . $item_num . '] needs lot number.']);
            return false;
        }

        // Find the item location
        $item_loc = new ItemLoc();
        $updateitemloc = $item_loc->where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->lockForUpdate()->first();
        // dd($updateitemloc);
        // Get Base UOM
        $getItem =  new Item();
        $baseuom = $getItem->select('uom')->where('item_num', $item_num)->value('uom');
        // Record found
        if ($updateitemloc != null) {
            // Reject if wrong UOM
            // if ($updateitemloc->uom != $uom) {
            //     throw ValidationException::withMessages(['details' => __('error.mobile.invaliduom')]);
            //     return false;
            // }
            // Item location is frozen?
            if ($updateitemloc->freeze == 'Y') {
                // dd("a");
                throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item_num, 'resource2' => $loc_num])]);
                return false;
            }
            $updateitemloc->uom = $baseuom;

            //dd($updateitemloc->qty_on_hand,(float)$qty,$qty);
            $updateitemloc->qty_on_hand += $qty;


            if ($picklistallocation_CheckpicklistId > 0) {
                $updateitemloc->qty_allocated = $updateitemloc->qty_allocated + $qty;
            }

            if ($lpn) {
                // $updateitemloc->qty_on_hand = $updateitemloc->qty_on_hand - abs($qty);
                $updateitemloc->qty_contained = $updateitemloc->qty_contained - abs($qty);
            }

            if ($updateitemloc->qty_on_hand < 0) {
                $extraMsg = "";
                $errMsg = "error.mobile.notenough_qty_available_for";
                if ($line) {
                    if ($line == 'coreturn')
                    {
                        $extraMsg = "Item ($item_num)";
                        $errMsg = "error.mobile.notenough_qty_available_for";
                    }
                    else if($line == 'item'){
                        $extraMsg = "Item $item_num";
                        $errMsg = "error.mobile.notenough_qty_available_for";
                    }
                    else if ($line == 'picknship')
                    {
                        $extraMsg = $updateitemloc->qty_available;
                        $errMsg = 'error.admin.qty_less_than_with_value';
                    }
                    else
                    {
                        $extraMsg = "Line $line";
                    }
                }
                // else if($module = 'Job Material Issue'){
                else {
                    $extraMsg = "material $item_num";
                }
                throw ValidationException::withMessages([__($errMsg, ['resource1' => $extraMsg])]);

                return false;
            } else {
                $updateitemloc->save();
            }
            // dd($updateitemloc->qty_on_hand);

        } else {
            if ($qty >= 0) {
                //Create a new record in table
                $item_loc->whse_num = $whse_num;
                $item_loc->loc_num = $loc_num;
                $item_loc->item_num = $item_num;
                $item_loc->qty_on_hand = $qty;
                $item_loc->uom = $baseuom;
                $item_loc->rank = app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($whse_num), base64_encode($item_num), '');

                // update qty_allocated Once is allocation for picklist ship
                if ($picklistallocation_CheckpicklistId > 0) {
                    // Due too giving -No. thereforce qty_allocated need to deduct by using + instead of -

                    $item_loc->qty_allocated = $item_loc->qty_allocated - abs($qty);
                }
                if ($lpn) {
                    //$item_loc->qty_on_hand = $item_loc->qty_on_hand - abs($qty);
                    $item_loc->qty_contained = $item_loc->qty_contained - abs($qty);
                }

                $item_loc->save();
            } else {
                return false;
            }
        }
        //dd($lot_num);
        // Lot number exists, update lot qty
        if ($lot_num != null) {
            $updateItemLotLoc = LotLoc::where('whse_num', $whse_num)->where('loc_num', $loc_num)->where('item_num', $item_num)->where('lot_num', $lot_num)->lockForUpdate()->first();
            // Item lot location is frozen?
            if ($updateItemLotLoc != null && $updateItemLotLoc->freeze == 'Y') {
                throw ValidationException::withMessages(['details' => 'Item ' . $item_num . ' at ' . $lot_num . ' is frozen.']);
                return false;
            }

            $lotLoc = new LotLoc(array(
                'whse_num' => $whse_num,
                'loc_num' => $loc_num,
                'lot_num' => $lot_num,
                'item_num' => $item_num,
                'qty_on_hand' => $qty,
                'uom' => $baseuom,
            ));


            return $updateLotLoc = LotLocationService::updateOrCreateLotQuantity($lotLoc, $qty, $picklistallocation_CheckpicklistId, $lpn);
        }
        return true;
    }

    public static function newMatlTrans($transtype, $request)
    {
        $matl_trans = new MatlTrans();
        $transaction = $request->all();
        // dd($request);
        if (@$request->item_num == "NON-INV") {
            $transaction['uom'] = $request->original_uom ?? $request->base_uom ?? $request->uom;
            $transaction['base_uom'] = $request->original_uom ?? $request->base_uom;
        }
        $transaction['trans_type'] = $transtype;
        $transaction['pick_num'] = $request->pick_num;
        $transaction['grn_num'] = $request->grn_num;
        $transaction['grn_line'] = $request->grn_line;

        if (in_array($transtype, config('icapt.outgoingTrans'))) {
            if ($transaction['qty'] > 0)

                $transaction['qty'] *= -1;
        }

        $transaction['trans_qty'] = $transaction['qty'];
        $transaction['trans_uom'] = $transaction['uom'];
        $transaction['pick_num'] = $request->pick_num;

        if ($transtype == 'CO Return') {
            $transaction['ref_num'] = $request->co_num;
            $transaction['ref_line'] = $request->co_line;
            $transaction['ref_release'] = $request->co_rel;
        }
        if ($transtype == 'PO Receipt') {
            $PoItem = PurchaseOrderItem::where('po_num', $request->ref_num)->where('po_rel', $request->ref_release)->where('po_line', $request->ref_line)->where('site_id', auth()->user()->site_id)->first();
            // dd($PoItem->unit_cost);
            if ($PoItem->unit_cost != "") {
                $custom_attributes = [];
                $custom_attributes['unit_cost'] = $PoItem->unit_cost;
                $transaction['custom_attributes'] = $custom_attributes;
            }
        }

        $uom_temp = $request->uom;

        // if ($transtype == 'PO Receipt') {
        //     if(isset($request->lpn_num)){
        //         $transaction['lpn_num'] = $request->lpn_num;
        //         $insertPalletData = PalletService::updatePalletItemQty($request);
        //         $request->uom = $uom_temp;
        //     }
        // }

        // dd($transtype);

        if ($transtype == 'Pallet Misc Receipt') {
            $transaction['trans_type'] = config('icapt.transtype.misc_receipt');
        }

        if ($transtype == 'Pallet Job Receipt') {
            // if(isset($request->lpn_num)){
            //     $transaction['lpn_num'] = $request->lpn_num;
            //     $insertPalletData = PalletService::updatePalletItemQty($request);
            //     $request->uom = $uom_temp;
            // }
            $transaction['trans_type'] = config('icapt.transtype.job_receipt');
        }

        if (isset($transaction['qty_conv'])) {
            if (in_array($transtype, config('icapt.outgoingTrans'))) {
                if ($transaction['qty_conv'] > 0)
                    $transaction['qty_conv'] *= -1;
            }

            $baseUomArr = ["CO Unpick From", "CO Unpick To", "CO Pick To", "CO Pick From", "CO Ship", "Pick N Ship", "PO Return"];

            $transaction['qty'] = $transaction['qty_conv'];
            // $transaction['uom'] = $transaction['uom_conv'];
            // $transaction['uom'] = $transaction['base_uom'];
            if ($transtype == "Job Material Issue") {
                $transaction['uom'] = $request->qty_available_uom;
            } else if (in_array($transtype, $baseUomArr)) {
                $transaction['uom'] = $transaction['base_uom'];
            } else {
                $transaction['uom'] = $transaction['uom_conv'];
            }

            $transaction['pick_num'] = $request->pick_num;
        }

        if ($request->base_uom == $request->uom) {
            $transaction['qty'] = $transaction['trans_qty'];
            $transaction['qty_conv'] = $transaction['trans_qty'];
        }
        if ($request['batch_id']) {
            $batch_id = $request['batch_id'];
            // $batch_id= str_replace("transType","$transtype", $batch_id);
            $transaction['batch_id'] = $batch_id;
        }

        // Set for non-inventory item
        if ($transtype == "Pick N Ship" && @$request->item_num == "NON-INV") {
            $transaction['trans_uom'] = $request->uom;
        }

        // Set for Non-Inventory item
        $trans_type_included = [
            config('icapt.transtype.co_pick_from'),
            config('icapt.transtype.co_pick_to'),
            config('icapt.transtype.co_unpick_from'),
            config('icapt.transtype.co_unpick_to'),
        ];
        if (in_array($transtype, $trans_type_included) && $request->item_num == "NON-INV") {
            $transaction['trans_uom'] = $request->uom;
        }


        // dd($transaction);
        // else{
        //     if($transtype=="PO Receipt")
        //     {
        //         $transaction['trans_qty'] = $request->qty;
        //         $transaction['trans_uom'] = $request->base_uom;


        //         $transaction['uom'] = $request->base_uom;
        //         $transaction['qty'] = $request->qty;
        //     }
        // }
        // dd($transaction, $request, $transtype);
        // Non-Inventory

        // dd($transaction);



        $result = $matl_trans->create($transaction);
        if ($transtype == "CO Pick To") {
            Session::put('trans_num_store', $result->trans_num);
        }


        //Update Job_matls table
        if ($transtype == "Job Material Return") {
            $qty_returned = MatlTrans::where('trans_type', 'Job Material Return')
                ->where('ref_num', $request->job_num)
                ->where('suffix', $request->suffix)
                ->where('ref_line', $request->oper_num)
                ->where('ref_release', $request->sequence)
                ->sum('qty');

            $job_matl = new JobMatl();
            $job_matl = $job_matl->where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('oper_num', $request->oper_num)->where('sequence', $request->sequence)->first();
            $job_matl->qty_returned = $qty_returned;
            $job_matl->save();
        }

        return $result;
    }


    public static function CoMaterialTrans($request)
    {
        $matl_trans = MatlTrans::createFromCORequest($request);
        return $matl_trans->save();
    }

    public static function updateTransOrderReceipt($trn_num, $trn_line, $qty, $qty_loss = 0)
    {

        $transLine = new TransferLine();
        $transLine = $transLine->where('trn_num', $trn_num)->where('trn_line', $trn_line)->first();

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $transLine->qty_received  += $qty;
        $transLine->qty_loss += $qty_loss;
        //$checkQtyShip =  (float)($transLine->qty_shipped) - (float)($transLine->qty_received) - (float)($transLine->qty_loss);
        $checkQtyShip =  number_format($transLine->qty_shipped - $transLine->qty_received - $transLine->qty_loss, $unit_quantity_format);


        // dd($checkQtyShip,$unit_quantity_format);
        if ($checkQtyShip < 0) {
            throw ValidationException::withMessages(['details' => __('error.admin.qty_received_more_than_qty_shipped')]);
        }

        // if ($transLine->tolerance > 0 && abs(($transLine->qty_required - $qty) / $transLine->qty_required) * 100 <= $transLine->tolerance) {
        //     $transLine->line_stat = 'C';
        // } else if (($transLine->qty_received + $transLine->qty_loss >= $transLine->qty_required)) {
        //     $transLine->line_stat = 'C';
        // }

        $transLine->save();
        return $transLine;
    }

    public static function updateTransOrderLotLocReceiptPallet($trn_num, $trn_line, $qty, $qty_loss, $lpn_num)
    {

        $transLine = new TransferOrderLinesSubline();
        $transLine = $transLine->where('trn_num', $trn_num)->where('trn_line', $trn_line)->where('lpn_num', $lpn_num)->get();
        foreach ($transLine as $data) {
            $transLine1 = $transLine->where('trn_num', $data->trn_num)->where('trn_lot', $data->trn_lot)->where('trn_line', $data->trn_line)->where('lpn_num', $data->lpn_num)->first();
            $transLine1->qty_received = $transLine1->qty_shipped;
            //$transLine->save();
            $transLine1->save();
        }





        // $transLine->save();
        return $transLine;
    }









    public static function updateTransOrderLotLocReceipt($loc_num, $lot_num, $trn_num, $trn_line, $qty, $qty_loss)
    {
        // Check Lot
        if ($lot_num) {
            $transLine = new TransferOrderLinesSubline();
            $transLine = $transLine->where('trn_num', $trn_num)->where('trn_line', $trn_line)->where('lpn_num', '')->where('trn_lot', $lot_num)->first();

            $transLine->qty_received += $qty;
            $transLine->qty_loss += $qty_loss;
        } else {
            $transLine = new TransferOrderLinesSubline();

            // $transLine = new TransferLine();

            $transLine = $transLine->where('trn_num', $trn_num)->where('trn_line', $trn_line)->where('lpn_num', '')->first();
            $transLine->qty_received += $qty;
            $transLine->qty_loss += $qty_loss;
        }

        $transLine->save();
        return $transLine;
    }

    public static function addUpdateTransferOderLot($qty_shipped, $loc_num, $item_num, $uom, $lot_num, $trn_num, $trn_line, $lpn_num)
    {

        // dd('data',$qty_shipped, $loc_num, $item_num, $uom, $lot_num, $trn_num, $trn_line, $lpn_num);
        $TransferOrder = new TransferOrder();
        $trn_loc =  $TransferOrder->where('trn_num', $trn_num)->first();
        if ($lot_num) {
            $TransferOrderLinesSubline = new TransferOrderLinesSubline();
            $TransferOrderLinesSubline = $TransferOrderLinesSubline->where('trn_num', $trn_num)->where('lpn_num', '=""')->where('trn_line', $trn_line)->where('trn_lot', $lot_num)->where('lpn_num', $lpn_num);



            $TransferOrderLinesSubline = $TransferOrderLinesSubline->first();
            //dd(1,$TransferOrderLinesSubline);
            if ($TransferOrderLinesSubline != null) {
                $TransferOrderLinesSubline->qty_shipped += $qty_shipped;
                $TransferOrderLinesSubline->save();
            } else {
                // Create New
                $arrStoge = array();
                $now = Carbon::now()->toDateTimeString();
                $TransferOrderLinesSubline = new TransferOrderLinesSubline();
                $arrStoge['trn_num'] = $trn_num;
                $arrStoge['trn_line'] = $trn_line;
                $arrStoge['item_num'] = $item_num;
                $arrStoge['uom'] = $uom;
                $arrStoge['trn_loc'] = $trn_loc->trn_loc;
                $arrStoge['trn_lot'] = $lot_num ?? null;
                $arrStoge['qty_shipped'] = $qty_shipped;
                $arrStoge['lpn_num'] = $lpn_num ?? null;

                $result = $TransferOrderLinesSubline->create($arrStoge);
                return $result;
            }
        } else {
            $TransferOrderLinesSubline = new TransferOrderLinesSubline();
            $TransferOrderLinesSubline = $TransferOrderLinesSubline->where('trn_num', $trn_num)->where('trn_line', $trn_line)->where('lpn_num', $lpn_num);

            $TransferOrderLinesSubline = $TransferOrderLinesSubline->first();
            //dd(2,$TransferOrderLinesSubline);
            if ($TransferOrderLinesSubline != null) {
                $TransferOrderLinesSubline->qty_shipped += $qty_shipped;
                $TransferOrderLinesSubline->save();
            } else {
                // Create New
                $arrStoge = array();
                $now = Carbon::now()->toDateTimeString();
                $TransferOrderLinesSubline = new TransferOrderLinesSubline();
                $arrStoge['trn_num'] = $trn_num;
                $arrStoge['trn_line'] = $trn_line;
                $arrStoge['item_num'] = $item_num;
                $arrStoge['uom'] = $uom;
                $arrStoge['trn_loc'] = $trn_loc->trn_loc ?? null;
                $arrStoge['trn_lot'] = $lot_num ?? null;
                $arrStoge['qty_shipped'] = $qty_shipped;
                $arrStoge['lpn_num'] = $lpn_num ?? "";

                $result = $TransferOrderLinesSubline->create($arrStoge);
                return $result;
            }
        }
    }

    public static function updatePalletTransOrderShipping($trn_num, $trn_line, $qty, $lot_num = null)
    {
        $site_id = auth()->user()->site_id;
        $user_name = auth()->user()->id;
        $transLine = new TransferLine();
        $transLine = $transLine->where('trn_num', $trn_num)->where('trn_line', $trn_line)->first();

        //dd($transLine,$trn_num,$trn_line);
        //$transLine->loc_num = $loc_num;
        $transLine->lot_num = $lot_num ?? null;
        $transLine->qty_shipped += $qty;
        $transLine->save();

        $transferOrderShipping = session('transferOrderShipping');
        return true;
    }

    public static function updateTransOrderShipping($trn_num, $trn_line, $qty, $lot_num = null, $qty_conv = null, $uom_conv = null)
    {

        // dd($qty);
        $site_id = auth()->user()->site_id;
        $user_name = auth()->user()->id;
        $transLine = new TransferLine();
        $transLine = $transLine->where('trn_num', $trn_num)->where('trn_line', $trn_line)->first();
        //$transLine->loc_num = $loc_num;
        $transLine->lot_num = $lot_num ?? null;
        $transLine->qty_shipped += $qty;
        $transLine->save();
        // dd($qty_ship_for_item, $qty);
        $transferOrderShipping = session('transferOrderShipping');
        $TransitLocQtyOnHand = TOService::TransitQtyOnHand($transferOrderShipping, $transLine->item_num);

        // validate over ship
        $tparm = new TparmView;
        $allow_over_ship = $tparm->getTparmValue('TOShipping', 'allow_over_ship');
        if (!$allow_over_ship) {
            if ($qty > $transLine->qty_required) {
                throw ValidationException::withMessages(['details' => 'Qty to Ship cannot bigger than Qty Req.']);
            }
        }

        // // insert or update transit loc
        // $checkItemLoc = ItemLoc::where('whse_num', $transLine->to_whse)->where('loc_num', $transferOrderShipping->trn_loc)->where('item_num', $transLine->item_num)->first();
        // if ($checkItemLoc) {
        //     // $qtyOnHand = $checkItemLoc->qty_on_hand + $qty;
        //     $qtyOnHand = $checkItemLoc->qty_on_hand + $qty_conv;
        //     // dd($checkItemLoc,$qty);
        //     $checkItemLoc->update(['qty_on_hand' => $qtyOnHand]);
        // } else {
        //     $createNewData = ItemLoc::create([
        //         'whse_num' => $transLine->to_whse,
        //         'loc_num' => $transferOrderShipping->trn_loc,
        //         'item_num' => $transLine->item_num,
        //         // 'qty_on_hand' => $TransitLocQtyOnHand,
        //         // 'uom' => $transLine->uom,
        //         'uom' => $uom_conv,
        //         'qty_on_hand' => $qty_conv,
        //         'site_id' => $site_id,
        //         'created_by' => $user_name,
        //         'freez' => 'N',
        //         'rank' => app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($transLine->to_whse), base64_encode($transLine->item_num), base64_encode($transferOrderShipping->trn_loc)),
        //     ]);
        // }
        // $itemloc = new ItemLoc;
        // Save TO's Transit Location into Item Location
        // $itemloc->updateOrCreate(
        //     [
        //         'whse_num' => $transLine->to_whse,
        //         'loc_num' => $transferOrderShipping->trn_loc,
        //         'item_num' => $transLine->item_num,
        //     ],
        //     [
        //         'qty_on_hand' => $TransitLocQtyOnHand,
        //         'uom' => $transLine->uom,
        //         'rank' => app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($transLine->to_whse), base64_encode($transLine->item_num), base64_encode($transferOrderShipping->trn_loc)),
        //     ]
        // );

        // Save TO's Transit Location into Item Lot Location
        if ($lot_num) {
            //$TransitLotLocQtyOnHand = TOService::TransitQtyOnHand($transferOrderShipping, $transLine->item_num, $lot_num);

            $TransitLotLocQtyOnHand = TOService::TransitQtyOnHandTO($transferOrderShipping, $qty, $transLine->item_num, $lot_num);

            // Check for new create
            $lotloccheck = new LotLoc;
            $lotloccheck = $lotloccheck->where('whse_num', $transLine->to_whse)
                ->where('loc_num', $transferOrderShipping->trn_loc)
                ->where('lot_num', $lot_num)
                ->where('item_num', $transLine->item_num)
                ->first();
            //dd($lotloccheck,$TransitLotLocQtyOnHand);

            // //update or create new transit loc loc.
            // if ($lotloccheck) {
            //     // $qtyonHand = $lotloccheck->qty_on_hand + $qty;
            //     $qtyonHand = $lotloccheck->qty_on_hand + $qty_conv;

            //     $lotloccheck->update(['qty_on_hand' => $qtyonHand]);
            // } else {
            //     $createNewData = LotLoc::create([
            //         'whse_num' => $transLine->to_whse,
            //         'loc_num' => $transferOrderShipping->trn_loc,
            //         'item_num' => $transLine->item_num,
            //         'lot_num' => $transLine->lot_num,
            //         // 'qty_on_hand' => $qty,
            //         // 'uom' => $transLine->uom,
            //         'uom' => $uom_conv,
            //         'qty_on_hand' => $qty_conv,
            //         'site_id' => $site_id,
            //         'created_by' => $user_name,
            //     ]);
            // }
            // if ($lotloccheck == null) {
            //     // Create New LotLoc
            //     $lotloc = new LotLoc;
            //     $lotloc->updateOrCreate([
            //         'whse_num' => $transLine->to_whse,
            //         'loc_num' => $transferOrderShipping->trn_loc,
            //         'item_num' => $transLine->item_num,
            //         'lot_num' => $transLine->lot_num,
            //     ], [
            //         'qty_on_hand' => $qty,
            //         'uom' => $transLine->uom,
            //     ]);
            // } else {
            //     // Update Transit Loc  Lot qty on hand
            //     $lotloc = new LotLoc;
            //     $lotloc->updateOrCreate([
            //         'whse_num' => $transLine->to_whse,
            //         'loc_num' => $transferOrderShipping->trn_loc,
            //         'item_num' => $transLine->item_num,
            //         'lot_num' => $transLine->lot_num,
            //     ], [
            //         'qty_on_hand' => $TransitLotLocQtyOnHand,
            //         'uom' => $transLine->uom,
            //     ]);
            // }
        }

        return true;
    }

    public static function coshippingMatlTrans($transtype, $StageLoc)
    {
        $tempStageLoc = [];
        $now = Carbon::now()->toDateTimeString();
        $matl_trans = new MatlTrans();
        $site_id = auth()->user()->site_id;
        //dd($StageLoc);
        if (@$StageLoc->lot_num) {
            $getRealtrans =  StagingLine::where('co_num', $StageLoc->co_num)->where('shipment_id', 0)->where('co_line', $StageLoc->co_line)->where('site_id', $site_id)->where('lot_num', $StageLoc->lot_num)->where('to', $StageLoc->stage_num)->where('qty_ship', '>', 0)->get();
        } else {
            $getRealtrans =  StagingLine::where('co_num', $StageLoc->co_num)->where('shipment_id', 0)->where('co_line', $StageLoc->co_line)->where('site_id', $site_id)->where('to', $StageLoc->stage_num)->where('qty_ship', '>', 0)->get();
        }
        // Group All co_num , co_line , uom , to , Lot
        $arrNewMltran = array();
        $baseuom = $StageLoc->uom_conv;
        // dd($StageLoc, $getRealtrans);
        foreach ($getRealtrans as $key) {
            $keyuniq = $key->co_num . $key->co_line . $site_id . $key->to . $key->uom . $StageLoc->lot_num;
            if (isset($arrNewMltran[$keyuniq]['qty_ship'])) {
                $arrNewMltran[$keyuniq]['qty_ship'] +=  $key->qty_ship;
            } else {
                $arrNewMltran[$keyuniq]['qty_ship'] =  $key->qty_ship;
            }
            $arrNewMltran[$keyuniq]['uom'] = $key->uom;
            $arrNewMltran[$keyuniq]['item_num'] = $key->item_num;
            $arrNewMltran[$keyuniq]['id'] = $key->id;
        }
        // dd($arrNewMltran);

        /* foreach ($getRealtrans as $key) {
            $convertQtyUom = UomConv::convertUOM($baseuom, $key->uom, '', $key->qty_ship, $key->item_num, '', '', 'CO Ship');

            // dd($getRealtrans,$baseuom,$convertQtyUom,$StageLoc);

            $qty  = $convertQtyUom['conv_qty_to_base']['qty'];
            //TEST
            $tempStageLoc[$key->id]['trans_type'] = $transtype;
            $tempStageLoc[$key->id]['trans_date'] = $now;
            $tempStageLoc[$key->id]['ref_num'] = $StageLoc->co_num;
            $tempStageLoc[$key->id]['ref_line'] = $StageLoc->co_line;
            $tempStageLoc[$key->id]['ref_release'] = $StageLoc->co_rel;
            $tempStageLoc[$key->id]['whse_num'] = $StageLoc->whse_num;
            $tempStageLoc[$key->id]['loc_num'] = $StageLoc->stage_num;
            $tempStageLoc[$key->id]['lot_num'] = $StageLoc->lot_num;
            $tempStageLoc[$key->id]['qty'] = -$StageLoc->qty_conv;
            $tempStageLoc[$key->id]['uom'] = $StageLoc->uom_conv;
            $tempStageLoc[$key->id]['trans_qty'] = -$key->qty_ship;
            $tempStageLoc[$key->id]['trans_uom'] = $key->uom;
            $tempStageLoc[$key->id]['item_num'] = $StageLoc->item_num;
            $tempStageLoc[$key->id]['lpn_num'] = $StageLoc->lpn_num;


            if ($StageLoc->item_num == "NON-INV") {
                if ($tempStageLoc[$key->id]['uom'] == "") {
                    $baseuom = coitem::select('uom')->where('item_num', $StageLoc->item_num)->where('site_id', $site_id)->value('uom');
                    $tempStageLoc[$key->id]['uom'] = $baseuom;
                }
            }


            // $StageLoc = $StageLoc->toArray();
            // dd($StageLoc, $tempStageLoc);
            // $result = $matl_trans->create($tempStageLoc[$key->id]);
        }
*/
        $cust_num = session()->get('cust_num');

        foreach ($arrNewMltran as $key => $value) {
            // $convertQtyUom = UomConv::convertUOM($baseuom, $value['uom'], '', $value['qty_ship'], $value['item_num'], '', '', 'CO Ship');
            //TEST
            $tempStageLoc[$value['id']]['trans_type'] = $transtype;
            $tempStageLoc[$value['id']]['trans_date'] = $now;
            $tempStageLoc[$value['id']]['ref_num'] = $StageLoc->co_num;
            $tempStageLoc[$value['id']]['ref_line'] = $StageLoc->co_line;
            $tempStageLoc[$value['id']]['ref_release'] = $StageLoc->co_rel;
            $tempStageLoc[$value['id']]['whse_num'] = $StageLoc->whse_num;
            $tempStageLoc[$value['id']]['loc_num'] = $StageLoc->stage_num;
            $tempStageLoc[$value['id']]['lot_num'] = $StageLoc->lot_num;

            if ($StageLoc->uom_conv != $value['uom']) {
                $convertQtyUom = UomConv::convertUOM($StageLoc->uom_conv, $value['uom'], '', $value['qty_ship'], $StageLoc->item_num, $cust_num, '', 'CO Shipping');
                $tempStageLoc[$value['id']]['qty'] = -$convertQtyUom['conv_qty_to_base']['qty'];
            } else {
                $tempStageLoc[$value['id']]['qty'] = -$value['qty_ship'];
            }

            $tempStageLoc[$value['id']]['uom'] = $StageLoc->uom_conv;
            $tempStageLoc[$value['id']]['trans_qty'] = -$value['qty_ship'];
            $tempStageLoc[$value['id']]['trans_uom'] = $value['uom'];
            $tempStageLoc[$value['id']]['item_num'] = $StageLoc->item_num;
            $tempStageLoc[$value['id']]['lpn_num'] = $StageLoc->lpn_num;

            $tempStageLoc[$value['id']]['batch_id'] = $StageLoc->batch_id;
            if ($StageLoc->item_num == "NON-INV") {
                if ($tempStageLoc[$value['id']]['uom'] == "") {
                    $baseuom = coitem::select('uom')->where('item_num', $StageLoc->item_num)->where('site_id', $site_id)->value('uom');
                    $tempStageLoc[$value['id']]['uom'] = $baseuom;
                }
            }
            // $StageLoc = $StageLoc->toArray();
            // dd($StageLoc, $tempStageLoc);
            // $result = $matl_trans->create($tempStageLoc[$value['id']]);
            $request = new Request($tempStageLoc[$value['id']]);
            $result = MatltransService::storeMatlTrans($request);
        }
        //dd($tempStageLoc);
        $result = 0;
        return $result;
    }

    public static function updateJobReceipt($job_num, $qty, $request = null)
    {

        $lastJobRoute = JobRoute::where('job_num', $job_num);
        if (isset($request) && $request->suffix != "") {
            $lastJobRoute = $lastJobRoute->where('suffix', $request->suffix);
        }
        $lastJobRoute = $lastJobRoute->orderBy('oper_num', 'desc')->first();

        $tparm = new TparmView();
        $job = new Job();
        if (isset($request) && $request->suffix != "") {
            $job = $job->where('suffix', $request->suffix);
        }
        $job = $job->where('job_num', $job_num)->first();
        $jobMatl = JobMatl::where('job_num', $job_num);
        if (isset($request) && $request->suffix != "") {
            $jobMatl = $jobMatl->where('suffix', $request->suffix);
        }
        $jobMatl = $jobMatl->select([DB::raw('(qty_issued-qty_returned)>=qty_required as net_qty')]);
        $jobMatlCount = $jobMatl->having('net_qty', ">", 0)->count();

        $allow_auto_complete = $tparm->getTparmValue('JobReceipt', 'allow_auto_complete');
        $allow_over_receive = $tparm->getTparmValue('JobReceipt', 'allow_over_receive');
        $qty_received = $job->qty_completed + $qty;
        // dd($jobMatlCount);
        // dd($allow_auto_complete, $allow_over_receive);
        if ($allow_over_receive == 0) {
            if ($allow_auto_complete == 1) {
                if (($qty_received - $job->qty_returned) == $job->qty_released) {
                    if ($jobMatlCount) {
                        $job->job_status = 'C';
                    } else {
                        $message = "Job order cannot be automatically completed as the received quantity matches the released quantity. Please ensure all required materials are issued before proceeding with job closure";
                        throw ValidationException::withMessages(['details' => $message]);
                        //                return false;
                        return false;
                    }
                }
            }
        }
        // dd($request->all(), $job, $lastJobRoute);
        //Update Job Order
        // $job = new Job();
        // if (isset($request) && $request->suffix != "") {
        //     $job = $job->where('suffix', $request->suffix);
        // }
        // $job = $job->where('job_num', $job_num)->first();

        $job->qty_completed += $qty;

        // if ($job->qty_released == $qty) {
        //     $job->job_status = 'C';
        // }
        $job->save();

        //Update Last Job Route
        // $lastJobRoute = JobRoute::where('job_num', $job_num);
        // if (isset($request) && $request->suffix != "") {
        //     $lastJobRoute = $lastJobRoute->where('suffix', $request->suffix);
        // }
        // $lastJobRoute = $lastJobRoute->orderBy('oper_num', 'desc')->first();
        //        dd($lastJobRoute);
        if ($lastJobRoute) {
            $lastJobRoute->qty_moved += $qty;
            $lastJobRoute->save();
        }
        return true;
    }

    public static function newTOLossMatlTrans($transtype, $request)
    {
        // $now = Carbon::now()->toDateTimeString();
        $trnLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->trn_num)->value('trn_loc');

        $now = session('timestamp');
        $matl_trans = new MatlTrans();
        $transaction = $request->except('_token');
        $transaction['trans_type'] = $transtype;
        $transaction['trans_date'] = $now;
        $transaction['reason_code'] = $request->reason_code;
        $transaction['trans_qty'] = -$request['qty_loss'];
        $transaction['trans_uom'] = $request['uom_loss'];
        $transaction['qty'] = -$request['qty_loss_conv'];
        $transaction['uom'] = $request['uom_loss_conv'];
        $transaction['loc_num'] = $trnLoc;
        $result = $matl_trans->create($transaction);
        return $result;
    }

    public static function updateJobMatl($job_num, $suffix, $oper_num, $sequence, $qty)
    {

        // $job_matl = new JobMatl();
        // $job_matl = $job_matl->where('job_num', $job_num)->where('suffix', $suffix)->where('oper_num', $oper_num)->where('sequence', $sequence)->first();
        $job_matl = new JobMatl();
        $job_matl = $job_matl->where('job_num', $job_num)->where('suffix', $suffix)
            ->where('sequence', $sequence);
        if ($oper_num) {
            $job_matl = $job_matl->where('oper_num', $oper_num);
        }
        $job_matl = $job_matl->first();
        $tparm = new TparmView();
        $allow_over_issue = $tparm->getTparmValue('JobMaterialIssue', 'allow_over_issue');
        if (!$allow_over_issue) {
            if ($job_matl->qty_required - $job_matl->qty_issued < $qty) {
                $balance = is_int($job_matl->qty_required - $job_matl->qty_issued);
                $newqty  = is_int($qty);

                if ($balance < $newqty) {
                    return false;
                }
            }
        }

        $job_matl->qty_issued += $qty;
        $job_matl->save();
        return true;
    }

    public static function checkJobMatl($job_num, $suffix, $oper_num, $sequence, $qty, $batch = false)
    {
        $job_matl = new JobMatl();
        $job_matl = $job_matl->where('job_num', $job_num)->where('suffix', $suffix)
            ->where('sequence', $sequence);
        if ($oper_num) {
            $job_matl = $job_matl->where('oper_num', $oper_num);
        }
        $job_matl = $job_matl->first();
        // dd($job_matl);
        if ($job_matl) {
            $tparm = new TparmView();
            $allow_over_issue = $tparm->getTparmValue('JobMaterialIssue', 'allow_over_issue');

            if ($batch)
                $allow_over_issue = $tparm->getTparmValue('BatchJobMaterialIssue', 'allow_over_issue');

            // dd($allow_over_issue);
            if (!$allow_over_issue) {
                // dd($allow_over_issue, $job_matl->qty_required, $qty);
                if ($job_matl->qty_required < $qty) {
                    return false;
                } else {
                    return true;
                }
            }

            return true;
        }
    }

    public static function updateJobMatlReturn($job_num, $oper_num, $sequence, $qty)
    {
        $job_matl = new JobMatl();
        $job_matl = $job_matl->where('job_num', $job_num)->where('oper_num', $oper_num)->where('sequence', $sequence)->first();
        if ($job_matl->qty_issued < -$qty) {
            return false;
        }
        return true;
    }

    public static function multiJobMatlUpdate($job_num, $oper_num, $whse_num, $loc_num)
    {
        $job = new JobMatl();
        $job = $job->where('job_num', $job_num)->where('oper_num', $oper_num)->get();
        DB::beginTransaction();
        if ($job->count() == 0) {
            return false;
        }
        foreach ($job as $job_matl) {
            //update job_matl

            $qty = $job_matl->qty_required - $job_matl->qty_issued;
            if ($qty > 0) {

                //new matl_trans
                $updateitemlocation = self::updateItemLocationQty($whse_num, $loc_num, $job_matl->matl_item, -$qty, null, $job_matl->uom, 0, null);
                if ($updateitemlocation == false) {
                    DB::rollback();
                    return false;
                }

                $job_matl->qty_issued = $job_matl->qty_required;
                $updatejobmatl = $job_matl->save();

                $now = session('timestamp');
                $matl_trans = new matl_trans();
                $transaction['trans_type'] = 'Job Material Issue';
                $transaction['trans_date'] = $now;
                $transaction['ref_num'] = $job_num;
                $transaction['ref_line'] = $oper_num;
                $transaction['ref_release'] = $job_matl->sequence;
                $transaction['qty'] = $qty;
                $transaction['item_num'] = $job_matl->matl_item;
                $transaction['uom'] = $job_matl->uom;
                $transaction['whse_num'] = $whse_num;
                $transaction['loc_num'] = $loc_num;
                $result = $matl_trans->create($transaction);
            }
        }
        DB::commit();
        return true;
    }

    public static function url_path_encode($url)
    {
        $parts = parse_url($url);
        $path_parts = array_map('rawurldecode', explode('/', $parts['path']));
        $port = isset($parts['port']) ? ':' . $parts['port'] : '';
        return
            $parts['scheme'] . '://' .
            $parts['host'] . $port .
            implode('/', array_map('rawurlencode', $path_parts)) .
            (isset($parts['query']) ? '?' . rawurldecode($parts['query']) : '');
    }

    public static function generateFileName($module)
    {
        $site_id = auth()->user()->site_id;
        $current_date_time = Carbon::now()->timestamp;
        $site_id = str_slug($site_id, "-");

        return $site_id . '/' . $module . $current_date_time;
    }

    public static function generateImportLogName($module, $user = null)
    {
        $site_id = $user ? $user->site_id : auth()->user()->site_id;
        $site_id = str_slug($site_id, "-");

        return now()->format('Y-m-d_H-i-s') . '-' . $site_id . '/' . 'import-' . $module;
    }

    public static function generateExportLogName($module, $user = null)
    {
        $site_id = $user ? $user->site_id : auth()->user()->site_id;
        $site_id = str_slug($site_id, "-");

        return now()->format('Y-m-d_H-i-s') . '-' . $site_id . '/' . 'export-' . $module;
    }

    public static function executeStockMove($request, $batch_id = "")
    {

        DB::beginTransaction();
        try {

            // $requestAB = UOMService::convertRequestItemGlobal($request);

            $convertUom = UomConv::convertUOM($request->base_uom, $request->uom, $request->original_uom, $request->qty, $request->item_num, '', '', '');

            $request['qty_conv'] = $convertUom['conv_qty_to_base']['qty'];
            $request['uom_conv'] = $convertUom['conv_qty_to_base']['uom'];

            // dd($convertUom);
            // dd($requestAB,$request);

            // $fromlocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, -$request->qty_conv, $request->lot_num, $request->uom_conv, 0, null);
            // $tolocation = GeneralService::updateItemLocationQty($request->whse_num, $request->toLoc, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, 0, null);

            $fromrequest = new \Illuminate\Http\Request();
            $torequest = new \Illuminate\Http\Request();

            $fromrequest->replace([
                'trans_type' => config('icapt.transtype.stock_move_from'),
                'whse_num' => $request->whse_num,
                'loc_num' => $request->loc_num,
                'item_num' => $request->item_num,
                'uom' => $request->uom_conv,
                'qty' => -$request->qty_conv,
                'trans_uom' => $request->uom,
                'trans_qty' => -$request->qty,
                'uom_conv' => $request->uom_conv,
                'qty_conv' => -$request->qty_conv,
                'lot_num' => $request->lot_num,
                'document_num' => $request->document_num,
                'batch_id' => $batch_id,
            ]);
            $torequest->replace([
                'trans_type' => config('icapt.transtype.stock_move_to'),
                'whse_num' => $request->whse_num,
                'loc_num' => $request->toLoc,
                'item_num' => $request->item_num,
                'uom' => $request->uom_conv,
                'qty' => $request->qty_conv,
                'trans_uom' => $request->uom,
                'trans_qty' => $request->qty,
                'uom_conv' => $request->uom_conv,
                'qty_conv' => $request->qty_conv,
                'lot_num' => $request->lot_num,
                'document_num' => $request->document_num,
                'batch_id' => $batch_id,
            ]);

            $insertmatl = MatltransService::postTransaction($fromrequest);
            $insertmatl = MatltransService::postTransaction($torequest);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
        DB::commit();
        return true;
    }

    // obsolete function
    // moved to putaway controller
    public static function executePutAway($request)
    {

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');



        DB::beginTransaction();
        try {

            $request = UOMService::convertRequest($request);

            // $fromlocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, -$request->qty_conv, $request->lot_num, $request->uom_conv, 0, null);
            // $tolocation = GeneralService::updateItemLocationQty($request->whse_num, $request->toLoc, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, 0, null);

            $secondrequest = new \Illuminate\Http\Request();
            $secondrequest->replace([
                'trans_type' => 'Put Away From',
                'whse_num' => $request->whse_num,
                'loc_num' => $request->loc_num,
                'lot_num' => $request->lot_num,
                'item_num' => $request->item_num,
                'uom' => $request->uom,
                'qty' => -$request->qty,
                'uom_conv' => $request->uom_conv,
                'qty_conv' => -$request->qty_conv
            ]);
            // if ($sap_trans_order_integration != 1) {
            //     $request['loc_num'] = $request->toLoc;
            // }
            //
            //dd($request->all(), $secondrequest->all());

            $request['loc_num'] = $request->toLoc;
            $request['trans_type'] = 'Put Away To';

            $insertmatl = MatltransService::postTransaction($secondrequest);
            $insertmatl = MatltransService::postTransaction($request);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
        DB::commit();
        return true;
    }

    public static function executeTransferOrderReceipt($request)
    {
        $request['ref_num'] = $request->trn_num;
        $request['ref_line'] = $request->trn_line;

        // DB::beginTransaction();
        // try {

        $baseuom = $request['base_uom'];

        $item_uom = Item::where('item_num', $request->item_num)->where('site_id', auth()->user()->site_id)->pluck('uom')->first();
        $baseuom = $item_uom;
        $selectuom_receipt = $request->uom;
        $lineuom =  $request['base_uom'];
        $selectuom_loss = $request->uom_loss;


        // Get qty receivable for validation
        $to_subline = new TransferOrderLinesSubline();
        $to_subline = $to_subline->select('*', DB::raw("SUM(qty_receivable) as qty_receivable"), DB::raw("SUM(qty_shipped) as qty_shipped"))
            ->where('item_num', $request->item_num)
            ->where('trn_num', $request->trn_num)
            ->where('lpn_num', "")
            ->where('trn_line', $request->trn_line);

        if ($request->lot_num)
        {
            $to_subline->where('trn_lot', $request->lot_num);
        }
        else
        {
            $to_subline->whereNull('trn_lot', $request->lot_num);
        }

        $to_subline = $to_subline->first();


        // dd($request->all());
        //convertUOM($baseuom = '', $selectuom = '', $lineuom = '', $qty, $item_num = '', $cust_num = '', $vend_num = '', $type_mode = '')
        $convertReceiptUom = UomConv::convertUOM($baseuom, $selectuom_receipt, $lineuom, $request->qty, $request->item_num, '', '', __('mobile.nav.to_receipt'));

        // Check for qty receivable
        if ($to_subline->qty_receivable < $convertReceiptUom['conv_qty_to_base']['qty'])
        {
            throw ValidationException::withMessages(['Qty to Receive cannot be more than ' . $to_subline->qty_receivable]);
        }

        // dd($convertReceiptUom);
        if ($request->qty_loss != "") {
            $convertLossUom = UomConv::convertUOM($baseuom, $selectuom_loss, $lineuom, $request->qty_loss, $request->item_num, '', '', __('mobile.nav.to_receipt'));
            $qty_loss_conv  = $convertLossUom['conv_qty_to_base']['qty'];
            $uom_loss_conv  = $convertLossUom['conv_qty_to_base']['uom'];
            $qty_loss_conv_line  = $convertLossUom['conv_qty_to_line']['qty'];
        } else {
            $qty_loss_conv  = 0;
            $qty_loss_conv_line = 0;
            $uom_loss_conv  = $request->uom_loss;
        }

        // dd($qty_loss_conv,$uom_loss_conv);


        $qty_conv = $convertReceiptUom['conv_qty_to_base']['qty'];

        $request['uom_conv'] = $convertReceiptUom['conv_qty_to_base']['uom'];
        $request['qty_conv'] =  $qty_conv;

        $request['uom_loss_conv'] = $uom_loss_conv;
        $request['qty_loss_conv'] = $qty_loss_conv;
        $request['qty_loss_conv_line'] = $qty_loss_conv_line;


        // dd($qty_loss_conv_line);
        //comm
        // $qty_received = $qty_conv;
        //Before TO UOM not able to change, $qty_received was same qty conv now it should get to line.
        $qty_received = $convertReceiptUom['conv_qty_to_line']['qty'];


        $toLossRequest = clone $request;
        // $request = UOMService::convertRequest($request);
        // $qty_received = UOMService::convertTORequest($request);
        // $toLossRequest = UOMService::convertTORequestForQtyLost($toLossRequest);

        //Update TO Lost Matl Trans

        // dd($request,$toLossRequest);
        if ($request->qty_loss != null && $request->qty_loss > 0) {
            $TOLossMatlTrans = GeneralService::newTOLossMatlTrans(config('icapt.transtype.to_lost'), $toLossRequest);
        }

        //Is TO Receipt qty > 0?
        if ($request->qty > 0) {
            //Update to Location
            $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $request->qty_conv, $request->lot_num, $request->uom_conv, 0, null);
            //Remove reason code if TO Lost exists.
            $request['reason_code'] = '';

            $trnLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->trn_num)->value('trn_loc');


            $fromrequest = new \Illuminate\Http\Request();
            $fromrequest->replace([
                'whse_num' => $request->to_whse,
                'loc_num' => $trnLoc,
                'item_num' => $request->item_num,
                'ref_num' => $request->ref_num,
                'ref_line' => $request->ref_line,
                'uom' => $request->uom,
                'qty' => $request->qty,
                'uom_conv' => $request->uom_conv,
                'qty_conv' => $request->qty_conv,
                'lot_num' => $request->lot_num,
                'document_num' => $request->document_num
            ]);

            if ($request['batch_id']) {
                $batch_id = $request['batch_id'];
                $fromrequest['batch_id'] = $batch_id;
            }

            $insertmatl = GeneralService::newMatlTrans(config('icapt.transtype.to_receipt_from'), $fromrequest);
            $insertmatl = GeneralService::newMatlTrans(config('icapt.transtype.to_receipt_to'), $request);
            // Update from Location - Pending Implementation
        }

        // $updateTransOrder = GeneralService::updateTransOrderReceipt($request->trn_num, $request->trn_line, $qty_received, $toLossRequest->qty_loss_conv);

        $updateTransOrder = GeneralService::updateTransOrderReceipt($request->trn_num, $request->trn_line, $qty_received, $qty_loss_conv_line);

        // $updateTransOrderSublines = GeneralService::updateTransOrderLotLocReceipt($request->loc_num, $request->lot_num, $request->trn_num, $request->trn_line, $qty_received, $toLossRequest->qty_loss_conv);
        $updateTransOrderSublines = GeneralService::updateTransOrderLotLocReceipt($request->loc_num, $request->lot_num, $request->trn_num, $request->trn_line, $qty_received, $qty_loss_conv_line);


        // dd($request,$convertReceiptUom,$qty_conv,$qty_loss_conv);
        //

        // $TransitLocQtyOnHand = TOService::TransitQtyOnHand($updateTransOrder->TransferOrder, $updateTransOrder->item_num);


        $itemloc = new ItemLoc;

        // Check if the Transit Location is in Item Location ( From Loc )
        $checkTransitLoc = $itemloc
            ->where('whse_num', $updateTransOrder->to_whse)
            ->where('loc_num', $updateTransOrder->TransferOrder->trn_loc)
            ->where('item_num', $updateTransOrder->item_num)
            ->first();

        $TransitLocQtyOnHand = $checkTransitLoc->qty_on_hand - $request->qty_conv -  $qty_loss_conv;
        // dd($checkTransitLoc->qty_on_hand,$TransitLocQtyOnHand, $request->qty_conv, $qty_loss_conv);
        //   dd($TransitLocQtyOnHand,"dvdaavd");
        if ($checkTransitLoc) {
            // Update the Transit Location's qty on hand
            $resultItemLoc = $itemloc->updateOrCreate(
                [
                    'whse_num' => $updateTransOrder->to_whse,
                    'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                    'item_num' => $updateTransOrder->item_num,
                ],
                [
                    'qty_on_hand' => $TransitLocQtyOnHand,
                ]
            );
        }
        // Create the Transit Location
        else {
            $resultItemLoc = $itemloc->updateOrCreate(
                [
                    'whse_num' => $updateTransOrder->to_whse,
                    'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                    'item_num' => $updateTransOrder->item_num,
                ],
                [
                    'qty_on_hand' => $TransitLocQtyOnHand,
                    // 'uom' => $updateTransOrder->uom,
                    'uom' => $baseuom,
                    'rank' => app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($updateTransOrder->to_whse), base64_encode($updateTransOrder->item_num), base64_encode($updateTransOrder->TransferOrder->trn_loc)),
                ]
            );
        }

        if ($resultItemLoc->qty_on_hand <= 0) {
            $resultItemLoc->delete();
        }
        // In transline only 1 lot , shd follow reques lot
        // dd($updateTransOrderSublines);
        if ($updateTransOrderSublines->trn_lot) {
            // LotLoc for original for deduct
            // $TransitLotLocQtyOnHand = TOService::TransitQtyOnHandTOReceipt($updateTransOrder->TransferOrder, $updateTransOrderSublines->trn_num, $updateTransOrderSublines->trn_lot);
            $TransitLotLocQtyOnHand = $temp = LotLoc::select('qty_on_hand')->where('whse_num', $updateTransOrder->to_whse)->where('item_num', $updateTransOrder->item_num)
                ->where('lot_num', $updateTransOrderSublines->trn_lot)->where('loc_num', $updateTransOrder->TransferOrder->trn_loc)->value('qty_on_hand');
            //dd($updateTransOrder->TransferOrder,$TransitLotLocQtyOnHand);
            // $TransitLotLocQtyOnHand = $TransitLotLocQtyOnHand - $request->qty_conv - $request->qty_conv_loss;

            $TransitLotLocQtyOnHand = $TransitLotLocQtyOnHand - $request->qty_conv - $qty_loss_conv;

            // dd($temp, $request->qty_conv, $request->qty_conv_loss);
            $lotloc = new LotLoc;
            $resultItemlotloc = $lotloc->updateOrCreate([
                'whse_num' => $updateTransOrder->to_whse,
                'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                'item_num' => $updateTransOrder->item_num,
                'lot_num' => $updateTransOrderSublines->trn_lot,
            ], [
                'qty_on_hand' => $TransitLotLocQtyOnHand,
                // 'uom' => $updateTransOrder->uom,
                'uom' => $baseuom,
            ]);

            if ($resultItemlotloc->qty_on_hand <= 0) {
                $resultItemlotloc->delete();
            }
        }


        // if ($updateTransOrderSublines->lot_num) {
        //     $qty = $request->qty;
        //     $TransitLotLocQtyOnHand = TOService::TransitQtyOnHand($updateTransOrderSublines->TransferOrder,$qty, $updateTransOrderSublines->item_num, $updateTransOrderSublines->lot_num);
        //     $lotloc = new LotLoc;
        //     $resultItemlotloc = $lotloc->updateOrCreate([
        //         'whse_num' => $updateTransOrderSublines->to_whse,
        //         'loc_num' => $updateTransOrderSublines->TransferOrder->trn_loc,
        //         'item_num' => $updateTransOrderSublines->item_num,
        //         'lot_num' => $updateTransOrderSublines->lot_num,
        //             ], [
        //         'qty_on_hand' => $TransitLotLocQtyOnHand,
        //         'uom' => $updateTransOrderSublines->uom,
        //     ]);
        //     if ($resultItemlotloc->qty_on_hand <= 0) {
        //         $resultItemlotloc->delete();
        //     }
        // }
        // } catch (\Exception $e) {
        //     DB::rollback();
        //     throw $e;
        // }
        // DB::commit();

        return true;
    }

    public function executeTransferOrderShip($request)
    {
        dd($request->all());
        $trn_num = $request->trn_num;
        $trn_line = $request->trn_line;
        $whse_num = $request->from_whse;
        $item_num = $request->item_num;
        $loc_num = $request->loc_num;
        $to_whse = $request->to_whse;
        $to_loc = $request->to_loc;
        $lot_num = $request->lot_num;
        $qty = $request->qty_to_ship;
        $uom = $request->uom;
        $request['ref_num'] = $request->trn_num;
        $request['ref_line'] = $request->trn_line;
        $request['qty'] = $request->qty_to_ship;
        $request['skip_item_base_check'] = true;
        // DB::beginTransaction();

        // try {
        $request['base_uom'] = $baseuom = Item::where('item_num', $item_num)->pluck('uom')->first();
        // Conversion for inventory and material trans
        // $request = UOMService::convertRequest($request);

        // Conversion for TO Line
        // $qty_shipped = UOMService::convertTORequest($request);

        $selectuom_receipt = $request->uom;
        $lineuom =  $request['to_uom'];

        // dd($request->all());
        //convertUOM($baseuom = '', $selectuom = '', $lineuom = '', $qty, $item_num = '', $cust_num = '', $vend_num = '', $type_mode = '')
        $convertReceiptUom = UomConv::convertUOM($baseuom, $selectuom_receipt, $lineuom, $qty, $item_num, '', '', __('mobile.nav.to_receipt'));
        $qty_conv = $convertReceiptUom['conv_qty_to_base']['qty'];

        $request['uom_conv'] = $convertReceiptUom['conv_qty_to_base']['uom'];
        $request['qty_conv'] =  $qty_conv;


        //comm
        // Conversion for TO Line
        $qty_shipped = $convertReceiptUom['conv_qty_to_line']['qty'];

        // dd($qty_shipped, $qty_conv);
        // dd($convertReceiptUom);
        // Update Allocation
        $allocations = Allocation::where('order_type', 'Transfer Order')
            ->where('ref_num', $request->trn_num)
            ->where('ref_line', $request->trn_line)
            ->where('item_num', $request->item_num)
            ->get();

        foreach ($allocations as $allocation) {
            $allocation_location = $allocation->allocation_locations()->where('loc_num', $request->loc_num)->where('lot_num', $request->lot_num)->first();
            if ($allocation_location) {
                // Minus qty allocated in table: allocation_locations
                $allocation_location->qty_manual_allocated = number_format($allocation_location->qty_allocated - $request->qty_conv, 5, '.', '');
                $allocation_location->save();

                // Minus qty allocated in table: allocations
                $allocation->qty_manual_allocated = number_format($allocation->qty_allocated - $request->qty_conv, 5, '.', '');
                $allocation->save();
            }
        }

        //Update From Location
        // $updateItemLocation = $this->updateItemLocationQty($whse_num, $loc_num, $item_num, -$request['qty_conv'], $lot_num, $request['uom_conv'], 0, null);

        $request['whse_num'] = $request->from_whse;

        // $trnLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->trn_num)->value('trn_loc');

        $fromrequest = new \Illuminate\Http\Request;
        $fromrequest->replace([
            'trans_type' => config('icapt.transtype.to_shipping_from'),
            'whse_num' => $request->whse_num,
            'loc_num' => $request->loc_num,
            'lot_num' => $request->lot_num,
            'item_num' => $request->item_num,
            'ref_num' => $request->ref_num,
            'ref_line' => $request->ref_line,
            'uom' => $request->uom_conv,
            'qty' => -$request->qty_conv,
            'trans_uom' => $request->uom,
            'trans_qty' => -$request->qty,
            'uom_conv' => $request->uom_conv,
            'qty_conv' => -$request->qty_conv,
            'document_num' => $request->document_num,
            'batch_id' => $request->batch_id
        ]);

        $torequest = new \Illuminate\Http\Request();
        $torequest->replace([
            'trans_type' => config('icapt.transtype.to_shipping_to'),
            'whse_num' => $request->to_whse,
            'loc_num' => $request->to_loc,
            'item_num' => $request->item_num,
            'ref_num' => $request->ref_num,
            'ref_line' => $request->ref_line,
            'uom' => $request->uom_conv,
            'qty' => $request->qty_conv,
            'trans_uom' => $request->uom,
            'trans_qty' => $request->qty,
            'uom_conv' => $request->uom_conv,
            'qty_conv' => $request->qty_conv,
            'lot_num' => $request->lot_num,
            'document_num' => $request->document_num,
            'batch_id' => $request->batch_id
        ]);

        // $insertmatl = $this->newMatlTrans(config('icapt.transtype.to_shipping_from'), $request);
        // $insertmatl = $this->newMatlTrans(config('icapt.transtype.to_shipping_to'), $torequest);

        // if item is catch weight
        if(@$request->catch_weight == 1){

            // qty shipped was not taking uom conv from item base uom so changed to qty_conv
            $updateTransOrder = $this->updateTransOrderShipping($trn_num, $trn_line, $qty_shipped, $lot_num, $request['qty_conv'], $request['uom_conv']);
            $addUpdateTransferOrder = $this->addUpdateTransferOderLot($qty_shipped, $loc_num, $request->item_num, $uom, $lot_num, $trn_num, $trn_line, "");
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($fromrequest, $request->tolerance_uom, config('icapt.transtype.to_shipping_from'), config('icapt.transtype.to_shipping_from'));
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($torequest, $request->tolerance_uom, config('icapt.transtype.to_shipping_to'), config('icapt.transtype.to_shipping_to'));
        }
        else{
            // qty shipped was not taking uom conv from item base uom so changed to qty_conv
            $updateTransOrder = $this->updateTransOrderShipping($trn_num, $trn_line, $qty_shipped, $lot_num, $request['qty_conv'], $request['uom_conv']);
            $addUpdateTransferOrder = $this->addUpdateTransferOderLot($qty_shipped, $loc_num, $request->item_num, $uom, $lot_num, $trn_num, $trn_line, "");
            // latest matl trans
            $insertmatl = MatltransService::postTransaction($fromrequest);
            $insertmatl = MatltransService::postTransaction($torequest);
        }


        // } catch (\Exception $e) {
        //     DB::rollback();
        //     throw $e;
        // }
        // DB::commit();
        return true;
    }

    public static function executeJobMatlReturn($request)
    {

        $job_num = $request->job_num;
        $oper_num = $request->oper_num;
        $sequence = $request->sequence;
        $loc_num = request('loc_num');
        $item_num = request('item_num');
        $lot_num = request('lot_num');
        $whse_num = request('whse_num');

        // dd($request->all());
        DB::beginTransaction();
        try {
            // Conversion for inventory and material trans - convert to item UOM
            $request = UOMService::convertRequest($request);

            $updateJobMatl = GeneralService::updateJobMatlReturn($job_num, $oper_num, $sequence, -$request->qty_conv);
            $newTrans = GeneralService::newTrans(
                config('icapt.transtype.job_matl_unissue'),
                $request,
                $whse_num,
                $loc_num,
                $item_num,
                $request->qty_conv,
                $lot_num,
                $request->uom_conv
            );
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
        DB::commit();
        return true;
    }

    public static function executeJobReceipt($request)
    {

        $tparm = new TparmView();
        $allow_incomplete_qty = $tparm->getTparmValue('JobReceipt', 'allow_incomplete_qty');
        $job = Job::where('job_num', $request->ref_num)->first();
        //added validation at frontend
        //        if (!$allow_incomplete_qty) {
        //            if ($request->qty > $job->qty_available) {
        //                throw ValidationException::withMessages(['details' => __('error.admin.incomplete_qty1')]);
        //                return false;
        //            }
        //        }

        DB::beginTransaction();
        try {
            $uom_conv = UomConv::convert($request->uom, $request->qty, $request->item_num, null, null, null);
            $updateItemLocation = GeneralService::updateItemLocationQty($request->whse_num, $request->loc_num, $request->item_num, $uom_conv['qty'], $request->lot_num, $request->uom, 0, null);
            $updateJobReceipt = GeneralService::updateJobReceipt($request->ref_num, $uom_conv['qty'], $request);
            $insertMatl = GeneralService::newMatlTrans(config('icapt.transtype.job_receipt'), $request);
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
        DB::commit();
        return true;
    }

    public static function customIsDirty($request, $item, $attribute)
    {

        if ($request[$attribute] != $item[$attribute]) {

            return true;
        } else {
            return false;
        }
    }

    public static function addIntegerationLog($type, $process_name, $post_from, $data, $response, $extras = [], $status = 0, $method = 'post', $site_id = null)
    {

        //        dd($process_name, $post_from, $data, $response);
        if ($process_name == "" || $post_from == "")
            return;
        if ($site_id == null) {
            $site_id = auth()->user()->site_id;
        }
        $user = auth()->user();
        $id =  DB::table('integration_logs')->insertGetId([
            'trans_num' => 0,
            'site_id' => $site_id,
            'type' => $type,
            'method' => $method,
            'process_name' => $process_name,
            'post_from' => $post_from,
            'post_data' => json_encode($data),
            'response' => json_encode($response),
            'extras' => json_encode($extras),
            'status' => $status,
            'created_by' => $user ? $user->name : "System",
            'created_date' => now(),
            'modified_date' => now(),
        ]);

        // $monthRange = 3;
        $dateRange = config('icapt.auto_delete_integration_logs');

        // if (config('icapt.is_saas')) {
        //     $monthRange = 2;
        // }

        dispatch(new DeleteIntegrationLog($id, $dateRange))->delay(now()->addDays($dateRange)); // Run background job to delete integration log after 1 month

        return $id;
    }
    // Update ID log
    public static function updateIntegerationLog($id,  $response,  $status, $tran_num)
    {
        DB::table('integration_logs')
            ->where('id', $id)
            ->update(['response' => json_encode($response), 'status' => $status, 'trans_num' => $tran_num]);
    }

    public static function addIntegerationLogs($type, $process_name, $post_from, $data, $response, $extras = [], $status = 0,$origindata=null, $method = 'post', $site_id = null)
    {

        //        dd($process_name, $post_from, $data, $response);
        if ($process_name == "" || $post_from == "")
            return;
        if ($site_id == null) {
            $site_id = auth()->user()->site_id;
        }
        $user = auth()->user();
        $id =  DB::table('integration_logs')->insertGetId([
            'trans_num' => 0,
            'site_id' => $site_id,
            'type' => $type,
            'method' => $method,
            'process_name' => $process_name,
            'post_from' => $post_from,
            'post_data' => json_encode($data),
            'response' => json_encode($response),
            'extras' => json_encode($extras),
            'post_origin_data' => json_encode($origindata),
            'status' => $status,
            'created_by' => $user ? $user->name : "System",
            'created_date' => now(),
            'modified_date' => now(),
        ]);

        $dateRange = config('icapt.auto_delete_integration_logs');

        // if (config('icapt.is_saas')) {
        //     $monthRange = 2;
        // }

        dispatch(new DeleteIntegrationLog($id, $dateRange))->delay(now()->addDays($dateRange)); // Run background job to delete integration log after 1 month

        return $id;
    }




    public static function formateQuantity($num)
    {
        return number_format($num, 4, '.', '');
    }
    public static function getStorage()
    {
        if (config('icapt.is_saas')) {
            return Storage::disk('azure');
        } else {
            return Storage::disk('public');
        }
    }
    public static function getStorageName()
    {
        if (config('icapt.is_saas')) {
            return "azure";
        } else {
            return "public";
        }
    }
    public static function showStorageMedia($url)
    {
      return route('showMedia')."?src=$url";
    }

    public static function autoCreateItemWhse($request, $arr_whse_field = ['whse_num'])
    {
        if ($request->item_num != "NON-INV") {
            foreach ($arr_whse_field as $whse_field) {
                $whse = $request[$whse_field];
                // validate item warehouse
                $validateItemWhse = new \App\Http\Controllers\ApiController();
                $newrequest = new \Illuminate\Http\Request;

                $newrequest->replace([
                    'whse_num' => base64_encode($whse),
                    'item_num' => base64_encode($request->item_num)
                ]);
                $validateItemWhse = $validateItemWhse->itemValidationCommon($newrequest);

                if ($validateItemWhse !== "true") {
                    // return error msg
                    return json_decode($validateItemWhse);
                }

                // create item warehouse
                $item = Item::where('item_num', $request->item_num)->first();
                $createItemWhse = $item->createItemWarehouse($item, $whse);
            }
        }

        return "true";
    }
}
