<?php

namespace App\Http\Controllers\MasterMaintenance;

use App\Allocation;
use App\CustomerOrder;

use App\Http\Requests\CustomerOrderHeaderRequest;
use App\Http\Requests\CustomerOrderRequest;
use App\DataTables\Master\CustomerOrderDataTable;
use App\Http\Requests\FileImportRequest;
use App\ImportExportLog;
use App\Imports\COImport;
use App\Services\GeneralService;
use App\Services\ImportService;
use App\SiteSetting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\CustomerOrderItem;
use App\Services\OverrideQtyService;
use Excel;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Throwable;
use Yajra\DataTables\Facades\DataTables;
use App\OrderNumberDefinition;
use App\Services\OrderNumberDefinitionService;
use App\View\TparmView;
use App\ShippingZone;
use App\PicklistTestItems;
use App\MatlTrans;
use Illuminate\Support\Facades\Lang;
use App\Traits\GetAddLineButton;

use App\Services\SapCallService;
use App\Services\SapApiCallService;
use Alert;
use App\helpers;


class COHeaderController extends Controller
{
    private $name = 'COHeader';
    private $table = 'customer_orders';

    use \App\Traits\HasCustomFields;
    use GetAddLineButton;

    public function __construct()
    {

        $this->middleware('auth', ['except' => ['coHeaderExist','exist']]);
        $this->middleware('can:hasCOMaintenance', ['except' => ['coHeaderExist','exist']]);
    }

    public function import(FileImportRequest $request)
    {
        $errorarray = array();
        try {
            $input = $request->validated();
            $import = new COImport;
            $import->import(request()->file('file'));
        } catch (ValidationException $e) {
            foreach ($e->errors() as $error) {
                array_push($errorarray, $error[0]);
            }
        } catch (Throwable $e) {
            array_push($errorarray, $e->getMessage());
        }

        $rowcount = $import->getRowCount();

        if (!empty($errorarray)) {
            $url = ImportService::logFailedImport($this->name, $errorarray, $request->file('file')->getClientOriginalName(),$rowcount);
            return back()->with('errormsg', 'Import failed. ' . '<a href="' . $url . '" target="_new">View Log file</a>.');
        } else {
            ImportExportLog::logImportSuccess($this->name, $request->file('file')->getClientOriginalName(),$rowcount);
            return back()->with('successmsg', __('success.imported',  ['rows' => $import->getRowCount()]));
        }

    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(CustomerOrderDataTable $customerOrderDataTable)
    {
        return view('MasterMaintenance.co.index');
        //return $customerOrderDataTable->render('MasterMaintenance.co.table');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
       $customFields = $this->getCustomFields();
       $site_id = auth()->user()->site_id;
       $order_type = 'CO';
       $co_num_generate = OrderNumberDefinitionService::getGenerateCode($order_type,$site_id);

       $prefix = OrderNumberDefinition::select('prefix_define')->where('status','Y')->where('order_type','CO')->first();

       if($prefix==null)
       {
        $prefix['prefix_define']="";
       }
       else{
        @$prefix = $prefix->toArray();
       }



        return view('MasterMaintenance.co.add')->with('customFields', $customFields)->with('CONum',$co_num_generate)->with('prefix_define',$prefix['prefix_define']);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\CustomerOrderStoreRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CustomerOrderHeaderRequest $request)
    {
        $request = validateSansentiveValue($request);
        $prefix = OrderNumberDefinition::select('prefix_define')->where('status', 'Y')->where('order_type', 'CO')->first();

        if ($prefix == NULL) {
            @$prefix == "";
        } else {
            @$prefix = $prefix->toArray();
        }

        $shipping_zone1 = ShippingZone::select('shipping_zone_code')->where('shipping_zone_code',$request->shipping_zone_code)->where('shipping_zone_status',1)->exists();

        if($request->shipping_zone_code!=null){

            if (!$shipping_zone1) {
                throw ValidationException::withMessages([__('error.admin.notexist_notactive',['resource' => __('admin.label.shipping_zone_code')])]);
            }

        }

        $existingHeader = CustomerOrder::where('co_num', $request->co_num)->first();
        if ($existingHeader)
            throw ValidationException::withMessages(['CO Number ['.$request->co_num.'] already exists']);

        $record = $request->except('_token', 'prefix', 'action');

        $co = CustomerOrder::create($record);
        session()->put('stored_co_id',$co->id);
        // $prefix = $request->prefix;
        $prefix = @$prefix['prefix_define'];
        $conum  = $request->co_num;
        if ($request->action == 'save_and_add'){
            $site_id = auth()->user()->site_id;
            $order_type = 'CO';
            $co_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type,$site_id,$prefix,$conum);
            return redirect()->route('addCO', $co->id);
        }
        else{
            $site_id = auth()->user()->site_id;
            $order_type = 'CO';
            $co_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type,$site_id,$prefix,$conum);

            return redirect()->route('CO')
                ->with('successmsg', __('success.addedv2',
                    ['resource' => __('Customer Order'), 'name' => $co->name]));
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(CustomerOrder $co)
    {

        $co = $co->load('customer');
        $customFields = $this->getCustomFields();
        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly="";
        if($sap_trans_order_integration==1 &&  auth()->user()->name!="sa")
        {
            $sap_readonly = "disabled";
        }

        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if($sap_trans_order_integration==1 && config('icapt.enable_cust_name_notfrom_cust_table') == true)
        {
            $cust_name = $co->cust_name;
        }
        else{
            $cust_name = $co->customer->cust_name;
        }

        if($co->co_status == 'C')
        {
           $status_disabled  = "disabled";
            $tooltips = "tooltip";
            $title = Lang::get('admin.tooltip.showaddline');
            $tooltipsdeletemsg = __('admin.tooltip.deleteaddline');
            $tooltipseditmsg = __('admin.tooltip.editaddline');
            $isDisabled = "disabled";

        }
        else{
            $status_disabled  = "";
            $tooltips = null;
            $title = null;
            $tooltipsdeletemsg =null;
            $tooltipseditmsg ="";
            $isDisabled = "";
        }
        $buttonName = Lang::get('admin.button.add_co_line');

        $route = route('addCO', ['customer_order' => $co->id, 'view' => base64_encode($co->co_num)]);
        $buttonHtml = $this->getAddLineButton($sap_readonly, $route,$status_disabled ,$tooltips,$buttonName,$title);



        return view('MasterMaintenance.co.view',compact('buttonHtml'))->with('isDisabled',$isDisabled)->with('tooltipsdeletemsg',$tooltipsdeletemsg)->with('tooltipseditmsg',$tooltipseditmsg)->with('status_disabled',$status_disabled)->with('tooltips',$tooltips)->with('status_disabled',$status_disabled)->with('sap_readonly',$sap_readonly)->with('cust_name',$cust_name)->with('customFields', $customFields)->with('details',$co)->with('total_quantity_format',$total_quantity_format)->with('unit_quantity_format',$unit_quantity_format);
    }

    public function showByCoNum($co_num)
    {

        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');


        // Datatable's data
        if (request()->ajax()) {
            $co_num = htmlspecialchars_decode(base64_decode($co_num));
            $co_item = CustomerOrderItem::where('co_num', $co_num)->orderby('co_line')->orderby('co_rel')->get();
            $result = DataTables::of($co_item)->make(true);
            return $result;
        }
        else{
            if ($this->is_base64_string($co_num)) {
                $co_num = base64_decode($co_num);
            }
            else{
                $co_num = utf8_encode(htmlspecialchars_decode(base64_decode($co_num)));
            }
        }
        $co = CustomerOrder::where('co_num', $co_num)->firstOrFail();
        //dd($co->customer);
        //If no PO, create one
        if (!$co) {
            $co = CustomerOrderItem::createHeader($co_num);
        }
        //PO item doesnt exist
        if (!$co){
            return redirect()->route('co.add');
        }

        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if($sap_trans_order_integration==1 && config('icapt.enable_cust_name_notfrom_cust_table') == true)
        {
            $cust_name = $co->cust_name;
        }
        else{
            $cust_name = $co->customer->cust_name;
        }

        $tparmSAP = new TparmView;
        $sap_trans_order_integration = $tparmSAP->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly = "";
        if($sap_trans_order_integration==1 &&  auth()->user()->name!="sa")
        {
            $sap_readonly = "disabled";
        }

        if($co->co_status == 'C')
        {
              $status_disabled = "disabled";
            $tooltips = "tooltip";

           $title = Lang::get('admin.tooltip.showaddline');
           $tooltipsdeletemsg = __('admin.tooltip.deleteaddline');
            $tooltipseditmsg = __('admin.tooltip.editaddline');
            $isDisabled = "disabled";


        }
        else{
             $status_disabled = "";
             $tooltips = null;
             $title = null;
             $tooltipsdeletemsg =null;
            $tooltipseditmsg ="";
            $isDisabled = "";

        }

        $buttonName = Lang::get('admin.button.add_co_line');

        $route = route('addCO', ['customer_order' => $co->id, 'view' => base64_encode($co->co_num)]);
        $buttonHtml = $this->getAddLineButton($sap_readonly, $route,$status_disabled,$tooltips,$buttonName,$title);


        $customFields = $this->getCustomFields();
        return view('MasterMaintenance.co.view',compact('buttonHtml'))->with('isDisabled',$isDisabled)->with('tooltipsdeletemsg',$tooltipsdeletemsg)->with('tooltipseditmsg',$tooltipseditmsg)->with('status_disabled',$status_disabled)->with('tooltips',$tooltips)->with('sap_readonly',$sap_readonly)->with('cust_name',$cust_name)->with('customFields', $customFields)->with('details',$co)->with('total_quantity_format',$total_quantity_format)->with('unit_quantity_format',$unit_quantity_format);
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param  CustomerOrder  $co
     * @return \Illuminate\Http\Response
     */
    public function edit(CustomerOrder $co)
    {
        $co = $co->load('customer');
        $customFields = $this->getCustomFields();
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly = "";
        // Check if the co_num is already in picklist pending table. If yes then user cannot edit the shipping zone
        $checkPickListItem = PicklistTestItems::where('ref_num', $co->co_num)->exists();
        $matlTransExists = MatlTrans::where('ref_num', $co->co_num)->exists();


        if($sap_trans_order_integration==1)
        {
            $cust_name = $co->cust_name;
        }
        else{
            $cust_name = $co->customer->cust_name;
        }



        return view('MasterMaintenance.co.edit')->with('customFields', $customFields)->with('details', $co)->with('cust_name',$cust_name)->with('picklist_exist',$checkPickListItem)->with('matl_trans_exist', $matlTransExists);
    }

      /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, CustomerOrder $co)
    {

        // $co = CustomerOrder::where('id', $id)->first();
        // // dd($id);
        // if (!$co) {
        //     return redirect(route('CO'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.customer_order'), 'resource1' => __('admin.button.update'), 'resource2' => $id]));
        // }
        $shipping_zone1 = ShippingZone::select('shipping_zone_code')->where('shipping_zone_code',$request->shipping_zone_code)->where('shipping_zone_status',1)->exists();

        if($request->shipping_zone_code!=null){

            if (!$shipping_zone1) {
                throw ValidationException::withMessages([__('error.admin.notexist_notactive',['resource' => __('admin.label.shipping_zone_code')])]);
            }

        }

        DB::beginTransaction();
        try {
            if ($request->co_status == "C") {
                // Throw error if data is in Allocation and allocation's status is Open / Generated
                if (Allocation::where('ref_num',$co->co_num)->where('order_type','Customer Order')->whereIn('status',['Open','Generated'])->exists()) {
                    throw ValidationException::withMessages([__('error.admin.error_exists_in_with_status',['resource1' => __('admin.option.co_num'), 'resource2' => __('admin.label.allocation'), 'resource3' => 'Open or Generated'])]);
                }
            }

            // Keep previous CO Header Status before update.
            $prev_co_status = $co->co_status;

            if($prev_co_status == 'C'){ // Check prev status is Completed
                $record = $request->except('_token','_method','co_num','cust_num','cust_name','add_num','co_notes','due_date');

                DB::table('coitems')->where('co_num', $co->co_num)->update([
                    'shipping_zone' => $request->shipping_zone_code,
                ]);
            }
            else if($prev_co_status == 'O'){ // Check prev status is Open
                $record = $request->except('_token','_method','co_num');

                $checkPickListItem = PicklistTestItems::where('ref_num', $co->co_num)->exists();

                if ($checkPickListItem)
                {
                    unset($record['shipping_zone_code']); // remove shipping_zone_code
                    unset($record['cust_num']); // remove cust_num
                    unset($record['cust_name']); // remove cust_name
                }
            }

            $co->update($record);
            $co->save();  // Observe: CustomerOrderHeaderObserver updating()

            // Record status change to value change log
            if ($prev_co_status != $co->co_status) {
                OverrideQtyService::newOverRideHistory(config('icapt.form.co'), 'Status',
                $prev_co_status,
                $co->co_status,
                $co->co_num,
                null,
                null);
            }

        // Post SAP direct close
        $tparm = new TparmView;
        $sap_trans_order_integration= $tparm->getTparmValue('System', 'sap_trans_order_integration');
        if($request->co_status=='C' && $sap_trans_order_integration==1 ){

            $type = "CO";
            $arrConfigType   = getPostNotificationToSAP($type);
            $U_DocType       = $arrConfigType['U_DocType'];
            $post_from       = $arrConfigType['post_from'];
            $sap_service     = $arrConfigType['sap_service'];
            $process_name    = $arrConfigType['process_name'];
            $service_name    = $arrConfigType['service_name'];            
            
            $arrData = array(
                "U_DocType" => $U_DocType,
                "U_DocEntry" => $co->erp_ID ?? null,
                "U_DocNum" => $co->co_num ?? null ,
                "U_ItemCode" => @$co->item_num ?? null,
                "U_LineNum" =>@$co->co_line ?? null,
                "U_Qty" => @$qty ?? null,
                "U_WhsCode" => @$co->from_whse ?? null,
                "U_ToWhsCode" => @$co->to_whse ?? null,
                "U_ManualClosed" =>'Y',
                "id" => @$co->id ?? null,
                "process_name" => $process_name,
                "post_from" => $post_from,
                "service_name" => $service_name
             );
             
             // 1 = Send Close API ; 0= Just Send Notification
             $result = SapCallService::postNotificationAndCloseAction($arrData,1);
            if($result!=200)
            {
                throw ValidationException::withMessages([__('error.mobile.sap_error'), __('error.mobile.sap_error_contact').$result]);
                //Alert::error( __('error.mobile.sap_error'), __('error.mobile.sap_error_contact').$result)->persistent('Dismiss');
            }

        }





            DB::commit();  // Observe: CustomerOrderObserver updating(), updated()
            return redirect()->route('co.show', ['customer_order' => $co->id])
                ->with('successmsg', __('success.updated',
                    ['resource' => __('Customer Order'), 'name' => $co->name]));
        }
        catch(Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(CustomerOrder $co, Request $request)
    {
        $co->delete();  // Observe: CustomerOrderHeaderObserver deleting()

        // This come from cancel button of MasterMaintenance.co_item.add
        if ($request->ajax()) {
            return 'true';
        }

        return redirect()->route('CO')
            ->with('successmsg', __('success.deleted', ['resource' => __('Customer Order')]));
    }

    public function coapi(CustomerOrderItem $co)
    {
        //get all
        $coitem = $co->orderby('due_date','desc')->orderby('co_num','asc')->orderby('co_line','asc')->orderby('co_rel','asc')->get();
        ini_set('memory_limit', '-1');
        set_time_limit(0);
        return json_encode($coitem);
    }

    public function ap_delete(Request $request)
    {
        $myString = $request->id;
        $myArray = explode(',', $myString);
        CustomerOrderItem::destroy($myArray);  // Observe: CustomerOrderObserver deleting()

        //if no row selected or request is null
        if($myString == "null" || $myString == "")
        {
            return redirect(route('CO'))->with('errormsg', __('error.admin.selectone'));
        }

        //if >1 selected and successful
        else{
            return redirect(route('CO'))->with('successmsg', __('success.deleted', ['resource' => __('Customer Order Line')]));
        }
    }

    public function coHeaderExist(Request $request) {
        $data = CustomerOrder::with('coitems')->where('co_num',$request->co_num)->first();
        if ($data) {
            // Delete old data of customer order that has no coitems
            if (count($data->coitems) == 0) {
                $data->delete();  // Observe: CustomerOrderHeaderObserver deleting()
            }
            else {
                return 'exist';
            }
        }
        else{
            return 'not exist';
        }
    }

    public function exist($co_num)
    {
        $data = CustomerOrderItem::where('co_num', $co_num)->exists();
        if($data){
            return 'exist';
        }else{
            return 'not exist';
        }
    }

    public function is_base64_string($s)
    {
        //Regex!
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s) || (strlen($s) % 4 != 0))
            return false;

        // first check if we're dealing with an actual valid base64 encoded string
        if (($b = base64_decode($s, TRUE)) === FALSE) {
            return FALSE;
        }

        // now check whether the decoded data could be actual text
        $e = mb_detect_encoding($b);
        if (in_array($e, array('UTF-8', 'ASCII'))) { // YMMV
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function validateCOHeader(Request $request)
    {
        if(isset($request->co_num)){
            $result = CustomerOrder::where('co_num', $request->co_num)->first();
            return $result ? "false" : "true";
        }
    }

}
