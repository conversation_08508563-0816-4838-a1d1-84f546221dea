        <style>
            i.icon-chevron-right2,
            i.icon-chevron-down2 {
                margin-right: 0px !important;
                float: right !important;
            }

            .show,
            .hide {
                position: absolute;
            }

            .show {
                display: none;
            }

            li.nav-item.has-sub {
                display: block !important;
            }
        </style>

        <!-- main menu-->
        <div data-scroll-to-active="false" class="main-menu menu-fixed menu-dark menu-accordion menu-shadow">
            <!-- main menu header-->
            @php
                //@if (@auth()->user()->site_id);
                $plan = \App\SiteSetting::select('plan_id')
                    ->where('site_id', @auth()->user()->site_id)
                    ->where('status', 1)
                    ->value('plan_id');
                $tparm = new \App\View\TparmView();
                $sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
                $plan = @customDecrypt($plan);
                $starterOrFree = $starterOnly = false;

                if ($plan == 1 || $plan == 4) {
                    $starterOnly = true;
                }
                if ($plan == 1 || $plan == 4 || $plan == 7) {
                    $starterOrFree = true;
                }
            @endphp


            <div class="main-menu-content">
                <ul id="main-menu-navigation" data-menu="menu-navigation" class="navigation navigation-main">
                    {{-- Home --}}
                    <li class=" nav-item"><a href="{{ route('web') }}"><i class="icon-home3"></i><span
                                data-i18n="nav.dash.main" class="menu-title">{{ __('admin.menu.home') }}</span></a></li>
                    {{-- Dashboard --}}
                    @if (config('icapt.icapt_logo') == false)
                        <li class=" nav-item"><a href="{{ route('dashboard') }}"><i class="icon-grid2"></i><span
                                    data-i18n="nav.dash.main"
                                    class="menu-title">{{ __('admin.menu.dashboard') }}</span></a></li>
                    @endif
                    {{-- Administration --}}
                    <li class=" nav-item {{ request()->segment(1) == 'administration' ? 'open' : '' }}"><a
                            href="#" class="item-list1"><i class="icon-user-md"></i><span
                                data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.admins') }}</span></i></a>
                        <ul class="menu-content">
                            <li><a href="{{ route('userlist') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('admin.menu.users') }}</a></li>
                            <li><a href="{{ route('usergrouplist.index') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('admin.menu.user_groups') }}</a></li>
                            <li><a href="{{ route('grouplist') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('admin.menu.objects') }}</a></li>
                            <li><a href="{{ route('transparm') }}" data-i18n="nav.dash.analytics"
                                    class="menu-item">{{ __('admin.menu.transparam') }}</a></li>
                            <li><a href="{{ route('customfields') }}" data-i18n="nav.dash.crm"
                                    class="menu-item">{{ __('admin.menu.custom_fields') }}</a></li>

                            @if (@auth()->user()->type == 'site_owner' || @auth()->user()->type == 'site_support')
                                <li><a href="{{ route('editsite') }}" data-i18n="nav.dash.crm"
                                        class="menu-item">{{ __('admin.menu.site_settings') }}</a></li>

                            @endif
                            @if (config('icapt.enable_connected_apps') == true)
                                <li><a href="{{ route('connection_settings') }}" data-i18n="nav.dash.crm"
                                        class="menu-item">{{ __('admin.menu.connection_settings') }}</a></li>
                            @endif
                            {{-- @if (@auth()->user()->type == 'site_owner' || @auth()->user()->type == 'site_administration') --}}
                            <li><a href="{{ route('numberdefinitionmenu') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('admin.menu.number_definition') }}</a></li>
                            {{-- @endif --}}
                        </ul>
                    </li>
                    {{-- Barcode Admin --}}
                    <li class=" nav-item {{ request()->segment(1) == 'barcode-admin' ? 'open' : '' }}"><a
                            href="#" class="item-list2"><i class="icon-barcode2"></i><span
                                data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.barcode_admin') }}</span></i></a>
                        <ul class="menu-content">

                            <li><a href="{{ route('labellist') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('admin.menu.labels') }}</a></li>
                                    @if( \Gate::allows('hasObjectLabel'))
                            <li><a href="{{ route('objectLabel') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('admin.menu.object_label') }}</a></li>
@endif
                            @if (config('icapt.bartender') == false)
                                <li><a href="{{ route('qrcodes.index') }}" data-i18n="nav.dash.ecommerce"
                                        class="menu-item">{{ __('admin.menu.qrcodes') }}</a></li>
                            @endif
                            @if (config('icapt.bartender'))
                                <li><a href="{{ route('printerlist') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('admin.menu.printers') }}</a></li>
                            @endif
                        </ul>
                    </li>
                    {{-- Inventory --}}
                    <li class=" nav-item {{ request()->segment(1) == 'maintenance-invty' ? 'open' : '' }}"><a
                            href="" class="item-list3"><i class="icon-box"></i><span data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.inventory') }}</span></a>
                        <ul class="menu-content">
                            <li class=" nav-item {{ request()->segment(2) == 'master-files-invty' ? 'open' : '' }}">
                                <a href="" class="item-list3-1 menu-item">{{ __('admin.menu.files') }}</a>
                                <ul class="menu-content">
                                    <li><a href="{{ route('warehouse') }}"
                                            class="menu-item">{{ __('admin.menu.warehouses') }}</a></li>
                                    @if ($plan != 7)
                                        <li><a href="{{ route('zones.index') }}"
                                                class="menu-item">{{ __('admin.menu.zones') }}</a></li>
                                    @endif
                                    <li><a href="{{ route('Location') }}"
                                            class="menu-item">{{ __('admin.menu.locations') }}</a></li>
                                    <li><a href="{{ route('ProductCode') }}"
                                            class="menu-item">{{ __('admin.menu.product_codes') }}</a></li>
                                    <li><a href="{{ route('count_group.index') }}"
                                            class="menu-item">{{ __('admin.menu.count_groups') }}</a></li>
                                    <li><a href="{{ route('UOM') }}"
                                            class="menu-item">{{ __('admin.menu.uoms') }}</a></li>
                                    <li><a href="{{ route('item') }}"
                                            class="menu-item">{{ __('admin.menu.items') }}</a></li>
                                    <li><a href="{{ route('UOMConversion') }}"
                                            class="menu-item">{{ __('admin.menu.uom_conversions') }}</a></li>
                                    <li><a href="{{ route('alternate_barcode.index') }}"
                                            class="menu-item">{{ __('admin.menu.alternate_barcodes') }}</a></li>
                                    <li><a href="{{ route('itemwhse') }}"
                                            class="menu-item">{{ __('admin.menu.item_warehouses') }}</a></li>
                                    <li><a href="{{ route('ItemLocation') }}"
                                            class="menu-item">{{ __('admin.menu.item_locations') }}</a></li>
                                    <li><a href="{{ route('lot.index') }}"
                                            class="menu-item">{{ __('admin.menu.lots') }}</a></li>
                                    <li><a href="{{ route('LotLocation') }}"
                                            class="menu-item">{{ __('admin.menu.item_lot_locations') }}</a></li>
                                    @if (config('icapt.special_modules.enable_pallet'))
                                        @if (!$starterOrFree)
                                            <li><a href="{{ route('Pallet') }}"
                                                    class="menu-item">{{ __('admin.menu.pallet') }}</a></li>
                                        @endif
                                    @endif
                                    <li><a href="{{ route('EmployeeInvty') }}"
                                            class="menu-item">{{ __('admin.menu.employees') }}</a></li>
                                    <li><a href="{{ route('ReasonCodeInvty') }}"
                                            class="menu-item">{{ __('admin.menu.reason_codes') }}</a></li>
                                    @if (config('icapt.client_prefix') == 'OceanCash')
                                        <li><a href="{{ route('Bundle') }}"
                                                class="menu-item">{{ __('admin.menu.bundle_builder') }}</a></li>
                                    @endif
                                </ul>
                            </li>
                            {{-- @if (1 == 2) --}}
                            <li class=" nav-item {{ request()->segment(1) == 'utilities' ? 'open' : '' }}"><a
                                    href=""
                                    class="item-list4-1 menu-item">{{ __('admin.menu.utilities') }}</a>

                                <ul class="menu-content">

                                    <li><a href="{{ route('inventoryCountUnmatched') }}"
                                            class="menu-item">{{ __('admin.menu.inventory_count_unmatched') }}</a>
                                    </li>
                                    <li><a href="{{ route('LotExpiryDateUpdate') }}"
                                            class="menu-item">{{ __('admin.menu.lot_expiry_date_update') }}</a>
                                    </li>
                                </ul>

                            </li>
                            {{-- @endif --}}
                            @if ($plan != 1 && $plan != 4 && $plan != 7)
                                <li><a href="{{ route('TransOrder') }}"
                                        class="menu-item">{{ __('admin.menu.transfer_orders') }}</a></li>
                            @endif
                            <li><a href="{{ route('allocation.index') }}"
                                    class="menu-item">{{ __('admin.menu.allocations') }}</a></li>
                            <li><a href="{{ route('picklistTest') }}"
                                    class="menu-item">{{ __('admin.menu.pick_lists') }}</a></li>
                            @if (config('icapt.client_prefix') == 'OceanCash')

                                <li><a href="{{ route('packinglist') }}"
                                        class="menu-item">{{ __('admin.menu.packinglist') }}</a></li>
                            @endif
                            <li><a href="{{ route('batch.index') }}"
                                    class="menu-item">{{ __('admin.menu.inventory_count') }}</a></li>
                        </ul>
                    </li>
                    {{-- Production --}}
                    @if ($plan != 1 && $plan != 4)
                        <li class=" nav-item {{ request()->segment(1) == 'maintenance-production' ? 'open' : '' }}">
                            <a href="" class="item-list4"><i class="icon-truck"></i><span
                                    data-i18n="nav.dash.main"
                                    class="menu-title">{{ __('admin.menu.production') }}</span></a>
                            <ul class="menu-content">
                                <li
                                    class=" nav-item {{ request()->segment(2) == 'master-files-productions' ? 'open' : '' }}">
                                    <a href="" class="item-list4-1 menu-item">{{ __('admin.menu.files') }}</a>
                                    <ul class="menu-content">
                                        @if ($plan != 2 && $plan != 5 && $plan != 7)
                                            <li><a href="{{ route('Machine') }}"
                                                    class="menu-item">{{ __('admin.menu.machines') }}</a></li>
                                        @endif
                                        <li><a href="{{ route('WorkCentre') }}"
                                                class="menu-item">{{ __('admin.menu.work_centers') }}</a></li>
                                        <li><a href="{{ route('bom.index') }}"
                                                class="menu-item">{{ __('admin.menu.boms') }}</a></li>
                                        <li><a href="{{ route('EmployeeProd') }}"
                                                class="menu-item">{{ __('admin.menu.employees') }}</a></li>
                                        <li><a href="{{ route('Task') }}"
                                                class="menu-item">{{ __('admin.menu.indirect_tasks') }}</a></li>
                                        <li><a href="{{ route('ReasonCodeProd') }}"
                                                class="menu-item">{{ __('admin.menu.reason_codes') }}</a></li>
                                    </ul>
                                </li>
                                <li class=" nav-item {{ request()->segment(1) == 'utilities' ? 'open' : '' }}"><a
                                        href=""
                                        class="item-list4-1 menu-item">{{ __('admin.menu.utilities') }}</a>
                                    <ul class="menu-content">

                                        <li><a href="{{ route('jobStatusUpdater') }}"
                                                class="menu-item">{{ __('admin.menu.job_status_updater') }}</a></li>
                                    </ul>
                                </li>
                                <li><a href="{{ route('Job') }}"
                                        class="menu-item">{{ __('admin.menu.job_orders') }}</a></li>
                                <li><a href="{{ route('JobRoute') }}"
                                        class="menu-item">{{ __('admin.menu.job_routes') }}</a></li>
                                <li><a href="{{ route('JobMatl') }}"
                                        class="menu-item">{{ __('admin.menu.job_matls') }}</a></li>
                                {{-- @if (config('icapt.special_modules.enable_suffix'))
                                    <li><a href="{{ route('LaborMachineNew') }}"
                                            class="menu-item">{{ __('admin.menu.labor_machine') }}</a></li>
                                @else
                                    <li><a href="{{ route('LaborMachine') }}"
                                            class="menu-item">{{ __('admin.menu.labor_machine') }}</a></li>
                                @endif --}}





                                {{-- <li><a href="{{ route('Machines') }}"
                                        class="menu-item">{{ __('admin.menu.machines') }}</a></li> --}}

                            </ul>
                        </li>
                    @endif
                    {{-- Sales --}}
                    <li class=" nav-item {{ request()->segment(1) == 'maintenance-sales' ? 'open' : '' }}"><a
                            href="" class="item-list5"><i class="icon-banknote"></i><span
                                data-i18n="nav.dash.main" class="menu-title">{{ __('admin.menu.sales') }}</span></a>
                        <ul class="menu-content">
                            <li class=" nav-item {{ request()->segment(2) == 'master-files-sales' ? 'open' : '' }}">
                                <a href="" class="item-list5-1 menu-item">{{ __('admin.menu.files') }}</a>
                                <ul class="menu-content">
                                    <li><a href="{{ route('customer') }}"
                                            class="menu-item">{{ __('admin.menu.customers') }}</a></li>
                                    <li><a href="{{ route('ShippingZone') }}"
                                            class="menu-item">{{ __('admin.label.shipping_zone') }}</a></li>
                                    @if (config('icapt.special_modules.enable_packing'))
                                        <li><a href="{{ route('PackageTypes') }}"
                                                class="menu-item">{{ __('admin.label.package_type') }}</a></li>
                                    @else
                                        <li hidden><a href="{{ route('PackageTypes') }}"
                                                class="menu-item">{{ __('admin.label.package_type') }}</a></li>
                                    @endif

                                </ul>
                            </li>
                            <li class=" nav-item {{ request()->segment(1) == 'utilities' ? 'open' : '' }}"><a
                                    href=""
                                    class="item-list4-1 menu-item">{{ __('admin.menu.utilities') }}</a>
                                <ul class="menu-content">

                                    <li><a href="{{ route('CODueDateUpdater') }}"
                                            class="menu-item">{{ __('admin.menu.co_line_due_date_updater') }}</a>
                                    </li>
                                </ul>
                            </li>
                            <li><a href="{{ route('CO') }}"
                                    class="menu-item">{{ __('admin.menu.customer_orders') }}</a></li>
                            @if (config('icapt.enable_customer_returns'))
                                <li><a href="{{ route('CustReturn') }}"
                                        class="menu-item">{{ __('admin.menu.customer_returns') }}</a></li>
                            @endif
                        </ul>
                    </li>
                    {{-- Purchase --}}
                    <li class=" nav-item {{ request()->segment(1) == 'maintenance-purchase' ? 'open' : '' }}"><a
                            href="" class="item-list6"><i class="icon-cart4"></i><span
                                data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.purchase') }}</span></a>
                        <ul class="menu-content">
                            <li
                                class=" nav-item {{ request()->segment(2) == 'master-files-purchase' ? 'open' : '' }}">
                                <a href="" class="item-list6-1 menu-item">{{ __('admin.menu.files') }}</a>
                                <ul class="menu-content">
                                    <li><a href="{{ route('vendor') }}"
                                            class="menu-item">{{ __('admin.menu.vendors') }}</a></li>
                                </ul>
                            </li>
                            <li class=" nav-item {{ request()->segment(1) == 'utilities' ? 'open' : '' }}"><a
                                    href=""
                                    class="item-list4-1 menu-item">{{ __('admin.menu.utilities') }}</a>
                                <ul class="menu-content">

                                    <li><a href="{{ route('PODueDateUpdater') }}"
                                            class="menu-item">{{ __('admin.menu.po_line_due_date_updater') }}</a>
                                    </li>
                                </ul>
                            </li>
                            <li><a href="{{ route('PO') }}"
                                    class="menu-item">{{ __('admin.menu.purchase_orders') }}</a></li>
                            @if (config('icapt.enable_grn'))
                                <li><a href="{{ route('GRN') }}" class="menu-item">{{ __('admin.menu.grn') }}</a>
                                </li>
                            @endif
                        </ul>
                    </li>
                    {{-- History --}}
                    <li class=" nav-item {{ request()->segment(1) == 'history' ? 'open' : '' }}"><a href=""
                            class="item-list7"><i class="icon-history"></i><span data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.history') }}</span></a>
                        <ul class="menu-content">
                            <li><a href="{{ route('MaterialTransaction') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-item">{{ __('admin.menu.matl_trans') }}</a></li>
                            @if ($plan != 1 && $plan != 4)
                                <li><a href="{{ route('JobTransaction') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('admin.menu.job_trans') }}</a></li>
                            @endif
                            @if ($plan != 1 && $plan != 2 && $plan != 4 && $plan != 5 && $plan != 7)
                                <li><a href="{{ route('MachineTransaction') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('admin.menu.machine_trans') }}</a></li>
                            @endif
                            @if (config('icapt.client_prefix') == 'OceanCash')
                                <li><a href="{{ route('ProductionTransaction') }}"
                                        class="menu-item">{{ __('admin.menu.production_trans') }}</a></li>
                            @endif
                            @if ($plan != 1 && $plan != 4)
                                <li><a href="{{ route('Labor') }}"
                                        class="menu-item">{{ __('admin.menu.active_labor_runs') }}</a>
                                </li>
                            @endif
                            @if ($plan == 3 || $plan == 6)
                                <li><a href="{{ route('ActiveMachineRuns') }}"
                                        class="menu-item">{{ __('admin.menu.active_machine_runs') }}</a>
                                </li>
                            @endif

                            <li><a href="{{ route('OverrideQtyHistory') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('admin.menu.value_change_log') }}</a></li>
                            <li><a href="{{ route('ImportExportLog') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('admin.title.import_export') }}</a></li>
                            <li><a href="{{ route('ExportLog') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('admin.menu.export_log') }}</a></li>
                            {{-- @if ($sap_integration == 1) --}}
                            @if (
                                \Gate::allows('hasApiLog') &&
                                    (@auth()->user()->type == 'site_owner' ||
                                        @auth()->user()->type == 'site_administration' ||
                                        auth()->user()->type == 'site_support'))
                                <li><a href="{{ route('ApiIntegrationLog') }}" data-i18n="nav.dash.project"
                                        class="menu-item">{{ __('admin.title.integration_log') }}</a></li>
                            @endif
                            <li><a href="{{ route('background_tasks') }}" data-i18n="nav.dash.project"
                                    class="menu-item">{{ __('admin.menu.background_tasks') }}</a></li>

                        </ul>
                    </li>
                    {{-- Reports --}}
                    <li class=" nav-item {{ request()->segment(1) == 'reports' ? 'open' : '' }}"><a href=""
                            class="item-list8"><i class="icon-bar-chart"></i><span data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.reports') }}</span></a>
                        <ul class="menu-content">
                            <li><a href="{{ route('inventoryMenu') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-title">{{ __('admin.menu.inventory') }}</a></li>
                            @if ($plan != 1 && $plan != 4)
                                <li><a href="{{ route('productionMenu') }}" data-i18n="nav.dash.ecommerce"
                                        class="menu-title">{{ __('admin.menu.production') }}</a></li>
                            @endif
                            <li><a href="{{ route('transactionMenu') }}" data-i18n="nav.dash.ecommerce"
                                    class="menu-title">{{ __('admin.menu.transaction') }}</a></li>
                        </ul>
                    </li>

                    {{-- System --}}
                    @if (auth()->user()->type == 'site_owner' || auth()->user()->type == 'site_administration' || auth()->user()->type == 'site_support')
                        <li class="nav-item {{ request()->segment(2) == 'import-schedule' ? 'open' : '' }}">
                            <a href="#" class="item-list9">
                                <i class="icon-cog"></i>
                                <span class="menu-title">System</span>
                            </a>
                            <ul class="menu-content">
                                <li><a href="{{ route('importSchedule.index') }}" class="menu-item">Import Schedule</a></li>
                            </ul>
                        </li>
                    @endif

                    {{-- Switch to mobile --}}
                    <li class=" nav-item"><a href="{{ route('mobile') }}"><i class="icon-mobile"></i><span
                                data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.switch_to_mobile') }}</span></a></li>
                    {{-- Logout --}}
                    <li class=" nav-item"><a href="{{ route('logout') }}"><i class="icon-power3"></i><span
                                data-i18n="nav.dash.main"
                                class="menu-title">{{ __('admin.menu.logout') }}</span></a></li>
                </ul>
            </div>
            <!-- main menu footer-->
            <!-- <div class="main-menu-footer footer-open">
        <div class="content text-xl-center">
                <span>Version 0.1</span> &nbsp;&nbsp; <span>Test Site</span>
         </div>
      </div> -->
        </div>
