<table class="table table-bordered table-xs display nowrap" width="100%" id="COPickingShippingStatusReport">
    {{-- Get Cookies --}}
    <?php
        $username = auth()->user()->id."COPickingShippingStatusReport";
        $value = Cookie::get($username);
        $COPickingShippingStatusReportArr = json_decode($value);

        if(is_array($COPickingShippingStatusReportArr)){

        }else{
            $COPickingShippingStatusReportArr = ['cust_num', 'cust_name', 'co_num', 'co_line', 'due_date', 'item_num', 'item_desc', 'qty_required', 'stage_location', 'qty_picked', 'qty_shipped', 'outstanding_qty'];
        }
    ?>
    <thead>
        {{-- Display Dynamic header --}}
        <tr>
            @foreach($COPickingShippingStatusReportArr as $key=>$title)
                <input type="hidden" value={{$name="admin.label.".$title}}>
                @if($title == "cust_num")
                    <th>Customer Code</th>
                @elseif($title == "cust_name")
                    <th>Customer Name</th>
                @elseif($title == "co_num")
                    <th>CO Number</th>
                @elseif($title == "co_line")
                    <th>Line</th>
                @elseif($title == "due_date")
                    <th>Due Date</th>
                @elseif($title == "item_num")
                    <th>Item</th>
                @elseif($title == "item_desc")
                    <th>Description</th>
                @elseif($title == "qty_required")
                    <th class="dt-right">Qty Required</th>
                @elseif($title == "stage_location")
                    <th>Stage Location</th>
                @elseif($title == "qty_picked")
                    <th class="dt-right">Qty Picked</th>
                @elseif($title == "qty_shipped")
                    <th class="dt-right">Qty Shipped</th>
                @elseif($title == "outstanding_qty")
                    <th class="dt-right">Outstanding Qty</th>
                @else
                    <th>{{__($name)}}</th>
                @endif
            @endforeach
        </tr>
        <tr class="search-filter">
            <th><input style="width:80px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:120px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:80px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:50px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:80px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:80px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:120px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:70px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:80px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:70px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:70px" type="text" class="form-control input-sm" placeholder="Search.."></th>
            <th><input style="width:80px" type="text" class="form-control input-sm" placeholder="Search.."></th>
        </tr>
    </thead>
</table>
