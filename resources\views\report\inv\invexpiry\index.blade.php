@extends('layout.app')
@include('report.style')
@section('content')
<div class="margin-header">
    <div class="card-header-title ">
        <table class="title-header">
            {{-- Inventory Expiry report renamed to Lot Expiry --}}
            <tr>
                <td class="title-division"><h4 class="title">{{ __($report_module_name) }}</h4></td>
            </tr>
        </table>
    </div>
    <div>
        <form class="form-group" id="inv_balance_report" name="inv_balance_report" autocomplete="off" method="POST" action="{{route('printinvexprep')}}">
            @csrf
            <input type="hidden" id="siteformat" name='siteformat' value="{{$siteformat}}">
            <div class="default-button">
                <button id="filter" type="button" hidden class="displayExcel-button btn btn-primary button-padding" style="background-color:#37BC9B;"><i class="icon-square-plus"></i> {{__('admin.button.generate')}}</button></td>
                <button id="btn-refresh" type="button" hidden class="btn btn-primary button-padding" onclick="document.getElementById('inv_trans_by_item_report').reset(); document.getElementById('from_date').value = null; return false;">
                    <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
                </button>
                <button type="submit" id="print" class="btn btn-info button-padding">
                    <i class="icon-print"></i> {{__('admin.button.print')}}
                </button>
            </div>
            <div id="filter_div" class="table-content" hidden>
                <table align="center">
                    <tr>
                        <td width='100px'><label for="item_num">{{__('admin.label.item')}}</label></td>
                        <td width='300px'><input type="text" name="from_item_num" id="from_item_num" tabindex="1" class="form-control border-primary" placeholder="From Item" ></td>
                        <td width='75px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','from_item_num','item_num','from_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='60px'><label> To</label></td>
                        <td width='300px'><input type="text" name="to_item_num" id="to_item_num" tabindex="6" class="form-control border-primary" placeholder="To Item" ></td>
                        <td width='100px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','to_item_num','item_num','to_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="product_code">{{ __('admin.label.product_code') }}</label></td>
                        <td><input type="text" name="from_product_code" id="from_product_code" tabindex="2" class="form-control border-primary" placeholder="From Product Code" ></td>
                        <td>
                            <button type="button" name="Product Code" onClick="selection('/getProdCode','from_product_code', 'product_code', 'from_product_code');modalheader(this.id,'Product Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_product_code" id="to_product_code" tabindex="7" class="form-control border-primary" placeholder="To Product Code" ></td>
                        <td>
                            <button type="button" name="Product Code" onClick="selection('/getProdCode','to_product_code', 'product_code', 'to_product_code');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="whse">{{__('admin.label.whse_num')}}</label></td>
                        <td><input type="text" name="from_whse_num" id="from_whse_num" tabindex="3" class="form-control border-primary" placeholder="From Warehouse" value={{$whse_pass}}></td>
                        <td>
                            <button type="button" name="{{__('admin.list.warehouses')}}" onClick="selection('/getWhse','from_whse_num', 'whse_num', 'from_whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_whse_num" id="to_whse_num" tabindex="8" class="form-control border-primary" placeholder="To Warehouse" value={{$whse_pass}}></td>
                        <td>
                            <button type="button" name="{{__('admin.list.warehouses')}}" onClick="selection('/getWhse','to_whse_num', 'whse_num', 'to_whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="lot_num">{{__('admin.label.lot_num')}}</label></td>
                        <td><input type="text" name="from_lot_num" id="from_lot_num" tabindex="4" class="form-control border-primary" placeholder="From Lot Number" ></td>
                        <td>
                            <button type="button" name="{{__('admin.label.lot_num')}}" onClick="selection('/getAllLot','from_lot_num','lot_num','from_lot_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_lot_num" id="to_lot_num" tabindex="9" class="form-control border-primary" placeholder="To Lot Number" ></td>
                        <td>
                            <button type="button" name="{{__('admin.label.lot_num')}}" onClick="selection('/getAllLot','to_lot_num','lot_num','to_lot_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="expiry_date">{{__('admin.label.expiry_date')}}</label></td>
                        <td><input type="text" name="from_expiry_date" id="from_expiry_date" tabindex="5" class="form-control border-primary from_any_date" placeholder="From Expiry Date" ></td>
                        <td>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_expiry_date" id="to_expiry_date" tabindex="10" class="form-control border-primary to_any_date" placeholder="To Expiry Date" ></td>
                        <td>

                        </td>
                    </tr>
                    <tr>
                        <td><label for="day_to_expiry">Days to Expiry &nbsp;</label></td>
                        <td><input type="number" name="day_to_expiry" id="day_to_expiry" tabindex="5" class="form-control border-primary" placeholder="Days to Expiry" value={{$day_expiry}}></td>
                    </tr>
                </table>
                <hr>
            </div>
        </form> 
                <div class="card-block card-dashboard">
                   @include('report.inv.invexpiry.list')
                </div>
    </div>
</div>

@push('scripts')
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/gh/jeffreydwalter/ColReorderWithResize@9ce30c640e394282c9e0df5787d54e5887bc8ecc/ColReorderWithResize.js"></script>
@endpush

@include('util.js-datatable')

<script type="text/javascript">

    var defaultColumns= ['item_num','item_desc','lot_num','days_to_expiry','expiry_date','whse_num',
            'qty_available','uom','loc_num'];//,'first_received_date'

    if(localStorage.getItem('columnsetting_InvExpiryReport') == null){
        localStorage.setItem('columnsetting_InvExpiryReport', JSON.stringify(defaultColumns));
    }

    //Get Cookies Columns
    var getItem = localStorage.getItem('columnsetting_InvExpiryReport');
    var columnsList = JSON.parse(getItem);

    var arrColumns=[];
    for(var i = 0; i < columnsList.length; i++){
        if(columnsList[i]=='days_to_expiry' || columnsList[i]=='qty_available'){
            arrColumns.push({data:columnsList[i], name:columnsList[i], class:'dt-right'});
        }else if (columnsList[i]=='expiry_date') {
            arrColumns.push({data:columnsList[i], name:columnsList[i], searchable: false});
        }else{
            arrColumns.push({data:columnsList[i], name:columnsList[i]});
        }
    }

    //onClick "Generate" button
    $('.displayExcel-button').click(function() {
        $('.default-button').css('padding-right', '220px');
    });

    jQuery(function($){
        $("#inv_balance_report").validate({
            onchange:true,
            rules:{
                from_item_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_item_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_product_code:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_product_code:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_whse_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_whse_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_lot_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                to_lot_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
            },
            messages:{
                from_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                to_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                from_product_code:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                },
                to_product_code:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                },
                from_whse_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                },
                to_whse_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                },
                from_lot_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.lot_num') ]) }}"
                },
                to_lot_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.lot_num') ]) }}"
                },
            },
        });
    });

    window.onload=function(){
        if("{{ $ref_pass }}" == "dashboard"){
            document.getElementById("filter").click();
        }
    };

    $(document).ready(function(){

        if("{{ $ref_pass }}" != "dashboard"){
            $("#filter_div").attr('hidden', false);
            $("#filter").attr('hidden', false);
            $("#btn-refresh").attr('hidden', false);
            $(".search-filter").attr('hidden', false);
        }
        // Call the DataTable
        getFilterReset(getTable,arrColumns);       
      

        $(window).resize(function (e) {
            $("#InvExpiryReport").DataTable().columns.adjust();
        });

        function getTable()
        {
            now_date = "{{$now_date}}";
            unit_quantity_format = "{{$unit_quantity_format}}";
            ref_pass = "{{ $ref_pass }}";

            let ajaxMethod = {
                url : '{!! route('invexprep.data') !!}',
                method: 'get',
                data: function (d) {
                    d.from_item_num = $('#from_item_num').val(); // Pass along start date and end date here
                    d.to_item_num = $('#to_item_num').val();
                    d.from_product_code = $("#from_product_code").val();
                    d.to_product_code = $('#to_product_code').val();
                    d.from_whse_num = $('#from_whse_num').val();
                    d.to_whse_num = $('#to_whse_num').val();
                    d.from_lot_num = $('#from_lot_num').val();
                    d.to_lot_num = $('#to_lot_num').val();
                    d.from_expiry_date = $('#from_expiry_date').val();
                    d.to_expiry_date = $('#to_expiry_date').val();
                    d.day_to_expiry = $('#day_to_expiry').val();
                    d.ref_pass = ref_pass;
                },
                dataSrc: function (json) {
                        // console.log(json);
                    var return_data = new Array();
                    for(var i=0;i< json.data.length; i++){

                        expiry_date_formatted = new Date(json.data[i].expiry_date);
                        expiry_date_conv = convertDateTimeJS(json.data[i].expiry_date);
                        now_date_formatted = new Date(now_date);
                        var days_to_expiry = Math.round((expiry_date_formatted - now_date_formatted)/(1000*60*60*24));

                        // if(json.data[i].whse_tot != null){  // if whse_tot is not null
                        //     return_data.push({
                        //         'item_num': "",
                        //         'item_desc'  : "",
                        //         'lot_num' : "",
                        //         'first_received_date' : "",
                        //         'expiry_date' : "",
                        //         'days_to_expiry' : "",
                        //         'whse_num' : "",
                        //         'loc_num' : "<strong>Warehouse Subtotal</strong>",
                        //         //'qty_on_hand' : "<strong>"+parseFloat(json.data[i].whse_tot).toFixed(unit_quantity_format)+"</strong>",
                        //         'qty_on_hand' : "<strong>"+FixWithoutRounding(json.data[i].whse_tot,unit_quantity_format)+"</strong>",
                        //         'uom' : "<strong>"+json.data[i].uom+"</strong>"
                        //     });
                        // }
                        // if(json.data[i].item_tot != null){  // if item_tot is not null
                        //     return_data.push({
                        //         'item_num': "",
                        //         'item_desc'  : "",
                        //         'lot_num' : "",
                        //         'first_received_date' : "",
                        //         'expiry_date' : "<strong>Item Subtotal</strong>",
                        //         'days_to_expiry' : "",
                        //         'whse_num' : "",
                        //         'loc_num' : "",
                        //         //'qty_on_hand' : "<strong>"+parseFloat(json.data[i].item_tot).toFixed(unit_quantity_format)+"</strong>",
                        //         'qty_on_hand' : "<strong>"+FixWithoutRounding(json.data[i].item_tot,unit_quantity_format)+"</strong>",
                        //         'uom' : "<strong>"+json.data[i].uom+"</strong>"
                        //     });
                        // }
                        if(i==0){ // first row
                            return_data.push({
                            'item_num': json.data[i].item_num,
                            'item_desc'  : json.data[i].item_desc,
                            'lot_num' : json.data[i].lot_num,
                            // 'first_received_date' : json.data[i].first_received_date,
                            'expiry_date' : expiry_date_conv,
                            'days_to_expiry' : days_to_expiry,
                            'whse_num' : json.data[i].whse_num,
                            //json.data[i].loc_num
                            'qty_available' : numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                            'uom' : json.data[i].uom,
                            'loc_num' : json.data[i].loc_num

                            });
                        }
                        else if(json.data[i].whse_tot == null && json.data[i].item_tot == null){ // row is not whse_tot and item_tot
                            if(json.data[i-1].item_num == json.data[i].item_num){
                                if(json.data[i-1].whse_num == json.data[i].whse_num){
                                    return_data.push({
                                    'item_num': json.data[i].item_num,
                                    'item_desc'  : json.data[i].item_desc,
                                    'lot_num' : json.data[i].lot_num,
                                    // 'first_received_date' : json.data[i].first_received_date,
                                    'expiry_date' : expiry_date_conv,
                                    'days_to_expiry' :  days_to_expiry,
                                    'whse_num' : json.data[i].whse_num,
                                    //'loc_num' : 'sss11',
                                    'qty_available' : numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                                    'uom' : json.data[i].uom,
                                    'loc_num' : json.data[i].loc_num
                                    });
                                }
                                else{
                                    return_data.push({
                                    'item_num': json.data[i].item_num,
                                    'item_desc'  : json.data[i].item_desc,
                                    'lot_num' : json.data[i].lot_num,
                                    // 'first_received_date' : json.data[i].first_received_date,
                                    'expiry_date' : expiry_date_conv,
                                    'days_to_expiry' : days_to_expiry,
                                    'whse_num' : json.data[i].whse_num,
                                    // 'loc_num' : 'sss22',
                                    'qty_available' : numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                                    'uom' : json.data[i].uom,
                                    'loc_num' : json.data[i].loc_num
                                    });
                                }
                            }
                            else{
                                return_data.push({
                                'item_num': json.data[i].item_num,
                                'item_desc'  : json.data[i].item_desc,
                                'lot_num' : json.data[i].lot_num,
                                // 'first_received_date' : json.data[i].first_received_date,
                                'expiry_date' : expiry_date_conv,
                                'days_to_expiry' : days_to_expiry,
                                'whse_num' : json.data[i].whse_num,
                                //'loc_num' : 'sss33',
                                'qty_available' : numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                                'uom' : json.data[i].uom,
                                'loc_num' : json.data[i].loc_num
                                });
                            }
                        }
                    }
                    
                    return return_data;
                }
            };

            let additionalParam = {
                rowCallback: function(row, data, index) {
                    // Add class 'negative' if days_to_expiry is a negative value
                    if(data['days_to_expiry'] < 0) {
                        $('td:eq(5)', row).addClass('negative');
                    }
                },
                serverSide: true, 
                searching: true
            };

            generateTable('InvExpiryReport', arrColumns, ajaxMethod, 'LotExpiry', additionalParam);
            ref_pass == "dashboard" ? $('div.dt-buttons').css('margin-top','-35.5px') : $('div.dt-buttons').css('margin-top','-54.5px');

            $(".dataTable thead input:not(#mass-chk)").on('keyup', function (e) {
                if (e.keyCode == 13) {
                    $('.dataTable').DataTable().column($(this).parent().index()).search(this.value).draw();
                }
            });
        }
    });















</script>
<style>
    .title-header{
        width:100%;}
    .title{
        padding-top:2px;
        font-size: 22px !important;
        font-weight: bold;}
    .title-division{
        width:50%;
        float:left;}
    .default-button{
        float:right;
        margin-top:-59.4px;
        padding-right:0px;
        position: relative;}
    .button-padding{
        margin:5px 0.5px;}
    .table-content{
        margin-top:15px;}
    .card-block{
        margin-top: -18px;
        padding-right: 0rem;
        padding-left: 0rem;}
    /* Export button */
    div.dt-buttons{
        float: right !important;}
    .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle){
        border-bottom-right-radius: 0.18rem !important;
        border-top-right-radius: 0.18rem !important;}
    .btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
        border-bottom-left-radius: 0.18rem !important;
        border-top-left-radius: 0.18rem !important;}
    div.dt-buttons.btn-group>a.btn-secondary{
        width: 110px !important;
        margin-left: 4px !important;}
    a.btn.btn-success{
        border-color:#4FC3F7;
        background-color:#4FC3F7;}
    label{
        margin-bottom: 0.1rem !important;}
    .margin-header input.form-control.border-primary{
        padding-left:10px;
        height: 25px;}
    select#trans_type.form-control.border-primary{
        padding-left: 3px !important;
        height: 25px;}
    .form-group{
        margin-top:1rem;}
    form td{
        padding: 2px 1px;}
    .table th, .table td{
        padding: 0.2rem 0.2rem !important;}
    .table td{
        border-bottom: 1px solid white !important;}
    div.dataTables_length>label{
        width: 110px !important;}
    button.ColVis_Button>span{
        font-size: 9pt;}
    /* table header */
    table.dataTable thead>tr>th.sorting_asc, table.dataTable thead>tr>th.sorting_desc,
    table.dataTable thead>tr>th.sorting, table.dataTable thead>tr>td.sorting_asc, table.dataTable thead>tr>td.sorting_desc,
    table.dataTable thead>tr>td.sorting div.card-block>table#JobOperHourReport thead>tr:first-child>th,
    div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        padding-right: 25px !important;
    }
    div.card-block>table#InvExpiryReport thead>tr:first-child>th, div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        background-color: rgb(220,220,220);}
    div.dataTables_scrollBody>table>thead>tr.row>th{
        background-color: white;}
    table#datatable.table.table-bordered.table-hover.nowrap.datatable.no-footer td>a>button.btn.btn-primary{
        padding: 0.1rem 0.2rem !important;}
    div.dataTables_wrapper div.dataTables_paginate{
        margin-bottom: 3rem !important;}
    li.paginate_button.page-item.active>a {
        font-size: 9pt;}
    .dt-center {
        text-align: center;}
    .dt-right {
        text-align: right;}
    .dt-buttons {
        padding-bottom: 15px;}
    .dataTables_length {
        float:left;
        padding-top: 0.5em;
        padding-bottom: 5px;}
     div.dataTables_info {
        padding-top: 0.85em;
        position:absolute;}
    div.dataTables_paginate {
        padding-top: 0.85em;
        right:0px;}
    .negative {
        color: red;
    }
</style>

@include('util.selection')
@include('util.datepicker')
@endsection
