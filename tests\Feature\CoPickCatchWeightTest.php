<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\User;
use App\Warehouse;
use App\Loc;
use App\Item;
use App\CustomerOrder;
use App\CustomerOrderItem;
use App\ItemLoc;
use App\LotLoc;
use App\Lot;
use App\Customer;
use App\SiteSetting;
use Illuminate\Support\Facades\Auth;

class CoPickCatchWeightTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $warehouse;
    protected $location;
    protected $item;
    protected $customer;
    protected $customerOrder;
    protected $customerOrderItem;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'site_id' => 1,
            'email' => '<EMAIL>'
        ]);

        // Create test warehouse
        $this->warehouse = Warehouse::factory()->create([
            'whse_num' => 'TEST_WH',
            'whse_status' => 1,
            'site_id' => 1
        ]);

        // Create test location
        $this->location = Loc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'loc_status' => 1,
            'site_id' => 1
        ]);

        // Create catch weight enabled item
        $this->item = Item::factory()->create([
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'item_status' => 1,
            'catch_weight' => 1,
            'catch_weight_tolerance' => 5.0,
            'lot_tracked' => 1,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create test customer
        $this->customer = Customer::factory()->create([
            'cust_num' => 'TEST_CUST',
            'cust_name' => 'Test Customer',
            'site_id' => 1
        ]);

        // Create customer order
        $this->customerOrder = CustomerOrder::factory()->create([
            'co_num' => 'CO_TEST_001',
            'cust_num' => 'TEST_CUST',
            'co_status' => 'O',
            'site_id' => 1
        ]);

        // Create customer order item
        $this->customerOrderItem = CustomerOrderItem::factory()->create([
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'qty_ordered' => 100.0,
            'qty_released' => 100.0,
            'qty_shipped' => 0.0,
            'qty_returned' => 0.0,
            'uom' => 'KG',
            'whse_num' => 'TEST_WH',
            'cust_num' => 'TEST_CUST',
            'rel_status' => 'O',
            'site_id' => 1
        ]);

        // Create item location with inventory
        ItemLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'item_num' => 'CW_ITEM_001',
            'qty_on_hand' => 200.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create lot
        $lot = Lot::factory()->create([
            'lot_num' => 'LOT_001',
            'item_num' => 'CW_ITEM_001',
            'site_id' => 1
        ]);

        // Create lot location with inventory
        LotLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'item_num' => 'CW_ITEM_001',
            'lot_num' => 'LOT_001',
            'qty_on_hand' => 200.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Set up site settings
        SiteSetting::factory()->create([
            'site_id' => 1,
            'timezone' => 'UTC'
        ]);

        // Authenticate user
        Auth::login($this->user);
    }

    /** @test */
    public function it_redirects_to_catch_weight_form_for_catch_weight_items()
    {
        $response = $this->get(route('CoPickingProcess', [
            'co_num' => base64_encode('CO_TEST_001'),
            'co_line' => 1,
            'co_rel' => 0,
            'stage_num' => base64_encode('STAGE_01'),
            'whse_num' => base64_encode('TEST_WH'),
            'item' => base64_encode('CW_ITEM_001'),
            'uom' => base64_encode('KG')
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('shipping.copick.process_cw');
        $response->assertViewHas('co_item');
        $response->assertViewHas('batch_id');
    }

    /** @test */
    public function it_processes_catch_weight_co_picking_successfully()
    {
        $requestData = [
            'batch_id' => 'TEST_BATCH_001',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'ref_num' => 'CO_TEST_001',
            'ref_line' => 1,
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'cust_num' => 'TEST_CUST',
            'stage_num' => 'STAGE_01',
            'loc_num' => 'TEST_LOC',
            'arr_lot_num' => ['LOT_001'],
            'arr_qty' => [50.0],
            'arr_expiry_date' => [null],
            'qty_conv' => 50.0,
            'base_uom' => 'KG',
            'original_uom' => 'KG',
            'disableCreateNewItemLoc' => 0,
            'incoming' => false
        ];

        $response = $this->post(route('runCoPickCWProcess'), $requestData);

        $response->assertRedirect();
        $response->assertSessionHas('alert.config.type', 'success');

        // Verify CO item quantities were updated
        $this->customerOrderItem->refresh();
        $this->assertEquals(50.0, $this->customerOrderItem->qty_shipped);

        // Verify inventory was reduced
        $itemLoc = ItemLoc::where('whse_num', 'TEST_WH')
            ->where('loc_num', 'TEST_LOC')
            ->where('item_num', 'CW_ITEM_001')
            ->first();
        $this->assertEquals(150.0, $itemLoc->qty_on_hand);

        $lotLoc = LotLoc::where('whse_num', 'TEST_WH')
            ->where('loc_num', 'TEST_LOC')
            ->where('item_num', 'CW_ITEM_001')
            ->where('lot_num', 'LOT_001')
            ->first();
        $this->assertEquals(150.0, $lotLoc->qty_on_hand);
    }

    /** @test */
    public function it_validates_catch_weight_tolerance()
    {
        // Test with quantity outside tolerance
        $requestData = [
            'batch_id' => 'TEST_BATCH_002',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'ref_num' => 'CO_TEST_001',
            'ref_line' => 1,
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'cust_num' => 'TEST_CUST',
            'stage_num' => 'STAGE_01',
            'loc_num' => 'TEST_LOC',
            'arr_lot_num' => ['LOT_001'],
            'arr_qty' => [120.0], // This exceeds tolerance (100 + 5% = 105)
            'arr_expiry_date' => [null],
            'qty_conv' => 120.0,
            'base_uom' => 'KG',
            'original_uom' => 'KG',
            'disableCreateNewItemLoc' => 0,
            'incoming' => false
        ];

        $response = $this->post(route('runCoPickCWProcess'), $requestData);

        $response->assertSessionHasErrors();
    }

    /** @test */
    public function it_handles_multiple_lots_in_catch_weight_picking()
    {
        // Create additional lot
        $lot2 = Lot::factory()->create([
            'lot_num' => 'LOT_002',
            'item_num' => 'CW_ITEM_001',
            'site_id' => 1
        ]);

        LotLoc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'item_num' => 'CW_ITEM_001',
            'lot_num' => 'LOT_002',
            'qty_on_hand' => 100.0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        $requestData = [
            'batch_id' => 'TEST_BATCH_003',
            'whse_num' => 'TEST_WH',
            'item_num' => 'CW_ITEM_001',
            'ref_num' => 'CO_TEST_001',
            'ref_line' => 1,
            'co_num' => 'CO_TEST_001',
            'co_line' => 1,
            'co_rel' => 0,
            'cust_num' => 'TEST_CUST',
            'stage_num' => 'STAGE_01',
            'loc_num' => 'TEST_LOC',
            'arr_lot_num' => ['LOT_001', 'LOT_002'],
            'arr_qty' => [30.0, 25.0],
            'arr_expiry_date' => [null, null],
            'qty_conv' => 55.0,
            'base_uom' => 'KG',
            'original_uom' => 'KG',
            'disableCreateNewItemLoc' => 0,
            'incoming' => false
        ];

        $response = $this->post(route('runCoPickCWProcess'), $requestData);

        $response->assertRedirect();
        $response->assertSessionHas('alert.config.type', 'success');

        // Verify CO item quantities were updated with total
        $this->customerOrderItem->refresh();
        $this->assertEquals(55.0, $this->customerOrderItem->qty_shipped);
    }
}
