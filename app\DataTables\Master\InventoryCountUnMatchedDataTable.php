<?php

namespace App\DataTables\Master;

use App\CountBatch;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Html\Editor\Fields;
use Yajra\DataTables\Html\Editor\Editor;
use Illuminate\Support\Facades\Cookie;
use App\SiteSetting;
use DB;
use Camroncade\Timezone\Facades\Timezone;
use App\DataTables\BaseDataTable;
use App\InventoryCountUnmatched;
use App\Item;
use Carbon\Carbon;
use App\Traits\DataTableOptions;

class InventoryCountUnMatchedDataTable extends BaseDataTable
{
    use DataTableOptions;

    protected $exportColumns = [];

    public function __construct()
    {
        $rawColumns = [
            ['data' => 'trans_num', 'title' => __('admin.label.trans_num')],
            ['data' => 'count_date', 'title' => __('admin.label.count_date')],
            ['data' => 'item_num', 'title' => __('admin.label.item_num')],
            ['data' => 'item_desc', 'title' => __('admin.label.item_desc')],
            ['data' => 'lot_num', 'title' => __('admin.label.lot_num')],
            ['data' => 'loc_num', 'title' => __('admin.label.loc_num')],
            ['data' => 'uom', 'title' => __('admin.label.uom')],
            ['data' => 'counted_qty', 'title' => __('admin.label.qty_counted')],
            ['data' => 'counter', 'title' => __('admin.label.counter')],
            ['data' => 'whse_num', 'title' => __('admin.label.whse_num')],
            ['data' => 'status_print', 'title' => __('admin.label.status')],
            ['data' => 'reason_code_print', 'title' => __('admin.label.reason_code')],
            ['data' => 'remarks', 'title' => __('admin.label.remarks')],
        ];
        $this->exportColumns = $rawColumns;
    }
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->editColumn('count_date', function ($q) {
                // $datFormat =   SiteSetting :: select('date_format')->where('site_id',auth()->user()->site_id)->value('date_format');
                // $datFormat = $datFormat.' h:i:s';
                // dd($datFormat);
                $counted_date = $q->count_date;

                return getDateTimeConverted($counted_date, true, true);
            })
            ->filterColumn('count_date', function ($q, $value) {
                $value = trim($value);
                $datFormat =   SiteSetting::select('date_format')->where('site_id', auth()->user()->site_id)->value('date_format');
                // dd($datFormat);
                $date = Timezone::convertToUTC($value, auth()->user()->timezone, \App\SiteSetting::getOutputDateFormat() . ' H:i:s');

                $dateTimeString = stripos($value, " ");
                if (!$dateTimeString) {

                    $q->where('count_date', 'LIKE', "%$value%");
                } else {
                    $q->where('count_date', 'LIKE', $date);
                }
            })
            ->editColumn('status', function ($q) {

                $e = $q->status == "P" ? "selected" : "";
                $a = $q->status == "A" ? "selected" : "";
                $r = $q->status == "R" ? "selected" : "";
                if ($q->status != "P") {
                    return
                        "<select name='unmatched_items[$q->id][status]' class='form-control status'>
              <option value='E' $e>Pending</option>
            <option value='A' $a>Added</option>
            <option value='R' $r>Rejected</option>
                </select>";
                } else {
                    return "Processed";
                }
            })
            ->editColumn('reason_code', function ($q) {
                $id = "reason_code_$q->id";
                $disabled = $q->status != "A" ? "disabled" : "";
                $hidden = $q->status != "A" ? "hidden" : "";
                $buttonName = __("mobile.list.defect_reasons");
                $button = '<button   style="width:10%; display:inline-block; float:left;" type="button" name="' . $buttonName . '"
                                onClick="selection(\'/getReasonCode/MiscReceipt\', \'' . $id . '\', \'reason_num\', \'' . $id . '\');modalheader(this.id,\'' . __('admin.menu.reason_codes') . '\');"
                                class="btn-magnify rc_btn btn-icon btn-magnify-class ' . $hidden . '" data-toggle="modal" data-target="#myModal"><i
                                    class="icon-search"></i></button>';
                $html = '<div style="display:flex; width:100%;"><input style="width:80%; display:inline-block; float:left;" type="text" name="unmatched_items[' . $q->id . '][reason_code]" id="' . $id . '" class="form-control reason_code border-primary"
                                placeholder="' . __("mobile.label.reason_code") . '" ' . $disabled . ' value="' . $q->reason_code . '" onchange="clickSelf(this.id)">
                                ' . $button . '</div>';
                return $html;
            })
            ->editColumn('remarks', function ($q) {


                return
                    "<textarea maxlength='100' name='unmatched_items[ $q->id][remarks]' class='form-control remarks'>$q->remarks</textarea>";
            })
            ->addColumn('action', function ($q) {
                $item_num = Item::where('item_num', $q->item_num)->first();
                if ($item_num->lot_tracked) {
                    return "<a target='_blank' href='" . route('LotLocation') . "?whse_num=$q->whse_num&item_num=$q->item_num&loc_num=$q->loc_num&lot_num=$q->lot_num' class='btn btn-icon btn-primary  btn-sm' id='edit' name='edit' title='View Lot/Location Details'><i class='icon-eye'></i></a>";
                }
                return "<a target='_blank' href='" . route('ItemLocation') . "?whse_num=$q->whse_num&item_num=$q->item_num&loc=$q->loc_num' class='btn btn-icon btn-primary  btn-sm' id='edit' name='edit' title='View Lot/Location Details'><i class='icon-eye'></i></a>";
            })->rawColumns(['status', 'action', 'remarks', 'reason_code']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\CountBatch $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(InventoryCountUnmatched $model)
    {
        return $model->orderBy('trans_num', 'desc')->newQuery();
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('InvCountUnMatched')
            ->addTableClass('nowrap table-bordered table-xs')
            ->parameters([
                'responsive' => true,
                'autoWidth' => false,
                'scrollY' => 400,
                'scrollX' => true,
                'scrollCollapse' => true,
                'colReorder' => [
                    'fixedColumnsLeft' => [1],
                ],
                // 'order'=>[1, 'desc'],
            ])
            ->orderCellsTop(true)
            ->fixedHeader(true)
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Blrtip')
            ->orderBy([1, 'desc'])
            ->buttons(
                // Button::make('export')->className('hidden btn buttons-excel buttons-html5 btn-info')
                Button::make('excel')->text('Export to Excel')->className('btn buttons-excel buttons-html5 btn-info')->responsive(false)->autoWidth(false),
                Button::make('csv')->text('Export to CSV')->className('btn btn-info')
            );
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        $username = auth()->user()->id . "InvBatch";
        $value = Cookie::get($username);
        $BatchArr = json_decode($value);
        // $BatchArr = false;
        if (is_array($BatchArr)) {
        } else {
            $BatchArr = [
                'id',
                'trans_num',
                'count_date',
                'item_num',
                'item_desc',
                'lot_num',
                'loc_num',
                'uom',
                'counted_qty',
                'counter',
                'whse_num',
                'status',
                'reason_code',
                'remarks',
                'action'
            ];
        }

        $arrData = [
            'id' => array('id' => '&nbsp;&nbsp;&nbsp;&nbsp;'),
            'trans_num' => array('title' => (__('admin.label.trans_num'))),
            'count_date' => array('title' => (__('admin.label.count_date'))),
            'item_num' => array(
                'title' => (__('admin.label.item_num')),
                'render' => 'function() {
                    var url = "' . route('batch.show', ':id') . '";
                    url = url.replace(":id",full.id);

                    var data = "<a href="+url+"> " + full.batch_name + "</a>";
                    return data;
                }'
            ),
            'item_desc' => array('title' => (__('admin.label.item_desc'))),
            'lot_num' => array('title' => (__('admin.label.lot_num'))),
            'loc_num' => array('title' => (__('admin.label.loc_num'))),
            'uom' => array('title' => (__('admin.label.uom'))),
            'counted_qty' => array('title' => (__('admin.label.qty_counted'))),
            'counter' => array('title' => (__('admin.label.counter'))),
            'whse_num' => array('title' => (__('admin.label.whse_num'))),
            'status' => array('title' => (__('admin.label.status')), 'class' => 'td-status'),
            'reason_code' => array('title' => (__('admin.label.reason_code'))),
            'remarks' => array('title' => (__('admin.label.remarks'))),
            'action' => array('title' => 'Action', 'orderable' => false, 'exportable' => false, 'printable' => false, 'width' => 80, 'class' => 'text-center'),
        ];

        $index = 0;
        foreach ($BatchArr as $key => $datalocastorage) {
            $arrDataFilter[$BatchArr[$index]] = $arrData[$BatchArr[$index]];
            $index++;
        }
        $arrRender = ['batch_name' => 0];
        $arrAddClass = ['status' => 0];
        $arrAction = ['action' => 0];
        array_push($arrAction);

        foreach ($arrDataFilter as $headertitle => $data) {
            if ($headertitle != 'id') {

                $arrRearrange[0] = Column::make('id')
                    ->title('')
                    ->addClass('checkbox')
                    ->orderable(false)
                    ->searchable(false)
                    ->exportable(false)
                    ->printable(false)
                    ->width('50px')
                    ->render('"<input name=\"save_value\" type=\"checkbox\" class=\"chk generate-row-data\" value=\"" + full.id + "\"  onChange=\"getValueUsingClass();\">"');
                if (array_key_exists($headertitle, $arrRender)) {
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->render($data['render']);
                } elseif (array_key_exists($headertitle, $arrAddClass)) {
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->addClass($data['class']);
                } elseif (array_key_exists($headertitle, $arrAction)) {
                    $arrRearrange[] = Column::make($headertitle)->title($data['title'])->orderable($data['orderable'])->exportable($data['exportable'])->printable($data['printable'])->width($data['width'])->addClass($data['class']);
                } else {
                    $arrRearrange[] = Column::make($headertitle)->title($data['title']);
                }
            }
        }
        return $arrRearrange;
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'InventoryCountUnMatched_' . date('YmdHis');
    }

    /**
     * Get sheetname for export.
     *
     * @return string
     */
    protected function sheetName()
    {
        return 'Inventory Count UnMatched';
    }
}
