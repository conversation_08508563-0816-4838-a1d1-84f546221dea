<?php

namespace App\Http\Controllers\Report;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Alert;
use DataTables;
use App\SiteSetting;
use DB;
use PDF;
use DateTime;
use App\View\TparmView;
use App\DataTables\InvBalRepDataTable;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Support\Facades\Session;

class COStatusReportController extends Controller
{

    public function index(){
       
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error'); ;
        }
        return view('report.inv.costatus.index')->with('unit_quantity_format',$unit_quantity_format)->with('report_module_name', 'Customer Order Status');

    }

    public function getQuery(Request $request) {
        $siteSettings = new SiteSetting();
        $now_date = Carbon::now()->toDateString();
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        // dd(config('icapt.enable_cust_name_notfrom_cust_table'));
 
         if($sap_trans_order_integration==1 && config('icapt.enable_cust_name_notfrom_cust_table')==true)
         {
             $cust_name = "co.cust_name";
         }
         else{
             $cust_name = "customers.cust_name" ;
         }

        $query = DB::query()->from('coitems as co')
        ->select('co.co_num', 'co.co_line', 'co.cust_num', $cust_name, DB::raw('DATE_FORMAT(co.due_date, "' . $siteSettings->getMySQLDateFormat() . '") as due_date'), 'co.item_num', 'co.item_desc', 'co.qty_picked', 'co.qty_ordered', 'co.qty_shipped', 'co.qty_returned', 'co.qty_required', 'co.uom')
        ->leftJoin('customers','customers.cust_num','=','co.cust_num')
        ->where('co.site_id', auth()->user()->site_id)
        ->where('customers.site_id', auth()->user()->site_id);

        if(request('from_item_num') && request('to_item_num')){
            $query->whereBetween('co.item_num', [request('from_item_num'), request('to_item_num')]);
        }
        else if(request('from_item_num')){
            $query->where('co.item_num', '>=', request('from_item_num'));
        }
        else if(request('to_item_num')){
            $query->where('co.item_num', '<=', request('to_item_num'));
        }

        if(request('from_co_num') && request('to_co_num')){
            $query->whereBetween('co.co_num', [request('from_co_num'), request('to_co_num')]);
        }
        else if(request('from_co_num')){
            $query->where('co.co_num', '>=', request('from_co_num'));
        }
        else if(request('to_co_num')){
            $query->where('co.co_num', '<=', request('to_co_num'));
        }

        if(request('from_cust_num') && request('to_cust_num')){
            $query->whereBetween('co.cust_num', [request('from_cust_num'), request('to_cust_num')]);
        }
        else if(request('from_cust_num')){
            $query->where('co.cust_num', '>=', request('from_cust_num'));
        }
        else if(request('to_cust_num')){
            $query->where('co.cust_num', '<=', request('to_cust_num'));
        }

        if(request('from_due_date') && request('to_due_date')){
            $from_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_due_date'))->format('Y-m-d');
            $to_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_due_date'))->format('Y-m-d');

            $query->whereBetween('co.due_date', [$from_due_date, $to_due_date.' 23:59:59']);
        }
        else if(request('from_due_date')){
            $from_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('from_due_date'))->format('Y-m-d');
            $query->where('co.due_date', '>=', $from_due_date);
        }
        else if(request('to_due_date')){
            $to_due_date = Carbon::createFromFormat(''.$siteSettings->getInputDateFormat() .'', request('to_due_date'))->format('Y-m-d');
            $query->where('co.due_date', '<=', $to_due_date.' 23:59:59');
        }

        if(request('status') == 'Fully Shipped'){
            // Qty Required == 0
            $query->where('qty_required', '=', '0');
        }
        if(request('status') == 'Partially Shipped'){
            // Qty Shipped > 0 && Qty Required > 0
            $query->where('qty_required', '>', '0')
                ->where('qty_shipped', '>', '0');
        }
        if(request('status') == 'Unshipped'){
            // Qty Shipped = 0
            $query->where('qty_shipped', '=', '0');
        }

        if(request('past_due') == 'on') {
            // Only show records with due date that has passed
            $query->whereDate('due_date', '<', $now_date);
        }
        
        $query->groupBy('co_num','co_line','cust_name','due_date','item_num','uom');

        $query->orderBy('co_num', 'asc')
            ->orderBy('co_line', 'asc')
            ->orderBy('cust_name', 'asc')
            ->orderBy('due_date', 'asc')
            ->orderBy('item_num', 'asc');

        // $dataTable = Datatables::of($query);
        // $response  = $dataTable->make(true);

        if (Session::get('shortAllocationReturn') == true){

            $w = Session::get('warehouse_select1');

            // dd($w);

            if($w == 'All' || $w==null)
            {

                $item_locs = DB::table('item_locs')
                ->select( DB::raw("SUM(qty_available) as qty_avail"),'item_num')
                ->where('site_id', auth()->user()->site_id)
                ->groupBy('item_num');

                $query = DB::table('coitems')
                ->leftjoinSub($item_locs, 'item_locs', function ($q) {
                    $q->on('coitems.item_num', '=', 'item_locs.item_num');
                })
                ->select(
                    'coitems.co_num',
                    'coitems.co_line',
                    'coitems.rel_status',
                    'coitems.item_num',
                    'qty_avail',
                    DB::raw("SUM(coitems.qty_shortage) as qty_shortage"),
                    'coitems.qty_ordered',
                    'coitems.modified_date',
                )
                ->where('coitems.site_id',auth()->user()->site_id)
                ->where('coitems.rel_status','!=','C')
                ->whereRaw('IFNULL(coitems.qty_allocated,0) < IFNULL(coitems.qty_ordered,0)');

                $query->orderBy('qty_shortage', 'desc');
                $query->groupBy('coitems.item_num');
            }
            else
            {
                $item_locs = DB::table('item_locs')
                ->select( DB::raw("SUM(qty_available) as qty_avail"),'item_num')
                ->where('whse_num', $w)
                ->where('site_id', auth()->user()->site_id)
                ->groupBy('item_num');
                $query = DB::table('coitems')

                ->leftjoinSub($item_locs, 'item_locs', function ($q) {
                $q->on('coitems.item_num', '=', 'item_locs.item_num');
                })
                ->select(
                'coitems.whse_num',
                'coitems.co_num',
                'coitems.co_line',
                'coitems.rel_status',
                'coitems.item_num',
                'qty_avail',
                DB::raw("SUM(coitems.qty_shortage) as qty_shortage"),
                'coitems.qty_ordered',
                'coitems.modified_date',
                )
                ->where('coitems.site_id',auth()->user()->site_id)
                ->where('coitems.whse_num',$w)
                ->where('coitems.rel_status','!=','C')
                ->whereRaw('IFNULL(coitems.qty_allocated,0) < IFNULL(coitems.qty_ordered,0)');

                $query->orderBy('qty_shortage', 'desc');
                $query->groupBy('coitems.item_num');


            }

            Session::put('shortAllocationReturn', false);
            Session::put('warehouse_select1', "");
        }

        return $query;
    }

    public function data(Request $request) {

        $query = $this->getQuery($request);

        if ($request->has('columns')) {
            foreach ($request->columns as $index => $column) {
                if (isset($column['data']) && $column['data'] === 'due_date' && 
                    isset($column['search']) && !empty($column['search']['value'])) {
                    
                    $keyword = $column['search']['value'];
                    $keyword = getDateFitlerValue($keyword);
                    
                    // Apply a simple LIKE filter to the raw query builder
                    $query->where(DB::raw("DATE(due_date)"), 'LIKE', $keyword);
                    
                    break;
                }
            }
        }

        $dataTable = Datatables::of($query);
        $response = $dataTable->make(true);

        return $response;
    }

    // Print function
    public function print(Request $request) {
        
        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');

        $query = $this->getQuery($request);
        $query1 = $query->get();

        // Site
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        if($site->logo != "") {
            $url = $site->getFileFullPath();
            $image = file_get_contents($url);
            $site->logoazure = "data:image/png+jpeg+jpg;base64,".base64_encode($image);
        }
        else
            $site->logoazure = "";

        if($site->company_info) {
            $sitedecode = json_decode($site->company_info);
            $site->company_name = strtoupper($sitedecode->company_name);
        }

        $prev_data = [
            'co_num' => '',
        ];
        
        $index = 1;

        $now = Carbon::now()->toDateTimeString();
        $date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, auth()->user()->timezone, 'H:i:s');

        $headerHtml = view()->make('report.trans.transactionReport')->with('siteLogo', $site->logo)->with('siteLogoAzure', $site->logoazure)->with('companyName', $site->company_name)->render();
        $footerHtml = view()->make('report.trans.footer')->with('date',$date)->with('time',$time)->render();

        $pdf = PDF::loadView('report.inv.costatus.print', compact('total_quantity_format', 'query1', 'request', 'prev_data', 'index', 'site'))
            ->setPaper('A4', 'portrait')
            ->setOption('header-html', $headerHtml)
            ->setOption('footer-html', $footerHtml)
            ->setOption('margin-bottom', 10)
            ->setOption('margin-top', 5)
            ->setOption('margin-right', 10)
            ->setOption('margin-left', 10)
            ->setOption("footer-right", "Page [page] of [topage]")
            ->setOption("footer-font-size", 8);

        return $pdf->stream('CO Status.pdf');
    }

    public function coViewDashboard(Request $request) {

        // dd($request);
        if($request->wh=="All" || $request->wh==null)
        {
            $whse_pass = "All";
        }
        else
        {
            $whse_pass = $request->wh;
        }

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error'); ;
        }
        return view('report.inv.costatus.dashboardStatus')->with('unit_quantity_format',$unit_quantity_format)->with('whse_pass',$whse_pass);
    }

    public function getQueryDashboard(Request $request) {

        $siteSettings = new SiteSetting();

        $itemquery = DB::table('coitems')
                ->select(
                    'coitems.item_num',
                    DB::raw("SUM(coitems.qty_shortage) as total_qty_shortage"),
                )
                ->where('coitems.site_id',auth()->user()->site_id)
                ->where('coitems.rel_status','!=','C')
                ->whereRaw('IFNULL(coitems.qty_allocated,0) < IFNULL(coitems.qty_ordered,0)')
                ->groupBy('item_num')
                ->orderBy('total_qty_shortage', 'desc')
                ->LIMIT(5);

        $itemarray = $itemquery->pluck('item_num')->toArray();

        $query = DB::table('coitems')
                ->select(
                    'coitems.co_num',
                    'coitems.co_line',
                    'coitems.rel_status',
                    'coitems.item_num',
                    'coitems.item_desc',
                    DB::raw('IFNULL(itemquery.total_qty_shortage,0) as total_qty_shortage'), 
                    'coitems.qty_shortage',
                    'coitems.qty_required', 
                    'coitems.qty_allocated',
                    'coitems.uom',
                    DB::raw('DATE_FORMAT(coitems.due_date, "' . $siteSettings->getMySQLDateFormat() . '") as line_due_date'), 
                )
                ->whereIn('coitems.item_num',$itemarray)
                ->where('coitems.site_id',auth()->user()->site_id)
                ->where('coitems.rel_status','!=','C')
                ->whereRaw('IFNULL(coitems.qty_allocated,0) < IFNULL(coitems.qty_ordered,0)');

        $query = $query->leftjoinSub($itemquery, 'itemquery', function ($q) {
                    $q->on('coitems.item_num', '=', 'itemquery.item_num');
                });

        if(request()->wh && request()->wh != 'All'){
            $query = $query->where('coitems.whse_num', request()->wh);
        }

        $query->orderBy('total_qty_shortage', 'desc');
        $query->orderBy('item_num');
        $query->orderBy('qty_shortage', 'desc');

        return $query;
    }

    public function dataDashboard(Request $request) {

        // dd($request);

        $query = $this->getQueryDashboard($request);

        $dataTable = Datatables::of($query);
        $response = $dataTable->make(true);

        return $response;
    }

    // public function dttest(POLinesRepDataTable $poLinesRepDataTable) {
    //     return $poLinesRepDataTable->render('report.polines.table');
    // }

}
