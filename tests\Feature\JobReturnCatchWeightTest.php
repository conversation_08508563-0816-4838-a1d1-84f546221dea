<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\User;
use App\Item;
use App\Job;
use App\JobRoute;
use App\Warehouse;
use App\Loc;
use Illuminate\Support\Facades\Gate;

class JobReturnCatchWeightTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $item;
    protected $job;
    protected $jobRoute;
    protected $warehouse;
    protected $location;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create([
            'site_id' => 1,
            'email' => '<EMAIL>'
        ]);

        // Create test warehouse
        $this->warehouse = Warehouse::factory()->create([
            'whse_num' => 'TEST_WH',
            'whse_status' => 1,
            'site_id' => 1
        ]);

        // Create test location
        $this->location = Loc::factory()->create([
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'loc_status' => 1,
            'site_id' => 1
        ]);

        // Create catch weight enabled item
        $this->item = Item::factory()->create([
            'item_num' => 'CW_ITEM_001',
            'item_desc' => 'Catch Weight Test Item',
            'item_status' => 1,
            'catch_weight' => 1,
            'catch_weight_tolerance' => 5.0,
            'lot_tracked' => 1,
            'site_id' => 1
        ]);

        // Create regular item
        Item::factory()->create([
            'item_num' => 'REG_ITEM_001',
            'item_desc' => 'Regular Test Item',
            'item_status' => 1,
            'catch_weight' => 0,
            'catch_weight_tolerance' => 0,
            'lot_tracked' => 0,
            'site_id' => 1
        ]);

        // Create test job
        $this->job = Job::factory()->create([
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'item_num' => 'CW_ITEM_001',
            'whse_num' => 'TEST_WH',
            'job_status' => 'R',
            'qty_ordered' => 100,
            'qty_completed' => 50,
            'qty_returned' => 0,
            'uom' => 'KG',
            'site_id' => 1
        ]);

        // Create test job route
        $this->jobRoute = JobRoute::factory()->create([
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10',
            'qty_completed' => 50,
            'qty_returned' => 0,
            'site_id' => 1
        ]);
    }

    /** @test */
    public function it_shows_catch_weight_view_for_catch_weight_items()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasJobReturn')
            ->andReturn(true);

        $this->actingAs($this->user);

        $response = $this->get(route('JobReturn', [
            'item_num' => 'CW_ITEM_001',
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('production.jobreturn.process_cw');
        $response->assertViewHas('item');
        $response->assertViewHas('job_order');
        $response->assertViewHas('job_route');
        $response->assertViewHas('batch_id');
    }

    /** @test */
    public function it_shows_regular_view_for_non_catch_weight_items()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasJobReturn')
            ->andReturn(true);

        $this->actingAs($this->user);

        $response = $this->get(route('JobReturn', [
            'item_num' => 'REG_ITEM_001',
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10'
        ]));

        $response->assertStatus(200);
        $response->assertViewIs('production.jobreturn.process');
        $response->assertViewHas('unit_quantity_format');
        $response->assertViewHas('sap_trans_order_integration');
        $response->assertViewHas('batch_id');
    }

    /** @test */
    public function it_validates_catch_weight_route_exists()
    {
        $this->assertTrue(
            collect(\Route::getRoutes())->contains(function ($route) {
                return $route->getName() === 'JobReturnCWProcess';
            })
        );
    }

    /** @test */
    public function it_redirects_catch_weight_items_from_regular_process()
    {
        // Mock Gate to allow permission
        Gate::shouldReceive('allows')
            ->with('hasJobReturn')
            ->andReturn(true);

        $this->actingAs($this->user);

        $response = $this->post(route('JobReturn.process'), [
            'item_num' => 'CW_ITEM_001',
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10',
            'whse_num' => 'TEST_WH',
            'loc_num' => 'TEST_LOC',
            'qty_to_return' => 10,
            'reason_code' => 'TEST_REASON',
            'batch_id' => 'TEST_BATCH_001'
        ]);

        $response->assertRedirect();
        $response->assertRedirect(route('JobReturn', [
            'item_num' => 'CW_ITEM_001',
            'job_num' => 'TEST_JOB_001',
            'suffix' => '001',
            'oper_num' => '10'
        ]));
    }
}
