@extends('layout.app')
@push('custom-head')
    <style>
    html body .content .content-wrapper{
        margin: 0.6rem 1.2rem;} 
    .card-header1{
        margin-bottom: -10px;}
    textarea{
        padding:2px;} 
    input {
        padding:2px;
        height: 25px;}
    select#conv_type.form-control.border-primary{
        padding-left:8px !important;
        height: 25px;}
    </style>
@endpush
@section('content')
    <div class="card-header1">
        <h4 class="card-title" id="basic-layout-colored-form-control">{{__('admin.title.edit_uomcon')}}</h4>
    </div>
    <br>
    <?php
    $k=$uomconversiondetails->conv_type =="C"?$uomconversiondetails->cust_num:$uomconversiondetails->vend_num;
    $actionUrl="/maintenance-invty/master-files-invty/uom-conversions/update2/".$uomconversiondetails->uom_from."/".$uomconversiondetails->uom_to."/".$uomconversiondetails->conv_type."/".$k;
    ?>
   <!--<form class="form" id="postform" autocomplete="off" action="/maintenance/master-files/uom-conversions/update/{{$uomconversiondetails->uom_from}}/{{$uomconversiondetails->uom_to}}/{{$uomconversiondetails->conv_type}}/{{$uomconversiondetails->item_num}}/" method="POST">--> 
    <form class="form" id="postform" autocomplete="off"  method="POST" action="/maintenance-invty/master-files-invty/uom-conversions/update2"> 
        <input type="hidden" name="id" id="id" value="{{$uomconversiondetails->id}}" />
        <input type="hidden" name="_uom_from" id="_uom_from" value="{{$uomconversiondetails->uom_from}}" />
        <input type="hidden" name="_uom_to" id="_uom_to" value="{{$uomconversiondetails->uom_to}}" />
        <input type="hidden" name="_conv_type" id="_conv_type" value="{{$uomconversiondetails->conv_type}}" />
        <input type="hidden" name="_item_num" id="_item_num" value="{{$uomconversiondetails->item_num}}" />
        <input type="hidden" name="_cust_num" id="_cust_num" value="{{$uomconversiondetails->cust_num}}" />
        <input type="hidden" name="_vend_num" id="_vend_num" value="{{$uomconversiondetails->vend_num}}" />

        @csrf

        <div class="form-body">
            <div>
                <ul id="errors"></ul>
            <div>
            <div class="form-group">
                <table>
                    <tr>
                        <td width='200px'><label for="uom_from" class="required">{{__('admin.label.uom_from')}}</label></td>
                        <td width='300px'><input type="text" id="uom_from" class="form-control border-primary" name="uom_from" value="{{old('uom_from', $uomconversiondetails->uom_from)}}"  readonly></td>
                    </tr>
                    <tr>
                        <td><label for="uom_to" class="required">{{__('admin.label.uom_to')}}</label></td>
                        <td><input type="text" id="uom_to" class="form-control border-primary"  value="{{old('uom_to', $uomconversiondetails->uom_to)}}"  name="uom_to" readonly></td>
                    </tr>
                    <tr>
                        <td><label for="conv_type" class="required">{{__('admin.label.conv_type')}}</label></td>
                        <td>
                            <select class="form-control border-primary" id="conv_type" class="form-control" name="conv_type" readonly disabled>
                                    <option value="G" <?php if($uomconversiondetails->conv_type=="G") echo 'selected="selected"'; ?>>{{__('admin.option.global')}}</option>
                                    <option value="I" <?php if($uomconversiondetails->conv_type=="I") echo 'selected="selected"'; ?>>{{__('admin.option.item')}}</option>
                                    <option value="V" <?php if($uomconversiondetails->conv_type=="V") echo 'selected="selected"'; ?>>{{__('admin.option.vendor')}}</option>
                                    <option value="C" <?php if($uomconversiondetails->conv_type=="C") echo 'selected="selected"'; ?>>{{__('admin.option.customer')}}</option>
                            </select>
                        </td>
                    </tr>
                    @if(!($uomconversiondetails->conv_type=="G"))
                    <tr id="itemrow">
                        <td><label for="item_num" class="required">{{__('admin.label.item_num')}}</label></td>
                        <td><input type="text" id="item_num" class="form-control border-primary" onchange="display('/displayItemDesc','item_num','item_desc');" placeholder="Item" value="{{$uomconversiondetails->item_num}}" name="item_num" readonly></td>
                        {{-- <td><button type="button" name="Item"  onClick="selection('/getItem','item_num','item_num','item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td> --}}
                    </tr>
                    <tr>
                        <td><label for="item_desc"> {{__('admin.label.desc')}}</label></td>
                        <td><textarea type="text" id="item_desc" class="form-control border-primary" placeholder="Item Description" readonly>{{@$uomconversiondetails->item->item_desc}}</textarea></td>
                    </tr>
                    @endif
                    @if ($uomconversiondetails->conv_type=="V")
                    <tr id="vendrow">
                        <td><label for="vend_num" class="required">{{__('admin.label.vendor')}}</td>
                        <td><input type="text" id="vend_num" class="form-control border-primary" placeholder="Vendor"  value="{{$uomconversiondetails->vend_num}}" name="vend_num" readonly></td>
                        {{-- <td><button type="button" tabindex="-1" name="Vendor" onClick="selection('/getVendor','vend_num','vend_num','vend_num');modalheader(this.id,this.name);" class="btn btn-icon btn-outline-secondary btn-square bg-primary white" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td> --}}
                    </tr>
                    <tr>
                        <td></td>
                        <td><input type="text" id="vend_name" class="form-control border-primary" placeholder="Vendor Name" value="{{$uomconversiondetails->vendor->vend_name}}" readonly></td>
                    </tr>
                    @endif
                        @if ($uomconversiondetails->conv_type=="C")
                    <tr id="custrow">
                        <td><label for="cust_num" class="required">{{__('admin.label.cust')}}</label></td>
                        <td><input type="text" id="cust_num" class="form-control border-primary" name="cust_num" onchange="display('/displayCustName','cust_num','cust_name');"  value="{{$uomconversiondetails->cust_num}}" placeholder="Customer" readonly></td>
                        {{-- <td><button type="button" name="Customer"  onClick="selection('/getCust','cust_num','cust_num','cust_num');modalheader(this.id, this.name);" class="btn btn-icon btn-outline-secondary btn-square bg-primary white" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button></td> --}}
                    </tr>
                    <tr>
                        <td></td>
                        <td><input type="text" id="cust_name" class="form-control border-primary" placeholder="Customer Name" value="{{$uomconversiondetails->customer->cust_name}}" readonly></td>
                    </tr>
                    @endif
                    <tr>
                        <td><label for="conv_factor" class="required">{{__('admin.label.conv_factor')}}</label></td>
                        <td><input type="text" id="conv_factor" class="form-control border-primary number-format" 
                            style="text-align: right;" placeholder="Conversion Factor" name="conv_factor" value="{{ old('conv_factor', $uomconversiondetails->conv_factor) }}" required></td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="form-actions center">
            <a href="{{route('UOMConversion')}}">
                <button type="button" class="btn btn-warning mr-1">
                    <i class="icon-cross2"></i> {{__('admin.button.cancel')}}
                </button>
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="icon-check2"></i> {{__('admin.button.save')}}
            </button>
        </div>
    </form>        
<script>
    jQuery(function($) {
        $("#postform").validate({
            onchange: true,
            rules: {
                item_num: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {_token: $('input[name="_token"]').val()}
                    }
                },
                cust_num: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {_token: $('input[name="_token"]').val()}
                    }
                },
                vend_num: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {_token: $('input[name="_token"]').val()}
                    }
                },
                conv_factor:{
                    required: true,
                    number: true,
                    number_size: true,
                    min_value: 0.0001,
                }
            },
            messages: {
                item_num: {
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                cust_num: {
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                },
                vend_num: {
                    remote: "{{ __('error.admin.notexist', ['resource' => __('admin.label.vend_num') ]) }}"
                },
                conv_factor:{
                    number: "{{ __('error.mobile.numbersonly', ['resource' => __('admin.label.conv_factor') ]) }}",
                    number_size: "{{__('error.mobile.max_characters')}}",
                    min_value: "{{ __('error.mobile.morethanequal', ['resource' => __('admin.label.conv_factor')])}} {0}"
                }
            },

            errorPlacement: function (error, element) {
                error.insertAfter(element);
            },

            submitHandler: function (form) {
                $(".pageloader").css("display", "block");
                $(".submitloader").attr("disabled", true);
                setTimeout(function () {
                    form.submit();
                }, 300);
            }
        });
    });

    $(document).ready(function() {
        $("#item_num").on("change", function(){
            if ($("#item_num").val()) {
                // Send error if manually type object that is not exist or inactive
                $.ajax({
                    url: '{{route('itemv2')}}',
                    type: "GET",
                    data: {
                        item_num: btoa($("#item_num").val()),
                    },
                    success: function(data){
                        if (data == "inactive") {
                            Alert.inactive('{{__('admin.label.item_num')}}',$("#item_num").val());
                            $("#item_num,#item_desc").val('');
                        }
                        else if (data == "not exist") {
                            Alert.notexist('{{__('admin.label.item_num')}}',$("#item_num").val());
                            $("#item_num,#item_desc").val('');
                        }
                        else if (data == "active") {
                            if ($("#conv_type").val() == 'I') {
                                ajaxurl = "{{ route('checkuomitem', ['uom_from','uom_to', 'conv_type', 'item_num'])}}";
                                url = ajaxurl.replace('item_num', $("#item_num").val());
                                url = url.replace('conv_type', $("#conv_type").val());
                                url = url.replace('uom_from', $("#uom_from").val());
                                url = url.replace('uom_to', $("#uom_to").val());
                                $.get(url, function (data) {
                                    if (data == 'exist') {
                                        Alert.exists('{{__('Item')}}', $("#item_num").val());
                                        $("#item_num, #item_desc").val('');
                                    } else {
                                        event.preventDefault();
                                    }
                                });
                            }
                            if ($("#conv_type").val() == 'V') {
                                if ($("#vend_num").val()) {
                                    ajaxurl = "{{ route('checkuomvendor', ['uom_from','uom_to', 'item_num', 'vend_num', 'id'])}}";
                                    url = ajaxurl.replace('item_num', $("#item_num").val());
                                    url = url.replace('vend_num', $("#vend_num").val());
                                    url = url.replace('conv_type', $("#conv_type").val());
                                    url = url.replace('uom_from', $("#uom_from").val());
                                    url = url.replace('uom_to', $("#uom_to").val());
                                    url = url.replace('id', $("#id").val());
                                    $.get(url, function (data) {
                                        if (data == 'exist') {
                                            Alert.exists('{{__('Item')}}', $("#item_num").val());
                                            $("#item_num, #item_desc").val('');
                                        } else {
                                            event.preventDefault();
                                        }
                                    });
                                }
                            }
                            if ($("#conv_type").val() == 'C') {
                                if ($("#cust_num").val()) {
                                    ajaxurl = "{{ route('checkuomcust', ['uom_from','uom_to', 'item_num', 'cust_num', 'id'])}}";
                                    url = ajaxurl.replace('item_num', $("#item_num").val());
                                    url = url.replace('cust_num', $("#cust_num").val());
                                    url = url.replace('conv_type', $("#conv_type").val());
                                    url = url.replace('uom_from', $("#uom_from").val());
                                    url = url.replace('uom_to', $("#uom_to").val());
                                    url = url.replace('id', $("#id").val());
                                    $.get(url, function (data) {
                                        if (data == 'exist') {
                                            Alert.exists('{{__('Item')}}', $("#item_num").val());
                                            $("#item_num, #item_desc").val('');
                                        } else {
                                            event.preventDefault();
                                        }
                                    });
                                }
                            }
                        }
                    }
                });
            }
        });

        $("#cust_num").on('change',function() {
            if ($("#cust_num").val()) {
                // Send error if manually type object that is not exist or inactive
                $.ajax({
                    url: '{{route('custv2')}}',
                    type: "GET",
                    data: {
                        cust_num: btoa($("#cust_num").val()),
                    },
                    success: function(data){
                        if (data == "inactive") {
                            Alert.inactive('{{__('admin.label.cust_num')}}',$("#cust_num").val());
                            $("#cust_num,#cust_name").val('');
                        }
                        else if (data == "not exist") {
                            Alert.notexist('{{__('admin.label.cust_num')}}',$("#cust_num").val());
                            $("#cust_num,#cust_name").val('');
                        }
                        else if (data == "active") {
                            ajaxurl = "{{ route('checkuomcust', ['uom_from','uom_to', 'item_num', 'cust_num', 'id'])}}";
                            url = ajaxurl.replace('item_num', $("#item_num").val());
                            url = url.replace('cust_num', $("#cust_num").val());
                            url = url.replace('conv_type', $("#conv_type").val());
                            url = url.replace('uom_from', $("#uom_from").val());
                            url = url.replace('uom_to', $("#uom_to").val());
                            url = url.replace('id', $("#id").val());
                            $.get(url, function (data) {
                                if (data == 'exist') {
                                    Alert.exists('{{__('Customer')}}', $("#cust_num").val());
                                    $("#item_num, #item_desc").val('');
                                } else {
                                    event.preventDefault();
                                }
                            });
                        }
                    }
                });
            }
        });

        $("#vend_num").on('change',function() {
            if ($("#vend_num").val()) {
                // Send error if manually type object that is not exist or inactive
                $.ajax({
                    url: '{{route('vendorv2')}}',
                    type: "GET",
                    data: {
                        vend_num: $("#vend_num").val(),
                    },
                    success: function(data){
                        if (data == "inactive") {
                            Alert.inactive('{{__('admin.label.vend_num')}}',$("#vend_num").val());
                            $("#vend_num,#vend_name").val('');
                        }
                        else if (data == "not exist") {
                            Alert.notexist('{{__('admin.label.vend_num')}}',$("#vend_num").val());
                            $("#vend_num,#vend_name").val('');
                        }
                        else if (data == "active") {
                            display('/displayVendName','vend_num','vend_name');
                            ajaxurl = "{{ route('checkuomvendor', ['uom_from','uom_to', 'item_num', 'vend_num', 'id'])}}";
                            url = ajaxurl.replace('item_num', $("#item_num").val());
                            url = url.replace('vend_num', $("#vend_num").val());
                            url = url.replace('conv_type', $("#conv_type").val());
                            url = url.replace('uom_from', $("#uom_from").val());
                            url = url.replace('uom_to', $("#uom_to").val());
                            url = url.replace('id', $("#id").val());
                            $.get(url, function (data) {
                                if (data == 'exist') {
                                    Alert.exists('{{__('Vendor')}}', $("#vend_num").val());
                                    $("#vend_num, #vend_name").val('');
                                } else {
                                    event.preventDefault();
                                }
                            });
                        }
                    }
                });
            }
        });
    });

    function itemexist() {
        ajaxurl ="{{ route('uomitemv', ['uom_from','uom_to','conv_type', 'item_num', 'cust_num', 'vend_num', 'id'])}}";
        url = ajaxurl.replace('item_num', $("#item_num").val());
        url = url.replace('cust_num', $("#cust_num").val());
        url = url.replace('vend_num', $("#vend_num").val());
        url = url.replace('conv_type', $("#conv_type").val());
        url = url.replace('uom_from', $("#uom_from").val());
        url = url.replace('uom_to', $("#uom_to").val());
        $.get(url, function(data){
            if(data == 'exist'){
                Alert.exists('{{__('Item')}}', $("#item_num").val());
                $('#postform').attr('onsubmit','return false;');
            }else{
                $('#postform').attr('onsubmit','return true;');
            }
        });
    }
</script>
@include('errors.maxchar')
@include('util.selection')
@endsection
