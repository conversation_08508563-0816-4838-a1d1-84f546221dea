@extends('layout.mobile.app')
@section('content')
@section('title', __('Customer Order Picking - Catch Weight'))

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$co_item->whse_num"
            :itemnum="$co_item->item_num"
            :itemdesc="$co_item->item_desc"
            :refnum="$co_item->co_num"
            :refline="$co_item->co_line"
            :qtybalance="$co_item->qty_shortage ?? 0"
            :qtybalanceuom="$co_item->uom"
            :submiturl="route('runCoPickCWProcess')"
            :catch-weight-tolerance="$co_item->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="0"
            :allow-over="$allow_over_pick"
            :line-uom="$co_item->uom"
            :print-label="$printLabel"
            transtype="co"
            trans-type="COPick"
            :incoming="false">

            <!-- Additional CO Pick specific hidden fields -->
            <input type="hidden" name="co_rel" value="{{ $co_item->co_rel }}">
            <input type="hidden" name="cust_num" value="{{ $co_item->cust_num }}">
            <input type="hidden" name="stage_num" value="{{ $stage_num }}">
            <input type="hidden" name="delivery_date" value="{{ request('delivery_date') }}">
            <input type="hidden" name="delivery_trip" value="{{ request('delivery_trip') }}">
            <input type="hidden" name="getCheckNONINV" value="{{ $getCheckNONINV ?? 0 }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num',$def_loc) }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.job_num') }} : {{ $joborder->job_num }} | {{ $joborder->suffix }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

@endsection
