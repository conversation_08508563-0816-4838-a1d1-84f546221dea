<?php

use App\View\TparmView;
use App\Item;
use App\Warehouse;
use App\Loc;
use App\Lot;
use App\ReasonCode;
use App\UOM;
use App\ProductCode;
use App\Vendor;
use App\Customer;
use App\Employee;
use App\Job;
use App\WorkCenter;
use App\ShippingZone;
use App\PurchaseOrder;
use App\CustomerOrder;
use App\TransferOrder;
use App\Machine;
use App\Task;
use App\TransferLine;
use App\TransferOrderLinesSubline;
use Carbon\Carbon;
use App\View\CoPickView;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\QrcodeController;
use App\ItemLoc;
use App\LotLoc;
use App\MatlTrans;
use App\po_item;
use App\SiteSetting;
use Camroncade\Timezone\Facades\Timezone;
use App\PicklistSummaryDetails;
use App\PurchaseOrderItem;
use App\GRN;
use App\GRNItem;
use App\JobRoute;
use App\JobMatl;
use Illuminate\Validation\ValidationException;



/* *************************************************
-- Author         : Reeve
-- Create date    : 01/07/2025
-- Description    : For All Headers ( JO, PO, CO and TO ) , direct close the notify to SAP 
-- Method         : post

-- Function        : getPostNotificationToSAP
-- --------------------------------------------------------

=============================================================================================================================
    'Rev      Project Code     Modified By - Date           Description

    '-----    ------------     --------------------         ----------------------


==============================================================================================================================*/
if (!function_exists('getPostNotificationToSAP')) {
     function getPostNotificationToSAP($type){        
        $arrJsonType = array(
            "TO"    => array('U_DocType' => '1250000001', 
                            'post_from' => 'TransferController_update()', 
                            'service_name' => 'InventoryTransferRequests', 
                            'sap_service'  => 'InventoryTransferRequests($id)', 
                            'process_name' => 'Close Transer Order'),

             "PO"    => array('U_DocType' => '22', 
                            'post_from' => 'POHeaderController_update()', 
                            'service_name' => 'PurchaseOrders', 
                            'sap_service'  => 'PurchaseOrders($id)', 
                            'process_name' => 'Close Purchase Order'),

             "CO"    => array('U_DocType' => '17', 
                            'post_from' => 'COHeaderController_update()', 
                            'service_name' => 'Orders',
                            'sap_service'  => 'Orders($id)',  
                            'process_name' => 'Close Customer Order'),
                            
             "JO"    => array('U_DocType' => '202', 
                            'post_from' => 'JobController_update()', 
                            'service_name' => 'ProductionOrders',
                            'sap_service'  => 'ProductionOrders($id)', 
                            'process_name' => 'Close Job Order'),
        );

        return $arrJsonType[$type];
     }
}











/* *************************************************
-- Author         : Reeve
-- Create date    : 286/05/2025
-- Description    : Formula to detech the Catch Weight
-- Method         : post

-- Function        : getCatchWeightTolerance
-- --------------------------------------------------------

=============================================================================================================================
    'Rev      Project Code     Modified By - Date           Description

    '-----    ------------     --------------------         ----------------------


==============================================================================================================================*/

if (!function_exists('getCatchWeightTolerance')) {
    function getCatchWeightTolerance($inputTotalWeight, $inputExpectedWeight, $inputToleranceRate)
    {
        $MinTolerance = $inputExpectedWeight - $inputToleranceRate;
        $MaxTolerance = $inputExpectedWeight + $inputToleranceRate;
        $arrResult = [];
        if($MinTolerance < $inputTotalWeight && $inputTotalWeight < $MaxTolerance)
        {
            $arrResult = [
                'color' => 'green',
                'message' => 'Within Tolerance',
                'min_tolerance' => $MinTolerance,
                'max_tolerance' => $MaxTolerance,
            ];

        }
        else{
           $arrResult = [
                'color' => 'red',
                'message' => 'Without Tolerance',
                'min_tolerance' => $MinTolerance,
                'max_tolerance' => $MaxTolerance,
            ];
        }

         return $arrResult;

    }
}



if (!function_exists('getExpectedWeight')) {
    function getExpectedWeight($inputType, $request)
    {
      switch ($inputType) {
        case 'PO Receipt':
            $exphWeight = $request->qty_ordered - ($request->qty_received + $request->qty_returned);
            break;

        case 'Job Material Issue':
            $exphWeight = $request->qty_required - ($request->qty_issued + $request->qty_returned);
            break;

        default:
            $exphWeight = 0; // or handle unknown $type appropriately
            break;
        }
        
    return $exphWeight;

    }
}















/* *************************************************
-- Author         : Reeve
-- Create date    : 28/07/2024
-- Description    : Deduct the qty accordingly the loc / lot
-- Method         : post
-- Return         : Array
                        array:3 [▼
                            "1" => "0"
                            "2" => 2
                        ]
-- Source File(s)      : PickShipService.php
-- Function            : executeCoUnPick
-- --------------------------------------------------------

=============================================================================================================================
    'Rev      Project Code     Modified By - Date           Description

    '-----    ------------     --------------------         ----------------------
     'R1       Gitlab #2675      Reeve - 06/07/2023          CO Shipping staging line bug.

==============================================================================================================================*/

if (!function_exists('getDeductValueByRecord')) {
    function getDeductValueByRecord($inputValue, $records)
    {

        // Sample $records = array('2'=>'7','4'=>'6','6'=>'10','10'=>'5');
        $deductedRecords = [];
        foreach ($records as $key  => $record) {
            if ($inputValue > 0) {
                if ($inputValue >= $record) {
                    $deductedRecords[$key] = 0;
                    $inputValue -= $record;
                } else {
                    $deductedRecords[$key] = $record - $inputValue;
                    $inputValue = 0;
                }
            } else {
                $deductedRecords[$key] = $record;
            }
        }
        return $deductedRecords;
    }
}
/* *************************************************
-- Author         : Reeve
-- Create date    : 05/11/2024
-- Description    : Redirect to correct flow either back to list , back to selection or continue to post the action.
-- Method         : post

-- Source File(s)      : POReturnController.php
-- Function            : runPoProcess(POReturnRequest $request)
-- --------------------------------------------------------

=============================================================================================================================
    'Rev      Project Code     Modified By - Date           Description

    '-----    ------------     --------------------         ----------------------
     'R1       Gitlab #2944      Reeve - 05/11/2024          PO Return : Cancel button navigation issue

==============================================================================================================================*/

// Redirect Logic
if (!function_exists('generateRedirectUrl')) {
    function generateRedirectUrl($strTransType, $input)
    {
        switch ($strTransType) {

            case "CustomerReturn":

                if ($input['indicate'] == 1) {
                    // from List or Single record
                    if($input['from_list']==0)
                    {
                        // Single Record
                        return redirect()->route('CustomerReturn');                    }
                    else{

                        // Multiple
                        if($input['count_list']==1){
                            return redirect()->route('CustomerReturn');
                        }
                        else{
                            $arrData =  [
                                'whse_num' => $input['all']['whse_num'],
                                'cust_num' => $input['all']['cust_num'],
                            ];
                            return redirect()->route('CustomerReturnDetails', $arrData);
                        }

                    }
                }
                else if($input['indicate'] == 3)
                {
                    //dd('lalal');
                    return redirect()->route('CustomerReturn');
                }
                else{
                    //dd($input,'sjsjsj');
                    if ($input['indicate'] == 2) {

                        $warehouse = $input['whse_num'];
                        $item = $input['item_num'];
                        $customer = $input['cust_num'];
                        $returnNum = $input['return_num'];
                        $returnNumLine = $input['return_line'];
                        $results = DB::table('customer_returns')
                        ->join('customer_return_lines', 'customer_returns.return_num', '=', 'customer_return_lines.return_num')
                        ->when($warehouse, function ($query, $warehouse) {
                            return $query->where('customer_return_lines.whse_num', $warehouse);
                        })
                        ->when($returnNum, function ($query, $returnNum) {
                            return $query->where('customer_returns.return_num', $returnNum);
                        })
                        ->select('customer_returns.*', 'customer_return_lines.*')
                        ->where('customer_return_lines.status','O') // or specific fields
                        ->get();


                       // Single Record
                       if($results->count() == 1){
                        // Return Back Process Page
                        $finaldeduct = $results[0]->qty_required - $input['qty'];
                        //dd($finaldeduct);
                        if($finaldeduct==0)
                        {

                            return redirect()->route('CustomerReturn');
                        }
                        else{

                            if($results[0]->return_line!=$input['all']['return_line'])
                            {
                                $arrData =  [
                                    'whse_num' => $results[0]->whse_num,
                                    'cust_num' => $results[0]->cust_num,
                                ];
                                return redirect()->route('CustomerReturnDetails', $arrData);
                               // dd($results[0]);
                                // $arrData =  [
                                //     'from_list'=> 2,
                                //     'whse_num' => $results[0]->whse_num,
                                //     'cust_num' => $results[0]->cust_num,
                                //     'return_num' => $input['all']['return_num'],
                                //     'return_line' => $results[0]->return_line,
                                //     'lot_num' => $results[0]->lot_num,
                                //  //   'loc_num' => $results[0]->loc_num,
                                //   //  'item_num' => $results[0]->item_num,
                                // ];
                            }
                            else{

                                $arrData =  [
                                    'from_list'=> 2,
                                    'whse_num' => $results[0]->whse_num,
                                    'cust_num' => $results[0]->cust_num,
                                    'return_num' => $input['all']['return_num'],
                                    'return_line' => $input['all']['return_line'],
                                    'lot_num' => $input['all']['lot_num'],
                                    'loc_num' => $input['all']['loc_num'],
                                    'item_num' => $input['all']['item_num'],
                                ];
                            }
                            //dd($results[0]->whse_num,$input);

                            return redirect()->route('CustomerReturnProcess', $arrData);
                        }

                       }
                       else if($results->count() > 1)
                       {

                        $arrData =  [
                            'whse_num' => $input['all']['whse_num'],
                            'cust_num' => $input['all']['cust_num'],
                        ];
                        $finaldeduct = $results[0]->qty_required - $input['qty'];
                        //dd($finaldeduct);


                            $arrData =  [
                                'whse_num' => $input['all']['whse_num'],
                                'cust_num' => $input['all']['cust_num'],
                               // 'cust_name' => $input['all']['cust_name'],
                            ];
                            return redirect()->route('CustomerReturnDetails', $arrData);

                       }

                    }
                    //dd('sss');
                    return redirect()->route('CustomerReturn');
                }

            break;

            case "GRNReturn":
                if ($input['indicate'] == 1) {
                    // no select Item
                    $arrData =  [
                        'whse_num' => $input['whse_num'],
                        'return_type' => $input['return_type'],
                        'grn_num'      => $input['grn_num'],
                        'whse_num'      => $input['whse_num'],
                        'item_num' => null,
                        'vend_do' => $input['vend_do']
                    ];
                    $getGrn = GRNItem::where('grn_num', $input['grn_num'])->where('net_received', '>', '0')->count();
                    if ($getGrn > 1) {
                        return redirect()->route('backshowGRNLineReturn', $arrData);
                    } else {
                        return redirect()->route('poReturn');
                    }
                } elseif ($input['indicate'] == 2) {
                    return redirect()->route('poReturn');
                }
                break;
            case "GRNReceiptAdd":
                if ($input['indicate'] == 1) {
                    // back to ger-add
                    return redirect()->route('addGrn', ['vend_name' => $input['vend_name'], 'vend_num' => $input['vend_num'], 'whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' =>  $input['item_num'], 'vend_do' => $input['vend_do'], 'grn_num' => $input['grn_num'], 'receipt_type' => 'NewGRN']);
                } elseif ($input['indicate'] == 2) {
                    // back to Listing all
                    return redirect()->route('backshowGRnLine', ['vend_name' => $input['vend_name'], 'vend_num' => $input['vend_num'], 'whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' =>  $input['item_num'], 'vend_do' => $input['vend_do'], 'grn_num' => $input['grn_num'], 'receipt_type' => 'NewGRN']);
                } elseif ($input['indicate'] == 3) {
                    //back to Specify PO line
                    if ($input['count'] > 1) {
                        return redirect()->route('backshowGRnLine', ['vend_num' => $input['vend_num'], 'whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' =>  $input['item_num'], 'vend_do' => $input['vend_do'], 'grn_num' => null, 'receipt_type' => 'NewGRN']);
                    } else {
                        return redirect()->route('backshowGRnLine', ['vend_num' => $input['vend_num'], 'whse_num' => $input['whse_num'], 'po_num' => null, 'item_num' =>  $input['item_num'], 'vend_do' => $input['vend_do'], 'grn_num' => $input['grn_num'], 'receipt_type' => 'NewGRN']);
                    }
                }

                break;
            case "GRNReceipt":
                //

                if ($input['indicate'] == 1) {
                    // redirect List ALL
                    //dd(1);
                    return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => null, 'receipt_type' => 'GRN']);
                }
                else if ($input['indicate'] == 2) {
                    $strCancelURL = redirect()->route('poReceive');
                }
                else if ($input['indicate'] == 3) {
                    if ($input['count'] > 1) {
                        if ($input['indicator_from'] == 2) {
                            //dd(2);
                            return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => $input['grn_num'], 'indicate' => 2, 'receipt_type' => 'GRN']);
                        } elseif ($input['indicator_from'] == 1) {
                            //dd(3);
                            return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => $input['grn_num'], 'indicate' => 1, 'receipt_type' => 'GRN']);
                        } else {

                            if (@$input['frm_listing'] != "PO") {
                                // Not specify GRN and back to GRN List
                                return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => null, 'indicate' => 1, 'receipt_type' => 'GRN']);
                            } else {
                                return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => $input['grn_num'], 'indicate' => 1, 'receipt_type' => 'NewGRN']);
                            }
                        }
                    } else {

                        if ($input['indicator_from'] == 1) {
                            // Back to public list ( with grn_num)
                            //dd(4);
                            return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => null, 'indicate' => 1, 'receipt_type' => 'GRN']);
                        } else {
                            if ($input['indicator_from'] == 2) {
                                // Got Specify PO num
                                // dd(5);
                                $strCancelURL = redirect()->route('poReceive');
                            } else {
                                // Not specify GRN and back to GRN List
                                if (@$input['frm_listing'] != "PO") {
                                    return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => null, 'indicate' => 1, 'receipt_type' => 'GRN']);
                                } else {
                                    return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => $input['grn_num'], 'indicate' => 1, 'receipt_type' => 'NewGRN']);
                                }
                            }
                        }
                    }
                } else {
                    // return back to listing
                    if (request('indicate') == 1) {
                        // dd(1111);
                        return redirect()->route('backshowGRnLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'item_num' => null, 'vend_do' => null, 'grn_num' => null, 'indicate' => 2, 'receipt_type' => 'GRN']);
                    }

                    if (request('indicate') == 2) {

                        $strCancelURL = redirect()->route('poReceive');
                    }
                }

                break;
            case "TranOrderShipping":
                break;
            case "Pick":
                // Data Variables
                $picklist_id = $input['picklist_id'];
                $cust_num    = $input['cust_num'];
                $item_num_en = $input['item_num_en'];
                $item_desc   = $input['item_desc'];
                $uom         = $input['uom'];
                $zone_num    = $input['zone_num'];
                $loc_num     = $input['loc_num'];
                $lot_num     = $input['lot_num'];
                $qty_to_pick = $input['qty_to_pick'];
                $emp_num     = $input['emp_num'];
                $whse_num    = $input['whse_num'];

                // Calculate List of records for picklist
                $picklists = new PicklistSummaryDetails();
                $picklists = $picklists->whereDoesntHave('picklist_items', function ($query) {
                    $query->where('item_num', '=', 'NON-INV');
                })->where('emp_num', $emp_num)
                    ->where('whse_num', $whse_num)
                    ->where('status', 'R')
                    ->orderBy('due_date', 'asc')
                    ->get();
                $countplist = $picklists->count();
                // Count the List of selection
                if ($countplist > 1) {
                    $strCancelURL = route('picksTest.pickItem', ['picklist' => $picklist_id]);
                } else {
                    // If the selection remaining records and selection list only 1 record
                    if (($qty_to_pick == 0 || $qty_to_pick == null) && $countplist == 1) {
                        $strCancelURL = route('picksTest.picklistDetails', ['picklist' => $picklist_id]);
                    } else {
                        // If no record redirect to pick selection`s page
                        $strCancelURL = route('picksTest.confirmPick', ['picklist' => $picklist_id, 'cust_num' => $cust_num, 'item_num' => $item_num_en, 'item_desc' => $item_desc, 'uom' => $uom, 'zone_num' => $zone_num, 'loc_num' => $loc_num, 'lot_num' => $lot_num, 'qty_to_pick' => $qty_to_pick]);
                    }
                }
                break;
            case "CoPick":
                //$po_rel = new PurchaseOrderItem();

                // $po_rel = $po_rel->where('po_num', $input['ref_num'])->where('whse_num', $input['whse_num'])->where('qty_returnable', '>', '0')->get();
                $co_num_base64 = session()->get('co_num_base64');
                if ($input['indicate'] == 1) {
                    if ($co_num_base64 == 1) {

                        $co_num = base64_decode($input['all']['co_num']);
                    } else {

                        $co_num = $input['all']['co_num'];
                    }
                } else {
                    $co_num = $input['all']['co_num'];
                }


                $pickby = session()->get('pick_by_as');
                $select_pick_by = session()->get('select_pick_by');
                $whse_num = session()->get('whse_num');
                $stage_num = session()->get('stage_num');

                $shipping_zone_code = session()->get('shipping_zone_code');
                $strsales_person = session()->get('strsales_person');
                $item_num = session()->get('item_num');
                $cust_num = session()->get('cust_num');


                $co_list = CoPickView::where('co_num', $co_num)
                    ->where('whse_num', $whse_num)
                    ->select('co_num', 'co_line', 'co_rel', 'uom', 'item_num', 'item_desc', 'qty_released', 'qty_shipped', 'qty_shortage', 'qty_returned', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                    ->orderBy('co_num')
                    ->groupBy('co_line')
                    ->groupBy('co_rel');


                if ($item_num && $item_num != "NON-INV") {
                    $co_list = $co_list->where('item_num', $item_num);
                }
                if ($input['all']['co_line']) {
                    //  $co_list = $co_list->where('co_line', 'like', '%' . $input['all']['co_line'] . '%');
                }


                $co_list = $co_list->get();

                foreach ($co_list as $co) {
                    $qty_shortage = $co->qty_shortage;
                }


                $input['count_list'] = $co_list->count();

                if ($input['count_list'] == 1) {
                    if ($input['all']['co_line']) {
                        $co_list = $co_list->where('co_line', 'like', '%' . $input['all']['co_line'] . '%');
                    }
                }

                //  dd($co_list);


                /*if (session()->get('qty_required') > 0) {
                    //dd($input);
                    $strCancelURL = redirect()->route('CoPickingProcess', [
                        'totalrecord' => $input['count_list'],
                        'stage_num' => base64_encode($stage_num),
                        'co_num' =>  base64_encode($co_num),
                        'delivery_date' => @$input['all']['delivery_date'],
                        'delivery_trip' => @$input['all']['delivery_trip'],
                        'co_line' => $input['all']['co_line'],
                        'co_rel' => @$input['co_rel'] ?? 0,
                        'item' =>  $input['all']['item']  ?? $input['all']['item_num'],
                        'uom' => base64_encode($input['all']['uom']),
                        'item_desc' => @$input['all']['item_desc'] ?? null,
                        'whse_num' => base64_encode($whse_num),
                        'qty_req' => $qty_shortage
                    ]);
                } else {*/






                    if ($input['count_list'] == 0) {
                        $strCancelURL = redirect()->route('CoPick');
                    } else {
                        if ($input['count_list'] > 1) {
                            // $strCancelURL = redirect()->route('backshowCoLineReturn', ['whse_num' => $input['whse_num'], 'po_num' => $input['ref_num'],  'vend_do' => $input['vend_do']]);
                            $strCancelURL = redirect()->route('CoPickingDetails', [
                                'co_num_base64' => $co_num_base64,
                                'pick_by' => $pickby,
                                'stage_num' => $stage_num,
                                'select_pick_by' => $select_pick_by,
                                'whse_num' => $whse_num,
                                'co_num' => $co_num,
                                'cust_num' => @$cust_num ?? null,
                                'item' => $item_num,
                                'shipping_zone_code' => @$shipping_zone_code ?? null,
                                'strsales_person' => @$strsales_person ?? null
                            ]);
                        }
                        //     // Redirect to the Process screen.
                        else if ($item_num && ($input['count_list'] == 1)) {


                            if ($input['indicate'] == 1) {
                                $strCancelURL = redirect()->route('CoPick');
                            } else {
                                $strCancelURL = redirect()->route('CoPickingProcess', [
                                    'totalrecord' => $input['count_list'],
                                    'stage_num' => base64_encode($stage_num),
                                    'co_num' =>  base64_encode($co_num),
                                    'delivery_date' => @$input['all']['delivery_date'],
                                    'delivery_trip' => @$input['all']['delivery_trip'],
                                    'co_line' => $input['all']['co_line'],
                                    'co_rel' => @$input['co_rel'] ?? 0,
                                    'item' =>  $input['all']['item']  ?? $input['all']['item_num'],
                                    'uom' => base64_encode($input['all']['uom']),
                                    'item_desc' => @$input['all']['item_desc'],
                                    'whse_num' => base64_encode($whse_num),
                                    'qty_req' => $qty_shortage
                                ]);
                            }
                        } else {
                            // dd($input['all'],$input);
                            if ($input['indicate'] == 1) {
                                $strCancelURL = redirect()->route('CoPick');
                            } else {






                                if ($input['count_list'] == 1) {
                                    $strCancelURL = redirect()->route('CoPickingProcess', [
                                        'totalrecord' => $input['count_list'],
                                        'stage_num' => base64_encode($stage_num),
                                        'co_num' =>  base64_encode($co_num),
                                        'delivery_date' => @$input['all']['delivery_date'],
                                        'delivery_trip' => @$input['all']['delivery_trip'],
                                        'co_line' => $input['all']['co_line'],
                                        'co_rel' => @$input['co_rel'] ?? 0,
                                        'item' =>  $input['all']['item']  ?? $input['all']['item_num'],
                                        'uom' => base64_encode($input['all']['uom']),
                                        'item_desc' => $input['all']['item_desc'],
                                        'whse_num' => base64_encode($whse_num),
                                        'qty_req' => $qty_shortage
                                    ]);
                                } else {

                                    if ($qty_shortage > 0) {
                                        $strCancelURL = redirect()->route('CoPickingProcess', [
                                            'totalrecord' => $input['count_list'],
                                            'stage_num' => base64_encode($stage_num),
                                            'co_num' =>  base64_encode($co_num),
                                            'delivery_date' => @$input['all']['delivery_date'],
                                            'delivery_trip' => @$input['all']['delivery_trip'],
                                            'co_line' => $input['all']['co_line'],
                                            'co_rel' => @$input['co_rel'] ?? 0,
                                            'item' =>  $input['all']['item']  ?? $input['all']['item_num'],
                                            'uom' => base64_encode($input['all']['uom']),
                                            'item_desc' => $input['all']['item_desc'],
                                            'whse_num' => base64_encode($whse_num),
                                            'qty_req' => $qty_shortage
                                        ]);


                                    }
                                    else{







                                        $strCancelURL = redirect()->route('CoPickingDetails', [
                                            'co_num_base64' => $co_num_base64,
                                            'pick_by' => $pickby,
                                            'stage_num' => $stage_num,
                                            'select_pick_by' => $select_pick_by,
                                            'whse_num' => $whse_num,
                                            'co_num' => $co_num,
                                            'cust_num' => @$cust_num ?? null,
                                            'item' => $item_num,
                                            'shipping_zone_code' => @$shipping_zone_code ?? null,
                                            'strsales_person' => @$strsales_person ?? null
                                        ]);
                                    }
                                }
                            }
                        }
                    }
               // }
                //dd($strCancelURL->getTargetUrl(),$input);
                break;
            case "POReturn":
                $po_rel = new PurchaseOrderItem();

                $po_rel = $po_rel->where('po_num', $input['ref_num'])->where('whse_num', $input['whse_num'])->where('qty_returnable', '>', '0')->get();

                if ($po_rel->count() == 0) {
                    $strCancelURL = redirect()->route('poReturn');
                } else {

                    if ($po_rel->count() > 1) {

                        $strCancelURL = redirect()->route('backshowPoLineReturn', ['whse_num' => $input['whse_num'], 'po_num' => $input['ref_num'], 'po_line' => $input['po_line'],  'vend_do' => $input['vend_do']]);
                    }
                    // Redirect to the Process screen.
                    else if ($input['item_num'] && ($po_rel->count() == 1)) {
                        if ($input['indicate'] == 1) {
                            $strCancelURL = redirect()->route('poReturn');
                        } else {
                            $strCancelURL = redirect()->route('showPoReturnProcess', ['po_num' => $input['ref_num'], 'po_line' => $input['po_line'],  'po_rel' => $input['po_rel'], 'item' => $input['item_num'], 'item_desc' => $input['item_desc'],  'qty_returnable' => $input['qty_returnable'],  'uom' => $input['uom']]);
                        }
                    } else {

                        $strCancelURL = redirect()->route('poReturn');
                    }
                }

                break;
            case "PoReceipt":
                $po_rel = new PurchaseOrderItem();

                $po_rel = $po_rel->where('po_num', $input['po_num'])->where('whse_num', $input['whse_num'])->get();
                //dd($po_rel,$input);
                if ($po_rel->count() == 0) {

                    $strCancelURL = redirect()->route('poReceive');
                } else {

                    if ($po_rel->count() > 1) {
                        $checkpo_rel = $po_rel->where('po_num', $input['po_num'])->where('po_line', $input['po_line'])->where('whse_num', $input['whse_num'])->first();
                        //dd($checkpo_rel->qty_received,$checkpo_rel->qty_ordered);
                        if ($checkpo_rel->qty_received == $checkpo_rel->qty_ordered) {
                            $count = $po_rel->count() - 1;
                            if ($count > 1) {

                                $strCancelURL = redirect()->route('backshowPoReceiptLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num']]);
                            }
                        }
                        // Redirect to the Process screen.
                        else if ($input['item_num'] && ($po_rel->count() == 1)) {
                            if ($input['indicate'] == 1) {
                                $strCancelURL = redirect()->route('poReceive');
                            } else {
                                //$strCancelURL = redirect()->route('showPoProcess', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'po_line' => $input['po_line'],  'po_rel' => $input['po_rel'], 'item' => $input['item_num'], 'item_desc' => $input['item_desc'],  'qty_required' => $input['qty_required'],  'uom' => $input['uom']]);

                                $checkpo_rel_status = $po_rel->where('po_num', $input['po_num'])->where('po_line', $input['po_line'])->where('whse_num', $input['whse_num'])->first();
                                if ($checkpo_rel_status->rel_status == "C") {
                                    $strCancelURL = redirect()->route('poReceive');
                                } else {
                                    $strCancelURL = redirect()->route('showPoProcess', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'po_line' => $input['po_line'],  'po_rel' => $input['po_rel'], 'item' => $input['item_num'], 'item_desc' => $input['item_desc'],  'qty_required' => $input['qty_required'],  'uom' => $input['uom']]);
                                }
                            }
                        } else {
                            $strCancelURL = redirect()->route('backshowPoReceiptLine', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num']]);
                            //dd('sssa');
                            // $strCancelURL = redirect()->route('poReceive');
                            //$strCancelURL = redirect()->route('showPoProcess', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'po_line' => $input['po_line'],  'po_rel' => $input['po_rel'], 'item' => $input['item_num'], 'item_desc' => $input['item_desc'],  'qty_required' => $input['qty_required'],  'uom' => $input['uom']]);
                        }
                        // break;
                    }
                    // Redirect to the Process screen.
                    else if ($input['item_num'] && ($po_rel->count() == 1)) {
                        if ($input['indicate'] == 1) {

                            $strCancelURL = redirect()->route('poReceive');
                        } else {
                            $checkpo_rel_status = $po_rel->where('po_num', $input['po_num'])->where('po_line', $input['po_line'])->where('whse_num', $input['whse_num'])->first();
                            if ($checkpo_rel_status->rel_status == "C") {
                                $strCancelURL = redirect()->route('poReceive');
                            } else {
                                $strCancelURL = redirect()->route('showPoProcess', ['whse_num' => $input['whse_num'], 'po_num' => $input['po_num'], 'po_line' => $input['po_line'],  'po_rel' => $input['po_rel'], 'item' => $input['item_num'], 'item_desc' => $input['item_desc'],  'qty_required' => $input['qty_required'],  'uom' => $input['uom']]);
                            }
                            //999dd('sss',$input['whse_num']);

                        }
                    } else {


                        $strCancelURL = redirect()->route('poReceive');
                    }
                    break;
                }
        }
        return @$strCancelURL;
    }
}


// Check log file
// Define the path to the log file

// Generate Batch Id
if (!function_exists('checkLogFile')) {
    function checkLogFile()
    {
        $date = date('Y-m-d');
        //Define the path to the log file
        $logFile = storage_path('logs/laravel-{$date}.log');
        if (file_exists($logFile)) {
            // Check the Onwer
            $fileOwnerId = fileowner($logFile);
            // Optional: Retrieve the owner's user info (only on Unix-like systems)
            $ownerInfo = function_exists('posix_getpwuid') ? posix_getpwuid($fileOwnerId) : null;
            // Get the owner's username or fallback to the UID
            $owner = $ownerInfo['name'] ?? $fileOwnerId;
            if ($owner === 'root') {
                //chown($file->getRealPath(), 'www-data');
                $user  = 'www-data';  // Replace with the desired user
                $group = 'www-data'; // Replace with the desired group

                // Construct the chown command
                $command = "sudo chown -R {$user}:{$group} " . escapeshellarg($logFile);
                $commandBoot = "sudo chgrp -R {$user} storage bootstrap/cache";

                // Execute the command
                $output = shell_exec($command);
                $outputBoot = shell_exec($commandBoot);
            }
        }
    }
}












// Generate Batch Id
if (!function_exists('generateBatchId')) {
    function generateBatchId($transType)
    {

        $sep = "|";
        $batch_id = auth()->user()->site_id . "$sep" . auth()->user()->name . "$sep" . date("YmdHis") . gettimeofday()['usec'];
        $session_key = $transType . "_batch_id";
        session()->put($session_key, $batch_id);
        // Session::put($session_key, $batch_id);

        return $batch_id;
    }
}
if (!function_exists('getBatchIdSession')) {
    function getBatchIdSession($transType)
    {

        $session_key = $transType . "_batch_id";
        $batch_id = session()->get($session_key);


        return $batch_id;
    }
}
if (!function_exists('checkBatchIdExists')) {
    function checkBatchIdExists($batch_id)
    {

        $batch = MatlTrans::where('batch_id', $batch_id)->first();
        return $batch ? true : false;
    }
}
if (!function_exists('checkMatlRequirementLastOper')) {
    function checkMatlRequirementLastOper($request, $qty_to_complete, $type)
    {
        // dd($request->all());
        // $qty_to_complete = $request->qty_complete;
        $oper_num = $request->oper_num;
        $job = Job::where('job_num', $request->job_num)->first();
        $qty_completed = $job->qty_completed;

        $tparm = new TparmView;
        $require_full_material_issue_for_job_receipt = $tparm->getTparmValue($type, 'require_full_material_issue_for_job_receipt');
        //    dd($require_full_material_issue_for_job_receipt);
        if ($require_full_material_issue_for_job_receipt) {
            $job_matls = JobMatl::where('job_num', $request->job_num)->get();
            // dd($job_matls, $qty_completed, $qty_to_complete, $request->job_num);
            foreach ($job_matls as $key => $job_matl) {
                # code...
                // dd($qty_completed, $qty_to_complete, $job_matl->qty_per, $job_matl->scrap_factor);
                $qty_issued = ($qty_completed + $qty_to_complete) * $job_matl->qty_per * (1 / (1 - $job_matl->scrap_factor));
                $qty_issued = numberFormatPrecision($qty_issued, 8);
                // echo $qty_issued . "_" . $job_matl->qty_issued . "_" . $job_matl->matl_item . "<br>";

                // $qty_issued = ($qty_completed + $qty_to_complete) * $job_matl->qty_per * (1 / 1 - $job_matl->scrap_factor);
                // $qty_issued=
                // dd($qty_issued, $qty_completed, $qty_to_complete, $job_matl->qty_per, $job_matl->scrap_factor, $job_matl->qty_required);
                if ($qty_issued > $job_matl->qty_issued) {
                    throw ValidationException::withMessages([__('error.mobile.insufficient_qty_issued')]);

                    return false;
                }
            }
        }
        // dd("a");
    }
}
if (!function_exists('checkMatlRequirementOper')) {
    function checkMatlRequirementOper($request, $qty_to_complete, $type)
    {
// dd($qty_to_complete);
        // $qty_to_complete = $request->qty_complete;
        $oper_num = $request->oper_num;
        $job = JobRoute::where('job_num', $request->job_num)->where('suffix',$request->suffix)->where('oper_num',$request->oper_num)->first();
        // dd($job);
        $qty_completed = $job->qty_completed;

        $tparm = new TparmView;
        $require_full_material_issue_for_job_receipt = $tparm->getTparmValue($type, 'require_full_material_issue_for_operation');
        if ($require_full_material_issue_for_job_receipt) {
            $job_matls = JobMatl::where('job_num', $request->job_num)->where('oper_num', $oper_num)->get();
            // dd($job_matls, $oper_num, $request->job_num);
            foreach ($job_matls as $key => $job_matl) {
                # code...
                // $qty_completed= $job_matl->qty_completed;
                $qty_issued = ($qty_completed + $qty_to_complete) * $job_matl->qty_per * (1 / (1 - $job_matl->scrap_factor));
                $qty_issued = numberFormatPrecision($qty_issued, 8);
                // echo $qty_issued . "_" . $job_matl->qty_issued . "_" . $job_matl->matl_item . "<br>";

                // $qty_issued = ($qty_completed + $qty_to_complete) * $job_matl->qty_per * (1 / 1 - $job_matl->scrap_factor);
                // $qty_issued=
                // dd($qty_issued, $job_matl->qty_required);
                if ($qty_issued > $job_matl->qty_issued) {
                    throw ValidationException::withMessages([__('error.mobile.insufficient_qty_issued')]);

                    return false;
                }
            }
        }
        // dd("b");
    }
}



if (!function_exists('allocateLotsToCoQty')) {
    function allocateLotsToCoQty($lots, $co, $arrItem)
    {

        // Sample Data for all array
        // $arrItem = array('BDORIOMTPA1504'=>array('item'=>'BDORIOMTPA1504','item_desc'=>'ORION 4D KING BED','QtyAvaible'=>40));
        // $arrLotPO['BDORIOMTPA1504'] = array('20240201'=>5,'20240202'=>3,'20240203'=>1,'20240204'=>1,'20240205'=>1,);
        // $arrCo['BDORIOMTPA1504']  = array('1'=>1,'2'=>3,'3'=>1);

        // Initialize quantity balance for each lot
        $lotBalances = $lots;
        $intTotalCO = [];
        // Iterate through each customer and allocate lots
        foreach ($co as $item => $value) {
            foreach ($value as $key => $demand) {
                // echo "Allocating for Customer $customer:\n";

                // Reset remaining demand for the customer
                $remainingDemand = $demand;
                @$intTotalCO[$item] += @$demand;
                // Iterate through each lot to fulfill customer demand
                foreach ($lotBalances[$item] as $lot => $quantity) {
                    if ($remainingDemand > 0) {
                        // Allocate from current lot as much as possible
                        $allocatedQuantity = min($quantity, $remainingDemand);

                        // Update remaining demand and lot balance
                        $remainingDemand -= @$allocatedQuantity;
                        @$lotBalances[$item][$lot] -= @$allocatedQuantity;

                        //echo "Allocated $allocatedQuantity units from Lot $lot\n";
                    } else {
                        break; // Stop allocation if customer demand is fulfilled
                    }
                }
                // Check if demand is still remaining after allocating all lots
                if ($remainingDemand > 0) {
                    // echo "Customer $customer demand not fulfilled completely.\n";
                }
            }
        }

        $arrSort = array();
        $i = 0;
        foreach ($lotBalances as $item => $key) {
            foreach ($key as $name => $value) {
                $arrSort[$item]['item'] = $item;
                $arrSort[$item]['item_desc'] = $arrItem[$item]['item_desc'];
                $arrSort[$item]['actual_live_stock_qty'] = $arrItem[$item]['QtyAvaible'] - ($intTotalCO[$item] ?? 0);
                $live_stock_qty = 0;
                if ($arrSort[$item]['actual_live_stock_qty'] >= 0) {
                    $live_stock_qty =  $arrSort[$item]['actual_live_stock_qty'];
                }
                $arrSort[$item]['actual_live_stock_qty'] = $arrSort[$item]['actual_live_stock_qty'];
                $arrSort[$item]['live_stock_qty'] = $live_stock_qty;

                if ($value > 0) {
                    // query from lot master table , and get the PO unit cost from po_items Lot master
                    $lot = new Lot();
                    $lotlocresute = $lot->select('reference_no', 'reference_line')->where('lot_source', 'PO Receipt')->where('lot_num', $name)->where('item_num', $item)->where('site_id', auth()->user()->site_id)->first();
                    $poitem = new po_item();
                    $arrSort[$item]['PoLot'][$name] = [];
                    $arrSort[$item]['PoLot'][$name]['POQty'] = $value;
                    $arrSort[$item]['PoLot'][$name]['PO'] = $lotlocresute->reference_no ?? null;

                    if ($lotlocresute != null) {
                        //$getPOUnitCost = $poitem->select('custom_attributes')->where('po_num', $lotlocresute->reference_no)->where('po_line', $lotlocresute->reference_line)->where('site_id', auth()->user()->site_id)->first();
                        //$arrSort[$item]['PoLot'][$name]['POPrice'] = json_decode($getPOUnitCost->custom_attributes)->unit_cost ?? 0;
                        @$arrSort[$item]['PoLot'][$name]['POPrice'] = $lotlocresute->custom_attributes['unit_cost'] ?? null;
                    } else {
                        $arrSort[$item]['PoLot'][$name]['POPrice'] = null;
                    }
                }
                $i++;
            }
        }
        //dd($arrSort);
        return $arrSort;
    }
}




if (!function_exists('checkDateValidation')) {
    function checkDateValidation($date)
    {
        $getSiteSetting = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        $format = $getSiteSetting->input_date_format;
        if (date($format, strtotime($date)) != date($date)) {
            return 1;
        }
    }
}





if (!function_exists('getQtyRequiredJob')) {
    function getQtyRequiredJob($qty_released, $qty_per, $scrap_factor, $number_format)
    {
        $value = $qty_released * $qty_per * (1 / (1 - $scrap_factor));
        return $value;
        //return number_format($qty_released * $qty_per * (1 / (1 - $scrap_factor)), $number_format);
    }
}



if (!function_exists('getFiles')) {
    function getFiles($dir, $format)
    {

        $files = array();
        $directory = opendir($dir);
        while ($item = readdir($directory)) {
            // We filter the elements that we don't want to appear ".", ".." and ".svn"
            if (($item != ".") && ($item != "..") && ($item != "." . $format)) {

                $files[filemtime($dir . $item)] = $item;
            }
        }

        ksort($files);
        return $files;
    }
}

// SAP API
// Pallet API trans mode
if (!function_exists('getSAPAPITrans')) {
    function getSAPAPITrans($type)
    {
        $arrConfigType = array(

            "Inventory Count"    => array('1' => 'Inventory Count', 'controller' => 'CountSheetLineController', 'function' => 'update', 'process_name' => 'Inventory Count'),

            "Stock Move" => array('1' => 'Stock Move From', '2' => 'Stock Move To', 'controller' => 'MoveController', 'function' => 'moveProcess', 'process_name' => 'Stock Move'),
            "Multi Stock Move" => array('1' => 'Stock Move From', '2' => 'Stock Move To', 'controller' => 'MoveController', 'function' => 'multiMoveProcess', 'process_name' => 'Multi Stock Move'),

            "Put Away" => array('1' => 'Put Away From', '2' => 'Put Away To', 'controller' => 'PutAwayController', 'function' => 'process', 'process_name' => 'Putaway'),

            "CO Pick" => array('1' => 'CO Pick From', '2' => 'CO Pick To', 'controller' => 'CoPickController', 'function' => 'CoPick without Lot', 'functionLot' => 'CoPick with Lot', 'process_name' => 'Co Pick'),
            "CO UnPick" => array('1' => 'CO Unpick From', '2' => 'CO Unpick To', 'controller' => 'CoUnPickController', 'function' => 'CoUnPick without Lot', 'functionLot' => 'CoUnPick with Lot', 'process_name' => 'Co UnPick'),

            "Co Pick" => array('1' => 'CO Pick From', '2' => 'CO Pick To', 'controller' => 'CoPickController', 'function' => 'CoPick without Lot', 'functionLot' => 'CoPick with Lot', 'process_name' => 'Co Pick'),
            "Co UnPick" => array('1' => 'CO Unpick From', '2' => 'CO Unpick To', 'controller' => 'CoUnPickController', 'function' => 'CoUnPick without Lot', 'functionLot' => 'CoUnPick with Lot', 'process_name' => 'Co UnPick'),



            "TO Ship" => array('1' => 'TO Ship From', '2' => 'TO Ship To', 'controller' => 'TransferOrderShippingController', 'function' => 'shipTransferOrder', 'trans_type' => 'TO Ship', 'process_name' => 'TO Ship'),
            "TO Ship Bulk" => array('1' => 'TO Ship From', '2' => 'TO Ship To', 'controller' => 'TransferOrderShippingController', 'function' => 'shipTransferOrder', 'trans_type' => 'TO Ship', 'process_name' => 'Bulk TO Ship'),


            "TO Receipt" => array('1' => 'TO Receipt From', '2' => 'TO Receipt To', 'controller' => 'TransferOrderReceiptController', 'function' => 'receiveTransferOrder', 'process_name' => 'TO Receipt'),
            "TO Receipt Bulk" => array('1' => 'TO Receipt From', '2' => 'TO Receipt To', 'controller' => 'TransferOrderReceiptController', 'function' => 'receiveTransferOrder', 'trans_type' => 'TO Receipt', 'process_name' => 'Bulk TO Receipt'),

            "Job Receipt" => array('1' => 'Job Receipt', 'controller' => 'JobReceiptController', 'function' => 'JobReceiptProcess', 'process_name' => 'Job Receipt'),
            "Machine Run" => array('1' => 'Machine Run', 'controller' => 'MachineRunController', 'function' => 'endMachineRun', 'process_name' => 'Machine Run'),

            "Job Run" => array('1' => 'Job Run', 'controller' => 'JobRunController', 'function' => 'endJobRun', 'process_name' => 'Job Run'),
            "Job Material Issue" => array('1' => 'Job Material Issue', 'controller' => 'JobMaterialController', 'function' => 'jobMatlProcess', 'process_name' => 'Job Material Issue'),
            "Batch Job Material Issue" => array('1' => 'Job Material Issue', 'controller' => 'JobMaterialController', 'function' => 'BatchJobMatlProcess', 'process_name' => 'Batch Job Material Issue'),

            "Job Material Return" => array('1' => 'Job Material Return', 'controller' => 'JobMaterialController', 'function' => 'unissueJobMatlProcess', 'process_name' => 'Job Material Return'),
            "Job Return" => array('1' => 'Job Return', 'controller' => 'JobReturnController', 'function' => 'process', 'process_name' => 'Job Return'),
            "WIP Move" => array('1' => 'WIP Move', 'controller' => 'WIPMoveController', 'function' => 'runWIPMove', 'process_name' => 'WIP Move'),
            "CO Pick List Pick" => array('1' => 'CO Pick List Pick From', '2' => 'CO Pick List Pick To', 'controller' => 'PickListTestController', 'function' => 'updateManualAllocate', 'process_name' => 'CO Pick List Pick'),
            "CO Pick List Unpick" => array('1' => 'CO Pick List Unpick From', '2' => 'CO Pick List Unpick To', 'controller' => 'PickListTestController', 'function' => 'updateUnpick', 'process_name' => 'CO Pick List Unpick'),
            //"TO Receipt" => array('1' => 'TO Receipt From', '2' => 'TO Receipt To', 'controller' => 'TransferOrderReceiptController', 'function' => 'processTransferReceive', 'process_name' => 'TO Receipt'),
            "TO Lost"    => array('1' => 'TO Lost', 'controller' => 'TransferOrderReceiptController', 'function' => 'processTransferReceive', 'process_name' => 'TO Receipt : Add Qty Loss'),

            "Misc Receipt" => array('1' => 'Misc Receipt', 'controller' => 'MiscController', 'function' => 'receiptprocess', 'process_name' => 'Misc Receipt'),
            "Misc Issue"    => array('1' => 'Misc Issue', 'controller' => 'MiscController', 'function' => 'issueprocess', 'process_name' => 'Misc Issue'),

        );
        return $arrConfigType[$type];
    }
}

// Pallet API trans mode
if (!function_exists('getPalletTrans')) {
    function getPalletTrans($type)
    {
        $arrConfigType = array(
            "CO Pick" => array('1' => 'CO Pick From', '2' => 'CO Pick To', 'controller' => 'CoPickController', 'function' => 'PickCobyPalletProcess'),
            "CO Unpick" => array('1' => 'CO Unpick From', '2' => 'CO Unpick To', 'controller' => 'CoUnPickController', 'function' => 'UnpickCobyPalletProcess'),
            "Pallet Move" => array('1' => 'Pallet Move From', '2' => 'Pallet Move To', 'controller' => 'PalletMobileController', 'function' => 'processPalletLetdown'),
            "Pallet Letdown" => array('1' => 'Pallet Letdown From', '2' => 'Pallet Letdown To', 'controller' => 'PalletMobileController', 'function' => 'processLetDown'),
            "Pallet Builder" => array('1' => 'Pallet Builder From', '2' => 'Pallet Builder To', 'controller' => 'PalletMobileController', 'function' => 'processPalletBuilder'),
            "TO Ship" => array('1' => 'TO Ship From', '2' => 'TO Ship To', 'controller' => 'TransferOrderShippingController', 'function' => 'shipTOByPallet'),

            "TO Receipt" => array('1' => 'TO Receipt From', '2' => 'TO Receipt To', 'controller' => 'TransferOrderShippingController', 'function' => 'receiveTOByPallet'),

        );
        return $arrConfigType[$type];
    }
}

// LPN CO / TO Match UnMatch Config
if (!function_exists('getPalletMatchUnMatchConfig')) {
    function getPalletMatchUnMatchConfig($requestData)
    {
        // CO Pick
        if ($requestData->type == "CO Pick") {
            $salesperson = $requestData->salesperson ?? null;
            $shipping_zone_code = $requestData->shipping_zone_code ?? null;
            $select_pick_by = $requestData->select_pick_by ?? null;
            $cust_num  = base64_decode($requestData->cust_num) ?? null;
            $co_num  = base64_decode($requestData->co_num) ?? null;

            if ($salesperson) {
                $salesperson = base64_decode($requestData->salesperson) ?? null;
            }
            if ($shipping_zone_code) {
                $shipping_zone_code = base64_decode($requestData->shipping_zone_code) ?? null;
            }
            if ($select_pick_by) {
                $select_pick_by = base64_decode($requestData->select_pick_by) ?? null;
            }
            //dd($salesperson, $shipping_zone_code, $select_pick_by);
            //shipping_zone_code , salesperson
            $arrSortArr = explode(",", $co_num);
            if (@$select_pick_by == "Customer") {
                $getCheckConum = CustomerOrder::select('co_num')->where('cust_num', $cust_num)->where('site_id', auth()->user()->site_id)->whereIn('co_num', $arrSortArr);
                if ($shipping_zone_code) {
                    $getCheckConum->where('shipping_zone_code', $shipping_zone_code);
                }
                if ($salesperson) {
                    $getCheckConum->where('strsales_person', $salesperson);
                }
                $getCheckConum  = $getCheckConum->get();
                // dd($getCheckConum);
                $arrConum = array();
                foreach ($getCheckConum as $key => $value) {
                    $arrConum[] = $value->co_num;
                }


                //dd($getCheckConum,$arrConum,$cust_num);
                //select('co_num', 'co_line', 'cust_num', 'co_rel', 'item_num', 'item_desc', 'uom', 'qty_released', 'qty_shipped', 'qty_returned', 'qty_shortage', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                $dataCOPick =  CoPickView::whereIn('co_num', $arrConum)
                    ->where('whse_num', $requestData->whse_num)
                    ->where('rel_status', '!=', 'C')
                    ->where('qty_shortage', '>', '0')
                    ->orderBy('co_line')
                    //->groupBy('co_line')
                    ->get();

                // dd($dataCOPick, $cust_num,$arrConum);
            } else {
                // Check the CustomerOrder Header
                $getCheckCo = CustomerOrder::where('co_num', $requestData->ref_num)->where('site_id', auth()->user()->site_id);
                if ($shipping_zone_code) {
                    $getCheckCo->where('shipping_zone_code', $shipping_zone_code);
                }
                if ($salesperson) {
                    $getCheckCo->where('strsalse_person', $salesperson);
                }
                $getCheckCo  = $getCheckCo->exists();

                if ($getCheckCo) {
                    $dataCOPick =  CoPickView::select('co_num', 'co_line', 'cust_num', 'co_rel', 'item_num', 'item_desc', 'uom', 'qty_released', 'qty_shipped', 'qty_returned', 'qty_shortage', DB::raw("IFNULL(SUM(qty_staged),0) as qty_staged"))
                        ->where('co_num', $requestData->ref_num)
                        ->where('whse_num', $requestData->whse_num)
                        ->where('rel_status', '!=', 'C')
                        ->where('qty_shortage', '>', '0')
                        ->orderBy('co_line')
                        ->groupBy('co_line')
                        ->get();
                } else {
                    $dataCOPick = [];
                }
            }
        }
        // CO Unpick
        $dataCOUnpick = DB::table('stage_locs')
            ->selectRaw('stage_locs.whse_num, stage_locs.co_num, stage_locs.co_line, stage_locs.co_rel, stage_locs.item_num, stage_locs.qty_copicking, stage_locs.uom, stage_locs.lot_num, stage_locs.stage_num, stage_locs.site_id,
        coitems.item_desc, coitems.qty_picked,coitems.qty_manual_picked, coitems.qty_shortage, sum(stage_locs.qty_staged) as qty_staged')
            ->leftJoin('coitems', function ($join) {
                $join->on('stage_locs.co_num', '=', 'coitems.co_num');
                $join->on('stage_locs.co_line', '=', 'coitems.co_line');
                $join->on('stage_locs.co_rel', '=', 'coitems.co_rel');
                $join->on('stage_locs.site_id', '=', 'coitems.site_id');
            })
            ->where('stage_locs.co_num', $requestData->ref_num)
            ->where('stage_locs.whse_num', $requestData->whse_num)
            ->where('stage_locs.stage_num', $requestData->stage_num)
            ->where('stage_locs.site_id', auth()->user()->site_id)
            ->orderBy('stage_locs.co_line')
            ->groupBy('stage_locs.co_line')
            ->get();

        // TO Shipping
        $dataTOShipping = TransferLine::where('trn_num', $requestData->ref_num)
            ->where('from_whse', $requestData->whse_num)
            ->where('line_stat', '!=', 'C')
            ->whereRaw('(IFNULL(qty_required,0) - IFNULL(qty_shipped,0)) > 0')
            ->orderBy('trn_line')
            ->get();

        // TO Receipt
        $dataTOReceipt = TransferOrderLinesSubline::selectRaw('*, sum(qty_receivable) as qty_receivable, sum(qty_shipped) as qty_shipped')->where('trn_num', $requestData->ref_num)
            ->whereRaw('IFNULL(qty_receivable,0) > 0')
            ->whereRaw('lpn_num !=""')
            // ->whereRaw('(IFNULL(qty_required,0) - IFNULL(qty_shipped,0)) > 0')
            ->orderBy('trn_line')
            ->groupBy('trn_line')
            ->get();

        $arrPalletConfigFormType = array(
            'LpnFormType'      => array('CO Pick' => 'CO Pick', 'CO Unpick' => 'CO Unpick', 'TO Receipt' => 'TO Receipt', 'TO Shipping' => 'TO Shipping'),
            'LpnFormTypeQty'   => array(
                'CO Pick' => 'qty_shortage',
                'CO Unpick' => 'qty_staged',
                'TO Shipping' => array('qty_required', 'qty_shipped'),
                'TO Receipt' => 'qty_received'
            ),
            'LpnFormTypeSql'   => array(
                'CO Pick' => $dataCOPick,
                'CO Unpick' => $dataCOUnpick,
                'TO Shipping' => $dataTOShipping,
                'TO Receipt' => $dataTOReceipt
            ),
            'LpnFormLines'    => array(
                'CO Pick' => 'co_line',
                'CO Unpick' => 'co_line',
                'TO Shipping' => 'trn_line',
                'TO Receipt' => 'trn_line'
            ),
            'LpnFormRefNum'    => array(
                'CO Pick' => 'co_num',
                'CO Unpick' => 'co_num',
                'TO Shipping' => 'trn_num',
                'TO Receipt' => 'trn_num'
            ),
        );

        return $arrPalletConfigFormType;
    }
}
// Convert Trans Type based on constant key value
if (!function_exists('getTransType')) {
    function getTransType($transtype)
    {
        $arrTransType = [
            'Misc Receipt'         => config('icapt.transtype.misc_receipt'),
            'Miscellaneous Receipt' => config('icapt.transtype.misc_receipt'),
        ];
        return $arrTransType[$transtype];
    }
}


// Store Table has site_id
if (!function_exists('getSiteIDTable')) {
    function getSiteIDTable()
    {

        return array(
            0 => 'allocation_locations',
            1 => 'allocations',
            2 => 'alternate_barcodes',
            3 => 'batchs',
            4 => 'bom_matls',
            5 => 'bom_routes',
            6 => 'boms',
            7 => 'cancel_subscriptions',
            8 => 'coitems',
            //9 => 'coitemviewallocation',
            10 => 'container_items',
            11 => 'containers',
            //12 =>'copickview',
            13 => 'count_groups',
            14 => 'count_sheet_lines',
            15 => 'count_sheets',
            16 => 'counters',
            17 => 'custom_fields',
            18 => 'customer_addresses',
            19 => 'customer_orders',
            20 => 'customers',
            21 => 'doc_notes',
            22 => 'ecsoft_logs',
            23 => 'employees',
            24 => 'env_settings',
            25 => 'field_labels',
            26 => 'grn_items',
            27 => 'grns',
            28 => 'import_export_logs',
            29 => 'integration_logs',
            30 => 'issued_lots',
            31 => 'item_locs',
            32 => 'item_warehouses',
            33 => 'items',
            34 => 'job_finished_parents',
            35 => 'job_matls',
            36 => 'job_routes',
            37 => 'job_trans',
            //38 =>'jobitemview',
            //39 =>'jobmatlitemviewallocation',
            40 => 'jobs',
            41 => 'label_modules',
            42 => 'labels',
            //43 =>'labormachineview',
            44 => 'license_details',
            45 => 'license_maint',
            46 => 'license_plate_number_definitions',
            47 => 'locs',
            48 => 'lot_locs',
            49 => 'lot_number_definitions',
            50 => 'lots',
            51 => 'machine_trans',
            52 => 'machines',
            53 => 'matl_trans',
            54 => 'matl_trans_store_ids',
            55 => 'middleware_connections',
            56 => 'order_number_definitions',
            57 => 'overridehistories',
            58 => 'pack_items',
            59 => 'package_types',
            60 => 'packages',
            61 => 'pickers',
            62 => 'picklist_allocates',
            63 => 'picklist_items',
            64 => 'picklist_line_picks',
            65 => 'picklist_lines',
            66 => 'picklists',
            67 => 'po_items',
            //68 =>'poitemview',
            69 => 'product_codes',
            70 => 'purchase_orders',
            71 => 'qrcodes',
            72 => 'quickbook_settings',
            73 => 'reason_codes',
            74 => 'sap_batchs',
            75 => 'sap_co_batch_sync',
            76 => 'sap_coitems',
            77 => 'sap_intg_coitems',
            78 => 'sap_invcountbatch_keylinenumbers',
            79 => 'sap_logs',
            80 => 'sap_po_batch_sync',
            81 => 'sap_po_items',
            82 => 'sap_to_batch_sync',
            83 => 'sap_transfer_orders',
            84 => 'shipments',
            85 => 'shipping_zones',
            86 => 'site_connections',
            87 => 'site_settings',
            88 => 'stage_locs',
            89 => 'staging_lines',
            90 => 'start_labour',
            91 => 'start_machine',
            92 => 'tasks',
            93 => 'temp_count_batch_lines',
            //94 =>'toitemview',
            95 => 'tparm_sites',
            //96 =>'tparmview',
            97 => 'transfer_lines',
            98 => 'transfer_orders',
            99 => 'transferoder_lines_sublines',
            100 => 'uom_convs',
            101 => 'uoms',
            102 => 'user_group_lists',
            103 => 'users',
            104 => 'vendors',
            105 => 'warehouses',
            106 => 'wcs',
            107 => 'zoho_log',
            108 => 'zones'
        );
    }
}




if (!function_exists('getICPSetting')) {

    function getICPSetting($module, $setName)
    {
        // ...
        // auth()->user()->getUserSession();
        //  $setting = new TparmView();
        return TparmView::getTransParm($module, $setName);
    }
}

if (!function_exists('customCrypt')) {

    function customCrypt($vWord)
    {
        $customKey = "11234567891011121314151617181910";
        $newEncrypter = new \Illuminate\Encryption\Encrypter($customKey, Config::get('app.cipher'));
        return $newEncrypter->encrypt($vWord);
    }
}

if (!function_exists('customDecrypt')) {

    function customDecrypt($vWord)
    {
        try {
            $customKey = "11234567891011121314151617181910";
            $newEncrypter = new \Illuminate\Encryption\Encrypter($customKey, Config::get('app.cipher'));
            return $newEncrypter->decrypt($vWord);
        } catch (\Throwable $th) {
            return $vWord;
        }
    }
}

if (!function_exists('is_base64_string')) {
    function is_base64_string($s)
    {
        //Regex!
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s) || (strlen($s) % 4 != 0))
            return false;

        // first check if we're dealing with an actual valid base64 encoded string
        if (($b = base64_decode($s, TRUE)) === FALSE) {
            return FALSE;
        }

        // now check whether the decoded data could be actual text
        $e = mb_detect_encoding($b);
        if (in_array($e, array('UTF-8', 'ASCII'))) { // YMMV
            return TRUE;
        } else {
            return FALSE;
        }
    }
}

if (!function_exists('exp2dec')) {

    function exp2dec($number)
    {
        // return floatval($number);
        $matches = array();
        // 0.0000003
        if (preg_match('/(.*)E-(.*)/', str_replace(".", "", $number), $matches)) {
            // dd("A");
            $num = "0.";
            // dd($matches);
            while ($matches[2] > 1) {
                $num .= "0";
                $matches[2]--;
            }
            return $num . $matches[1];
        }
        return $number;
    }
}
if (!function_exists('numberFormatPrecision')) {
    // function to format number according to precision
    function numberFormatPrecision($number, $precision = 2, $decimalPoint = '.', $thousandSeparator = ',')
    {
        // handling value stored not as float(18,8)
        // dd($number);
        // $number = sprintf("%d", $number);
        // return $number;

        $number = str_replace(',', '', $number);

        $number = ($number == null || $number == 'NaN' || $number == 0 || $number == '') ? '0' : $number;
        $number = exp2dec($number);
        // dd($number);
        // declare thousand separator (some code still pass '')
        $thousandSeparator = ',';

        // check if number is negative
        $negative = $number < 0 ? true : false;
        // dd($negative);
        // separate the number by decimal point
        $numberParts = explode($decimalPoint, $number);
        // get the length of the decimal places, default to 2
        $decimalLen = count($numberParts) > 1 ? strlen($numberParts[1]) : 2;
        // $numberParts[0] = abs((int)$numberParts[0]);

        $numberParts[0] = bcadd($numberParts[0], 0);
        // try {
        //     $numberParts[0] = bcadd($numberParts[0], 0);
        // } catch (\Throwable $th) {
        //     //throw $th;
        //     dd($number);
        // }

        $numberParts[0] = trim($numberParts[0], "-");
        // dd($numberParts, $number);
        // to handle forms other than value change log listing, extend the decimal place digits
        if ($precision > 0) {
            $numberParts[1] = isset($numberParts[1]) ? str_pad($numberParts[1], 10, '0') : str_pad('0', 10, '0');
        }

        // set precision
        $precision = $precision !== false ? (int)$precision : $decimalLen;

        // initialize the return value with the integer part of the number and add thousand separator
        $response = strrev(implode($thousandSeparator, str_split(strrev($numberParts[0]), 3)));
        $response = $negative ? '-' . $response : $response;

        // check if the number is decimal and the precision is set
        if (count($numberParts) > 1 && $precision > 0) {
            // append the decimalPoint
            $response .= $decimalPoint;
            // append the decimal part, truncated to the specified precision
            $response .= substr($numberParts[1], 0, $precision);
        }

        // return response
        return $response;
    }
}

if (!function_exists('validateSiteId')) {

    function validateSiteId($site_id)
    {
        $arrSkipSite = array('NSTY' => '1');
        if (array_key_exists($site_id, $arrSkipSite)) {
            return 1;
        } else {
            return 0;
        }
    }
}


if (!function_exists('validateSansentiveValue')) {

    function validateSansentiveValue($request)
    {
        // define the variables
        $req_empnum = '';
        $req_jobnum = '';
        $req_reasoncode = '';
        $req_locnum = '';
        $req_lotnum  = '';
        $req_unitweightuom = '';
        $req_itemnum = '';
        $req_transuom = '';
        $req_fromwhse = '';
        $req_ponum = '';
        $req_fromloc = '';
        $req_tasktype = '';
        $req_trnnum = '';
        $req_refnum = '';
        $req_conum = '';
        $req_custnum = '';
        $req_stagenum = '';
        $req_vendnum = '';
        $req_venddo  = '';
        //dd($request, "checking....");
        if (@$request->item_num) {
            $itemnum = Item::select('item_num')->where('item_num', $request->item_num)->first();

            if ($itemnum) {
                $req_itemnum = $itemnum->item_num;
            } else {
                $req_itemnum = $request->item_num;
            }
        }

        if (@$request->loc_num) {
            $locnum = Loc::select('loc_num')->where('loc_num', $request->loc_num)->first();

            if ($locnum) {
                $req_locnum = $locnum->loc_num;
            } else {
                $req_locnum = $request->loc_num;
            }
        }

        if (@$request->whse_num) {
            $whsenum = Warehouse::select('whse_num')->where('whse_num', $request->whse_num)->first();

            if ($whsenum) {
                $req_whsenum = $whsenum->whse_num;
            } else {
                $req_whsenum = $request->whse_num;
            }
        }

        if (@$request->lot_num) {
            $lotnum = Lot::select('lot_num')->where('lot_num', $request->lot_num)->first();
            if ($lotnum) {
                $req_lotnum = $lotnum->lot_num;
            } else {
                $req_lotnum = $request->lot_num;
            }
        }

        if (@$request->reason_code) {
            if (is_array($request->reason_code)) {
                $rc_array = [];

                foreach ($request->reason_code as $rc) {
                    $reasoncode = ReasonCode::select('reason_num')->where('reason_num', $rc)->first();
                    if ($reasoncode) {
                        $rc_array[] = $reasoncode->reason_num;
                    } else {
                        $rc_array[] = $rc;
                    }
                }
                // dd($rc_array);
                $req_reasoncode = $rc_array;
                // dd($req_reasoncode);
            } else {
                $reasoncode = ReasonCode::select('reason_num')->where('reason_num', $request->reason_code)->first();

                if ($reasoncode) {
                    $req_reasoncode = $reasoncode->reason_num;
                } else {
                    $req_reasoncode = $request->reason_code;
                }
            }
        }

        // UOM
        if (@$request->uom) {
            $quom = UOM::select('uom')->where('uom', $request->uom)->first();

            if ($quom) {
                $req_uom = $quom->uom;
            } else {
                $req_uom = $request->uom;
            }
        }

        // Product Code
        if (@$request->product_code) {
            $productcode = ProductCode::select('product_code')->where('product_code', $request->product_code)->first();

            if ($productcode) {
                $req_productcode = $productcode->product_code;
            } else {
                $req_productcode = $request->product_code;
            }
        }

        // From UOM
        if (@$request->uom_from) {
            $uomfrom = UOM::select('uom')->where('uom', $request->uom_from)->first();

            if ($uomfrom) {
                $req_uomfrom = $uomfrom->uom;
            } else {
                $req_uomfrom = $request->uom_from;
            }
        }

        // To UOM
        if (@$request->uom_to) {
            $uomto = UOM::select('uom')->where('uom', $request->uom_to)->first();

            if ($uomto) {
                $req_uomto = $uomto->uom;
            } else {
                $req_uomto = $request->uom_to;
            }
        }

        // Vendor
        if (@$request->vend_num) {
            $vendnum = Vendor::select('vend_num')->where('vend_num', $request->vend_num)->first();

            if ($vendnum) {
                $req_vendnum = $vendnum->vend_num;
            } else {
                $req_vendnum = $request->vend_num;
            }
        }

        // Customer
        if (@$request->cust_num) {
            $custnum = Customer::select('cust_num')->where('cust_num', $request->cust_num)->first();

            if ($custnum) {
                $req_custnum = $custnum->cust_num;
            } else {
                $req_custnum = $request->cust_num;
            }
        }

        // From Whse
        if (@$request->from_whse) {
            $fromwhse = Warehouse::select('whse_num')->where('whse_num', $request->from_whse)->first();

            if ($fromwhse) {
                $req_fromwhse = $fromwhse->whse_num;
            } else {
                $req_fromwhse = $request->from_whse;
            }
        }

        // To Whse
        if (@$request->to_whse) {
            $towhse = Warehouse::select('whse_num')->where('whse_num', $request->to_whse)->first();

            if ($towhse) {
                $req_towhse = $towhse->whse_num;
            } else {
                $req_towhse = $request->to_whse;
            }
        }

        // Trans Loc
        if (@$request->trn_loc) {
            $trnloc = Loc::select('loc_num')->where('loc_num', $request->trn_loc)->first();

            if ($trnloc) {
                $req_trnloc = $trnloc->loc_num;
            } else {
                $req_trnloc = $request->trn_loc;
            }
        }

        // Picker Id
        if (@$request->picker_id) {
            $pickerid = Employee::select('emp_num')->where('emp_num', $request->picker_id)->first();

            if ($pickerid) {
                $req_pickerid = $pickerid->emp_num;
            } else {
                $req_pickerid = $request->picker_id;
            }
        }

        // Packing Loc
        if (@$request->packing_loc) {
            $packingloc = Loc::select('loc_num')->where('loc_num', $request->packing_loc)->first();

            if ($packingloc) {
                $req_packingloc = $packingloc->loc_num;
            } else {
                $req_packingloc = $request->packing_loc;
            }
        }

        // Emp Number
        if (@$request->emp_num) {
            $empnum = Employee::select('emp_num')->where('emp_num', $request->emp_num)->first();

            if ($empnum) {
                $req_empnum = $empnum->emp_num;
            } else {
                $req_empnum = $request->emp_num;
            }
        }

        // Counter1_num
        if (@$request->counter1_num) {
            $counter1num = Employee::select('emp_num')->where('emp_num', $request->counter1_num)->first();

            if ($counter1num) {
                $req_counter1num = $counter1num->emp_num;
            } else {
                $req_counter1num = $request->counter1_num;
            }
        }

        // Counter2_num
        if (@$request->counter2_num) {
            $counter2num = Employee::select('emp_num')->where('emp_num', $request->counter2_num)->first();

            if ($counter2num) {
                $req_counter2num = $counter2num->emp_num;
            } else {
                $req_counter2num = $request->counter2_num;
            }
        }

        // Job Num
        if (@$request->job_num) {
            $jobnum = Job::select('job_num')->where('job_num', $request->job_num)->first();

            if ($jobnum) {
                $req_jobnum = $jobnum->job_num;
            } else {
                $req_jobnum = $request->job_num;
            }
        }

        // Work Center
        if (@$request->wc_num) {
            $wcnum = WorkCenter::select('wc_num')->where('wc_num', $request->wc_num)->first();

            if ($wcnum) {
                $req_wcnum = $wcnum->wc_num;
            } else {
                $req_wcnum = $request->wc_num;
            }
        }

        // Matl Item
        if (@$request->matl_item) {
            $matlitem = Item::select('item_num')->where('item_num', $request->matl_item)->first();

            if ($matlitem) {
                $req_matlitem = $matlitem->item_num;
            } else {
                $req_matlitem = $request->matl_item;
            }
        }

        // End Code
        if (@$request->endcode) {
            $endcode = ReasonCode::select('reason_num')->where('reason_num', $request->endcode)->first();

            if ($endcode) {
                $req_endcode = $endcode->reason_num;
            } else {
                $req_endcode = $request->endcode;
            }
        }

        // Shipping  Zone
        if (@$request->shipping_zone_code) {
            $shippingzonecode = ShippingZone::select('shipping_zone_code')->where('shipping_zone_code', $request->shipping_zone_code)->first();

            if ($shippingzonecode) {
                $req_shippingzonecode = $shippingzonecode->shipping_zone_code;
            } else {
                $req_shippingzonecode = $request->shipping_zone_code;
            }
        }

        // To Loc
        if (@$request->toLoc) {
            $to_Loc = Loc::select('loc_num')->where('loc_num', $request->toLoc)->first();

            if ($to_Loc) {
                $req_toLoc = $to_Loc->loc_num;
            } else {
                $req_toLoc = $request->toLoc;
            }
        }

        // Vendor
        if (@$request->vend_do) {
            $venddo = Vendor::select('vend_num')->where('vend_num', $request->vend_do)->first();

            if ($venddo) {
                $req_venddo = $venddo->vend_num;
            } else {
                $req_venddo = $request->vend_do;
            }
        }

        // PO Num
        if (@$request->po_num) {
            $ponum = PurchaseOrder::select('po_num')->where('po_num', $request->po_num)->first();

            if ($ponum) {
                $req_ponum = $ponum->po_num;
            } else {
                $req_ponum = $request->po_num;
            }
        }

        // From Loc
        if (@$request->from_loc) {
            $fromloc = Loc::select('loc_num')->where('loc_num', $request->from_loc)->first();

            if ($fromloc) {
                $req_fromloc = $fromloc->loc_num;
            } else {
                $req_fromloc = $request->from_loc;
            }
        }

        // CO Num
        if (@$request->co_num) {
            $conum = CustomerOrder::select('co_num')->where('co_num', $request->co_num)->first();

            if ($conum) {
                $req_conum = $conum->co_num;
            } else {
                $req_conum = $request->co_num;
            }
        }

        // TO Num
        if (@$request->trn_num) {
            $trnnum = TransferOrder::select('trn_num')->where('trn_num', $request->trn_num)->first();

            if ($trnnum) {
                $req_trnnum = $trnnum->trn_num;
            } else {
                $req_trnnum = $request->trn_num;
            }
        }

        // Stage Num
        if (@$request->stage_num) {
            $stagenum = Loc::select('loc_num')->where('loc_num', $request->stage_num)->first();

            if ($stagenum) {
                $req_stagenum = $stagenum->loc_num;
            } else {
                $req_stagenum = $request->stage_num;
            }
        }

        // C Item
        if (@$request->citem) {
            $citem = Item::select('item_num')->where('item_num', $request->citem)->first();

            if ($citem) {
                $req_citem = $citem->item_num;
            } else {
                $req_citem = $request->citem;
            }
        }

        // Ref Num
        if (@$request->ref_num) {
            $refnum = Job::select('job_num')->where('job_num', $request->ref_num)->first();

            if ($refnum) {
                $req_refnum = $refnum->job_num;
            } else {
                $req_refnum = $request->ref_num;
            }
        }

        // Res Id
        if (@$request->res_id) {
            $resid = Machine::select('res_id')->where('res_id', $request->res_id)->first();

            if ($resid) {
                $req_resid = $resid->res_id;
            } else {
                $req_resid = $request->res_id;
            }
        }

        // Task Type
        if (@$request->task_type) {
            $tasktype = Task::select('task_name')->where('task_name', $request->task_type)->first();

            if ($tasktype) {
                $req_tasktype = $tasktype->task_name;
            } else {
                $req_tasktype = $request->task_type;
            }
        }

        // Unit Weight UOM
        if (@$request->unit_weight_uom) {
            $unitweightuom = UOM::select('uom')->where('uom', $request->unit_weight_uom)->first();

            if ($unitweightuom) {
                $req_unitweightuom = $unitweightuom->uom;
            } else {
                $req_unitweightuom = $request->unit_weight_uom;
            }
        }

        // Emp Id
        if (@$request->emp_id) {
            $empid = Employee::select('emp_num')->where('emp_num', $request->emp_id)->first();

            if ($empid) {
                $req_empid = $empid->emp_num;
            } else {
                $req_empid = $request->emp_id;
            }
        }

        // Trans UOM
        if (@$request->trans_uom) {
            $transuom = UOM::select('uom')->where('uom', $request->trans_uom)->first();

            if ($transuom) {
                $req_transuom = $transuom->uom;
            } else {
                $req_transuom = $request->trans_uom;
            }
        }



        if (@$req_whsenum && @$req_empnum && @$req_jobnum && @$req_reasoncode && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'emp_num' => $req_empnum,
                'job_num' => $req_jobnum,
                'reason_code' => [$req_reasoncode],
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_unitweightuom && @$req_uom) {
            return $request->merge([
                'unit_weight_uom' => $req_unitweightuom,
                'product_code' => $req_productcode,
                'uom' => $req_uom,
            ]);
        } else if (@$req_unitweightuom) {
            return $request->merge([
                'unit_weight_uom' => $req_unitweightuom,
                'product_code' => $req_productcode,
            ]);
        } else if (@$req_locnum && @$req_lotnum && @$req_reasoncode && @$req_jobnum) {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
                'reason_code' => $req_reasoncode
            ]);
        } else if (@$req_lotnum && @$req_reasoncode && @$req_jobnum) {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'lot_num' => $req_lotnum,
                'reason_code' => $req_reasoncode
            ]);
        } else if (@$req_locnum && @$req_reasoncode && @$req_jobnum) {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'loc_num' => $req_locnum,
                'reason_code' => $req_reasoncode
            ]);
        } else if (@$req_lotnum && @$req_reasoncode) {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
                'reason_code' => $req_reasoncode
            ]);
        } else if (@$req_whsenum && @$req_empnum && @$req_jobnum && @$req_reasoncode) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'emp_num' => $req_empnum,
                'job_num' => $req_jobnum,
                'reason_code' => [$req_reasoncode],
            ]);
        } else if (@$req_whsenum && @$req_conum && @$req_itemnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
                'item_num' => $req_itemnum,
                'from_loc' => @$req_fromloc, //Issue 437 undefined variable $req_fromloc
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_conum && @$req_itemnum && @$req_locnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
                'item_num' => $req_itemnum,
                'from_loc' => @$req_fromloc, //Issue 438 undefined variable $req_fromloc
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_transuom) {
            return $request->merge([
                'trans_uom' => $req_transuom,
            ]);
        } else if (@$req_fromwhse && @$req_trnnum && @$req_itemnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'from_whse' => $req_fromwhse,
                'trn_num' => $req_trnnum,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_fromwhse && @$req_trnnum && @$req_itemnum && @$req_locnum) {
            return $request->merge([
                'from_whse' => $req_fromwhse,
                'trn_num' => $req_trnnum,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_whsenum && @$req_jobnum && @$req_itemnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_ponum && @$req_itemnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'po_num' => $req_ponum,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_fromloc && @$req_itemnum && @$req_lotnum && @$req_locnum && @$req_uom) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'from_loc' => $req_fromloc,
                'item_num' => $req_itemnum,
                'lot_num' => $req_lotnum,
                'loc_num' => $req_locnum,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_fromloc && @$req_itemnum && @$req_lotnum && @$req_locnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'from_loc' => $req_fromloc,
                'item_num' => $req_itemnum,
                'lot_num' => $req_lotnum,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_whsenum && @$req_fromloc && @$req_itemnum && @$req_locnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'from_loc' => $req_fromloc,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_whsenum && @$req_itemnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_empnum && @$req_resid && @$req_reasoncode) {
            return $request->merge([
                'emp_num' => $req_empnum,
                'reason_code' => $req_reasoncode,
                'res_id' => $req_resid,
            ]);
        } else if (@$req_locnum && @$req_lotnum && @$req_reasoncode) {
            return $request->merge([
                'reason_code' => $req_reasoncode,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_tasktype) {
            return $request->merge([
                'task_type' => $req_tasktype,
            ]);
        } else if (@$req_locnum && @$req_lotnum && @$req_custnum) {
            return $request->merge([
                'cust_num' => $req_custnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_itemnum && @$req_citem && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'citem' => $req_citem,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_itemnum && @$req_citem && @$req_locnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'citem' => $req_citem,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_whsenum && @$req_jobnum && @$req_matlitem && @$req_uom) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'matl_item' => $req_matlitem,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_jobnum && @$req_matlitem) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'matl_item' => $req_matlitem,
            ]);
        } else if (@$req_locnum && @$req_reasoncode) {
            return $request->merge([
                'reason_code' => $req_reasoncode,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_whsenum && @$req_trnnum && @$req_itemnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'trn_num' => $req_trnnum,
                'item_num' => $req_itemnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_refnum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'ref_num' => $req_refnum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_refnum && @$req_locnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'ref_num' => $req_refnum,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_whsenum && @$req_trnnum && @$req_itemnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'trn_num' => $req_trnnum,
                'item_num' => $req_itemnum,
            ]);
        } else if (@$req_whsenum && @$req_trnnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'trn_num' => $req_trnnum,
            ]);
        } else if (@$req_whsenum && @$req_conum && @$req_stagenum && @$req_itemnum && @$req_locnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
                'loc_num' => $req_locnum,
                'item_num' => $req_itemnum,
                'stage_num' => $req_stagenum,
            ]);
        } else if (@$req_whsenum && @$req_conum && @$req_stagenum && @$req_itemnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
                'stage_num' => $req_stagenum,
                'item_num' => $req_itemnum,
            ]);
        } else if (@$req_whsenum && @$req_conum && @$req_stagenum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
                'stage_num' => $req_stagenum,
            ]);
        } else if (@$req_whsenum && @$req_custnum && @$req_stagenum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'cust_num' => $req_custnum,
                'stage_num' => $req_stagenum,
            ]);
        } else if (@$req_whsenum && @$req_conum && @$req_itemnum && @$req_uom) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
                'item_num' => $req_itemnum,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_conum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'co_num' => $req_conum,
            ]);
        } else if (@$req_vendnum && @$req_itemnum && @$req_whsenum && @$req_venddo && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'vend_num' => $req_vendnum,
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'vend_do' => $req_venddo,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_vendnum && @$req_itemnum && @$req_whsenum && @$req_locnum && @$req_lotnum) {
            return $request->merge([
                'vend_num' => $req_vendnum,
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'loc_num' => $req_locnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_whsenum && @$req_ponum && @$req_itemnum && @$req_venddo) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'po_num' => $req_ponum,
                'item_num' => $req_itemnum,
                'vend_do' => $req_venddo,
            ]);
        } else if (@$req_whsenum && @$req_ponum && @$req_itemnum && @$req_uom) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'po_num' => $req_ponum,
                'item_num' => $req_itemnum,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_ponum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'po_num' => $req_ponum,
            ]);
        } else if (@$req_itemnum && @$req_whsenum && @$req_locnum && @$req_toLoc && @$req_uom) {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'loc_num' => $req_locnum,
                'toLoc' => $req_toLoc,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_locnum && @$req_empnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'loc_num' => $req_locnum,
                'emp_num' => $req_empnum,
            ]);
        } else if (@$req_counter1num && @$req_counter2num) {
            return $request->merge([
                'counter1_num' => $req_counter1num,
                'counter2_num' => $req_counter2num,
            ]);
        } else if (@$req_fromwhse && @$req_towhse && @$req_trnloc && @$req_itemnum) {
            return $request->merge([
                'from_whse' => $req_fromwhse,
                'to_whse' => $req_towhse,
                'trn_loc' => $req_trnloc,
                'item_num' => $req_itemnum,
            ]);
        } else if (@$req_fromwhse && @$req_towhse && $req_trnloc) {
            return $request->merge([
                'from_whse' => $req_fromwhse,
                'to_whse' => $req_towhse,
                'trn_loc' => $req_trnloc,
            ]);
        } else if (@$req_pickerid && @$req_packingloc) {
            return $request->merge([
                'picker_id' => $req_pickerid,
                'packing_loc' => $req_packingloc,
            ]);
        } else if (@$req_whsenum && @$req_empnum && @$req_jobnum && @$req_resid) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'emp_num' => $req_empnum,
                'job_num' => $req_jobnum,
                'res_id' => $req_resid,
            ]);
        } else if (@$req_empnum && @$req_resid && @$req_endcode) {
            return $request->merge([
                'emp_num' => $req_empnum,
                'res_id' => $req_resid,
                'endcode' => $req_endcode,
            ]);
        } else if (@$req_empnum && @$req_resid) {
            return $request->merge([
                'emp_num' => $req_empnum,
                'res_id' => $req_resid,
            ]);
        } else if (@$req_resid) {
            return $request->merge([
                'res_id' => $req_resid,
            ]);
        } else if (@$req_whsenum && @$req_empnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'emp_num' => $req_empnum,
            ]);
        } else if (@$req_whsenum && @$req_jobnum && @$req_wcnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'job_num' => $req_jobnum,
                'wc_num' => $req_wcnum,
            ]);
        } else if (@$req_shippingzonecode && @$req_whsenum && @$req_itemnum && @$req_uom) {
            return $request->merge([
                'shipping_zone_code' => $req_shippingzonecode,
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_itemnum && @$req_custnum && @$req_uom) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'cust_num' => $req_custnum,
                'uom' => $req_uom,
            ]);
        } else if (@$req_whsenum && @$req_itemnum && @$req_custnum) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'cust_num' => $req_custnum,
            ]);
        } else if (@$req_whsenum && @$req_itemnum && @$req_uom) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'item_num' => $req_itemnum,
                'uom' => $req_uom,
            ]);
        } else if (@$req_custnum && @$req_shippingzonecode) {
            return $request->merge([
                'cust_num' => $req_custnum,
                'shipping_zone_code' => $req_shippingzonecode,
            ]);
        } else if (@$req_whsenum && @$req_trnloc) {
            return $request->merge([
                'whse_num' => $req_whsenum,
                'trn_loc' => $req_trnloc
            ]);
        } else if (@$whsenum) {
            return $request->merge([
                'whse_num' => $req_whsenum
            ]);
        } else if (@$req_reasoncode && @$req_lotnum == "" && @$req_itemnum != '') {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'loc_num' => $req_locnum,
                'reason_code' => $req_reasoncode
            ]);
        } else if (@$req_itemnum && @$req_lotnum) {
            return $request->merge([
                'item_num' => $req_itemnum,
                'lot_num' => $req_lotnum,
            ]);
        } else if (@$req_itemnum && @$req_lotnum && @$req_reasoncode == "") {
            return $request->merge([
                'item_num' => $req_itemnum,
                'whse_num' => $req_whsenum,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_uom && @$req_productcode) {
            return $request->merge([
                'uom' => $req_uom,
                'product_code' => $req_productcode
            ]);
        } else if (@$req_uomfrom && @$req_uomto && @$req_vendnum) {
            return $request->merge([
                'uom_from' => $req_uomfrom,
                'uom_to' => $req_uomto,
                'vend_num' => $req_vendnum,
                'item_num' => $req_itemnum,
            ]);
        } else if (@$req_uomfrom && @$req_uomto && @$req_custnum) {
            return $request->merge([
                'uom_from' => $req_uomfrom,
                'uom_to' => $req_uomto,
                'cust_num' => $req_custnum,
                'item_num' => $req_itemnum,
            ]);
        } else if (@$req_itemnum) {
            return $request->merge([
                'item_num' => $req_itemnum,
            ]);
        } else if (@$req_endcode) {
            return $request->merge([
                'endcode' => $req_endcode,
            ]);
        } else if (@$req_empnum && @$req_locnum) {
            return $request->merge([
                'emp_num' => $req_empnum,
                'loc_num' => $req_locnum,
            ]);
        } else if (@$req_shippingzonecode) {
            return $request->merge([
                'shipping_zone_code' => $req_shippingzonecode,
            ]);
        } else if (@$req_vendnum) {
            return $request->merge([
                'vend_num' => $req_vendnum,
            ]);
        } else if (@$req_empnum) {
            return $request->merge([
                'emp_num' => $req_empnum,
            ]);
        } else if (@$req_empid) {
            return $request->merge([
                'emp_id' => $req_empid,
            ]);
        } else {
            return $request->merge([
                'item_num' => @$req_itemnum,
                'whse_num' => @$req_whsenum,
                'loc_num' => @$req_locnum,
            ]);
        }

        // Transaction Parameter
        if ($request->parm) {
            foreach ($request->parm as $parm) {
                dd($parm);
            }
        }
    }
}

/* *************************************************
-- Author         : Afiq
-- Create date    : 06/03/2023
-- Description    : Get timezone based on site setting.
-- Method         : GET
-- Return         : Array
                        "timezone" => array:3 [▼
                            "date" => "EA"
                            "time" => 30
                        ]
-- Source File(s) : PalletController.php
-- URL            : /maintenance-invty/master-files-invty/pallet/add
-- --------------------------------------------------------

=============================================================================================================================
    'Rev      Project Code     Modified By - Date           Description

    '-----    ------------     --------------------         ----------------------
     'R1       Gitlab #609      Afiq - 06/03/2023            Get timezone based on site setting.

==============================================================================================================================*/
function parseNumericValue($value)
{
    return preg_replace("/([^0-9\.])/", '', $value);
}
function getTimezone()
{
    $timezone = auth()->user()->timezone;
    $now = Carbon::now()->toDateTimeString();
    $date = Timezone::convertFromUTC($now, $timezone, 'Y-m-d');
    $time = Timezone::convertFromUTC($now, $timezone, 'H:i:s');
    $returnDateTime = $date . ' ' . $time;
    return $returnDateTime;
}
if (!function_exists('getDateTimeConverted')) {
    function getDateTimeConverted($date = null, $return = true, $onlyDate = false)
    {

        if (!$date && !$return)
            return "";
        if (!$date)
            $date = now();
        if ($onlyDate)
            return  Timezone::convertFromUTC($date, auth()->user()->getSiteTimezone(), \App\SiteSetting::getOutputDateFormat());

        return  Timezone::convertFromUTC($date, auth()->user()->getSiteTimezone(), \App\SiteSetting::getOutputDateFormat() . ' H:i:s');
    }
}
if (!function_exists('getAllocationQtyAvailable')) {
    function getAllocationQtyAvailable($result)
    {
        $qty_available = 0;
        if (isset($result->qty_available)) {
            $qty_available = $result->qty_available;
        } else {
            $tparm = new TparmView;
            $allow_expired_item_Picklist = $tparm->getTparmValue('System', 'allow_expired_item_Picklist');

            $itemloc = new ItemLoc();
            $lotloc = new LotLoc();
            $item = new Item();
            $checkLotTracked = $item->where('item_num', $result->item_num)->where('lot_tracked', 1)->first();
            // Lot tracked item
            if ($checkLotTracked) {
                $dataitemloclotloc = $lotloc->with('lot')->with('location')
                    ->select('uom', DB::raw('sum(qty_available) as sum_qty_available'))
                    ->whereHas('location', function ($q) {
                        $q->where('loc_status', 1)
                            ->where('loc_type', 'S');
                    })
                    ->whereHas('lot', function ($q) use ($allow_expired_item_Picklist) {
                        // Expiry date must be more than current date
                        if ($allow_expired_item_Picklist == 0) {
                            $q->where(function ($x) {
                                $x->where('expiry_date', '>=', now()->format('Y-m-d 00:00:00'))
                                    ->orWhere('expiry_date', null);
                            });
                        }
                    })
                    ->where('freeze', 'N')
                    ->where('whse_num', $result->whse_num)
                    ->where('item_num', $result->item_num)
                    ->groupBy('whse_num', 'item_num')
                    ->first();
            }
            // Non-lot tracked item
            else {
                $dataitemloclotloc = $itemloc->with('location')
                    ->select('uom', DB::raw('sum(qty_available) as sum_qty_available'))
                    ->whereHas('location', function ($q) {
                        $q->where('loc_status', 1)
                            ->where('loc_type', 'S');
                    })
                    ->where('freeze', 'N')
                    ->where('whse_num', $result->whse_num)
                    ->where('item_num', $result->item_num)
                    ->groupBy('whse_num', 'item_num')
                    ->first();
            }
            $qty_available = $dataitemloclotloc ? $dataitemloclotloc->sum_qty_available : 0;
        }
        return $qty_available;
    }
}
if (!function_exists('getDateTimeConvertedToUTC')) {
    function getDateTimeConvertedToUTC($date = null, $site_id = "", $onlyDate = false)
    {
        if ($site_id == null) {
            $site_id = auth()->user()->site_id;
        }
        //dd($site_id);
        $siteSettings =  App\SiteSetting::where('site_id', $site_id)->first();
        // dd($siteSettings);
        if ($siteSettings) {
            $timezone = $siteSettings->timezone;
            $dateformat = $siteSettings->date_format;
            // dd($date,$timezone);
            try {
                if ($onlyDate)
                    return Timezone::convertToUTC($date, $timezone,  'Y-m-d');
                else
                    return Timezone::convertToUTC($date, $timezone,  'Y-m-d H:i:s');
            } catch (\Throwable $th) {
                return "";
            }
        }
        return "";

        // return  Timezone::convertFromUTC($date, auth()->user()->getSiteTimezone(), \App\SiteSetting::getOutputDateFormat() . ' H:i:s');
    }
}
if (!function_exists('call_generateQrCodeHtml')) {
    function call_generateQrCodeHtml($type = null, $width = 60, $height = 60, $param = [])
    {
        $tparm = new TparmView;
        $enable_multi_scan = $type == 'job' ? $tparm->getTparmValue('System', 'enable_multi_scan_production') : ($type == 'inventory' ? $tparm->getTparmValue('System', 'enable_multi_scan_inventory') : false);

        return QrcodeController::generateQrCodeHtml($type, $width, $height, $param, $enable_multi_scan);
    }
}

if (!function_exists('truncate')) {
    function truncate($value, $precision)
    {
        if ($value) {
            $multiplier = pow(10, $precision);
            return floor($value * $multiplier) / $multiplier;
        }
        return null;
    }
}





if (!function_exists('getRoutePageTitle')) {
    function getRoutePageTitle($_route)
    {

        if (isset($_REQUEST['dev'])) {
            dd($_route);
        }

        $titleArray = [
            'usergrouplist.edit' => __('admin.title.edit_usergroup'),
            'usergrouplist.view' => __('admin.title.view_usergroup'),
            'edituser' => __('admin.title.edit_user'),
            'showuser' => __('admin.title.view_user'),
            'editwarehouse' => __('admin.title.edit_whse'),
            'warehouse.show' => __('admin.title.view_whse'),
            'zones.show' => __('admin.title.view_zone'),
            'zones.edit' => __('admin.title.edit_zone'),
            'viewLocation' => __('admin.title.view_loc'),
            'editLocation' => __('admin.title.edit_loc'),
            'editProductCode' => __('admin.title.edit_pc'),
            'ViewProductCode' => __('admin.title.view_pc'),
            'count_group.show' => __('admin.title.view_count_group'),
            'count_group.edit' => __('admin.title.edit_count_group'),
            'showUOM' => __('admin.title.view_uom'),
            'editUOM' => __('admin.title.edit_uom'),
            'viewitem' => __('admin.title.view_item'),
            'item.edit' => __('admin.title.edit_item'),
            'alternate_barcode.edit' => __('admin.title.edit_alternate_barcode'),
            'alternate_barcode.show' => __('admin.title.view_alternate_barcode'),
            'editItemWhse' => __('admin.title.edit_item_whse'),
            'editItemLocation' => __('admin.title.edit_item_loc'),
            'ViewItemLocation' => __('admin.title.view_item_loc'),
            'lot.show' => __('admin.title.view_lot'),
            'lot.edit' => __('admin.title.edit_lot'),
            'viewLotLocation' => __('admin.title.view_lotloc'),
            'editLotLocation' => __('admin.title.edit_lotloc'),
            'viewPallet' => __('admin.title.view_pallet'),
            'editPallet' => __('admin.title.edit_pallet'),
            'editPalletItem' => __('admin.title.edit_pallet'),
            'viewUOMConversion' => __('admin.title.view_uomcon'),
            'editUOMConversion' => __('admin.title.edit_uomcon'),
            'viewEmpInvty' => __('admin.title.view_emp'),
            'employeeInvty.edit' => __('admin.title.edit_emp'),
            'ViewReasonCodeInvty' => __('admin.title.view_rc'),
            'editReasonCodeInvty' => __('admin.title.edit_rc'),
            'previewlabel' => __('admin.title.preview_label'),
            'editlabel' => __('admin.title.edit_label'),
            'displaylabel' => __('admin.title.view_label'),
            'qrcodes.edit' => __('admin.title.edit_qr_code'),
            'customfields.show' => __('admin.title.view_custom_field'),
            'customfields.edit' => __('admin.title.edit_custom_field'),
            'lot_number_definition.edit' => __('admin.title.edit_lot_number_definition'),
            'order_number_definition.edit' => __('admin.title.edit_order_number_definition'),
            'lpn_number_definition.edit' => __('admin.title.edit_lpn_definition'),
            'viewPalletItem' => __('admin.title.view_pallet_item'),
            'editPalletItem' => __('admin.title.edit_pallet_item'),
            'allocation.edit' => __('admin.title.edit_allocation'),
            'allocation.show' => __('admin.title.view_allocation'),
            'viewTransOrder' => __('admin.title.view_to'),
            'editTransOrder' => __('admin.title.edit_to'),
            'viewTransLines' => __('admin.title.view_toline'),
            'editTransLines' => __('admin.title.edittoline'),
            'viewTransLine' => __('admin.title.view_toline'),
            'editTransLine' => __('admin.title.edittoline'),
            'addTransLine' => __('admin.title.add_toline'),
            'machine.edit' => __('admin.title.edit_mac'),
            'viewMachine' => __('admin.title.view_mac'),
            'picklistTest.show' => __('admin.title.view_picklist'),
            'picklistTest.edit' => __('admin.title.edit_picklist'),
            'picklistTest.editPicklistitem' => __('admin.title.edit_picklistitem'),
            'batch.show' => __('admin.title.count_batch.view'),
            'count_sheet.edit' => __('admin.title.edit_countsheet'),
            'count_sheet.show' => __('admin.title.countsheet'),
            'batch.edit' => __('admin.title.count_batch.edit'),
            'editWorkCentre' => __('admin.title.edit_wc'),
            'viewWorkCentre' => __('admin.title.view_wc'),
            'bom.show' => __('admin.title.view_bom'),
            'bom.edit' => __('admin.title.edit_bom'),
            'bom.print' => __('admin.title.print_bom'),
            'viewEmpProd' => __('admin.title.view_emp'),
            'employeeProd.edit' => __('admin.title.edit_emp'),
            'viewTask' => __('admin.title.view_task'),
            'editTask' => __('admin.title.edit_task'),
            'ViewReasonCodeProd' => __('admin.title.view_rc'),
            'editReasonCodeProd' => __('admin.title.edit_rc'),
            'viewJob' => __('admin.title.view_job'),
            'editJob' => __('admin.title.edit_job'),
            'viewJobMatlNew' => __('admin.title.view_jobm'),
            'editJobMatlNew' => __('admin.title.edit_jobm'),
            'viewJobRouteNew' => __('admin.title.view_jobr'),
            'editJobRouteNew' => __('admin.title.edit_jobr'),
            'copyBomJob' => __('admin.title.copy_job_bom'),
            'showcustomer' => __('admin.title.view_cust'),
            'editcustomer' => __('admin.title.edit_cust'),
            'ViewShippingZone' => __('admin.title.view_shipping_zone'),
            'editShippingZone' => __('admin.title.edit_shipping_zone'),
            'co.showByCoNum' => __('admin.title.view_co'),
            'co.edit' => __('admin.title.edit_co'),
            'viewCO' => __('admin.title.view_co_line'),
            'editCO' => __('admin.title.edit_co_line'),
            'addCO' => __('admin.title.add_co_line'),
            'showvendor' => __('admin.title.view_vendor'),
            'editvendor' => __('admin.title.edit_vendor'),
            'showPO' => __('admin.title.view_po_line'),
            'editPO' => __('admin.title.edit_po_line'),
            'po.edit' => __('admin.title.edit_po'),
            'po.showByPoNum' => __('admin.title.view_po'),
            'showGRN' => __('admin.title.view_grn_line'),
            'grn.showByGrnNum' => __('admin.title.view_grn'),
            'grn.show' => __('admin.title.view_grn'),
            'grn.edit' => __('admin.title.edit_grn'),
        ];
        return $titleArray[$_route] ?? "";
    }
}

if (!function_exists('getDateFitlerValue')) {
    function getDateFitlerValue($value)
    {
        $timezone = auth()->user()->getSiteTimezone();

        $dateTimeFormat = SiteSetting::getOutputDateFormat() . ' H:i:s'; // Date Format
        $dateFormat = SiteSetting::getOutputDateFormat();
        $len = strlen($value);
        $dataValue = \DateTime::createFromFormat($dateTimeFormat, $value); // Convert to proper date format

        $complete_date = true;
        $have_year = true;
        $have_month = true;
        $have_day = true;

        $complete_hours = true;
        $have_hour = true;
        $have_minute = true;
        $have_second = true;

        preg_match_all('/[^\w\s]/', $dateFormat, $splited_date_format);
        $date_format_symbol = $splited_date_format[0][0];

        $date_value_splitted = explode($date_format_symbol, $value);
        $date_format_splitted = explode($date_format_symbol, $dateFormat);

        // fail convert to datetime
        if ($dataValue == false) {
            // Fill the remaining Time with zero to prevent error when convertring
            $dataValue = explode(" ", $value);

            if (count($dataValue) == 1) {
                $complete_hours = false;
                $have_hour = false;
                $have_minute = false;
                $have_second = false;

                $time = "00:00:00";

                $total_date_format = count($date_format_splitted);
                $total_current_date = count($date_value_splitted);

                if ($total_current_date < $total_date_format) {
                    $complete_date = false;

                    $end_date_result = [];

                    foreach ($date_format_splitted as $key => $val) {
                        // for Year
                        if (in_array(strtolower($val), ['y', 'Y'])) {
                            if (array_key_exists($key, $date_value_splitted) && $date_value_splitted[$key]) {
                                $end_date_result[$key] = $date_value_splitted[$key];
                            } else {
                                $end_date_result[$key] = '00';
                                $have_year = false;
                            }
                        }
                        // for Month
                        else if (in_array(strtolower($val), ['f', 'm', 'M', 'n'])) {
                            if (array_key_exists($key, $date_value_splitted) && $date_value_splitted[$key]) {
                                $end_date_result[$key] = $date_value_splitted[$key];
                            } else {
                                if ($val == 'M') {
                                    $end_date_result[$key] = 'Jan';
                                } else if ($val == 'F') {
                                    $end_date_result[$key] = 'January';
                                } else {
                                    $end_date_result[$key] = '0';
                                }
                                $have_month = false;
                            }
                        }
                        // for day
                        else if (in_array(strtolower($val), ['d', 'j'])) {
                            if (array_key_exists($key, $date_value_splitted) && $date_value_splitted[$key]) {
                                $end_date_result[$key] = $date_value_splitted[$key];
                            } else {
                                $end_date_result[$key] = '0';
                                $have_day = false;
                            }
                        }
                    }

                    $dataValue[0] = implode($date_format_symbol, $end_date_result);
                }
            } else {
                $time = $dataValue[1];
            }

            $time = explode(":", $time);

            if (count($time) < 3) {
                $complete_hours = false;

                if (count($time) == 2) {
                    $have_minute = false;
                    $have_second = false;
                }

                if (count($time) == 1) {
                    $have_second = false;
                }

                for ($i = count($time); $i < 3; $i++) {
                    $time[] = "00";
                }
            }
            // If the last char were :
            if (empty($time[count($time) - 1])) {
                $time[count($time) - 1] = "00";
            }

            // Combine Array into string
            $dataValue[1] = implode(":", $time);
            $dataValue = implode(" ", $dataValue);

            $dataValue = \DateTime::createFromFormat($dateTimeFormat, $dataValue);
        }

        // $dataValue = $dataValue->format('Y-m-d H:i:s');

        if (!$dataValue) {
            return $value;
        }

        if ($dataValue->format('His') > 0) {
            $dataValue = $dataValue->format('Y-m-d H:i:s');
            $dataValue = Timezone::convertToUTC($dataValue, $timezone); // change timezone
        } else {
            if (!$complete_date) {
                $end_date_value = [];
                if (!$have_year) {
                    $end_date_value[] = "%";
                } else {
                    $end_date_value[] = $dataValue->format("Y");
                }

                if (!$have_month) {
                    $end_date_value[] = "%";
                } else {
                    $end_date_value[] = $dataValue->format("m");
                }

                if (!$have_day) {
                    $end_date_value[] = "%";
                } else {
                    $end_date_value[] = $dataValue->format("d");
                }

                $dataValue = implode("-", $end_date_value);
            } else {
                if (!$complete_hours) {
                    $end_hours_value = [];

                    if (!$have_hour && !$have_minute && !$have_second) {
                        $dataValue = $dataValue->format('Y-m-d');
                    } else {
                        if (!$have_hour) {
                            $end_hours_value[] = "%";
                        } else {
                            $end_hours_value[] = $dataValue->format("H");
                        }

                        if (!$have_minute) {
                            $end_hours_value[] = "%";
                        } else {
                            $end_hours_value[] = $dataValue->format("i");
                        }

                        if (!$have_second) {
                            $end_hours_value[] = "%";
                        } else {
                            $end_hours_value[] = $dataValue->format("s");
                        }

                        $dataValue = $dataValue->format('Y-m-d') . ' ' . implode(":", $end_hours_value);
                    }
                } else {
                    $dataValue = $dataValue->format('Y-m-d H:i:s');
                }
            }
        }

        // $dataValue = substr($dataValue, 0, $len); // get time based on length

        return $dataValue;
    }
}
