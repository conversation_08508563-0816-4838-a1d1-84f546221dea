function dataTableSearch(selector, index, val) {
    var re = /^[+-]?[0-9]+([.][0-9]+)?([eE][+-]?[0-9]+)?$/;
    // console.log(re.test(val));
    val2 = val.replace(/,/g, '');
    // var val2 = parseFloat(val.replace(/,/g, ""));
    // console.log(val2);
    if (re.test(val2) == false) {
        // console.log("aa");
        $(selector).DataTable().column(index).search(val).draw();
    } else {
        console.log('bb');
        $(selector).DataTable().column(index).search(val2).draw();
    }
}

function floatify(number) {
    return parseFloat(number.toFixed(10));
}
function getFloatFromInput(selector) {
    var val = $(selector).val().replace(/,/g, '');
    if (val == '') val = 0;
    return parseFloat(val);
}
function updateTotalQty(elmName, targetElem) {
    var totalQty = 0;
    $("input[name='" + elmName + "[]']").each(function () {
        // console.log($(this).val());
        if ($(this).val() != '') totalQty = totalQty + parseFloat($(this).val());
    });
    // console.log(elmName,targetElem);
    var finaltotal = parseFloat(totalQty).toFixed(4);
    // console.log(elmName,targetElem);
    $('#' + targetElem)
        .val(finaltotal)
        .change();
}

function getScrapQtyMax() {
    var qtyreceived = getFloatFromInput('.op_received'); //qty_received
    var qtycompleted = getFloatFromInput('.op_completed'); //qty_completed
    var qty_to_complete = getFloatFromInput('.qty_completed'); //qty_to_complete
    //                            return Math.max(0,qtyreceived - (qtycompleted + qty_to_complete));

    var allow_over_scrap = $('#allow_over_scrap').val();
    //var res = Math.max(0,qtyreceived - (qtycompleted + qty_to_complete));
    var res = Math.max(0, qtyreceived - (qtycompleted + qty_to_complete));
    // console.log("qtyreceived " + qtyreceived + " qtycompleted " + qtycompleted + " qty_to_complete " + qty_to_complete);
    res = floatify(res);
    //                             console.log(floatify(res));
    if (allow_over_scrap == 0) {
        if (res >= 0) {
            return res;
        }
    }
}
function getCompleteQtyMax() {
    var qtyreceived = getFloatFromInput('.op_received'); //qty_received
    var qtycompleted = getFloatFromInput('.op_completed'); //qty_completed
    var allow_over_complete = $('#allow_over_complete').val(); //allow_over_complete
    var difference = qtyreceived - qtycompleted;
    difference = floatify(difference);
    if (allow_over_complete == 0) {
        return Math.max(0, difference);
    }
}
function getMoveQtyMax() {
    //    if ($(".op_completed").val() != "" && $(".op_moved").val() != "") {
    var qtyreceived = getFloatFromInput('.op_received'); //qty_received
    var qty_completed = getFloatFromInput('.op_completed'); //qty_completed
    var qty_to_complete = getFloatFromInput('.qty_completed'); //qty_to_complete
    var qty_moved = getFloatFromInput('.op_moved'); //qty_moved

    var difference = qty_completed + qty_to_complete - qty_moved;
    difference = floatify(difference);
    // console.log(difference);
    // console.log("qty_moved " + qty_moved + " qtycompleted " + qty_completed + " qty_to_complete " + qty_to_complete);

    return Math.max(0, difference);
    //    }
}
function getJobMaxReceiveQty() {
    var allow_over_receive = $('#allow_over_receive').val();
    var allow_incomplete_qty = $('#allow_incomplete_qty').val();
    var job_max_qty_to_receive = getFloatFromInput('#job_max_qty_to_receive');
    var qty_released = getFloatFromInput('#qty_released');
    var last_oper_qty_diff = getFloatFromInput('#last_oper_qty_diff');
    var job_oper_qty_diff = getFloatFromInput('#job_oper_qty_diff');

    console.log('last_oper_qty_diff=' + last_oper_qty_diff);
    console.log('job_oper_qty_diff=' + job_oper_qty_diff);

    console.log('allow_over_receive=' + allow_over_receive);
    console.log('allow_incomplete_qty=' + allow_incomplete_qty);

    console.log('job_max_qty_to_receive=' + job_max_qty_to_receive);
    console.log('qty_released=' + qty_released);

    var retrunQty = '';

    ////qty moved=2, qty completed = 6, qty released = 10, then what is the max can receive? example like this
    // so max qty= 6-2=4;
    // then if both checkbox are not cheched it will return 4 as qty released is 10 which is greater than max qty;
    //  then if allow_over_receive checked and allow_incomplete_qty not cheched it will return 4;
    //  then if allow_over_receive not checked and allow_incomplete_qty is cheched it will return 10;
    //
    //
    ////qty moved=6, qty completed = 6, qty released = 10, then what is the max can receive? example like this
    // so max qty= 6-6=0;
    // then if both checkbox are not cheched it will return qty released is 10 which is greater than max qty;
    //  then if allow_over_receive checked and allow_incomplete_qty not cheched it will return 4;
    //  then if allow_over_receive not checked and allow_incomplete_qty is cheched it will return 10;

    if (allow_over_receive == 0 && allow_incomplete_qty == 0) {
        //        if (last_oper_qty_diff > 0) {
        var minQty = Math.min(last_oper_qty_diff, job_oper_qty_diff);
        //        } else {
        retrunQty = Math.max(0, minQty);
        //        }
    } else if (allow_over_receive == 1 && allow_incomplete_qty == 0) retrunQty = Math.max(0, last_oper_qty_diff);
    else if (allow_over_receive == 0 && allow_incomplete_qty == 1) retrunQty = Math.max(0, job_oper_qty_diff);

    console.log('final qty=' + retrunQty);

    //    console.log(retrunQty);
    return retrunQty;
}
function goBackAndRefresh() {
    //    console.log(document.referrer)
    if (document.referrer != '') {
        window.location.href = document.referrer;
    }
    return false;
    window.history.go(-1);
    setTimeout(() => {
        location.reload();
    }, 500);
    return false;
}
$(document).on('keydown', '.form-group :input:not(textarea)', function (event) {
    // console.log("a", event.key);
    // event.preventDefault();
    var filterBtn = $('#filter');
    var submitBtn = $('.form-group button[type="submit"]');
    // console.log(filterBtn.length, submitBtn.length, submitBtn.attr('id'));
    if (event.key == 'Enter' && filterBtn.length > 0 && submitBtn.attr('id') == 'print') {
        event.preventDefault();
        filterBtn.trigger('click');
        // console.log("a");
    }
});
$('.form').submit(function (eventObj) {
    var _form = $(this);
    $('span.pseudoinput').each(function () {
        $('<input />')
            .attr('type', 'hidden')
            .attr('name', $(this).attr('name'))
            .attr('value', $(this).text().trim())
            .appendTo(_form);
    });

    return true;
});
$(function () {
    // add able row code
    $(document).on('click', '.addable_item_remove_trigger', function () {
        $(this).parents('.addable_item_row').remove();
        var length = $('.addable_item_row').length;
        // var index = length + 1;
        console.log(length);
        $('.addable_item_row_' + length)
            .find('.addable_item_remove_trigger')
            .show();
    });
    $(document).on('click', '.addable_item_trigger', function () {
        // console.log("as");
        var addableClass = $(this).data('addable');
        var length = $('.addable_item_row.' + addableClass).length;
        var template = $('.addable_item_template.' + addableClass).clone();
        var labelText = $(template).find('.label-control').text();
        $(template).removeClass('hidden addable_item_template');
        //         $(template).removeClass('');
        var index = length + 1;
        // console.log(length);
        // console.log(template);
        // $(".addable_item_row_1 .label-control").text(labelText + " " + 1);
        // $(template).find('.label-control').text(labelText + " " + index);

        $(template).addClass('addable_item_row ' + addableClass + ' addable_item_row_' + index);
        $('.addable_item_row_' + length + '.' + addableClass).after(template);
        // console.log(length, template);
        $('.addable_item_row .label-control').each(function (i, j) {
            var indexx = i + 1;
            $(this).text(labelText + ' ' + indexx);
        });
        $('.addable_item_remove_trigger').hide();
        $('.addable_item_row_' + index)
            .find('.addable_item_remove_trigger')
            .show();
    });

    var _timeoutHandler = 0,
        _inputString = '',
        _onKeypress = function (e) {
            if (_timeoutHandler) {
                clearTimeout(_timeoutHandler);
            }
            _inputString += e.key;

            _timeoutHandler = setTimeout(function () {
                if (_inputString.length <= 3) {
                    _inputString = '';
                    return;
                }
                $(e.target).trigger('altdeviceinput', _inputString);
                _inputString = '';
            }, 20);
        };
    $(document).on({
        keypress: _onKeypress,
    });
    $(document).on('altdeviceinput', function (e, barCode) {
        //alert(barCode + "ddd");
    });
    var alternateMapping = {
        from_loc: 'to_loc',
        // to_loc: "from_loc"
    };
    // { RS } item_num || YNTest23060701{ RS } lot_num || LYNt23060701{ RS }

    // Placeholders mapping with ids of input field.
    //Key= placeholder in the QR Code
    // value= Array of ids of input feilds want to attatch with qrcode placeholder
    var placeholdersMapping = {
        item_num: [
            'item_num',
            'item_num_cw',
            // 'matl_item',
            'citem',
        ],

        loc_num: [
            'loc_num',
            'from_loc',
            'stage_num'
            // 'to_loc',
        ],

        lot_num: ['lot_num'],
        uom: ['uom'],
        job_num: ['job_num', 'job_id'],
        suffix: ['suffix'],
        oper_num: ['oper_num'],
        seq_num: ['seq_num'],
        // "matl_item": [
        //     "matl_item",
        // ],

        // "emp_num": [
        //     "emp_num",
        // ],
        po_num: ['po_num'],
        po_line: ['po_line'],
        vend_num: ['vend_num', 'vend_name'],
        vend_lot: ['vend_lot'],
        // "vend_name": [
        //     "vend_name",
        // ],
        co_num: ['co_num'],
        co_line: ['co_line'],
        cust_num: ['cust_num'],
        // "cust_name": [
        //     "cust_name",
        // ],
        trn_num: ['trn_num'],
        trn_line: ['trn_line'],
        from_whse: ['from_whse'],
        to_whse: ['to_whse'],
        whse_num: ['whse_num', 'from_whse'],
        qtyinbox: ['qty', 'count_qty', 'qty_to_pick', 'qty_to_unpick', 'qty_to_ship', 'input4', 'qty_to_issue', 'qty_to_return', 'qty_completed', 'qty_moved', 'qty_complete', 'qty_move', 'qty_input'],
        // "stage_num": [
        //     "stage_num",
        // ],
        // 'machine_num': [
        //     "res_id"
        // ],
        lpn_num: ['lpn_num', 'from_lpn'],
        // 'from_lpn': [
        //     'lpn_num',
        // ]
        grn_num: ['grn_num'],
    };
    // var placeholdersMapping = {
    //     "job_num": [
    //         "job_num",
    //         "job_id"
    //     ],
    //     "item_num": [
    //         "suffix"
    //     ],
    //     "item_num": [
    //         "item_num"
    //     ]

    // };

    $.each(placeholdersMapping, function (i, j) {
        var class_name = i;
        // console.log(class_name);
        $.each(j, function (key, elm) {
            // console.log(elm);
            if (!$('#' + elm).hasClass('no_scan')) {
                $('#' + elm).attr('data-mapping', class_name);
                // $("#" + elm).addClass(class_name);
                $('#' + elm).addClass('qrcodeInput');
            }


        });
    });
    // $(document).on('change', '.qrcodeInput', function (event) {
    //     // console.log("aa");
    //     setTimeout(function () {
    //         const list = $(".form-group:not('hidden') :input:not([readonly])[type='text']");   //all containers
    //         var newlist = [];
    //         $(list).each(function (i, j) {
    //             if ($(j).is(':visible')) {
    //                 // console.log(j)
    //                 newlist.push(j);
    //             }
    //         });
    //         const index = list.index($(event.target));  //current container
    //         // console.log(list, index);
    //         const target = (index + 1) % list.length;    //increment with wrap
    //         const next = newlist[target];
    //         next.focus();
    //     }, 500)

    // })

    // Auto Jump for mobile forms
    // $(document).on('change', ".content.mobile .form-group:not('hidden') .form-control:not([readonly]):not(.not_autop)", function (event) {
    //     // console.log("aa");
    //     setTimeout(function () {
    //         const list = $(".content.mobile .form-group:not('hidden') .form-control:not([readonly]):not(.not_autop):visible");   //all containers
    //         // console.log(list);
    //         // var newlist = [];
    //         // $(list).each(function (i, j) {
    //         //     if ($(j).is(':visible')) {
    //         //         // console.log(j)
    //         //         newlist.push(j);
    //         //     }
    //         // });
    //         // console.log(newlist, list);
    //         const index = list.index($(event.target));  //current container
    //         // console.log(list, index);
    //         const target = (index + 1) % list.length;    //increment with wrap

    //         if (target > index) {
    //             const next = list[target];
    //             // console.log(list, target);

    //             if (typeof next != "undefined")
    //                 next.focus();
    //         }

    //     }, 1000)

    // });

    $('#scan_input').on('change', function () {
        var $val = $(this).val();
        // console.log($val);
        elm = $(this);
        parseQrCodeBeta($val);
        setTimeout(function () {
            $(elm).focus();
            $(elm).val('');
        }, 500);
    });

    $(window).load(function () {
        // $(".qrcodeInput")[0].focus();
        if ($('#scan_input').length) {
            $('#scan_input')[0].focus();
        }
    });
    function parseQrCodeBeta($val) {
        // $val = "job_num:31212__suffix:0001";
        var inpVal = '';

        if ($val == '' || $val.indexOf(feild_separator) <= 0) return;

        // { RS } item_num || YNTest23060701{ RS } lot_num || LYNt23060701{ RS }
        var $valArray = $val.split(qr_separator);
        // console.log($valArray);
        var inputArrays = [];
        $.each($valArray, function (key, elm) {
            // console.log(elm);
            if (elm != '') {
                var $valSubArray = elm.split(feild_separator);
                var inputClass = $valSubArray[0];
                var inputVal = $valSubArray[1];
                inputArrays[inputClass] = inputVal;
            }
        });
        // console.log(inputArrays);
        var _time = 100;
        // $('.qrcodeInput:not([readonly])').each(function () {
        $('.qrcodeInput').each(function () {
            var $elm = $(this);
            var inputName = $(this).data('mapping');
            _time = _time + 200;
            if ((!$elm.is('[readonly]') && $elm.is(":visible")) || $elm.hasClass('scan_match')) {
                // console.log($elm.is(":visible"), inputName);
                // console.log(inputName);
                if (typeof inputArrays[inputName] != 'undefined' && inputArrays[inputName] != '') {
                    setTimeout(function () {
                        var newVal = inputArrays[inputName];
                        var oldVal = $($elm).val();
                        if (newVal != oldVal) {
                            $($elm).attr('myVal', inputArrays[inputName]);
                            $($elm).val(inputArrays[inputName]).change();
                        }

                        $('#scan_input')[0].focus();
                    }, _time);
                }
            } else {
                // { RS } whse_num || MAINWH{ RS } item_num ||0208{ RS } loc_num || 55{ RS } lot_num || LotAfiqUomConv{ RS } uom || EA{ RS }
                var elmName = $('#' + inputName).attr('name');
                var alterNameField = alternateMapping[elmName];
                console.log(elmName);
                if (typeof alterNameField != 'undefined') {
                    // console.log(alterNameField);
                    $elm = $('#' + alterNameField).not('readonly');
                    setTimeout(function () {
                        if ($($elm).is(":visible")) {
                            var newVal = inputArrays[inputName];
                            var oldVal = $($elm).val();
                            if (newVal != oldVal) {
                                $($elm).attr('myVal', inputArrays[inputName]);
                                $($elm).val(inputArrays[inputName]).change();
                            }

                            $('#scan_input')[0].focus();
                        }

                    }, _time);
                }
                // console.log(alterNameField, inputName, elmName, alternateMapping);
                // $("#" + elmName).not('readonly').val("aa");
            }
            // var inputName = $(this).attr('id');
        });
        // $("#scan_input").val("");
    }
    function parseQrCode($val, $elm) {
        // $val = "job_num:31212__suffix:0001";
        var inpVal = '';
        var orgVal = $val;
        if ($val == '' || $val.indexOf(feild_separator) <= 0) return;
        // $val = $val.trim('{RS}');
        // console.log($val);
        // return;
        var orgVal = $val;
        var $valArray = $val.split(qr_separator);
        // console.log($valArray);

        // console.log($valArray);
        $elm.val('');
        $.each($valArray, function (key, elm) {
            // console.log(elm);
            if (elm != '') {
                var $valSubArray = elm.split(feild_separator);
                if (elm.indexOf('||') <= 0) inpVal = elm;

                var inputClass = $valSubArray[0];
                var inputVal = $valSubArray[1];
                // console.log(inputClass, inputVal);
                // console.log($("." + inputClass).length);
                if ($('.' + inputClass).length) {
                    setTimeout(function () {
                        $('.' + inputClass).val(inputVal);
                    }, 500);

                    orgVal = orgVal.replace('{RS}' + elm + '{RS}', '');
                }
            }
        });
        $elm.val(inpVal);
        // $(".item_num")[0].val("sasasaas");
    }
});

// function to format number according to precision
function numberFormatPrecision($number, $precision = 2, $decimalPoint = '.', $thousandSeparator = ',') {
    // handling value stored not as float(18,8)
    $number = $number == null || $number == NaN || $number == 0 ? '0' : $number;
    $number = parseFloat($number).toFixed(10);

    // separate the number by decimal point
    $numberParts = $number.split($decimalPoint);

    // initialize the return value with the integer part of the number
    $response = $numberParts[0];

    // check if the number is decimal and the precision is set
    if ($numberParts.length > 1 && $precision > 0) {
        // append the decimalPoint
        $response = $response + $decimalPoint;
        // append the decimal part, truncated, or extended to the specified precision
        $response = $response + $numberParts[1].substr(0, $precision);
    }

    // using thousandSeparator to add the thousand separator symbol
    $response = thousandSeparator($response, $thousandSeparator);

    // return value
    return $response;
}

// Function to add thousand separator
function thousandSeparator(number, separator = ',') {
    return number.toString().replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, separator);
}

$(document).ready(function () {
    // only allow number, dot and comma
    $('.number-format').on('input', function () {
        this.value = this.value.replace(/[^0-9\.,]/g, '');
    });

    // only allow number
    $('.strictly-number-format').on('keyup', function () {
        this.value = this.value.replace(/[^0-9]/g, '');
    });
});

jQuery(function ($) {
    // validate number format including thousand separator
    // console.log("as");
    $.validator.addMethod(
        'number_size',
        function (value, element) {
            // regex to check number with comma and dot
            // if(!((/(?!0+\.00)(?=.{1,9}(\.|$))(?!0(?!\.))\d{1,3}(,\d{3})*(\.\d+)?/g).test(value)))
            //     return false;
            console.log('ss');
            value = value.replace(/,/g, '');
            numberParts = value.split('.');

            if (numberParts[0].length > 10) return false;

            if (numberParts.length > 1) {
                if (numberParts[1].length > 8) return false;
            }

            return true;
        },
        "{{__('error.mobile.max_characters')}}"
    );

    // validate number format including thousand separator
    $.validator.addMethod(
        'scrap_number_size',
        function (value, element) {
            numberParts = value.split('.');

            if (numberParts.length > 1) {
                if (numberParts[1].length > 4) return false;
            }

            return true;
        },
        "{{__('error.mobile.max_characters')}}"
    );

    // validate minimum value
    $.validator.addMethod('min_value', function (value, element, param) {
        console.log('min_value', value, param);
        value = value.replace(/,/g, '');
        return value >= param;
    });

    // validate strict minimum value
    $.validator.addMethod('minStrict_value', function (value, element, param) {
        // console.log( value, param );
        value = value.replace(/,/g, '');
        return value > param;
    });

    // validate maximum value
    $.validator.addMethod('max_value', function (value, element, param) {
        // console.log('max_value', value, param);
        value = value.replace(/,/g, '');
        // console.log('value <= param', value <= param);
        return value <= param;
    });
});

function restForm() {
    window.location.reload(true);
}

// Call the DataTable and Reset Purposes
function getFilterReset(callback, arrColumns) {
    // Execute the callback function
    if (typeof callback === 'function') {
        $('#filter').click(function () {
            unit_quantity_format = "{{$unit_quantity_format}}";
            total_quantity_format = "{{$total_quantity_format}}";

            callback();
            $('#btn-refresh').click(function () {
                $('input').each(function () {
                    $(this).val('');
                });
                $('input[type="checkbox"][value="specificValue"]').prop('checked', false);
                $('input[type="radio"]').each(function () {
                    $('input[type="radio"][name="' + this.name + '"]')
                        .first()
                        .prop('checked', true);
                });

                $('.dataTable')
                    .DataTable()
                    .$('select')
                    .each(function () {
                        $(this).val('');
                    });
                $('.dataTable').DataTable().columns().search('');
                $('.dataTable').DataTable().clear().draw();
                //         // $('.dataTable').DataTable().draw();
                //         // $('.dataTable').DataTable().draw();
            });

            var colreorder = new $.fn.dataTable.ColReorder(table);

            var colvis = new $.fn.dataTable.ColVis(table);
            $(colvis.button()).insertAfter('div.dataTables_length');

            table.on('column-reorder.dt.mouseup column-resize.dt.mouseup', function (event) {
                $('.buttons-excel').css('width', '');
                $('div').find('.ColVis').remove();
                var colvis = new $.fn.dataTable.ColVis(table);
                var colvis = new $.fn.dataTable.ColVis(table);
                var colvis = new $.fn.dataTable.ColVis(table);
                var colvis = new $.fn.dataTable.ColVis(table);
                var colvis = new $.fn.dataTable.ColVis(table);
                $(colvis.button()).insertAfter('div.dataTables_length');

                var arrColumns = [];
                for (i = 0; i < colvis.s.dt.aoColumns.length; i++) {
                    arrColumns.push(colvis.s.dt.aoColumns[i].data);
                }

                // Save Cookies
                var urlgo = "<?php echo route('save-cookies');?>";
                var token = '<?php echo csrf_token() ?>';
                $.ajax({
                    url: urlgo,
                    data: {
                        datasetting: arrColumns,
                        tableName: colvis.s.dt.nTable.id,
                        _token: token,
                    },
                    dataType: 'json',
                    type: 'POST',
                    success: function (resp) {
                        console.log(resp);
                    },
                });

                localStorage.setItem('columnsetting' + '_' + colvis.s.dt.nTable.id, JSON.stringify(arrColumns)); //stringify object and store
            });
        });
        // This will call the getTable function
    } else {
        console.log('Callback is not a function');
    }
}

// hide show-std-form on load
$(document).ready(function () {
    // Global function to reset datatable and form
    $('#btn-refresh').click(function () {
        // clear datatable
        $('.dataTable').DataTable().$('select').each(function () {
            $(this).val('');
        });
        // clear search
        $('.dataTable').DataTable().columns().search('');
        // clear datatable
        $('.dataTable').DataTable().clear().draw();
        // reset form field
        $('form').trigger('reset');
        // clear all error msg
        $("form").find('label.error').remove();
    });
});

function checkCatchWeight(item_num, ref_num, ref_line) {
    if (!item_num || item_num == '') {
        $('.show-std-form').hide();
        return;
    }

    // ajax call to check for catch weight
    $.ajax({
        url: '/check-catch-weight',
        type: 'GET',
        data: {
            item_num: item_num,
        },
        success: function (response) {
            console.log('Catch weight response:', response);
            if (response == 1) {
                // Show loader with message
                showLoader('Loading catch weight form...');
                $('.show-std-form').hide();
                window.location.href = window.location.href + '?item_num=' + item_num + '&ref_num=' + ref_num + '&ref_line=' + ref_line;
            } else {
                // Hide loader when no catch weight found
                hideLoader();
                $('.show-std-form').show();
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.error('Error checking catch weight:', textStatus, errorThrown);
            // Hide loader on error
            hideLoader();
        }
    });

    // Show loader with optional message
    function showLoader(message = 'Loading...') {
        // Show the loader
        $(".pageloader").not(".loader-message").css("display", "block");

        // Update and show the message
        $(".loader-message").text(message).css("display", "block");
    }

    // Hide loader
    function hideLoader() {
        $(".pageloader").css("display", "none");
    }

}
function errorBeep() {
    $('#errorSound')[0].play();

}
function successBeep() {
    $('#successSound')[0].play();

}
