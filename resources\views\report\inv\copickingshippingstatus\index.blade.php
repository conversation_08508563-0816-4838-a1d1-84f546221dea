@extends('layout.app')
@section('head')
@include('report.style')
@endsection
@section('content')
<div class="margin-header">
    <div class="card-header-title ">
        <table class="title-header">
            <tr>
                <td class="title-division"><h4 class="title">CO Picking vs Shipping Status</h4></td>
            </tr>
        </table>
    </div>
    <div>
        <form class="form-group" id="co_picking_shipping_report" name="co_picking_shipping_report" method="POST" action="{{route('printcopickingshippingstatusrep')}}">
            @csrf
            <div class="default-button">
                <button id="filter" type="button" class="displayExcel-button btn btn-primary button-padding" style="background-color:#37BC3B;"><i class="icon-square-plus"></i> {{__('admin.button.generate')}}</button>
                <button id="btn-refresh" type="button" class="btn btn-primary button-padding">
                    <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
                </button>
                <button type="submit" id="print" class="btn btn-info button-padding">
                    <i class="icon-print"></i> {{__('admin.button.print')}}
                </button>
            </div>
            <div class="table-content">
                <table align="center">
                    <tr>
                        <td width='100px'><label for="co_num">{{ __('admin.label.co') }}</label></td>
                        <td width='300px'><input type="text" name="from_co_num" id="from_co_num" tabindex="1" class="form-control border-primary" placeholder="From CO Number"></td>
                        <td width='75px'>
                            <button type="button" name="{{ __('admin.label.co') }}" onClick="selection('/getCoNum','from_co_num','co_num','from_co_num');modalheader(this.id,'Customer Orders');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='65px'><label> To</label></td>
                        <td width='300px'><input type="text" name="to_co_num" id="to_co_num" tabindex="2" class="form-control border-primary" placeholder="To CO Number"></td>
                        <td width='100px'>
                            <button type="button" name="{{ __('admin.label.co') }}" onClick="selection('/getCoNum','to_co_num','co_num','to_co_num');modalheader(this.id,'Customer Orders');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="cust_num">{{__('admin.label.cust_num')}}</label></td>
                        <td><input type="text" name="from_cust_num" id="from_cust_num" tabindex="3" class="form-control border-primary" placeholder="From Customer Code"></td>
                        <td>
                            <button type="button" name="From Customer" onClick="selection('/getCust','from_cust_num','cust_num','from_cust_num');modalheader(this.id,'Customer Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_cust_num" id="to_cust_num" tabindex="4" class="form-control border-primary" placeholder="To Customer Code"></td>
                        <td>
                            <button type="button" name="To Customer" onClick="selection('/getCust','to_cust_num','cust_num','to_cust_num');modalheader(this.id,'Customer Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="due_date">{{ __('admin.label.due_date') }}</label></td>
                        <td><input type="text" autocomplete="off" id="from_due_date" tabindex="5" class="form-control border-primary input-group from_due_date" name="from_due_date" placeholder="From Due Date"></td>
                        <td>&nbsp;</td>
                        <td><label> To</label></td>
                        <td><input type="text" autocomplete="off" id="to_due_date" tabindex="6" class="form-control border-primary input-group to_due_date" name="to_due_date" placeholder="To Due Date"></td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td width='100px'><label for="item_num">{{ __('admin.label.item_num') }}</label></td>
                        <td width='300px'><input type="text" name="from_item_num" id="from_item_num" tabindex="7" class="form-control border-primary" placeholder="From Item"></td>
                        <td width='75px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','from_item_num','item_num','from_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='65px'><label> To</label></td>
                        <td width='300px'><input type="text" name="to_item_num" id="to_item_num" tabindex="8" class="form-control border-primary" placeholder="To Item"></td>
                        <td width='100px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','to_item_num','item_num','to_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="co_status">{{__('admin.label.co_status')}} &nbsp;</label></td>
                        <td>
                            <select tabindex="9" class="form-control border-primary" name="co_status" id="co_status">
                                <option value="All" selected>&nbsp;All</option>
                                <option value="O">&nbsp;Open</option>
                                <option value="C">&nbsp;Completed</option>
                            </select>
                        </td>
                        <td></td>
                        <td><label for="co_line_status">Line Status &nbsp;</label></td>
                        <td>
                            <select tabindex="10" class="form-control border-primary" name="co_line_status" id="co_line_status">
                                <option value="All" selected>&nbsp;All</option>
                                <option value="O">&nbsp;Open</option>
                                <option value="C">&nbsp;Completed</option>
                            </select>
                        </td>
                        <td></td>
                    </tr>
                    <tr>
                        <td><label for="stage_locations">Stage Location &nbsp;</label></td>
                        <td>
                            <select tabindex="11" class="form-control border-primary select2" name="stage_locations[]" id="stage_locations" multiple="multiple" data-placeholder="Select Stage Locations">
                                @foreach($stagingLocations as $location)
                                    <option value="{{$location->stage_num}}">{{$location->stage_num}}</option>
                                @endforeach
                            </select>
                        </td>
                        <td></td>
                        <td width='120px'><label for="outstanding_only">Outstanding Only: &nbsp;</label></td>
                        <td>
                            <label class="custom-control custom-checkbox">
                                <input type="checkbox" name="outstanding_only" id="outstanding_only" tabindex="12" class="custom-control-input" checked>
                                <span class="custom-control-indicator"></span>
                                <span class="custom-control-description"></span>
                            </label>
                        </td>
                        <td></td>
                    </tr>
                </table>
            </div>
        </form>
        <hr>
        <div class="card-block card-dashboard">
            <div class="table-content">
                @include('report.inv.copickingshippingstatus.list')
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/gh/jeffreydwalter/ColReorderWithResize@9ce30c640e394282c9e0df5787d54e5887bc8ecc/ColReorderWithResize.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endpush

@include('util.js-datatable')

<script type="text/javascript">
    var defaultColumns = ['cust_num', 'cust_name', 'co_num', 'co_line', 'due_date', 'item_num', 'item_desc', 'qty_required', 'stage_location', 'qty_picked', 'qty_shipped', 'outstanding_qty'];

    if(localStorage.getItem('columnsetting_COPickingShippingStatusReport') == null){
        localStorage.setItem('columnsetting_COPickingShippingStatusReport', JSON.stringify(defaultColumns));
    }

    //Get Cookies Columns
    var getItem = localStorage.getItem('columnsetting_COPickingShippingStatusReport');
    var columnsList = JSON.parse(getItem);

    var arrColumns = [];
    for(var i = 0; i < columnsList.length; i++){
        if(columnsList[i] == 'cust_num' || columnsList[i] == 'cust_name' || columnsList[i] == 'co_num' || columnsList[i] == 'co_line' || columnsList[i] == 'due_date' || columnsList[i] == 'item_num' || columnsList[i] == 'item_desc' || columnsList[i] == 'stage_location'){
            arrColumns.push({data: columnsList[i], name: columnsList[i]});
        } else if(columnsList[i] == 'qty_required' || columnsList[i] == 'qty_picked' || columnsList[i] == 'qty_shipped' || columnsList[i] == 'outstanding_qty'){
            arrColumns.push({data: columnsList[i], name: columnsList[i], class: 'dt-right'});
        } else {
            arrColumns.push({data: columnsList[i], name: columnsList[i]});
        }
    }

    //onClick "Generate" button
    $('.displayExcel-button').click(function() {
        $('.default-button').css('padding-right', '220px');
    });

    jQuery(function($){
        $("#co_picking_shipping_report").validate({
            onchange:true,
            rules:{
                from_co_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_co_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_cust_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_cust_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_item_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_item_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
            },
            messages:{
                from_co_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.co_num') ]) }}"
                },
                to_co_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.co_num') ]) }}"
                },
                from_cust_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                },
                to_cust_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                },
                from_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                to_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
            },
        });
    });

    $(document).ready(function(){
        // Initialize Select2 for multi-select
        $('.select2').select2({
            width: "100%"
        });

        $('#filter').click(function(){
            getTable();
        });

        var table;
        
        function bindSearchEvents() {
            // Bind search events after DataTable is initialized
            $("#COPickingShippingStatusReport thead input:not(#mass-chk)").off('keyup').on('keyup', function (e) {
                if (e.keyCode == 13) {
                    console.log('Search column index:', $(this).parent().index());
                    table.column($(this).parent().index()).search(this.value).draw();
                }
            });
            
            // Setup ColReorder and ColVis
            var colreorder = new $.fn.dataTable.ColReorder(table);
            var colvis = new $.fn.dataTable.ColVis(table);
            $(colvis.button()).insertAfter('div.dataTables_length');

            table.on('column-reorder.dt.mouseup column-resize.dt.mouseup', function(event) {
                $(".buttons-excel").css('width','');
                $("div").find('.ColVis').remove();
                var colvis = new $.fn.dataTable.ColVis(table);
                $(colvis.button()).insertAfter('div.dataTables_length');

                var arrColumns = [];
                for(i=0; i<colvis.s.dt.aoColumns.length; i++){
                    arrColumns.push(colvis.s.dt.aoColumns[i].data);
                }

                // Save Cookies
                var urlgo = '<?php echo route('save-cookies');?>';
                var token = '<?php echo csrf_token() ?>';
                $.ajax({
                    "url": urlgo,
                    "data": {
                        "datasetting": arrColumns,
                        "tableName": colvis.s.dt.nTable.id,
                        "_token": token,
                    },
                    "dataType": 'json',
                    "type": 'POST',
                    "success": function (resp) {
                        console.log(resp);
                    },
                });

                localStorage.setItem('columnsetting'+'_'+colvis.s.dt.nTable.id, JSON.stringify(arrColumns));
            });
        }

        $('#btn-refresh').click(function(){
            $('input').each(function() {
                $(this).val('');
            });
            $('input[type="checkbox"]').each(function() {
                $(this).prop('checked', false);
            });
            $('#outstanding_only').prop('checked', true); // Default checked
            $('#stage_locations').val(null).trigger('change');
            $('#co_picking_shipping_report')[0].reset();
            
            // Clear validation errors
            $('#co_picking_shipping_report').find('.error').removeClass('error');
            $('#co_picking_shipping_report').find('label.error').remove();
            
            // Clear search inputs
            $("#COPickingShippingStatusReport thead input").val('');
            
            if (table) {
                table.columns().search('').draw();
                table.clear().draw();
            }
        });

        $(window).resize(function (e) {
            $("#COPickingShippingStatusReport").DataTable().columns.adjust();
        });

        function getTable(){
            unit_quantity_format = "{{$unit_quantity_format}}";

            let ajaxMethod = {
                url: '{!! route('copickingshippingstatusrep.data') !!}',
                method: 'get',
                data: function (d) {
                    d.from_co_num = $('#from_co_num').val();
                    d.to_co_num = $('#to_co_num').val();
                    d.from_cust_num = $("#from_cust_num").val();
                    d.to_cust_num = $('#to_cust_num').val();
                    d.from_due_date = $('#from_due_date').val();
                    d.to_due_date = $('#to_due_date').val();
                    d.from_item_num = $('#from_item_num').val();
                    d.to_item_num = $('#to_item_num').val();
                    d.co_status = $('#co_status').val();
                    d.co_line_status = $('#co_line_status').val();
                    d.stage_locations = $('#stage_locations').val();
                    if ($('#outstanding_only').is(":checked")) {
                        d.outstanding_only = 'on';
                    } else {
                        d.outstanding_only = 'off';
                    }
                },
                dataSrc: function (json) {
                    var return_data = new Array();
                    for(var i=0; i<json.data.length; i++){
                        return_data.push({
                            'cust_num': json.data[i].cust_num,
                            'cust_name': json.data[i].cust_name,
                            'co_num': json.data[i].co_num,
                            'co_line': json.data[i].co_line,
                            'due_date': json.data[i].due_date,
                            'item_num': json.data[i].item_num,
                            'item_desc': json.data[i].item_desc,
                            'qty_required': numberFormatPrecision(json.data[i].qty_required, unit_quantity_format),
                            'stage_location': json.data[i].stage_location,
                            'qty_picked': numberFormatPrecision(json.data[i].qty_picked, unit_quantity_format),
                            'qty_shipped': numberFormatPrecision(json.data[i].qty_shipped, unit_quantity_format),
                            'outstanding_qty': numberFormatPrecision(json.data[i].outstanding_qty, unit_quantity_format),
                        });
                    }
                    return return_data;
                }
            };

            table = generateTable('COPickingShippingStatusReport', arrColumns, ajaxMethod, 'COPickingShippingStatus');
            
            // Bind search events after table is generated
            setTimeout(function() {
                bindSearchEvents();
            }, 100);
        }
    });

    window.onload = function(){
        // Auto-generate when page loads
        // document.getElementById("filter").click();
    };
</script>

<style>
    .title-header{
        width:100%;
    }
    .title{
        padding-top:2px;
        font-size: 22px !important;
        font-weight: bold;
    }
    .title-division{
        width:50%;
        float:left;
    }
    .default-button{
        float:right;
        margin-top:-59.4px;
        padding-right:0px;
        position: relative;
    }
    .button-padding{
        margin:5px 0.5px;
    }
    .table-content{
        margin-top:15px;
    }
    .card-block{
        margin-top: -18px;
        padding-right: 0rem;
        padding-left: 0rem;
    }
    div.dt-buttons{
        float: right !important;
        margin-top: -54.5px;
    }
    .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle){
        border-bottom-right-radius: 0.18rem !important;
        border-top-right-radius: 0.18rem !important;
    }
    .btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
        border-bottom-left-radius: 0.18rem !important;
        border-top-left-radius: 0.18rem !important;
    }
    div.dt-buttons.btn-group>a.btn-secondary{
        width: 110px !important;
        margin-left: 4px !important;
    }
    a.btn.btn-success{
        border-color:#4FC3F7;
        background-color:#4FC3F7;
    }
    label{
        margin-bottom: 0.1rem !important;
    }
    .margin-header input.form-control.border-primary{
        padding-left:10px;
        height: 25px;
    }
    select.form-control.border-primary{
        padding-left: 2px !important;
        height: 25px;
    }
    .form-group{
        margin-top:1rem;
    }
    form td{
        padding: 2px 1px;
    }
    .table th, .table td{
        padding: 0.2rem 0.2rem !important;
    }
    .table td{
        border-bottom: 1px solid white !important;
    }
    div.dataTables_length>label{
        width: 110px !important;
    }
    button.ColVis_Button>span{
        font-size: 9pt;
    }
    div.card-block>table#COPickingShippingStatusReport thead>tr:first-child>th, div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        background-color: rgb(220,220,220);
    }
    div.dataTables_scrollBody>table>thead>tr.row>th{
        background-color: white;
    }
    table#datatable.table.table-bordered.table-hover.nowrap.datatable.no-footer td>a>button.btn.btn-primary{
        padding: 0.1rem 0.2rem !important;
    }
    div.dataTables_wrapper div.dataTables_paginate{
        margin-bottom: 3rem !important;
    }
    li.paginate_button.page-item.active>a {
        font-size: 9pt;
    }
    .dt-center {
        text-align: center;
    }
    .dt-right {
        text-align: right;
    }
    .dt-buttons {
        padding-bottom: 15px;
    }
    .dataTables_length {
        float:left;
        padding-top: 0.5em;
        padding-bottom: 5px;
    }
    div.dataTables_info {
        padding-top: 0.85em;
        position:absolute;
    }
    div.dataTables_paginate {
        padding-top: 0.85em;
        right:0px;
    }
    .select2-container--default .select2-selection--multiple {
        padding: 0.25rem 0.5rem 0.5rem 0.5rem !important;
        min-height: 25px !important;
        border: 1px solid #4FC3F7 !important;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 23px;
    }
    
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        top: 0px !important;
        right: 6px !important;
    }
    
    .select2-container--default .select2-results__option[aria-disabled=true] {
        display: none;
    }
    
    .select2-results__option {
        display: block;
    }
    
    .select2-container--default span.select2-selection--multiple {
        min-height: 1.45rem !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
    
    .select2-container--default .select2-selection--multiple .select2-selection__rendered li.select2-selection__choice {
        margin-top: 5px !important;
        margin-bottom: 5px !important;
        background-color: #f0f0f0;
        border: 1px solid #aaa;
        border-radius: 4px;
        cursor: default;
        float: left;
        margin-right: 5px;
        padding: 0 5px;
    }
    
    .select2-search__field {
        height: 1.33rem !important;
        margin-top: 0px !important;
    }
</style>

@include('util.selection')
@include('util.datepicker')
@endsection
