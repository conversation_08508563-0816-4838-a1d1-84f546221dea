@extends('layout.app')
@include('report.style')
@section('content')
<div class="margin-header">
    <div class="card-header-title ">
        <table class="title-header">
            <tr>
                <td class="title-division"><h4 class="title">{{ __($report_module_name) }}</h4></td>
            </tr>
        </table>
    </div>
    <div>
        <form class="form-group" id="job_oper_report" name="job_oper_report" method="POST" action="{{route('printreorder_itemrep')}}">
            @csrf
            <div class="default-button">
                <button id="filter" type="button" hidden class="displayExcel-button btn btn-primary button-padding" style="background-color:#37BC9B;"><i class="icon-square-plus"></i> {{__('admin.button.generate')}}</button></td>
                <button id="btn-refresh" type="button" hidden class="btn btn-primary button-padding" >
                    <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
                </button>
                <button type="submit" id="print" class="btn btn-info button-padding">
                    <i class="icon-print"></i> {{__('admin.button.print')}}
                </button>
            </div>
            <div class="table-content" id="filter_div" hidden>
                <table align="center">
                    <tr>
                        <td><label for="whse">{{__('admin.label.whse_num')}}</label></td>
                        <td><input type="text" name="from_whse_num" id="from_whse_num" tabindex="1" class="form-control border-primary" placeholder="From Warehouse" value={{$whse_pass}}></td>
                        <td>
                            <button type="button" name="{{__('admin.list.warehouses')}}" onClick="selection('/getWhse','from_whse_num', 'whse_num', 'from_whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_whse_num" id="to_whse_num" tabindex="4" class="form-control border-primary" placeholder="To Warehouse" value={{$whse_pass}}></td>
                        <td>
                            <button type="button" name="{{__('admin.list.warehouses')}}" onClick="selection('/getWhse','to_whse_num', 'whse_num', 'to_whse_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td width='100px'><label for="item_num">{{__('admin.label.item')}}</label></td>
                        <td width='300px'><input type="text" name="from_item_num" id="from_item_num" tabindex="2" class="form-control border-primary" placeholder="From Item" ></td>
                        <td width='75px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','from_item_num','item_num','from_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td width='300px'><input type="text" name="to_item_num" id="to_item_num" tabindex="5" class="form-control border-primary" placeholder="To Item" ></td>
                        <td width='100px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','to_item_num','item_num','to_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="product_code">{{ __('admin.label.product_code') }}</label></td>
                        <td><input type="text" name="from_product_code" id="from_product_code" tabindex="3" class="form-control border-primary" placeholder="From Product Code" ></td>
                        <td>
                            <button type="button" name="Product Code" onClick="selection('/getProdCode','from_product_code', 'product_code', 'from_product_code');modalheader(this.id,'Product Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_product_code" id="to_product_code" tabindex="6" class="form-control border-primary" placeholder="To Product Code" ></td>
                        <td>
                            <button type="button" name="Product Code" onClick="selection('/getProdCode','to_product_code', 'product_code', 'to_product_code');modalheader(this.id,'Product Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                </table>
                <hr>
            </div>
        </form>
                <div class="card-block card-dashboard">
                    @include('report.inv.reorder_item.list')
                </div>
    </div>
</div>

@push('scripts')
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/gh/jeffreydwalter/ColReorderWithResize@9ce30c640e394282c9e0df5787d54e5887bc8ecc/ColReorderWithResize.js"></script>
@endpush

@include('util.js-datatable')

<script type="text/javascript">
    var defaultColumns= ['item_num','item_desc','product_code','qty_available','reorder_level','qty_to_reorder','uom','reorder_level_percentage','whse_num'];

    if(localStorage.getItem('columnsetting_ReorderItemReport') == null){
        localStorage.setItem('columnsetting_ReorderItemReport', JSON.stringify(defaultColumns));
    }

    //Get Cookies Columns
    var getItem = localStorage.getItem('columnsetting_ReorderItemReport');
    var columnsList = JSON.parse(getItem);

    var arrColumns=[];
    for(var i = 0; i < columnsList.length; i++){
        if(columnsList[i]=='reorder_level' || columnsList[i]=='reorder_level_percentage' || columnsList[i]=='qty_available' || columnsList[i]=='qty_to_reorder'){
            arrColumns.push({data:columnsList[i], name:columnsList[i], class:'dt-right'});
        }else{
            arrColumns.push({data:columnsList[i], name:columnsList[i]});
        }
    }

    //onClick "Generate" button
    $('.displayExcel-button').click(function() {
        $('.default-button').css('padding-right', '220px');
    });

    jQuery(function($){
        $("#reorder_item").validate({
            onchange:true,
            rules:{
                from_item_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_item_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_product_code:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_product_code:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_whse_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_whse_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
            },
            messages:{
                from_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                to_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                from_product_code:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                },
                to_product_code:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                },
                from_whse_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                },
                to_whse_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.whse_num') ]) }}"
                },
            },
        });
    });

    window.onload=function(){
        if("{{ $ref_pass }}" == "dashboard"){
            document.getElementById("filter").click();
        }
    };

    $(document).ready(function() {
        
        if("{{ $ref_pass }}" != "dashboard"){
            $("#filter_div").attr('hidden', false);
            $("#filter").attr('hidden', false);
            $("#btn-refresh").attr('hidden', false);
            $(".search-filter").attr('hidden', false);
        }

        $('#filter').click(function(){
            getTable();

            $('#btn-refresh').click(function(){
         
                $('input').each(function() {
                    $(this).val('');
                });
                 $('.dataTable').DataTable().columns().search('');
                 $('.dataTable').DataTable().clear().draw();
                //$('.dataTable').DataTable().ajax.reload();
                //getTable();
            });

            var colreorder = new $.fn.dataTable.ColReorder(table);
            

            var colvis = new $.fn.dataTable.ColVis( table );
            $(colvis.button() ).insertAfter('div.dataTables_length');

            table.on('column-reorder.dt.mouseup column-resize.dt.mouseup', function(event) {
                $(".buttons-excel").css('width','');
                $("div").find('.ColVis').remove();
                var colvis = new $.fn.dataTable.ColVis( table );
                $(colvis.button() ).insertAfter('div.dataTables_length');

                var arrColumns = [];
                for(i=0;i<colvis.s.dt.aoColumns.length;i++){
                    arrColumns.push(colvis.s.dt.aoColumns[i].data);
                }

                // Save Cookies
                var urlgo = '<?php echo route('save-cookies');?>';
                var token = '<?php echo csrf_token() ?>';
                $.ajax({
                            "url": urlgo,
                            "data": {
                                "datasetting": arrColumns,
                                "tableName": colvis.s.dt.nTable.id,
                                "_token": token,
                            },
                            "dataType": 'json',
                            "type": 'POST',
                            "success": function (resp) {
                                console.log(resp);
                            },
                        });

                localStorage.setItem('columnsetting'+'_'+colvis.s.dt.nTable.id, JSON.stringify(arrColumns)); //stringify object and store
            });
        });

        $("#ReorderItemReport thead input:not(#mass-chk)").on( 'keyup', function (e) {
            if (e.keyCode == 13) {
                $('#ReorderItemReport').DataTable().column($(this).parent().index() + ':visible').search(this.value).draw();
            }
        });
        function getTable(){
             unit_quantity_format = "{{$unit_quantity_format}}";
            ref_pass = "{{ $ref_pass }}";

            let ajaxMethod = {
                url: '{{ route('reorder_itemrep.data') }}',
                    method: 'get',
                data:  {
                    from_item_num: $('#from_item_num').val(),
                    to_item_num: $('#to_item_num').val(),
                    from_whse_num: $('#from_whse_num').val(),
                    to_whse_num: $('#to_whse_num').val(),
                    from_product_code: $('#from_product_code').val(),
                    to_product_code: $('#to_product_code').val(),
                    ref_pass: ref_pass
                },
                dataSrc: function (json) {
                    var result = new Array();
                    for (i = 0; i < json.data.length; i++) {
                        result.push({
                            'item_num' : json.data[i].item_num,
                            'item_desc' : json.data[i].item_desc,
                            'product_code' : json.data[i].product_code,
                            'qty_available' : numberFormatPrecision(json.data[i].qty_available,unit_quantity_format),
                            'reorder_level' : numberFormatPrecision(json.data[i].reorder_level,4),
                            'qty_to_reorder' : numberFormatPrecision(json.data[i].qty_to_reorder,unit_quantity_format),
                            'uom' : json.data[i].uom,
                            'reorder_level_percentage' : (json.data[i].reorder_level_percentage > 0 ? numberFormatPrecision(json.data[i].reorder_level_percentage,4) : "N/A"),
                            'whse_num' : json.data[i].whse_num,
                        });
                    }
                    return result;
                }
            };

            generateTable('ReorderItemReport', arrColumns, ajaxMethod, 'ReorderReport', {serverSide: true, searching: true});
            ref_pass == "dashboard" ? $('div.dt-buttons').css('margin-top','-35.5px') : $('div.dt-buttons').css('margin-top','-54.5px');

            $(".dataTable thead input:not(#mass-chk)").on('keyup', function (e) {
                if (e.keyCode == 13) {
                    $('.dataTable').DataTable().column($(this).parent().index()).search(this.value).draw();
                }
            });
            
        }
        $(window).resize(function (e) {
            $("#ReorderItemReport").DataTable().columns.adjust();
        });
    });
</script>
<style>
    .title-header{
        width:100%;}
    .title{
        padding-top:2px;
        font-size: 22px !important;
        font-weight: bold;}
    .title-division{
        width:50%;
        float:left;}
    .default-button{
        float:right;
        margin-top:-59.4px;
        padding-right:0px;
        position: relative;}
    .button-padding{
        margin:5px 0.5px;}
    .table-content{
        margin-top:15px;}
    .card-block{
        margin-top: -18px;
        padding-right: 0rem;
        padding-left: 0rem;}
    /* Export button */
    div.dt-buttons{
        float: right !important;}
    .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle){
        border-bottom-right-radius: 0.18rem !important;
        border-top-right-radius: 0.18rem !important;}
    .btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
        border-bottom-left-radius: 0.18rem !important;
        border-top-left-radius: 0.18rem !important;}
    div.dt-buttons.btn-group>a.btn-secondary{
        width: 110px !important;
        margin-left: 4px !important;}
    a.btn.btn-success{
        border-color:#4FC3F7;
        background-color:#4FC3F7;}
    label{
        margin-bottom: 0.1rem !important;}
    .margin-header input.form-control.border-primary{
        padding-left:10px;
        height: 25px;}
    select#trans_type.form-control.border-primary{
        padding-left: 3px !important;
        height: 25px;}
    .form-group{
        margin-top:1rem;}
    form td{
        padding: 2px 1px;}
    .table th, .table td{
        padding: 0.2rem 0.2rem !important;}
    div.dataTables_length>label{
        width: 110px !important;}
    button.ColVis_Button>span{
        font-size: 9pt;}
    /* table header */
    table.dataTable thead>tr>th.sorting_asc, table.dataTable thead>tr>th.sorting_desc,
    table.dataTable thead>tr>th.sorting, table.dataTable thead>tr>td.sorting_asc, table.dataTable thead>tr>td.sorting_desc,
    table.dataTable thead>tr>td.sorting div.card-block>table#JobOperHourReport thead>tr:first-child>th,
    div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        padding-right: 25px !important;
    }
    div.card-block>table#ReorderItemReport thead>tr:first-child>th, div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        background-color: rgb(220,220,220);}
    div.dataTables_scrollBody>table>thead>tr.row>th{
        background-color: white;}
    table#datatable.table.table-bordered.table-hover.nowrap.datatable.no-footer td>a>button.btn.btn-primary{
        padding: 0.1rem 0.2rem !important;}
    div.dataTables_wrapper div.dataTables_paginate{
        margin-bottom: 3rem !important;}
    li.paginate_button.page-item.active>a {
        font-size: 9pt;}
    .dt-center {
        text-align: center;}
    .dt-right {
        text-align: right;}
    .dt-buttons {
        padding-bottom: 15px;}
    .dataTables_length {
        float:left;
        padding-top: 0.5em;
        padding-bottom: 5px;}
     div.dataTables_info {
        padding-top: 0.85em;
        position:absolute;}
    div.dataTables_paginate {
        padding-top: 0.85em;
        right:0px;}
</style>

@include('util.selection')
@endsection
