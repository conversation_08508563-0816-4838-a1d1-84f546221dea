<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\API\BaseController as BaseController;
use App\PurchaseOrderItem;
use App\PurchaseOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\PurchaseOrder as PurchaseOrderResource;
use App\MatlTrans;
use App\Vendor;
use App\Item;
use Carbon\Carbon;
use Illuminate\Validation\Rule;
use App\SiteSetting;
use DB;
use App\ItemWarehouse;
use Illuminate\Validation\ValidationException;
use App\Services\GeneralService;

class PurchaseOrderController extends BaseController
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public $sap_integration;

    public function __construct()
    {
        $tparm = new \App\View\TparmView();
        $this->sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $this->middleware(function ($request, $next) {
            $header = $request->header('content-type');
            if ($header != 'application/json') {
                return $this->sendError(__('error.admin.validation_error'), __('error.admin.notexistcontenttype'), 403);
            }

            if ($request->method() !== 'GET' && $request->method() !== 'DELETE') {
                $input = $request->json()->all();
                // dd($input);
                if (empty($input)) {
                    return $this->sendError(__('error.admin.invalid_json'));
                }
            }
            return $next($request);
        });
    }

    public function index(Request $request)
    {
        $tparm = new \App\View\TparmView();
        $this->sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        //        $po_item = PurchaseOrderItem::orderBy('id', 'desc')->paginate(50);
        $site_id = auth()->user()->site_id;
        $query = \App\PurchaseOrderItem::select(
            'po_items.id',
            'po_items.due_date',
            'po_items.po_num',
            'po_items.notes',
            'po_items.vend_do',
            'po_items.po_line',
            'po_items.item_num',
            'po_items.item_desc',
            'po_items.vend_num',
            'po_items.whse_num',
            'po_items.rel_status',
            'po_items.created_date',
            'po_items.modified_date',
            'purchase_orders.id as po_id',
            'purchase_orders.due_date as due_date',
            'purchase_orders.po_status as po_status',
            'purchase_orders.vend_name as vend_name',
            'purchase_orders.erp_ID as erp_ID',
            'purchase_orders.created_by as created_by',
            'purchase_orders.modified_by as modified_by',
            'po_items.uom',
            'po_items.po_rel',
            'po_items.qty_ordered',
            'po_items.qty_ordered_conv',
            'po_items.qty_received',
            'po_items.qty_balance',
            'po_items.qty_picked',
            'po_items.qty_returned',
            'po_items.qty_returnable',
            'po_items.tolerance',
            'po_items.qty_ordered_conv',
            'po_items.site_id',
        )
            ->leftJoin('purchase_orders', 'purchase_orders.po_num', '=', 'po_items.po_num', 'purchase_orders.site_id', '=', 'po_items.site_id')
            ->where('po_items.site_id', $site_id);

        $po_num = \App\PurchaseOrderItem::select('po_num');
        //
        $query2 = DB::query()->from('purchase_orders')->select(
            DB::raw('TO_BASE64(po_num) as id'),
            'due_date',
            'po_num',
            'notes',
            'vend_do',
            'po_rel',
            DB::raw('null as po_line'),
            DB::raw('"" as item_num'),
            DB::raw('"" as item_desc'),
            'vend_num',
            DB::raw('"" as whse_num'),
            DB::raw('po_status as rel_status'),
            'created_date',
            'modified_date'
        )
            ->where('purchase_orders.site_id', $site_id)
            ->whereNotIn('po_num', $po_num);
        //        $query->union($query2);
        $query = $this->filters($query, $request);

        $po_item = $query->paginate(50);

        $arrData = $po_item->toArray();


        for ($i = 1; $i <= 10; $i++) {
            $field = "field_" . $i;
            foreach ($arrData['data'] as $key => $value) {
                if (array_key_exists($field, $arrData['data'][$key])) {
                    unset($arrData['data'][$key][$field]);
                }
            }
        }

        foreach ($arrData['data'] as $key => $value) {
            $arrData['data'][$key]['created_date'] = Carbon::parse($value['created_date'])->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
            $arrData['data'][$key]['modified_date'] = getDateTimeConverted($value['modified_date']);
            // $arrData['data'][$key]['modified_date'] = Carbon::parse($value['modified_date'])->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        }

        return $this->sendResponse($arrData, __('error.admin.retrieved_successfully', ['resource' => 'Purchase Order Lines']), 200);
        //return $this->sendResponsePaged(PurchaseOrderResource::collection($po_item), 'Purchase Order Lines retreived successfully');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {


        $input = $request->json()->all();

        $siteSettings = \App\SiteSetting::first();

        $tparm = new \App\View\TparmView();
        // $item_warehouse_error = $tparm->getTparmValue('PurchaseOrder', 'item_warehouse_error');
        $this->sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        // if (Empty($input)){
        //     return $this->sendError('Invalid request json');
        // }

        $cHeadLines = count($input['line']);
        $AddNewline = $input['type'];

        if ($cHeadLines == 0) {
            return $this->sendError(__('error.admin.validation_error'), 'Line Items cannot be empty.', 400);
        }

        $validator = Validator::make($input, [
            'po_num' => 'required|max:30',
            'po_status' => 'required|in:O,C',
            //'po_notes' => 'nullable|max:300',
            'due_date' => 'required|date_format:"' . $siteSettings->getInputDateFormat() . '"',
            'vend_num' => 'required|max:30|exists:vendors,vend_num,site_id,' . auth()->user()->site_id,
            'vend_do' => 'nullable|max:30',

        ]);
        if ($validator->fails()) {

            return $this->sendError(__('error.admin.validation_error'), $validator->errors(), 400);
        }
        $arrStoreError = array();
        if ($AddNewline != "AddNewLinesOnly") {
            $existingHeader = PurchaseOrder::where('po_num', $input['po_num'])->first();

            if ($existingHeader){
                //throw ValidationException::withMessages(['PO Number ['.$input['co_num'].'] already exists']);
                $arrStoreError = array('po_num' => ['PO Number [' . $input['po_num'] . '] already exists. Please try another PO Number.']);
                //throw ValidationException::withMessages([__('error.admin.qty_released_min', ['resource' => $job->qty_released])]);
                return $this->sendError(__('error.admin.validation_error'), $arrStoreError, 400);
                //return $this->sendError(__('error.admin.validation_error'), 'PO Number [' . $input['po_num'] . '] already exists', 400);
            }
            $Validatorpo = Validator::make($input, [
                'po_num' => [
                    'required',
                    'max:30',
                     Rule::unique('po_items')->where(function ($query) {
                         $query->where('site_id', auth()->user()->site_id);
                     })
                ],
                'po_status' => 'required|in:O,C',
                //'due_date' => 'date_format:"' . $siteSettings->getInputDateFormat() . '"|required',

                'due_date' => 'required|date_format:"' . $siteSettings->getInputDateFormat() . '"',
                'vend_num' => 'required|max:30|exists:vendors,vend_num,site_id,' . auth()->user()->site_id,
                //                        'vend_name' => 'required|max:200',
                'vend_do' => 'nullable|max:30',
                //'po_notes' => 'nullable|max:300',

            ]);

            if ($Validatorpo->fails()) {
                //dd('sss');
                return $this->sendError(__('error.admin.validation_error'), $Validatorpo->errors(), 400);
            }
        }

        //dd($item_warehouse_error);
        if ($this->sap_integration) {
            $validator = Validator::make($input, [
                'erp_ID' => 'required',
            ]);
            if ($validator->fails()) {
                return $this->sendError(__('error.admin.validation_error'), $validator->errors(), 400);
            }
        }
        //dd('jsjsj');
        // PO Header Record

        $vendor_name = Vendor::select('vend_name')->where('vend_num', $input['vend_num'])->value('vend_name');

        $record = array(
            "po_num" => $input['po_num'],
            "erp_ID" => @$input['erp_ID'],
            //            "whse_num" => @$input['whse_num'],
            "vend_num" => @$input['vend_num'],
            "vend_name" => $vendor_name,
            "po_status" => $input['po_status'],
            "vend_do" => @$input['vend_do'],
            "due_date" => $input['due_date'],
            "notes" => @$input['po_notes']
        );

        $valid = true;
        $arrRebuild = array();

        foreach ($input['line'] as $key => $value) {
            $po_num = $request->po_num;
            $po_line = $request->po_line;
            $arrValidator = Validator::make(
                $value,
                array(
                    'uom' => array('required', 'exists:uoms,uom,site_id,' . auth()->user()->site_id),
                    'po_rel' => array(
                        'required',
                        'in:0,1'

                    ),
                    'qty_ordered' => array('required', 'max:10'),
                    'po_line' => array('required', 'max:10',
                    Rule::unique('po_items')->where(function ($query) use ($po_num, $po_line) {
                        $query->where('site_id', auth()->user()->site_id)
                            ->where('po_num', $po_num)
                            ->where('po_line', $po_line);
                    })

                ),
                    'item_num' => array('required', 'exists:items,item_num,site_id,' . auth()->user()->site_id),
                    'whse_num' => array('required', 'exists:warehouses,whse_num,site_id,' . auth()->user()->site_id),
                    'rel_status' => array('required','in:O,C'),
                    //'notes' => 'nullable|max:300',
                    'due_date' => 'required|date_format:"' . $siteSettings->getInputDateFormat() . '"',
                )
            );
            if ($arrValidator->fails()) {
                return $this->sendError(__('error.admin.validation_error'), $arrValidator->errors(), 400);
                break;
            }

            $existingHeaderPoline = PurchaseOrderItem::where('po_num', $input['po_num'])->where('po_line', $value['po_line'])->first();
            //dd($existingHeaderPoline);
            if ($existingHeaderPoline){
                //throw ValidationException::withMessages(['PO Number ['.$input['co_num'].'] already exists']);
                $arrStoreError = array('po_num' => ['PO Number [' . $input['po_num'] . '] and PO Line already exists. Please try another PO Number or PO Line.']);
                //throw ValidationException::withMessages([__('error.admin.qty_released_min', ['resource' => $job->qty_released])]);
                return $this->sendError(__('error.admin.validation_error'), $arrStoreError, 400);
                //return $this->sendError(__('error.admin.validation_error'), 'PO Number [' . $input['po_num'] . '] already exists', 400);
            }

            // if ($item_warehouse_error) {
            //     if (!ItemWarehouse::where('whse_num', $value['whse_num'])->where('item_num', $value['item_num'])->exists()) {
            //         return $this->sendError(__('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $value['whse_num'], 'resource2' => __('admin.label.whse_num')]), 400);
            //         break;
            //     }
            // }

            $arrRebuild[$key]['po_num'] = $input['po_num'];
            $arrRebuild[$key]['erp_ID'] = @$input['erp_ID'];
            $arrRebuild[$key]['po_line'] = $value['po_line'];
            $arrRebuild[$key]['po_rel'] = @$value['po_rel'];
            $arrRebuild[$key]['due_date'] = $value['due_date'];
            $arrRebuild[$key]['whse_num'] = $value['whse_num'];
            $arrRebuild[$key]['item_num'] = $value['item_num'];
            $arrRebuild[$key]['vend_num'] = $input['vend_num'];
            $arrRebuild[$key]['uom'] = $value['uom'];
            $arrRebuild[$key]['notes'] = @$value['notes'];
            $arrRebuild[$key]['rel_status'] = $value['rel_status'];

            $arrRebuild[$key]['qty_ordered'] = $value['qty_ordered'];
            //            $arrRebuild[$key]['qty_received'] = @$value['qty_received'];
            //            $arrRebuild[$key]['qty_returned'] = @$value['qty_returned'];
            // $arrRebuild[$key]['sap_res_inv_id'] = $value['sap_res_inv_id'];
            // $arrRebuild[$key]['sap_res_line_num'] = @$value['sap_res_line_num'];
            // $arrRebuild[$key]['sap_update_time'] = @$value['sap_update_time'];
            // $arrRebuild[$key]['sap_base_type'] = @$value['sap_base_type'];
            // $arrRebuild[$key]['sap_base_entry'] = @$value['sap_base_entry'];
            // $arrRebuild[$key]['sap_po_return_id'] = @$value['sap_po_return_id'];

            $po_num = $input['po_num'];
            $po_line = $value['po_line'];

            // if ($this->sap_integration) {
            //     $validator = Validator::make($value, [
            //                 'sap_res_inv_id' => 'required',
            //                 'sap_res_line_num' => 'required',
            //                 'sap_update_time' => 'required',
            //                 'sap_base_type' => 'required',
            //                 'sap_base_entry' => 'required',
            //     ]);
            //     if ($validator->fails()) {
            //     return $this->sendError('Validation Error.', $arrValidator->errors());
            //     break;
            //     }
            // }
        }
        $po = PurchaseOrder::where('po_num', $record['po_num'])->first();
        if ($AddNewline != "AddNewLinesOnly") {
            $po = PurchaseOrder::create($record);
        } else {
            if ($po == null) {
                $po = PurchaseOrder::create($record);
            }
        }
        // dd($arrRebuild);
        DB::beginTransaction();
        foreach ($arrRebuild as $keyv => $data) {
            try {
                //
                $purchaseOrderItem = PurchaseOrderItem::create($data);

                $arrRebuild[$keyv]['id'] = $purchaseOrderItem->id;

                // validate and auto create item whse
                $autoCreateItemWhse = GeneralService::autoCreateItemWhse($purchaseOrderItem, ['whse_num']);
                if ($autoCreateItemWhse !== "true") {
                    throw ValidationException::withMessages([$autoCreateItemWhse]);
                }
            } catch (Throwable $e) {
                DB::rollback();
                return $this->sendError('Validation Error', $e->getMessage(), 403);
            }
        }
        DB::commit();








        foreach ($arrRebuild as $keyv => $data) {
            $arrRebuild[$keyv]['po_id'] = $po->id;
        }

        $id = $po->id;
        $po = PurchaseOrder::find($id);
        $arrData = $po->toArray();

        for ($i = 1; $i <= 10; $i++) {
            $field = "field_" . $i;
            if (array_key_exists($field, $arrData)) {
                unset($arrData[$field]);
            }
        }

        $arrData['created_date'] = Carbon::parse($po->created_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        // $arrData['modified_date'] = Carbon::parse($po->modified_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        $arrData['po_notes'] =  $arrData['notes'];
        unset($arrData['notes']);
        //dd();
        return $this->sendResponse($arrData, __('admin.message.created_successfully', ['resource' => 'Purchase Order Line']), 201);
        //return $this->sendResponse(new PurchaseOrderResource($arrRebuild), 'Purchase Order Line created successfully.', 200);
    }
    //  Add PO Bulk
    public function postorebulk(Request $request)
    {
        $input = $request->json()->all();

        $tparm = new \App\View\TparmView();
        // $item_warehouse_error = $tparm->getTparmValue('PurchaseOrder', 'item_warehouse_error');

        if (empty($input)) {
            return $this->sendError('Invalid request json');
        }

        // dd("PO Bulk Here");
        $arrinput = $request->all();
        $arrStoreError = array();

        $arrEStore = array();
        $arrPOError = array();
        $arrSucess = array();
        $arrFailed = array();
        $arrPOLineError = array();

        foreach ($arrinput['Records'] as $k => $input) {
            $cHeadLines = count($input['line']);
            $AddNewline = $input['type'];

            if ($cHeadLines == 0) {
                return $this->sendError(__('error.admin.validation_error'), 'Line Items cannot be empty.', 400);
            }

            $validator = Validator::make($input, [

                'po_num' => 'required|max:30',
                'vend_num' => 'required|exists:vendors,vend_num,site_id,' . auth()->user()->site_id
            ]);



            if ($validator->fails()) {
                $arrStoreError[$k][0] = array('err_msg' => $validator->errors(), 'po_num' => $input['po_num']);

                $arrPOError[$input['po_num']][0] = "Po_num cannot empty / vend_num not exist : " . $validator->errors();
                // return $this->sendError('Validation Error.', $validator->errors());
            }
            if ($AddNewline != "AddNewLinesOnly") {
                $existingHeader = PurchaseOrderItem::where('po_num', $input['po_num'])->first();

                if ($existingHeader) {
                    //throw ValidationException::withMessages(['PO Number ['.$input['co_num'].'] already exists']);
                    // return $this->sendError('Validation Error.', 'PO Number [' . $input['po_num'] . '] already exists');
                    $arrStoreError[$k][1] = array('err_msg' => 'PO Number [' . $input['po_num'] . '] already exists.', 'po_num' => $input['po_num']);

                    $arrPOError[$input['po_num']][1] = 'PO Number [' . $input['po_num'] . '] already exists.';
                }
                $siteSettings = new SiteSetting();
                $poValidatior = Validator::make($input, [
                    'po_num' => [
                        'required',
                        'max:30',
                        Rule::unique('po_items')->where(function ($query) {
                            $query->where('site_id', auth()->user()->site_id);
                        })
                    ],
                    'po_status' => 'required',
                    'due_date' => 'date_format:"' . $siteSettings->getInputDateFormat() . '"|required',
                    'vend_num' => 'required|max:30|exists:vendors,vend_num,site_id,' . auth()->user()->site_id,
                    //  'item_num' => array('required', 'exists:items,item_num,site_id,' . auth()->user()->site_id),
                    //                        'vend_name' => 'required|max:200',
                    'vend_do' => 'nullable|max:30',
                    //'notes' => 'nullable|max:300'
                ]);
                if ($poValidatior->fails()) {
                    $arrStoreError[$k][2] = array('err_msg' => $validator->errors(), 'po_num' => $input['po_num']);
                    $arrPOError[$input['po_num']][2] = "Invalid input of the parameters : " . $poValidatior->errors();

                    //$arrPOLineError[$input['co_num']][2][$input['po_line']]  = $input['po_line'];
                    //return $this->sendError('Validation Error.', $validator->errors());
                }
            }


            if ($this->sap_integration) {
                $validator = Validator::make($input, [
                    'erp_ID' => 'required',
                ]);
                if ($validator->fails()) {
                    $arrStoreError[$k][3] = array('err_msg' => $validator->errors(), 'po_num' => $input['po_num']);
                    $arrPOError[$input['po_num']][3] = "erp_ID cannot be empty";
                    //return $this->sendError('Validation Error.', $validator->errors());
                }
            }

            // PO Header Record

            $vendor_name = Vendor::select('vend_name')->where('vend_num', $input['vend_num'])->value('vend_name');

            // Check Item
            // $checkItem = Item::where('item_num',$input['item_num'])->where('site_id',auth()->user->site_id)->exists();
            //dd($input);

            $record = array(
                "po_num" => $input['po_num'],
                "erp_ID" => @$input['erp_ID'],
                //            "whse_num" => @$input['whse_num'],
                "vend_num" => @$input['vend_num'],
                "vend_name" => $vendor_name,
                "po_status" => $input['po_status'],
                "vend_do" => @$input['vend_do'],
                "due_date" => $input['due_date'],
                "notes" => @$input['notes']
            );

            $valid = true;
            $arrRebuild = array();


            foreach ($input['line'] as $key => $value) {
                $arrRebuild[$key]['po_num'] = $input['po_num'];
                $arrRebuild[$key]['erp_ID'] = @$input['erp_ID'];
                $arrRebuild[$key]['po_line'] = $value['po_line'];
                $arrRebuild[$key]['po_rel'] = @$value['po_rel'];
                $arrRebuild[$key]['due_date'] = $value['due_date'];
                $arrRebuild[$key]['whse_num'] = $value['whse_num'];
                $arrRebuild[$key]['item_num'] = $value['item_num'];
                $arrRebuild[$key]['vend_num'] = $input['vend_num'];
                $arrRebuild[$key]['uom'] = $value['uom'];
                $arrRebuild[$key]['notes'] = @$value['notes'];
                $arrRebuild[$key]['rel_status'] = $value['rel_status'];

                $arrRebuild[$key]['qty_ordered'] = $value['qty_ordered'];
                //            $arrRebuild[$key]['qty_received'] = @$value['qty_received'];
                //            $arrRebuild[$key]['qty_returned'] = @$value['qty_returned'];
                // $arrRebuild[$key]['sap_res_inv_id'] = $value['sap_res_inv_id'];
                // $arrRebuild[$key]['sap_res_line_num'] = @$value['sap_res_line_num'];
                // $arrRebuild[$key]['sap_update_time'] = @$value['sap_update_time'];
                // $arrRebuild[$key]['sap_base_type'] = @$value['sap_base_type'];
                // $arrRebuild[$key]['sap_base_entry'] = @$value['sap_base_entry'];
                // $arrRebuild[$key]['sap_po_return_id'] = @$value['sap_po_return_id'];

                $po_num = $input['po_num'];
                $po_line = $value['po_line'];
                $arrValidator = Validator::make(
                    array(
                        'uom' => $value['uom'],
                        'po_rel' => @$value['po_rel'],
                        'po_line' => $value['po_line'],
                        'item_num' => $value['item_num'],
                        'whse_num' => $value['whse_num'],
                        'qty_ordered' => $value['qty_ordered'],
                        //                                'qty_returned' => $value['qty_returned'],
                        'rel_status' => $value['rel_status'],
                    ),
                    array(
                        'uom' => array('required', 'exists:uoms,uom,site_id,' . auth()->user()->site_id),
                        'po_rel' => array(
                            'sometimes',
                            'max:1',
                            Rule::unique('po_items')->where(function ($query) use ($po_num, $po_line) {
                                $query->where('site_id', auth()->user()->site_id)
                                    ->where('po_num', $po_num)
                                    ->where('po_line', $po_line);
                            })
                        ),
                        'qty_ordered' => array('required'),
                        'po_line' => array('required', 'max:30'),
                        'item_num' => array('required', 'exists:items,item_num,site_id,' . auth()->user()->site_id),
                        'whse_num' => array('required', 'exists:warehouses,whse_num,site_id,' . auth()->user()->site_id),
                        'rel_status' => array('required'),
                    )
                );
                if ($arrValidator->fails()) {

                    $arrStoreError[$k][4] = array('err_msg' => $validator->errors(), 'po_num' => $input['po_num']);
                    $arrPOError[$input['po_num']][4] = "Invalid input of the parameters." . $arrValidator->errors();
                    $arrPOLineError[$input['po_num']][4][$po_line]  = $po_line;
                    // return $this->sendError('Validation Error.', $arrValidator->errors());
                    // break;
                }

                // if ($item_warehouse_error) {
                //     if (!ItemWarehouse::where('whse_num', $value['whse_num'])->where('item_num', $value['item_num'])->exists()) {
                //         $error_msg = __('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $value['whse_num'], 'resource2' => __('admin.label.whse_num')]);
                //         $arrStoreError[$k][5] = array('err_msg' => $error_msg, 'po_num' => $input['po_num']);
                //         $arrPOError[$input['po_num']][5] = "Invalid input of the parameters." . $error_msg;
                //         $arrPOLineError[$input['po_num']][5][$po_line]  = $po_line;
                //     }
                // }
                // if ($this->sap_integration) {
                //     $validator = Validator::make($value, [
                //                 'sap_res_inv_id' => 'required',
                //                 'sap_res_line_num' => 'required',
                //                 'sap_update_time' => 'required',
                //                 'sap_base_type' => 'required',
                //                 'sap_base_entry' => 'required',
                //     ]);
                //     if ($validator->fails()) {
                //     return $this->sendError('Validation Error.', $arrValidator->errors());
                //     break;
                //     }
                // }

                $cError = count($arrStoreError);

                $storePONumError = array();
                if ($cError > 0) {
                    foreach ($arrStoreError as $key => $dt) {
                        foreach ($dt as $k2 => $v) {

                            $arrEStore[$v['po_num']] = $v['po_num'];
                            // $arrSucess[$input['po_num']]['po_num'] = $v['po_num'];

                        }
                    }
                }
            }
            // dd($arrRebuild,$arrFailed);

            foreach ($arrRebuild as $keyv => $data) {

                if (!array_key_exists($data['po_num'], $arrEStore)) {
                    try {
                        //
                        $purchaseOrderItem = PurchaseOrderItem::create($data);
                        $arrRebuild[$keyv]['id'] = $purchaseOrderItem->id;
                        $arrinput['Records'][$k][$keyv]['id']  = $purchaseOrderItem->id;
                        $arrSucess[$input['po_num']]['po_num'] = $input['po_num'];
                        $arrSucess[$input['po_num']]['erp_ID'] = $input['erp_ID'];

                        $arrSucess[$input['po_num']]['po_line'][$keyv] = $data['po_line'];

                        $arrSucess[$input['po_num']]['data'][$keyv] = $purchaseOrderItem->id;
                        $arrSucess[$input['po_num']]['message'] = "Purchase Order Line created successfully";
                    } catch (\Illuminate\Database\QueryException $e) {
                        return $this->sendError('Database Error', $e->getMessage(), 403);
                    }
                } else {
                    $arrFailed[$input['po_num']]['po_num'] = $data['po_num'];
                    $arrFailed[$input['po_num']]['erp_ID'] = $data['erp_ID'];
                    $arrFailed[$input['po_num']]['data'] = $arrPOError[$data['po_num']];
                    //$arrFailed[$input['po_num']]['po_line'] = $arrPOLineError[$data['po_num']] ?? null;
                    $arrFailed[$input['po_num']]['po_line'][$keyv] = $data['po_line'];
                    $arrFailed[$input['po_num']]['message'] = "Validation Error";
                }
            }
            if ($AddNewline != "AddNewLinesOnly") {

                if (!array_key_exists($record['po_num'], $arrEStore)) {
                    //  $arrSucess[$input['po_num']] = $input['po_num'];
                    $po = PurchaseOrder::create($record);
                }
            }
            // $po = PurchaseOrder::where('po_num', $input['po_num'])->first();

            // foreach ($arrSucess as $keyv => $data) {
            //     $arrSucess[$keyv]['po_id'] = $po->id;
            // }

            // $poID =  PurchaseOrder::select('id')
            // ->where('po_num', $input['po_num'])
            // ->value('id');

            if (count($arrSucess ?? []) > 0) {
                foreach ($arrSucess as $key => $v) {
                    $poID =  PurchaseOrder::select('id')
                        ->where('po_num',  $key)
                        ->value('id');

                    $arrSucess[$key]['po_id'] = $poID ?? $key;
                }
            }



            //$arrSucess[$input['po_num']]['po_id'] = $poID ;



        }

        if (count($arrEStore) > 0) {
            $msg = $arrPOError;
            // $msg = 'Purchase Order Line created successfully and some PO num may not sucess to insert : ['.implode(",",@$arrEStore).' ]';
        } else {
            $msg = 'Purchase Order Line created successfully';
        }


        $arrData = array("response" => array(
            'Success' => $arrSucess,
            'Failed' => $arrFailed

        ));

        return $arrData;
    }








    public function updateNew(Request $request, $id)
    {

        $tparm = new \App\View\TparmView();
        // $item_warehouse_error = $tparm->getTparmValue('PurchaseOrder', 'item_warehouse_error');
        $podetails = PurchaseOrderItem::where('po_num', $po_num)->where('po_line', $po_line)->where('po_rel', $po_rel)->first();
        if (is_null($podetails)) {
            return $this->sendResponse(__('error.admin.validation_error'), __('error.admin.not_found', ['resource' => 'You are unable to update this purchase order line, because the purchase order lineID ']), 404);
        }

        $input = $request->all();
        $arrUpdate = array();
        $arrValidator = Validator::make(
            $input,
            array(
                'item_num' => array('required'),
                'qty_ordered' => array('required'),
                'rel_status' => array('required'),
                'due_date' => array('required'),
            )
        );

        // if ($item_warehouse_error) {
        //     if (!ItemWarehouse::where('whse_num', $input['whse_num'])->where('item_num', $input['item_num'])->exists()) {
        //         $error_msg = __('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $input['whse_num'], 'resource2' => __('admin.label.whse_num')]);
        //         // $arrStoreError[$k][5] = array('err_msgh' => $error_msg, 'po_num' => $input['po_num']);
        //         // $arrPOError[$input['po_num']][5] = "Invalid input of the parameters." . $error_msg;
        //         // $arrPOLineError[$input['po_num']][5][$po_line]  = $po_line;

        //         return $this->sendError($error_msg , 400);
        //     }
        // }
        if ($arrValidator->fails()) {
            return $this->sendError(__('error.admin.validation_error'), $arrValidator->errors(), 400);
        }

        $record = $request->except('_token');
        // Event::dispatch(new PurchaseOrderEvent($record,'updating'));
        // Keep previous PO Line Status before update.
        $prev_po_line_status = $podetails->rel_status;

        $podetails->fill($record);

        DB::beginTransaction();
        try {
            $podetails->save();

            // validate and auto create item whse
            $autoCreateItemWhse = GeneralService::autoCreateItemWhse($podetails, ['whse_num']);
            if ($autoCreateItemWhse !== "true") {
                throw ValidationException::withMessages([$autoCreateItemWhse]);
            }
        } catch (Throwable $e) {
            DB::rollback();
            return $this->sendError('Validation Error', $e->getMessage(), 403);
        }
        DB::commit();
        // Event::dispatch(new PurchaseOrderEvent($request,'updated'));
        // Record status change to value change log
        if ($prev_po_line_status != $podetails->rel_status) {
            OverrideQtyService::newOverRideHistory(
                __('admin.label.po_line'),
                'Status',
                $prev_po_line_status,
                $podetails->rel_status,
                $podetails->po_num,
                $podetails->po_line,
                null
            );
        }



        $arrData = $podetails->toArray();

        for ($i = 1; $i <= 10; $i++) {
            $field = "field_" . $i;
            if (array_key_exists($field, $arrData)) {
                unset($arrData[$field]);
            }
        }

        $arrData['created_date'] = Carbon::parse($podetails->created_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        // $arrData['modified_date'] = Carbon::parse($podetails->modified_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');

        return $this->sendResponse($arrData, 'Purchase Order updated successfully.', 200);
        //return $this->sendResponse($cPurchaseOrderItem, 'Purchase Order updated successfully.');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\PurchaseOrderItem  $purchaseOrderItem
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $PurchaseOrders = PurchaseOrderItem::find($id);
        if (is_null($PurchaseOrders)) {
            return $this->sendResponse(__('error.admin.validation_error'), __('error.admin.not_found', ['resource' => 'Purchase Order Line']), 404);
        }

        $arrData = $PurchaseOrders->toArray();

        if (@$PurchaseOrders['po_num'] != null) {
            $po_id = PurchaseOrder::select('id')->where('po_num', @$PurchaseOrders['po_num'])->value('po_id');
            $arrData['po_id'] = $po_id;
        }

        if (@$PurchaseOrders['po_num'] != null) {
            $po_status = PurchaseOrder::select('po_status')->where('po_num', @$PurchaseOrders['po_num'])->value('po_status');
            $arrData['po_status'] = $po_status;
        }

        for ($i = 1; $i <= 10; $i++) {
            $field = "field_" . $i;
            if (array_key_exists($field, $arrData)) {
                unset($arrData[$field]);
            }
        }

        $arrData['created_date'] = Carbon::parse($PurchaseOrders->created_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        $arrData['modified_date'] = getDateTimeConverted($PurchaseOrders->modified_date);
        // $arrData['modified_date'] = Carbon::parse($PurchaseOrders->modified_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');

        return $this->sendResponse($arrData, __('error.admin.retrieved_successfully', ['resource' => 'Purchase Order Line']), 200);
        //return $this->sendResponse(new PurchaseOrderResource($PurchaseOrders), 'purchase order line retrieved successfully.');
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\PurchaseOrderItem  $purchaseOrderItem
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $tparm = new \App\View\TparmView();
        $this->sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        $tparm = new \App\View\TparmView();
        // $item_warehouse_error = $tparm->getTparmValue('PurchaseOrder', 'item_warehouse_error');
        $cPurchaseOrderItem = \App\PurchaseOrderItem::find($id);
        if (is_null($cPurchaseOrderItem)) {
            return $this->sendResponse(__('error.admin.validation_error'), __('error.admin.not_found', ['resource' => 'Purchase Order Line ']), 404);
        }
        //dd($cPurchaseOrderItem);
        // $input = $request->all();

        $input = $request->except('qty_received', 'qty_returned', 'qty_balance');
        $siteSettings = new SiteSetting();

        // $input['item_num']= str_replace(' ','%20',$input['item_num']);
        // $input['item_num'] = str_replace('#','%23',$input['item_num']);

        if ($input['item_num'] == "" || $input['item_num'] == null) {

            if (isset($input['notes'])) {
                $data['notes'] = $input['notes'];
            }
            $data['qty_ordered'] = $input['qty_ordered'];
            $data['due_date'] = $input['due_date'];
            $data['rel_status'] = $input['rel_status'];

            if ($this->sap_integration) {
                // $validator = Validator::make($value, [
                //             'sap_res_inv_id' => 'required',
                //             'sap_res_line_num' => 'required',
                //             'sap_update_time' => 'required',
                //             'sap_base_type' => 'required',
                //             'sap_base_entry' => 'required',
                // ]);
                // if ($validator->fails()) {
                // return $this->sendError('Validation Error.', $validator->errors());
                // break;
                // }
            }

            $arrValidator = Validator::make(
                $input,
                array(
                    'qty_ordered' => array('required'),
                    'rel_status' => array('required'),
                    'due_date' => 'date_format:"' . $siteSettings->getInputDateFormat() . '"|required',
                )
            );
            if ($arrValidator->fails()) {
                return $this->sendError(__('error.admin.validation_error'), $arrValidator->errors(), 400);
            }
        } else {
            if ($this->sap_integration) {
                // $validator = Validator::make($value, [
                //             'sap_res_inv_id' => 'required',
                //             'sap_res_line_num' => 'required',
                //             'sap_update_time' => 'required',
                //             'sap_base_type' => 'required',
                //             'sap_base_entry' => 'required',
                // ]);
                // if ($validator->fails()) {
                // return $this->sendError('Validation Error.', $validator->errors());
                // break;
                // }
            }
            $arrValidator = Validator::make(
                $input,
                array(
                    'item_num' => array('required'),
                    'qty_ordered' => array('required'),
                    'rel_status' => array('required'),
                    'due_date' => 'date_format:"' . $siteSettings->getInputDateFormat() . '"|required',
                )
            );
            if ($arrValidator->fails()) {
                return $this->sendError(__('error.admin.validation_error'), $arrValidator->errors(), 400);
            }
            $dataDetails = $cPurchaseOrderItem->toArray();
            //dd($dataDetails);
            if ($dataDetails['qty_received'] == 0 && $dataDetails['rel_status'] == "O") {
                $ItemDetails = Item::where('item_num', $input['item_num'])->first();
                if ($ItemDetails == null) {
                    return $this->sendError('Validation Error. Item not exists', 404);
                }
                // dd($ItemDetails->uom);
                // Fullfill requirements to change Item
                $data['item_num'] = $input['item_num'];
                //$data['uom'] = $ItemDetails->uom;
                $data['uom']           = $input['uom'];
                $data['item_desc'] = $ItemDetails->item_desc;
                $data['whse_num'] = $input['whse_num'];
                if (isset($input['po_notes'])) {
                    $data['notes'] = $input['po_notes'];
                }
                $data['qty_ordered'] = $input['qty_ordered'];
                $data['due_date'] = $input['due_date'];
                $data['rel_status'] = $input['rel_status'];
            } else {
                return $this->sendError('Validation Error. Item cannot be changed.', 400);
            }
        }

        // if ($item_warehouse_error) {
        //     if (!ItemWarehouse::where('whse_num', $input['whse_num'])->where('item_num', $input['item_num'])->exists()) {
        //         $error_msg = __('error.admin.notexistin', ['resource1' => __('admin.label.item_num'), 'name' => $input['whse_num'], 'resource2' => __('admin.label.whse_num')]);
        //         // $arrStoreError[$k][5] = array('err_msgh' => $error_msg, 'po_num' => $input['po_num']);
        //         // $arrPOError[$input['po_num']][5] = "Invalid input of the parameters." . $error_msg;
        //         // $arrPOLineError[$input['po_num']][5][$po_line]  = $po_line;

        //         return $this->sendError($error_msg , 400);
        //     }
        // }




        //        try {
        $prev_po_line_status = $cPurchaseOrderItem->rel_status;
        // dd($data);

        DB::beginTransaction();
        try {
            //
            $cPurchaseOrderItem->update($data);

            // validate and auto create item whse
            $autoCreateItemWhse = GeneralService::autoCreateItemWhse($cPurchaseOrderItem, ['whse_num']);
            if ($autoCreateItemWhse !== "true") {
                throw ValidationException::withMessages([$autoCreateItemWhse]);
            }
        } catch (Throwable $e) {
            DB::rollback();
            return $this->sendError('Validation Error', $e->getMessage(), 403);
        }
        DB::commit();

        if ($prev_po_line_status != $cPurchaseOrderItem->rel_status) {
            OverrideQtyService::newOverRideHistory(
                __('admin.label.po_line'),
                'Status',
                $prev_po_line_status,
                $cPurchaseOrderItem->rel_status,
                $cPurchaseOrderItem->po_num,
                $cPurchaseOrderItem->po_line,
                null
            );
        }
        //        } catch (\Illuminate\Database\QueryException $e) {
        //            return $this->sendError('Database Error', $e->getMessage(), 422);
        //        }

        $arrData = $cPurchaseOrderItem->toArray();

        for ($i = 1; $i <= 10; $i++) {
            $field = "field_" . $i;
            if (array_key_exists($field, $arrData)) {
                unset($arrData[$field]);
            }
        }

        $arrData['created_date'] = Carbon::parse($cPurchaseOrderItem->created_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        $arrData['modified_date'] = getDateTimeConverted($cPurchaseOrderItem->modified_date);
        // $arrData['modified_date'] = Carbon::parse($cPurchaseOrderItem->modified_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');

        return $this->sendResponse($arrData, __('admin.message.updated_successfully', ['resource' => 'Purchase Order']), 200);
        //return $this->sendResponse($cPurchaseOrderItem, 'Purchase Order updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\PurchaseOrderItem  $purchaseOrderItem
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //        $purchaseOrderItem = PurchaseOrderItem::find($id);
        //        if (is_null($purchaseOrderItem)) {
        //            return $this->sendResponse([], 'Purchase Order Line not Found.');
        //        }

        $purchaseOrderItem = PurchaseOrderItem::find($id);
        if (is_null($purchaseOrderItem)) {
            return $this->sendResponse(__('error.admin.validation_error'), __('error.admin.not_found', ['resource' => 'You are unable to delete this purchase order line, because the purchase order line ID ']), 404);
        }
        $matlTrans = MatlTrans::where('ref_num', $purchaseOrderItem->po_num)->where('ref_line', $purchaseOrderItem->po_line)->where('ref_release', $purchaseOrderItem->po_rel)->first();

        if ((@$purchaseOrderItem->qty_shipped <> 0) || (@$purchaseOrderItem->qty_returned <> 0))
            return $this->sendError('Permission Error', "Purchase Order Qty Shipped or Qty Returned is not 0.", 403);
        if ($purchaseOrderItem->rel_status == 'C')
            return $this->sendError('Permission Error', "Purchase Order Line status is Completed.", 403);
        if ($matlTrans)
            return $this->sendError('Permission Error', "Purchase Order Line has associated Material Transaction", 403);

        $arrData = $purchaseOrderItem->toArray();

        if (@$PurchaseOrders['po_num'] != null) {
            $erp_ID = PurchaseOrder::select('erp_ID')->where('po_num', @$PurchaseOrders['po_num'])->value('erp_ID');
            $arrData['erp_ID'] = $erp_ID;
        }

        if (@$PurchaseOrders['po_num'] != null) {
            $po_rel = PurchaseOrderItem::select('po_rel')->where('po_num', @$PurchaseOrders['po_num'])->value('po_rel');
            $arrData['po_rel'] = $po_rel;
        }

        $arrData['created_date'] = Carbon::parse($purchaseOrderItem->created_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');
        $arrData['modified_date'] = getDateTimeConverted($purchaseOrderItem->modified_date);
        // $arrData['modified_date'] = Carbon::parse($purchaseOrderItem->modified_date)->format(SiteSetting::getOutputDateFormat() . ' H:i:s');

        $purchaseOrderItem->delete();

        for ($i = 1; $i <= 10; $i++) {
            $field = "field_" . $i;
            if (array_key_exists($field, $arrData)) {
                unset($arrData[$field]);
            }
        }

        return $this->sendResponse($arrData, __('admin.message.deleted_successfully', ['resource' => 'Purchase Order Line']), 200);
        //return $this->sendResponse(new PurchaseOrderResource($purchaseOrderItem), 'Purchase Order Line deleted successfully.');
    }

    public function getAllPOByponum($ponum)
    {
        $ponum = base64_decode($ponum);


        $getPoDetails = PurchaseOrder::where('po_num', $ponum)->with("po_items:id,erp_ID,po_num,po_line,po_rel,item_num,item_desc,created_date,modified_date,site_id")->get()->makehidden(['field_1', 'field_2', 'field_3', 'field_4', 'field_5', 'field_6', 'field_7', 'field_8', 'field_9', 'field_10']);

        $arrData = array();
        if (is_null($getPoDetails)) {
            return $this->sendResponse([], __('error.admin.not_found', ['resource' => 'Purchase Order']), 404);
        }

        return $this->sendResponse(new PurchaseOrderResource($getPoDetails), 'Purchase Order Details retrieve successfully.');
    }


    public function filters($query, $request)
    {

        if (isset($request->po_num)) {
            $query->where('po_items.po_num', "LIKE", $request->po_num);
        }
        if (isset($request->vend_num)) {
            $query->where('po_items.vend_num', "LIKE", $request->vend_num);
        }
        if (isset($request->whse_num)) {
            $query->where('po_items.whse_num', "LIKE", $request->whse_num);
        }
        if (isset($request->item_desc)) {
            $query->where('po_items.item_desc', "LIKE", $request->item_desc);
        }
        if (isset($request->item_num)) {
            $query->where('po_items.item_num', "LIKE", $request->item_num);
        }
        if (isset($request->due_date)) {
            $query->where('po_items.due_date', $request->due_date);
        }
        if (isset($request->rel_status)) {
            $query->where('po_items.rel_status', $request->rel_status);
        }
        if (isset($request->po_line)) {
            $query->where('po_items.po_line', $request->po_line);
        }
        if (isset($request->created_date)) {
            $query->where('po_items.created_date', $request->created_date);
        }
        if (isset($request->modified_date)) {
            $query->where('po_items.modified_date', $request->modified_date);
        }
        $query = $this->dateFilters($query, $request, "po_items");

        return $query;
    }
}
