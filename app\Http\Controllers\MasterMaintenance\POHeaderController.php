<?php

namespace App\Http\Controllers\MasterMaintenance;

use App\Item;
use Throwable;
use App\Vendor;
use App\PurchaseOrderItem;
use App\SiteSetting;
use App\PurchaseOrder;
use App\ImportExportLog;
use App\Imports\POImport;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Services\ImportService;
use App\Services\OverrideQtyService;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Requests\FileImportRequest;
use Illuminate\Validation\ValidationException;
use App\DataTables\Master\PurchaseOrderDataTable;
use App\Http\Requests\PurchaseOrderHeaderRequest;
use Yajra\DataTables\Facades\DataTables;
use App\View\TparmView;

use App\OrderNumberDefinition;
use App\Services\OrderNumberDefinitionService;

use Illuminate\Support\Facades\Event;
use App\Events\PurchaseOrderHeaderEvent;
use App\MatlTrans;
use Illuminate\Support\Facades\Lang;
use App\Traits\GetAddLineButton;

use App\Services\SapCallService;
use App\Services\SapApiCallService;
use Alert;
use App\helpers;

class POHeaderController extends Controller
{
    private $name = 'POHeader';
    private $table = 'purchase_orders';

    use \App\Traits\HasCustomFields;
    use GetAddLineButton;

    public function __construct()
    {

        $this->middleware('auth',['except' => ['exist','pov2']]);
        $this->middleware('can:hasPOMaintenance',['except' => ['exist','pov2']]);
    }

    public function import(FileImportRequest $request){

        $errorarray = array();
        try{
            $import = new POImport;
            $import->import(request()->file('file'));
        } catch (ValidationException $e) {
            foreach ($e->errors() as $error){
                array_push($errorarray,$error[0]);
            }
        } catch (Throwable $e) {
            array_push($errorarray,$e->getMessage());
        }

        $rowcount = $import->getRowCount();

        if (!empty($errorarray)) {
            $url = ImportService::logFailedImport($this->name, $errorarray, $request->file('file')->getClientOriginalName(),$rowcount);
            return back()->with('errormsg', 'Import failed. ' . '<a href="' . $url . '" target="_new">View Log file</a>.');
        } else {
            ImportExportLog::logImportSuccess($this->name, $request->file('file')->getClientOriginalName(),$rowcount);
            return back()->with('successmsg', __('success.imported',  ['rows' => $rowcount]));
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(PurchaseOrderDataTable $purchaseOrderDataTable)
    {
        return view('MasterMaintenance.po.index');
        //return $purchaseOrderDataTable->render('MasterMaintenance.po.table');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $site_id = auth()->user()->site_id;
        $order_type = 'PO';
        $co_num_generate = OrderNumberDefinitionService::getGenerateCode($order_type,$site_id);
        $prefix = OrderNumberDefinition::select('prefix_define')->where('status','Y')->where('order_type','PO')->first();

        if($prefix==NULL)
        {
            $prefix['prefix_define']="";
        }
        else{
         @$prefix = $prefix->toArray();
        }

        $vendor = new Vendor();
        $activeVendors = $vendor->active()->get();
        if ($activeVendors->isEmpty()) {
            return redirect(route('PO'))->with('errormsg', 'Can\'t Create PO. No active vendors available');
        }

        $item = new Item();
        $activeItems = $item->getAllActiveSiteItems();
        if ($activeItems->isEmpty()) {
            return redirect(route('PO'))->with('errormsg', 'Can\'t Create PO. No active items available');
        }

        else {
            $customFields = $this->getCustomFields();
            return view('MasterMaintenance.po.add')->with('customFields', $customFields)->with('PONum',$co_num_generate)->with('prefix_define',@$prefix['prefix_define']);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(PurchaseOrderHeaderRequest $request)
    {
        $request = validateSansentiveValue($request);
        $record = $request->validated();
        $prefix = OrderNumberDefinition::select('prefix_define')->where('status', 'Y')->where('order_type', 'PO')->first();

        if ($prefix == NULL) {
            @$prefix == "";
        } else {
            @$prefix = $prefix->toArray();
        }
        //dd($request,$record);

        $existingHeader = PurchaseOrder::where('po_num', $request->po_num)->first();
        if ($existingHeader)
            throw ValidationException::withMessages(['PO Number ['.$request->po_num.'] already exists']);

        $record = $request->except('_token', 'prefix_define', 'action');

       // dd($record);
        $po = PurchaseOrder::create($record);
        session()->put('stored_po_id',$po->id);

        //session()->put('stored_po_id',$po->id);


        // $prefix = $request->prefix_define;
        $conum  = $request->po_num;
        $prefix = @$prefix['prefix_define'];

        if ($request->action == 'save_and_add'){
            $site_id = auth()->user()->site_id;
            $order_type = 'PO';
            $co_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type,$site_id,$prefix,$conum);
            return redirect()->route('addPO', $po->id)->with('prefix',$request->prefix_define)->with('po_num',$request->po_num);
        }
        else{
            $site_id = auth()->user()->site_id;
            $order_type = 'PO';
            $co_num_generate = OrderNumberDefinitionService::getGenerateCodeSucess($order_type,$site_id,$prefix,$conum);
            return redirect()->route('PO')->with('successmsg', __('success.addedv2',['resource' => __('Purchase Order'), 'name' => $po->name]));
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show(PurchaseOrder $po)
    {
        $customFields = $this->getCustomFields();
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $tparmSAP = new TparmView;
        $sap_trans_order_integration = $tparmSAP->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly = "";
        if($sap_trans_order_integration==1 && auth()->user()->name!="sa")
        {
            $sap_readonly = "disabled";
        }


        if($po->po_status == 'C')
        {
            $status_disabled  = "disabled";
            $tooltips = "tooltip";
            $title = Lang::get('admin.tooltip.showaddline');
            $tooltipsdeletemsg = __('admin.tooltip.deleteaddline');
            $tooltipseditmsg = __('admin.tooltip.editaddline');
            $isDisabled = "disabled";


        }
        else{
           $status_disabled  = "";
            $tooltips = null;
            $title = null;
            $tooltipsdeletemsg =null;
            $tooltipseditmsg ="";
            $isDisabled = "";
        }
        $buttonName = Lang::get('admin.button.add_po_line');

        $route = route('addPO', ['purchase_order' => $po->id, 'view' => base64_encode($po->po_num)]);
        $AddNewLineButton = $this->getAddLineButton($sap_readonly, $route,$status_disabled,$tooltips,$buttonName,$title);

        return view('MasterMaintenance.po.view',compact('AddNewLineButton'))->with('isDisabled',$isDisabled)->with('tooltipsdeletemsg',$tooltipsdeletemsg)->with('tooltipseditmsg',$tooltipseditmsg)->with('status_disabled',$status_disabled)->with('tooltips',$tooltips)->with('status_disabled',$status_disabled)->with('sap_readonly',$sap_readonly)->with('details',$po)->with('customFields', $customFields)->with('unit_quantity_format',$unit_quantity_format);
    }

    public function showByPoNum($po_num)
    {


        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');


        $tparmSAP = new TparmView;
        $sap_trans_order_integration = $tparmSAP->getTparmValue('System', 'sap_trans_order_integration');
        $sap_readonly = "";
        if($sap_trans_order_integration==1 && auth()->user()->name!="sa")
        {
            $sap_readonly = "disabled";
        }


        if ($this->is_base64_string($po_num)){
            $po_num = base64_decode($po_num);
            $po_num = htmlspecialchars_decode($po_num);
        }
        else{
            $po_num = utf8_encode(base64_decode($po_num));
            $po_num = htmlspecialchars_decode($po_num);
        }

        // $po_num = base64_decode($po_num);
        // dd($po_num);
        if (request()->ajax()) {
            $poItem = PurchaseOrderItem::where('po_num', $po_num)->orderby('po_line')->orderby('po_rel')->get();
            $result = DataTables::of($poItem)->make(true);
            return $result;
        }

        $po = PurchaseOrder::where('po_num', $po_num)->firstOrFail();
        // If no PO, create one
        // dd($po);
        if (!$po) {
            // $po = PurchaseOrderItem::createHeader($po_num);
        }
        // PO item doesnt exist
        if (!$po){
            return redirect()->route('po.add');
        }
        else {
            $customFields = $this->getCustomFields();

        if($po->po_status == 'C')
        {
            $status_disabled  = "disabled";
            $tooltips = "tooltip";
            $title = Lang::get('admin.tooltip.showaddline');
            $tooltipsdeletemsg = __('admin.tooltip.deleteaddline');
            $tooltipseditmsg = __('admin.tooltip.editaddline');
            $isDisabled = "disabled";;

        }
        else{
           $status_disabled  = "";
            $tooltips = null;
            $title = null;
            $tooltipsdeletemsg =null;
            $tooltipseditmsg ="";
            $isDisabled = "";
        }
        $buttonName = Lang::get('admin.button.add_po_line');

        $route = route('addPO', ['purchase_order' => $po->id, 'view' => base64_encode($po->po_num)]);
        $AddNewLineButton = $this->getAddLineButton($sap_readonly, $route,$status_disabled,$tooltips,$buttonName,$title);




            //dd($customFields);
            return view('MasterMaintenance.po.view',compact('AddNewLineButton'))->with('isDisabled',$isDisabled)->with('tooltipsdeletemsg',$tooltipsdeletemsg)->with('tooltipseditmsg',$tooltipseditmsg)->with('status_disabled',$status_disabled)->with('tooltips',$tooltips)->with('status_disabled',$status_disabled)->with('sap_readonly',$sap_readonly)->with('details', $po)->with('customFields', $customFields)->with('unit_quantity_format',$unit_quantity_format);
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit(PurchaseOrder $po)
    {
        $customFields = $this->getCustomFields();
        $poItem = PurchaseOrderItem::where('po_num', $po->po_num)->get()->sum('qty_received');

        return view('MasterMaintenance.po.edit')->with('details',$po)->with('po_item', $poItem)->with('customFields', $customFields);
    }

      /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param  int  $id
     * @return Response
     */
    public function update(Request $request, PurchaseOrder $po)
    {
        // $request['due_date'] = date(SiteSetting::getInputDateFormat(), strtotime($request->due_date));
        // dd($request);
        // $po = PurchaseOrder::where('id', $id)->first();
        // // dd($id);
        // if (!$po) {
        //     return redirect(route('vendor'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.vend_num'), 'resource1' => __('admin.button.update'), 'resource2' => $id]));
        // }

       
        $record = $request->except('_token', 'po_num');
        // Event::dispatch(new PurchaseOrderHeaderEvent($record,'update'));
        //dd($record);
        // Keep previous PO Header Status before update.
        $prev_po_status = $po->po_status;

        // Check this PO wheter has transaction or not in Maltran
       // dd($po->po_num);
        $getMaltranResult = MatlTrans::where('ref_num',$po->po_num)->where(function($query) {
			$query->where('trans_type','PO Receipt')
						->orWhere('trans_type','PO Return');
            })->exists();


        if($getMaltranResult==false)
        {
            $getVendName = Vendor::select('vend_name')->where('vend_num',$request->vend_num)->value('vend_name');

            //dd($getVendName);


            DB::table('po_items')->where('po_num', $request->po_num)->update([
                'vend_num' => $request->vend_num,
                'vend_name' => $getVendName,
            ]);

            $record['vend_name'] = $getVendName ?? null;
        }


        $po->update($record);
        $po->save();  // Observe: PurchaseOrderHeaderObserver updated()

        // Record status change to value change log
        if ($prev_po_status != $po->po_status) {
            OverrideQtyService::newOverRideHistory(config('icapt.form.po'), 'Status',
            $prev_po_status,
            $po->po_status,
            $po->po_num,
            null,
            null);
        }

        // SAP Intergration Refer to Observer

         $tparm = new TparmView;
        $sap_trans_order_integration= $tparm->getTparmValue('System', 'sap_trans_order_integration');
        if($request->status=='C' && $sap_trans_order_integration==1){

            $type = "PO";
            $arrConfigType   = getPostNotificationToSAP($type);
            $U_DocType       = $arrConfigType['U_DocType'];
            $post_from       = $arrConfigType['post_from'];
            $sap_service     = $arrConfigType['sap_service'];
            $process_name    = $arrConfigType['process_name'];
            $service_name    = $arrConfigType['service_name'];            
            
            $arrData = array(
                "U_DocType" => $U_DocType,
                "U_DocEntry" => $po->erp_ID ?? null,
                "U_DocNum" => $po->po_num ?? null ,
                "U_ItemCode" => @$po->item_num ?? null,
                "U_LineNum" =>@$po->po_line ?? null,
                "U_Qty" =>  0,
                "U_WhsCode" => @$po->from_whse ?? null,
                "U_ToWhsCode" => @$po->to_whse ?? null,
                "U_ManualClosed" =>'Y',
                "id" => @$po->id ?? null,
                "process_name" => $process_name,
                "post_from" => $post_from,
                "service_name" => $service_name
             );
             //dd($arrData);
             // 1 = Send Close API ; 0= Just Send Notification
             $result = SapCallService::postNotificationAndCloseAction($arrData,1);
            if($result!=200)
            {
                throw ValidationException::withMessages([__('error.mobile.sap_error'), __('error.mobile.sap_error_contact').$result]);
               // Alert::error( __('error.mobile.sap_error'), __('error.mobile.sap_error_contact').$result)->persistent('Dismiss');
            }

        }
       

        return redirect()->route('po.show', ['purchase_order' => $po->id])
        ->with('successmsg', __('success.updated',
        ['resource' => __('Purchase Order'), 'name' => $po->name]));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy(PurchaseOrder $po, Request $request)
    {
        // Event::dispatch(new PurchaseOrderHeaderEvent($po,'delete'));
        $po->delete();  // Observe: PurchaseOrderHeaderObserver deleting()

        // This come from cancel button of MasterMaintenance.po_item.add
        if ($request->ajax()) {
            return 'true';
        }

        return redirect()->route('PO')
            ->with('successmsg', __('success.deleted', ['resource' => __('Purchase Order')]));
    }

    public function list()
    {
        $po = PurchaseOrder::orderby('due_date','asc')->orderby('po_num','asc')->get();
        ini_set('memory_limit', '-1');
        set_time_limit(0);
        return json_encode($po);
    }

    public function ap_delete(Request $request)
    {
        $myString = $request->id;
        $myArray = explode(',', $myString);


        PurchaseOrderItem::destroy($myArray);  // Observe: PurchaseOrderObserver updated()

        //if no row selected or request is null
        if($myString == "null" || $myString == "")
        {
            return redirect(route('PO'))->with('errormsg', __('error.admin.selectone'));
        }

        //if >1 selected and successful
        else{
            return redirect(route('PO'))->with('successmsg', __('success.deleted', ['resource' => __('Purchase Order')]));
        }
    }

    public function exist($po_num)
    {
        $data = PurchaseOrder::where('po_num', $po_num)->exists();

        if ($data) {
            return 'exist';
        }
        else {
            return 'not exist';
        }
    }

    public function pov2(Request $request)
    {
        $data = PurchaseOrder::where('po_num', $request->po_num)->first();

        if ($data) {
            return 'exist';
        }
        else {
            return 'not exist';
        }
    }

    public function is_base64_string($s)
    {
        //Regex!
        if (!preg_match('/^[a-zA-Z0-9\/\r\n+]*={0,2}$/', $s) || (strlen($s) % 4 != 0))
            return false;

        // first check if we're dealing with an actual valid base64 encoded string
        if (($b = base64_decode($s, TRUE)) === FALSE) {
            return FALSE;
        }

        // now check whether the decoded data could be actual text
        $e = mb_detect_encoding($b);
        if (in_array($e, array('UTF-8', 'ASCII'))) { // YMMV
            return TRUE;
        } else {
            return FALSE;
        }
    }

    public function validatePOHeader(Request $request)
    {
        if(isset($request->po_num)){
            $result = PurchaseOrder::where('po_num', $request->po_num)->first();
            return $result ? "false" : "true";
        }
    }
}
