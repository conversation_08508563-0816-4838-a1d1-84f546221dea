<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\SiteSetting;
use Carbon\Carbon;
use DataTables;
use Illuminate\Support\Facades\DB;
use PDF;
use Alert;
use App\View\TparmView;
use App\DataTables\InvBalRepDataTable;
use Illuminate\Support\Facades\Session;
use Camroncade\Timezone\Facades\Timezone;

class InvExpiryReportController extends Controller
{


    public function index(){

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        $siteformat = SiteSetting::getOutputDateFormat();

        //dd($siteformat);

        if(request()->wh == "All")
        {
            $whse_pass = "";

            if(request()->day_expiry == null)
            {
                $day_expiry = "";
            }
            else
            {
                $day_expiry = 100;
            }
        }
        else
        {
            $whse_pass = request()->wh;
            
            if(request()->day_expiry == null)
            {
                $day_expiry = "";
            }
            else
            {
                $day_expiry = 100;
            }
            
        }

        $ref_pass = request()->ref ?? null; 
        $report_module_name = $ref_pass == "dashboard" ? "admin.title.lot_near_expiry" : "admin.title.lot_expiry";

        $now_date = Carbon::now()->toDateString();

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error');
        }
        return view('report.inv.invexpiry.index')->with('siteformat',$siteformat)->with('unit_quantity_format',$unit_quantity_format)->with('now_date',$now_date)->with('whse_pass',$whse_pass)->with('day_expiry',$day_expiry)->with('report_module_name', $report_module_name)->with('ref_pass', $ref_pass);

    }

    public function getQuery() {

        $now_date = Carbon::now()->toDateString();
        $now_date = Carbon::parse($now_date);

        $siteSettings = new SiteSetting;

        $lot_locs = DB::query()->from('lot_locs')->where('lot_locs.site_id', auth()->user()->site_id)->groupBy('lot_num', 'item_num');

        $items = DB::query()->from('items')->where('items.site_id', auth()->user()->site_id);

        $query = DB::query()->from('lots')
        ->select( 'lot_locs.item_num','items.item_desc', 'lot_locs.whse_num', 'lot_locs.lot_num', 'lots.expiry_date', 'lot_locs.loc_num', 'lot_locs.qty_available', 'lot_locs.qty_on_hand', DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'items.uom') // DB::raw('DATE_FORMAT(lots.first_received_date, "' . $siteSettings->getMySQLDateFormat() . '") as first_received_date'), 
        ->where('lots.site_id',auth()->user()->site_id)
        ->leftjoinSub($lot_locs, "lot_locs", function ($q) {
            $q->on('lots.lot_num', '=', 'lot_locs.lot_num');
            $q->on('lots.item_num', '=', 'lot_locs.item_num');
        })
        ->leftjoinSub($items, "items", function ($q) {
            $q->on('lots.item_num', '=', 'items.item_num');
        });

         //dd($query->get());

        // $lots = DB::table('lots')
        //         ->select('lot_num','item_num','expiry_date','first_received_date')
        //         ->where('site_id', auth()->user()->site_id)
        //         ->where('item_num', '5041')
        //         ->groupBy('lot_num');

        // $query = DB::table('lot_locs')
        //     ->leftjoinSub($lots, 'lots', function ($q) {
        //         $q->on('lot_locs.lot_num', '=', 'lots.lot_num');
        //     })
        //     ->leftJoin('items','items.item_num','=','lot_locs.item_num')
        //     ->select( 'lot_locs.item_num','items.item_desc', 'lot_locs.whse_num', 'lot_locs.lot_num', DB::raw('DATE_FORMAT(lots.first_received_date, "' . $siteSettings->getMySQLDateFormat() . '") as first_received_date'), DB::raw('DATE_FORMAT(lots.expiry_date, "' . $siteSettings->getMySQLDateFormat() . '") as expiry_date'), 'lot_locs.loc_num', 'lot_locs.qty_on_hand', DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'items.uom');

        // $query = DB::query()->from('lot_locs')
        //     ->select( 'lot_locs.item_num','items.item_desc', 'lot_locs.whse_num', 'lot_locs.lot_num', DB::raw('DATE_FORMAT(lots.first_received_date, "' . $siteSettings->getMySQLDateFormat() . '") as first_received_date'), 'lots.expiry_date', 'lot_locs.loc_num', 'lot_locs.qty_on_hand', DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'items.uom')
        //     ->leftJoin('items','items.item_num','=','lot_locs.item_num')
        //     ->leftJoin('lots','lots.item_num','lot_locs.item_num');

        $whse_total = DB::query()->from('lot_locs')
            ->select( 'lot_locs.item_num', DB::raw('null as item_desc'), 'lot_locs.whse_num', 'lot_locs.lot_num', 'lots.first_received_date', 'lots.expiry_date', DB::raw('null as loc_num'), DB::raw('null as qty_on_hand'), DB::raw('SUM(lot_locs.qty_on_hand) as whse_tot'), DB::raw('null as item_tot'), 'items.uom')
            ->leftJoin('items','items.item_num','=','lot_locs.item_num')
            ->leftJoin('lots','lots.lot_num','lot_locs.lot_num');

        $item_total = DB::query()->from('lot_locs')
            ->select( 'lot_locs.item_num', DB::raw('null as item_desc'), DB::raw('null as whse_num'), 'lot_locs.lot_num', 'lots.first_received_date', 'lots.expiry_date', DB::raw('null as loc_num'), DB::raw('null as qty_on_hand'), DB::raw('null as whse_tot'), DB::raw('SUM(lot_locs.qty_on_hand) as item_tot'), 'items.uom')
            ->leftJoin('items','items.item_num','=','lot_locs.item_num')
            ->leftJoin('lots','lots.lot_num','lot_locs.lot_num');

        if(request('from_item_num') && request('to_item_num')){
            $query->whereBetween('items.item_num', [request('from_item_num'), request('to_item_num')]);
            $whse_total->whereBetween('items.item_num', [request('from_item_num'), request('to_item_num')]);
            $item_total->whereBetween('items.item_num', [request('from_item_num'), request('to_item_num')]);
        }
        else if(request('from_item_num')){
            $query->where('items.item_num', '>=', request('from_item_num'));
            $whse_total->where('items.item_num', '>=', request('from_item_num'));
            $item_total->where('items.item_num', '>=', request('from_item_num'));
        }
        else if(request('to_item_num')){
            $query->where('items.item_num', '<=', request('to_item_num'));
            $whse_total->where('items.item_num', '<=', request('to_item_num'));
            $item_total->where('items.item_num', '<=', request('to_item_num'));
        }

        if(request('from_product_code') && request('to_product_code')){
            $query->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
            $whse_total->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
            $item_total->whereBetween('items.product_code', [request('from_product_code'), request('to_product_code')]);
        }
        else if(request('from_product_code')){
            $query->where('items.product_code', '>=', request('from_product_code'));
            $whse_total->where('items.product_code', '>=', request('from_product_code'));
            $item_total->where('items.product_code', '>=', request('from_product_code'));
        }
        else if(request('to_product_code')){
            $query->where('items.product_code', '<=', request('to_product_code'));
            $whse_total->where('items.product_code', '<=', request('to_product_code'));
            $item_total->where('items.product_code', '<=', request('to_product_code'));
        }

        if(request('from_whse_num') && request('to_whse_num')){
            $query->whereBetween('lot_locs.whse_num', [request('from_whse_num'), request('to_whse_num')]);
            $whse_total->whereBetween('lot_locs.whse_num', [request('from_whse_num'), request('to_whse_num')]);
            $item_total->whereBetween('lot_locs.whse_num', [request('from_whse_num'), request('to_whse_num')]);
        }
        else if(request('from_whse_num')){
            $query->where('lot_locs.whse_num', '>=', request('from_whse_num'));
            $whse_total->where('lot_locs.whse_num', '>=', request('from_whse_num'));
            $item_total->where('lot_locs.whse_num', '>=', request('from_whse_num'));
        }
        else if(request('to_whse_num')){
            $query->where('lot_locs.whse_num', '<=', request('to_whse_num'));
            $whse_total->where('lot_locs.whse_num', '<=', request('to_whse_num'));
            $item_total->where('lot_locs.whse_num', '<=', request('to_whse_num'));
        }

        if(request('from_lot_num') && request('to_lot_num')){
            $query->whereBetween('lot_locs.lot_num', [request('from_lot_num'), request('to_lot_num')]);
            $whse_total->whereBetween('lot_locs.lot_num', [request('from_lot_num'), request('to_lot_num')]);
            $item_total->whereBetween('lot_locs.lot_num', [request('from_lot_num'), request('to_lot_num')]);
        }
        else if(request('from_lot_num')){
            $query->where('lot_locs.lot_num', '>=', request('from_lot_num'));
            $whse_total->where('lot_locs.lot_num', '>=', request('from_lot_num'));
            $item_total->where('lot_locs.lot_num', '>=', request('from_lot_num'));
        }
        else if(request('to_lot_num')){
            $query->where('lot_locs.lot_num', '<=', request('to_lot_num'));
            $whse_total->where('lot_locs.lot_num', '<=', request('to_lot_num'));
            $item_total->where('lot_locs.lot_num', '<=', request('to_lot_num'));
        }

        if(request('from_expiry_date') && request('to_expiry_date')){
            $from_expiry_date = Carbon::createFromFormat($siteSettings->getInputDateFormat(),request('from_expiry_date'))->format('Y-m-d');
            $to_expiry_date = Carbon::createFromFormat($siteSettings->getInputDateFormat(),request('to_expiry_date'))->format('Y-m-d');
            $query->whereBetween('lots.expiry_date', [$from_expiry_date, $to_expiry_date.' 23:59:59']);
            $whse_total->whereBetween('lots.expiry_date', [$from_expiry_date, $to_expiry_date.' 23:59:59']);
            $item_total->whereBetween('lots.expiry_date', [$from_expiry_date, $to_expiry_date.' 23:59:59']);
        }
        else if(request('from_expiry_date')){
            $from_expiry_date = Carbon::createFromFormat($siteSettings->getInputDateFormat(),request('from_expiry_date'))->format('Y-m-d');
            $query->where('lots.expiry_date', '>=', $from_expiry_date);
            $whse_total->where('lots.expiry_date', '>=', $from_expiry_date);
            $item_total->where('lots.expiry_date', '>=', $from_expiry_date);
        }
        else if(request('to_expiry_date')){
            $to_expiry_date = Carbon::createFromFormat($siteSettings->getInputDateFormat(),request('to_expiry_date'))->format('Y-m-d');
            $query->where('lots.expiry_date', '<=', $to_expiry_date.' 23:59:59');
            $whse_total->where('lots.expiry_date', '<=', $to_expiry_date.' 23:59:59');
            $item_total->where('lots.expiry_date', '<=', $to_expiry_date.' 23:59:59');
        }

        if(request('day_to_expiry') != null){
            $now_date = Carbon::now()->toDateString();
            if(request('day_to_expiry') > 0){
                //dd($query->get());
                //dd($now_date);
                $date_from_day = Carbon::now()->addDays(request('day_to_expiry'))->toDateString();
               
                //$date_from_day = Carbon::createFromFormat($siteSettings->getInputDateFormat(),$date_from_day)->format('Y-m-d');
                //dd($now_date,$date_from_day,$siteSettings->getInputDateFormat());
                $query->whereBetween('lots.expiry_date', [$now_date, $date_from_day.' 23:59:59']);
                $whse_total->whereBetween('lots.expiry_date', [$now_date, $date_from_day.' 23:59:59']);
                $item_total->whereBetween('lots.expiry_date', [$now_date, $date_from_day.' 23:59:59']);
            }
            else if(request('day_to_expiry') == 0){
                $date_from_day = Carbon::now()->addDays(request('day_to_expiry'))->toDateString();
                // $query->where('lots.expiry_date', '=', $now_date);
                // $whse_total->where('lots.expiry_date', '=', $now_date);
                // $item_total->where('lots.expiry_date', '=', $now_date);
               // dd($now_date,$date_from_day,$siteSettings->getInputDateFormat());
                
                $query->whereBetween('lots.expiry_date', [$now_date, $date_from_day.' 23:59:59']);
                $whse_total->whereBetween('lots.expiry_date', [$now_date, $date_from_day.' 23:59:59']);
                $item_total->whereBetween('lots.expiry_date', [$now_date, $date_from_day.' 23:59:59']);
                //dd($query->get(),$now_date);  
            }
            else if(request('day_to_expiry') < 0){
             
                $date_from_day = Carbon::now()->addDays(request('day_to_expiry'))->toDateString();
                //$date_from_day = Carbon::createFromFormat($siteSettings->getInputDateFormat(),$date_from_day)->format('Y-m-d');
                $query->whereBetween('lots.expiry_date', [$date_from_day, $now_date.' 23:59:59']);
                $whse_total->whereBetween('lots.expiry_date', [$date_from_day, $now_date.' 23:59:59']);
                $item_total->whereBetween('lots.expiry_date', [$date_from_day, $now_date.' 23:59:59']);
            }
        }

        $query->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id)
            ->where('lots.site_id', auth()->user()->site_id)
            ->where('lots.expiry_date','!=',null)
            ->groupBy('lot_locs.lot_num','lot_locs.loc_num','lot_locs.whse_num','items.item_num');
        $whse_total->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id)
            ->where('lots.site_id', auth()->user()->site_id)
            ->where('lots.expiry_date','!=',null)
            ->groupBy('lot_locs.whse_num','items.item_num');
        $item_total->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id)
            ->where('lots.site_id', auth()->user()->site_id)
            ->where('lots.expiry_date','!=',null)
            ->groupBy('items.item_num');
         
        // $query->union($whse_total);
        // $query->union($item_total);

        if (Session::get('lotExpReturn') == true){
            $w = Session::get('warehouse_select3');
        
            $query->where('lot_locs.site_id', auth()->user()->site_id)
                ->where('items.site_id', auth()->user()->site_id)
                ->where('lots.site_id', auth()->user()->site_id)
                ->where('lots.expiry_date', '>', Carbon::today())
                ->groupBy('lot_locs.lot_num','lot_locs.loc_num','lot_locs.whse_num','items.item_num');

            if($w && $w != 'All'){
                $query = $query->where('whse_num', $w);
            }

            $query->orderBy('expiry_date', 'asc');
            // $query->orderBy('item_num', 'asc');
            // $query->orderBy('lot_num', 'asc');

            Session::put('lotExpReturn', false);
            Session::put('warehouse_select3', "");

        }else if (request('ref_pass') == "dashboard"){
            $query->where('lots.expiry_date', '>', Carbon::today());

            $query->orderBy('lots.expiry_date', 'asc');
            $query->orderBy('items.item_num', 'asc');
            $query->orderBy('lot_locs.lot_num', 'asc');
            $query->orderBy('lot_locs.qty_available', 'desc');
            $query->LIMIT(5);
        }else {
            $query->orderBy('expiry_date', 'asc');
            $query->orderBy('items.item_num', 'asc');
            $query->orderBy('lot_locs.lot_num', 'asc');
            $query->orderBy('lot_locs.whse_num', 'desc');
            $query->orderBy('lot_locs.loc_num', 'desc');
        }
        
        // $query->orderBy('whse_tot', 'asc');
        // $query->orderBy('item_tot', 'asc');

        // $dataTable = Datatables::of($query);
        // $response  = $dataTable->make(true);
       
        return $query;
    }

    public function data(Request $request) {

        $query = $this->getQuery($request);

        if ($request->has('columns')) {
            foreach ($request->columns as $index => $column) {
                if (isset($column['data']) && $column['data'] === 'expiry_date' && 
                    isset($column['search']) && !empty($column['search']['value'])) {
                    
                    $keyword = $column['search']['value'];
                    $keyword = getDateFitlerValue($keyword);
                    
                    // Apply a simple LIKE filter to the raw query builder
                    $query->where(DB::raw("DATE(expiry_date)"), 'LIKE', $keyword);
                    
                    break;
                }
            }
        }

        $dataTable = Datatables::of($query);
        $response  = $dataTable->make(true);
        //dd($response);

        return $response;
    }

    public function print(Request $request) {

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $now_date = Carbon::now()->toDateString();
        $now_date = Carbon::parse($now_date);
        
        $query = $this->getQuery($request);
        $query1 = $query->get();
        
        // Site
        $site = SiteSetting::where('site_id', auth()->user()->site_id)->first();
        if($site->logo != "") {
            $url = $site->getFileFullPath();
            $image = file_get_contents($url);
            $site->logoazure = "data:image/png+jpeg+jpg;base64,".base64_encode($image);
        }
        else
            $site->logoazure = "";

        if($site->company_info) {
            $sitedecode = json_decode($site->company_info);
            $site->company_name = strtoupper($sitedecode->company_name);
        }

        $datas = [];

        foreach($query1 as $q) {
            $q->qty_on_hand = numberFormatPrecision($q->qty_on_hand, $unit_quantity_format);
            $q->expiry_date = Carbon::parse($q->expiry_date);

            $i = 0;
            $datas[] = [
                'item_num' => $q->item_num,
                'item_desc' => $q->item_desc,
                'lot_num' => $q->lot_num,
                'first_received_date' => $q->first_received_date,
                'expiry_date' => $q->expiry_date,
                'whse_num' => $q->whse_num,
                'loc_num' => $q->loc_num,
                'qty_on_hand' => $q->qty_on_hand,
                'uom' => $q->uom,
            ];
            $i++;
        }

        $prev_data = [
            'item_num' => '',
            'lot_num' => '',
        ];

        $days_to_expiry = 0;
        $index = 1;

        $now = Carbon::now()->toDateTimeString();
        $date = Timezone::convertFromUTC($now, auth()->user()->timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, auth()->user()->timezone, 'H:i:s');

        $headerHtml = view()->make('report.trans.transactionReport')->with('siteLogo', $site->logo)->with('siteLogoAzure', $site->logoazure)->with('companyName', $site->company_name)->render();
        $footerHtml = view()->make('report.trans.footer')->with('date',$date)->with('time',$time)->render();
        
        $pdf = PDF::loadView('report.inv.invexpiry.print', compact('now_date', 'query1', 'request', 'datas', 'prev_data', 'days_to_expiry','index', 'site'))
            ->setPaper('A4', 'portrait')
            ->setOption('header-html', $headerHtml)
            ->setOption('footer-html', $footerHtml)
            ->setOption('margin-bottom', 10)
            ->setOption('margin-top', 5)
            ->setOption('margin-right', 10)
            ->setOption('margin-left', 10)
            ->setOption("footer-right", "Page [page] of [topage]")
            ->setOption("footer-font-size", 8);

        return $pdf->stream('Lot Expiry.pdf');
    }

    public function lotNearViewDashboard(Request $request) {

       // dd($request);

        $tparm = new TparmView;
        $total_quantity_format = $tparm->getTparmValue('System', 'decimal_setting_total_qty');
        if($request->wh=="All" || $request->wh==null)
        {
            $whse_pass = "All";
        }
        else
        {
            $whse_pass = $request->wh;
        }
        $now_date = Carbon::now()->toDateString();

        if(!\Gate::allows('hasReport')){
            return view('errors.404v2')->with('page','error'); ;
        }
        return view('report.inv.invexpiry.dashboardStatus')->with('total_quantity_format',$total_quantity_format)->with('whse_pass',$whse_pass)->with('now_date',$now_date);
    }

    public function getQueryDashboard(Request $request) {

        $now_date = Carbon::now()->toDateString();
        $now_date = Carbon::parse($now_date);
        $siteSettings = new SiteSetting;

        if(request('wh') == 'All')
        {
            $query = DB::query()->from('lot_locs')
            ->select( 'lot_locs.item_num','items.item_desc', 'lot_locs.whse_num', 'lot_locs.lot_num', DB::raw('DATE_FORMAT(lots.first_received_date, "' . $siteSettings->getMySQLDateFormat() . '") as first_received_date'), DB::raw('DATE_FORMAT(lots.expiry_date, "' . $siteSettings->getMySQLDateFormat() . '") as expiry_date'), 'lot_locs.loc_num', 'lot_locs.qty_on_hand', DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'items.uom')
            ->leftJoin('items','items.item_num','=','lot_locs.item_num')
            ->leftJoin('lots','lots.lot_num','lot_locs.lot_num');

            $query->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id)
            ->where('lots.site_id', auth()->user()->site_id)
            ->where('lots.expiry_date', '>', Carbon::today())
            ->limit(5);
        }
        else
        {

            $query = DB::query()->from('lot_locs')
            ->select( 'lot_locs.item_num','items.item_desc', 'lot_locs.whse_num', 'lot_locs.lot_num', DB::raw('DATE_FORMAT(lots.first_received_date, "' . $siteSettings->getMySQLDateFormat() . '") as first_received_date'), DB::raw('DATE_FORMAT(lots.expiry_date, "' . $siteSettings->getMySQLDateFormat() . '") as expiry_date'), 'lot_locs.loc_num', 'lot_locs.qty_on_hand', DB::raw('null as whse_tot'), DB::raw('null as item_tot'), 'items.uom')
            ->leftJoin('items','items.item_num','=','lot_locs.item_num')
            ->leftJoin('lots','lots.lot_num','lot_locs.lot_num');

            $query->where('lot_locs.site_id', auth()->user()->site_id)
            ->where('items.site_id', auth()->user()->site_id)
            ->where('lots.site_id', auth()->user()->site_id)
            ->where('lots.expiry_date', '>', Carbon::today())
            ->where('whse_num', request()->wh)
            ->limit(5);

        }

        
        $query->groupBy('lot_locs.lot_num','lot_locs.loc_num','lot_locs.whse_num','items.item_num');
        $query->orderBy('expiry_date', 'asc');


        return $query;
    }

    public function dataDashboard(Request $request) {

        // dd($request);

        $query = $this->getQueryDashboard($request);

        $dataTable = Datatables::of($query);
        $response = $dataTable->make(true);

        return $response;
    }
}
