<?php

namespace App\Http\Controllers\Outgoing;

use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\Services\SapCallService;
use App\Services\SapApiCallService;

use App\Services\UOMService;
use Illuminate\Http\Request;
use App\Shipment;
use App\StageLoc;
use App\Lot;
use App\StagingLine;
use Alert;
use App\Allocation;
use App\Http\Controllers\Controller;
use App\Services\GeneralService;
use App\Services\CoPickService;
use DB;
use App\View\TparmView;
use Illuminate\Support\Facades\Session;
use App\CustomerOrderItem;
use App\Customer;
use App\CustomerOrder;
use App\Item;
use App\ContainerItem;
use App\Container;
use App\View\CoPickView;
use App\Http\Controllers\BarcodeController;
use App\IssuedLot;
use App\ItemLoc;
use App\Services\LotService;
use Exception;
use Illuminate\Validation\ValidationException;

use Illuminate\Support\Facades\Log;

use App\PicklistTestItems;
use App\PicklistAllocate;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;
use App\UomConv;
use App\Services\SiteConnectionService;

class CoShippingController extends Controller
{
    public function index()
    {

        if (!\Gate::allows('hasCoShip')) {
            return view('errors.404')->with('page', 'error');;
        }
        // $loc = (int) SapCallService::getAbsEntryByLoc('GVBB-D02');
        // dd($loc);
        $tparm = new TparmView();
        $allow_expired_item = $tparm->getTparmValue('System', 'allow_expired_item');
        $enable_warehouse = $tparm->getTparmValue('CustOrdShipping', 'enable_warehouse');
        $enable_parial_shipping_check = $tparm->getTparmValue('CustOrdShipping', 'enable_parial_shipping_check');
        $def_ship_by = $tparm->getTparmValue('CustOrdShipping', 'def_ship_by') ?: "Customer Order";
       
        // Get tparm's Default Packing Location
        $def_location = $tparm->getTparmValue('Picklist', 'def_location');

        $whse_num = json_decode($def_location)->whse_num ?? null;
        $loc_num = json_decode($def_location)->loc_num ?? null;

        $stageLocs = StageLoc::all();

        return view('shipping.coship.coship', compact('whse_num', 'loc_num'))->with('def_location', $def_location)
            ->with('enable_warehouse', $enable_warehouse)
            ->with('stageLocs', $stageLocs)
            ->with('enable_parial_shipping_check', $enable_parial_shipping_check)
            ->with('allow_expired_item', $allow_expired_item)
            ->with('def_ship_by', $def_ship_by);
    }

    public function CoShipDetails(Request $request)
    {
        // dd($request);
        $shipBy = $request->ship_by;
        $sales_person = $request->strsales_person;
        $shipping_zone = $request->shipping_zone_code;

        if (!\Gate::allows('hasCoShip')) {
            return view('errors.404')->with('page', 'error');;
        }
        $request = validateSansentiveValue($request);
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $enable_parial_shipping_check = $tparm->getTparmValue('CustOrdShipping', 'enable_parial_shipping_check');
        $request->validate([
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'whse_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
        ]);

        $builder = StageLoc::select('id', 'co_num', 'co_line', 'co_rel', 'item_num', 'uom', 'qty_staged', 'lot_num', 'qty_staged', 'picklist_allocates_id', 'lpn_num', 'lpn_line')
            ->with('item')
            ->with('coitem')
            ->with('coitem.customer')
            ->where('whse_num', $request->whse_num)
            ->where('stage_num', $request->stage_num);
        if (request('delivery_date') != "") {
            $builder->whereJsonContains('stage_locs.custom_attributes->delivery_date', $request->delivery_date);
        }
        if (request('delivery_trip') != "") {
            $builder->whereJsonContains('stage_locs.custom_attributes->delivery_trip', $request->delivery_trip);
        }

        // dd($request->delivery_date);
        if ($shipBy == "Customer") {
            // dd('sss',$sales_person);
            //$getColist = CustomerOrder::where('cust_num', $request->cust_num)->where('site_id',auth()->user()->site_id);

            if ($request->cust_num) {
                //dd($sales_person,);
                $builder->whereHas('customer_order', function ($q) use ($request) {
                    $q->where('cust_num', $request->cust_num);
                })
                    ->orderBy('co_num')
                    ->orderBy('co_line');
            }
            if ($sales_person) {
                // dd('sssss');
                $builder->whereHas('customer_order', function ($q) use ($request) {
                    $q->where('strsales_person', $request->strsales_person);
                });
            }
            // dd($request);
            if ($shipping_zone) {

                $builder->whereHas('customer_order', function ($q) use ($request) {
                    $q->where('shipping_zone_code', $request->shipping_zone_code);
                });
                //$getColist .= $getColist->where('shipping_zone', $shipping_zone);
            }
        }

        // Pick by Customer Order
        else {


            // Customer order
            if ($request->co_num) {
                $builder->where('co_num', $request->co_num)->orderBy('co_line');
            }


            // Customer
            else if ($request->cust_num) {
                $builder->whereHas('customer_order', function ($q) use ($request) {
                    $q->where('cust_num', $request->cust_num);
                })
                    ->orderBy('co_num')
                    ->orderBy('co_line');
            }

            if ($sales_person) {
                // dd('sssss');
                $builder->whereHas('customer_order', function ($q) use ($request) {
                    $q->where('strsales_person', $request->strsales_person);
                });
            }
            // dd($request);
            if ($shipping_zone) {

                $builder->whereHas('customer_order', function ($q) use ($request) {
                    $q->where('shipping_zone_code', $request->shipping_zone_code);
                });
                //$getColist .= $getColist->where('shipping_zone', $shipping_zone);
            }
        }
        // $builder->where('item_num','!=','NON-INV');
        $co_list = $builder->get();
        if (count($co_list ?? []) == 0) {
           // throw ValidationException::withMessages([__('error.mobile.shippingzon_salesperson')]);
        }
        // dd($co_list, $request);

        //  dd($co_list,@$co_list[0]->coitem->customer->cust_name);
        // Change the Qty Ship
        $index = 0;
        // dd($co_list);
        foreach ($co_list as $extdata) {

            //dd($extdata);
            $co_num  = $extdata->co_num;
            $co_line = $extdata->co_line;
            $item_num = $extdata->item_num;


            if ($item_num == "NON-INV") {
                $getCheckNONINV =  DB::table('coitems_sap_exts')->select('*')->where(
                    [
                        ['co_num', '=',  $co_num],
                        ['co_line', '=', $co_line],
                        ['site_id', '=', auth()->user()->site_id]
                    ]
                )->whereNotNull('family_line')->get();
                // dd($getCheckNONINV[$index]->coitems_id);
                if (@$getCheckNONINV[$index]->coitems_id) {

                    $getItemDesc = DB::table('coitems')->select('item_desc')->where('id', @$getCheckNONINV[$index]->coitems_id)->value('item_desc');
                } else {
                    $getItemDesc = DB::table('coitems')->select('item_desc')->where(
                        [
                            ['co_num', '=',  $co_num],
                            ['co_line', '=', $co_line],
                            ['site_id', '=', auth()->user()->site_id]
                        ]
                    )->value('item_desc');
                }
                //dd(@$getCheckNONINV[$index]->coitems_id,$getItemDesc);
                $getCheckNONINV = 1;

                //  dd(@$getItemDesc,@$getCheckNONINV[$index]->coitems_id);
                $co_list[$index]->item_desc =  $getItemDesc;
                $co_list[$index]->getCheckNONINV = 1;
                //dd('ssssaaa');
            } else {

                $getCheckNONINV = 0;
                $co_list[$index]->getCheckNONINV = 0;
            }


            $index++;
            $stagelinerecordqty = StagingLine::where('picklist_allocation_id', $extdata->picklist_allocates_id)->where('shipment_id', 0)->sum('qty_ship');
        }
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        // set back 1
        if ($sap_trans_order_integration == 1 && config('icapt.enable_cust_name_notfrom_cust_table') == true) {
            $cust_name = @$co_list[0]->coitem->cust_name;
        } else {
            $cust_name = @$co_list[0]->coitem->customer->cust_name;
        }



        return view('shipping.coship.list')
            ->with('co_list', $co_list)
            ->with('cust_name', $cust_name)
            ->with('ship_by', $request->ship_by)
            ->with('whse_num', $request->whse_num)
            ->with('stage_num', $request->stage_num)
            ->with('strsales_person', $request->strsales_person)
            ->with('shipping_zone_code', $request->shipping_zone_code)
            ->with('co_num', $request->co_num)
            ->with('enable_parial_shipping_check', $enable_parial_shipping_check)
            ->with('unit_quantity_format', $unit_quantity_format);
    }

    public function CoShipValidate(Request $request)
    {

        $tparm = new TparmView();

        $enable_parial_shipping_check = $tparm->getTparmValue('CustOrdShipping', 'enable_parial_shipping_check');
        if (!$enable_parial_shipping_check)
            return "true";
        $colines = DB::table('coitems')->where('co_num', $request->co_num)->groupBy('co_num')->selectRaw("sum(qty_ordered)-sum(qty_picked) as qty_balance")->first();
        // dd($colines);
        if ($colines)
            return $colines->qty_balance > 0 ? "false" : "true";
        return "true";
    }
    public function GenerateCOShipment(Request $request)
    {
       // dd($request,"dede");
        // dd(session('selected'));
        $shipment_id = "0";
        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $allow_expired_item = $tparm->getTparmValue('System', 'allow_expired_item');
        $batch_id = generateBatchId("CustOrdShipping");
        //dd($request);
        $co_list = StageLoc::whereIn('id', $request->selected ?? session('selected'))->get();
        $co_list = $co_list->sortBy('co_line');
        $delivery_date = $request->delivery_date ?? "";
        $delivery_trip = $request->delivery_trip ?? "";

        // dd($request->all());
        return view('shipping.coship.process')
            ->with('co_list', $co_list)
            ->with('cust_num', $request->cust_num ?? session('cust_num'))
            ->with('cust_name', $request->cust_name ?? session('cust_name'))
            ->with('ship_by', $request->ship_by ?? session('ship_by'))
            ->with('shipment_id', $shipment_id)
            ->with('allow_expired_item', $allow_expired_item)
            ->with('getCheckNONINV', $request->getCheckNONINV)
            ->with('item_desc', $request->item_desc)
            ->with('delivery_date', $delivery_date)
            ->with('delivery_trip', $delivery_trip)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('batch_id', $batch_id);
    }

    public function delete()
    {

        DB::table('shipments')->where('status', "P")->delete();

        return 'true';
    }

    public function ShipCo(Request $request)
    {

        // dd($request);

        if ($request['batch_id'] && checkBatchIdExists($request['batch_id'])) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);

        }

        $tparm = new TparmView;
        $sap_error_block = $tparm->getTparmValue('CustOrdShipping', 'block_trans_after_sap_error');


        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if($sap_require_check_online==1 && $sap_trans_order_integration==1){
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id,null,'CO Shipping',1);

            if($checkConnection > 2 ){
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }

        }

        //dd($sap_error_block);


        Session::put('selected', $request->selected);
        Session::put('cust_num', $request->cust_num);
        Session::put('cust_name', $request->cust_name);
        Session::put('ship_by', $request->ship_by);
        Session::put('errormsg', '');

        $gotSAP = 0;

        //Session::put('selected', 'COShip');
        Session::put('modulename', 'COShip');
        DB::beginTransaction();
        try {
            //sort($request->selected);
            $arrId = implode(",", $request->selected);
            $arrSort = $request->selected;


            //dd($request->selected);

            $selected_co = StageLoc::wherein('id', $request->selected)->orderby('co_line')->get();
            //$selected_co = $selected_co->sortBy('co_line');
            $arrLpn = array();
            $index = 0;

            // dd($selected_co);
            // Send error if at least one CO is closed
            foreach ($selected_co as $key => $ship_selected_co) {

                $co_num =  $ship_selected_co->co_num;
                $co_line = $ship_selected_co->co_line;
                $item_num = $ship_selected_co->item_num;

                // $getCheckNONINV =  DB::table('coitems_sap_exts')->select('id')->where(                    [
                //         ['co_num', '=',  $co_num ],
                //         ['co_line', '=', $co_line],
                //         ['site_id', '=', auth()->user()->site_id]
                //     ]
                //     )->whereNotNull('family_line')->get();

                if ($item_num == "NON-INV") {

                    $getCheckNONINV = 1;
                    $ship_selected_co->getCheckNONINV = 1;
                } else {

                    $ship_selected_co->getCheckNONINV = 0;
                }



                if (config('icapt.special_modules.enable_pallet')) {
                    // put lpn_num inside array to be deleted later.
                    if ($ship_selected_co->lpn_num != "") {
                        if (!in_array($ship_selected_co->lpn_num, $arrLpn, true)) {
                            array_push($arrLpn, $ship_selected_co->lpn_num);
                        }
                    }
                }

                // Update qty_shipped in PicklistTestItem
                $aUpdatePicklist =  PicklistAllocate::where('id', $ship_selected_co->picklist_allocates_id)->first();
                $picklistallocation_CheckpicklistId =  @$aUpdatePicklist->id;
                // dd( $picklistallocation_CheckpicklistId );

                if ($aUpdatePicklist) {
                    // dd($aUpdatePicklist,$ship_selected_co->qty_staged);
                    $aUpdatePicklist->qty_shipped = $aUpdatePicklist->qty_shipped + $ship_selected_co->qty_staged;
                    $aUpdatePicklist->save();
                }

                // check stage loc freeze
                $check_stage_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->stage_num)->where('item_num', $request->item_num)->value('freeze');
                if ($check_stage_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $request->item_num, 'resource2' => $request->stage_num])]);
                }

                $closedCOExists = CoPickView::where('co_line', $ship_selected_co->co_line)
                    ->where('whse_num', $ship_selected_co->whse_num)
                    ->where('stage_num', $ship_selected_co->stage_num)
                    ->where('co_num', $ship_selected_co->co_num)
                    ->where('rel_status', 'C')
                    ->exists();

                if ($closedCOExists == 'false') {
                    Alert::error('Error', __('error.admin.copickclose'));
                    return back();
                }

                if ($ship_selected_co->lot_num) {
                    $issued_lot = IssuedLot::where('from_module', 'CO Shipping')
                        ->where('whse_num', $ship_selected_co->whse_num)
                        ->where('ref_num', $ship_selected_co->co_num)
                        ->where('ref_line', $ship_selected_co->co_line)
                        ->where('ref_release', $ship_selected_co->co_rel)
                        ->where('lot_num', $ship_selected_co->lot_num)
                        ->first();

                    // Update
                    if ($issued_lot) {
                        $issued_lot->qty = $issued_lot->qty + $ship_selected_co->qty_staged;
                    }
                    // Store
                    else {
                        $issued_lot = new IssuedLot;
                        $issued_lot->from_module = 'CO Shipping';
                        $issued_lot->whse_num = $ship_selected_co->whse_num;
                        $issued_lot->item_num = $ship_selected_co->item_num;
                        $issued_lot->ref_num = $ship_selected_co->co_num;
                        $issued_lot->ref_line = $ship_selected_co->co_line;
                        $issued_lot->ref_release = $ship_selected_co->co_rel;
                        $issued_lot->lot_num = $ship_selected_co->lot_num;
                        $issued_lot->qty = $ship_selected_co->qty_staged;
                    }
                    $issued_lot->save();
                }
            }

            $arrSelectedCO = array();
            $check_expiry_date = array();
            $arrSelectedCO = $selected_co->toArray();
            $total_qty_shipped = 0;

            //dd($selected_co->toArray(),$arrSelectedCO);
            foreach ($arrSelectedCO as $selectedCO) {
                // Send error if co_num's status is not open
                $checkCoNum = CustomerOrderItem::where('co_num', $selectedCO['co_num'])
                    ->where('co_line', $selectedCO['co_line'])
                    ->where('whse_num', $selectedCO['whse_num'])
                    ->where('item_num', $selectedCO['item_num'])
                    ->where('rel_status', 'C')
                    ->first();

                if ($checkCoNum) {
                    throw ValidationException::withMessages(['co_num' => $selectedCO->co_num . ' cannot be proceed due to status is completed.']);
                }
            }

            // Get Qty Shipped and UOM
            $total_qty_shipped = array_sum(array_column($arrSelectedCO, 'qty_staged'));
            $uom = $arrSelectedCO[0]['uom'] ?? null; // Only take the first selected CO's uom because all of the selected CO's uom are the same
            $index = 0;
            //dd($selected_co);
            foreach ($selected_co as $ship_selected_co) {

                $getCheckNONINV = $ship_selected_co->getCheckNONINV;

                // Minus coitem's qty allocated
                $coitem = CustomerOrderItem::where('co_num', $ship_selected_co->co_num)
                    ->where('co_line', $ship_selected_co->co_line)
                    ->where('co_rel', $ship_selected_co->co_rel)
                    ->first();

                if ($coitem) {
                    $coitem->qty_allocated = max(0, $coitem->qty_allocated - $ship_selected_co->qty_staged + $ship_selected_co->qty_copicking); // Using max 0 to avoid it to become negative if there is bad data
                    // Fix qty_shortage
                    // if($coitem->pick_num=="")
                    // {
                    //     $coitem->qty_manualshippick =   $coitem->qty_manualshippick + $ship_selected_co->qty_copicking;
                    //    // dd('9898');
                    // }
                    //dd($coitem);
                    $coitem->save();

                    // Will use to store in table: coitem_shipment
                    $coitem_ids[] = $coitem->id;
                }

                // Update Allocation
                $allocations = Allocation::where('order_type', 'Customer Order')
                    ->where('ref_num', $ship_selected_co->co_num)
                    ->where('ref_line', $ship_selected_co->co_line)
                    ->where('ref_release', $ship_selected_co->co_rel)
                    ->where('item_num', $ship_selected_co->item_num)
                    ->get();

                foreach ($allocations as $allocation) {
                    $allocation_location = $allocation->allocation_locations()->where('loc_num', $ship_selected_co->stage_num)->where('lot_num', $ship_selected_co->lot_num)->where('picked', 'Yes')->first();
                    if ($allocation_location) {

                        // Minus qty allocated in table: allocation_locations
                        $allocation_location->qty_manual_allocated = $allocation_location->qty_allocated - $ship_selected_co->qty_staged;
                        $allocation_location->save();

                        // Minus qty allocated in table: allocations
                        $allocation->qty_manual_allocated = $allocation->qty_allocated - $ship_selected_co->qty_staged;

                        // Minus qty required in table: allocations
                        $allocation->qty_required = $allocation->qty_required - $ship_selected_co->qty_staged;

                        // Change status to Completed if qty required is 0
                        if ($allocation->qty_required == 0) {
                            $allocation->status = "Completed";
                        }

                        $allocation->save();
                    }
                }

                $copickview = new CoPickView;

                $tparm = new TparmView;
                $allow_expired_item = $tparm->getTparmValue('System', 'allow_expired_item');

                if ($allow_expired_item == 0) {

                    // Send error if lot_num is expired
                    // $checkCoPickView = $copickview->with('lot:lot_num,expiry_date')
                    $checkCoPickView = $copickview
                        ->select('copickview.lot_num', 'copickview.item_num', 'lots.expiry_date')
                        ->join('lots', function($join) {
                            $join->on('copickview.item_num', 'lots.item_num');
                            $join->on('copickview.lot_num', 'lots.lot_num');
                            $join->on('copickview.site_id', 'lots.site_id');
                        })
                        ->where('whse_num', $ship_selected_co->whse_num)
                        ->where('stage_num', $ship_selected_co->stage_num)
                        ->where('co_num', $ship_selected_co->co_num)
                        ->where('co_line', $ship_selected_co->co_line)
                        ->where('rel_status', '!=', 'C')
                        ->where('qty_staged', '>', 0)
                        // ->whereHas('lot', function ($q) {
                        //     $q->where('expiry_date', 'IS NOT', null)->where('expiry_date', '<', now()->format('Y-m-d 00:00:00'));
                        // })
                        ->first();

                    if ($checkCoPickView && $checkCoPickView->expiry_date) {
                        $expiry_date = Carbon::createFromFormat('Y-m-d H:i:s', $checkCoPickView->expiry_date);

                        if (Carbon::now('UTC')->gt($expiry_date))
                        {
                            Alert::error('Error', __('error.mobile.item_expired', ['resource' => $checkCoPickView->lot->item_num, 'resource2' => getDateTimeConverted($checkCoPickView->lot->expiry_date)]));
                            throw ValidationException::withMessages([]);
                        }
                    }
                }

                $stagedOpenCoItems = $copickview->where('co_line', $ship_selected_co->co_line)
                    ->where('whse_num', $ship_selected_co->whse_num)
                    ->where('stage_num', $ship_selected_co->stage_num)
                    ->where('co_num', $ship_selected_co->co_num)
                    ->where('lot_num', $ship_selected_co->lot_num)
                    ->where('rel_status', '!=', 'C')
                    ->where('qty_staged', '>', 0)
                    ->first();

                if (@$stagedOpenCoItems->rel_status == 'C') {

                    // Session::put('modulename', 'COShip');
                    Alert::error('Error', __('error.admin.copickclose'));
                    return back();
                } else {

                    // SAP Intergration
                    // $tparm = new TparmView;
                    // $sap_trans_order_integration= $tparm->getTparmValue('System', 'sap_trans_order_integration');

                    // if($sap_trans_order_integration == 1){
                    //     $result = SapCallService::postCOShipping($request,$stagedOpenCoItems,$ship_selected_co);

                    // }

                    $stagedOpenCoItems = $copickview->where('co_line', $ship_selected_co->co_line)
                        ->where('whse_num', $ship_selected_co->whse_num)
                        ->where('stage_num', $ship_selected_co->stage_num)
                        ->where('co_num', $ship_selected_co->co_num)
                        ->where('lot_num', $ship_selected_co->lot_num)
                        ->where('rel_status', '!=', 'C')
                        ->where('qty_staged', '>', 0);

                        //OC Customization change
                    if ($ship_selected_co->delivery_date != "" && $ship_selected_co->delivery_trip != "") {
                        $stagedOpenCoItems = $stagedOpenCoItems->whereJsonContains('custom_attributes->delivery_trip', $ship_selected_co->delivery_trip)
                            ->whereJsonContains('custom_attributes->delivery_date', $ship_selected_co->delivery_date);
                    }
                    $stagedOpenCoItems = $stagedOpenCoItems->get();
                    // dd($stagedOpenCoItems,$ship_selected_co->delivery_date);

                    if (@$ship_selected_co->lot_num) {
                        $uniquekey = $ship_selected_co->co_num . "-" . $ship_selected_co->co_line . "-" . $ship_selected_co->lot_num;
                        @$check_expiry_date[$uniquekey] = Lot::select('expiry_date')->where('item_num', $ship_selected_co->item_num)->where('lot_num', $ship_selected_co->lot_num)->value('expiry_date');
                    } else {
                        $uniquekey = $ship_selected_co->co_num . "-" . $coitem->id . "-" . $ship_selected_co->co_line;
                    }
                    $arrInput[$uniquekey] = BarcodeController::GetCustOrdLabelDataMuplti($ship_selected_co->co_num, $ship_selected_co->co_line, $ship_selected_co->co_rel, $ship_selected_co->qty_staged, $ship_selected_co->uom, $request->whse_num, $request->stage_num, $ship_selected_co->lot_num, @$check_expiry_date, $uniquekey, null, 'CustOrdShipping');

                    // Stage Lines
                    // $staging_lines =

                    //dd($stagedOpenCoItems);
                    foreach ($stagedOpenCoItems as $stageLoc) {

                        // echo $stageLoc->lot_num;
                        // echo "<br>";
                        // Convert Stage Loc UOM Qty to Inv UOM Qty
                        $stageLoc = UOMService::convertStageQty($stageLoc);

                        $arrStoreQtyConv[$stageLoc->co_num . $stageLoc->co_line . $stageLoc->lot_num . $stageLoc->stage_num] = $stageLoc->qty_conv;

                        $getLPN = StageLoc::where('co_line', $ship_selected_co->co_line)
                            ->where('whse_num', $ship_selected_co->whse_num)
                            ->where('stage_num', $ship_selected_co->stage_num)
                            ->where('co_num', $ship_selected_co->co_num)
                            ->where('lot_num', $ship_selected_co->lot_num)
                            ->where('qty_staged', '>', 0)
                            ->value('lpn_num');
                        // dd($stageLoc,$arrStoreQtyConv,"skksk",$getLPN);
                        $stageLoc['lpn_num'] = $getLPN ?? null;
                        $stageLoc['batch_id'] = $request['batch_id'] ;
                        // Minus from  Stage Location inventory
                        // $updateItemLocation = GeneralService::updateItemLocationQty($stageLoc->whse_num, $stageLoc->stage_num, $stageLoc->item_num, -$stageLoc->base_qty, $stageLoc->lot_num, $stageLoc->uom_conv, 0, $picklistallocation_CheckpicklistId, $stageLoc->co_line);
                        //dd($getCheckNONINV);
                        if ($getCheckNONINV == 0) {
                            $updateItemLocation = GeneralService::updateItemLocationQty($stageLoc->whse_num, $stageLoc->stage_num, $stageLoc->item_num, -$stageLoc->qty_conv, $stageLoc->lot_num, $stageLoc->uom_conv, 0, $picklistallocation_CheckpicklistId, $stageLoc->co_line, $stageLoc['lpn_num']);
                        }

                        // dd($updateItemLocation);
                        // Update CO to say that the qty is shipped
                        // Issue557 $stageLoc->qty_conv change to  $ship_selected_co->qty_staged due to coitem Table already handled uom conversion

                        $updateCoRel = CoPickService::updateCoLineRel($stageLoc->co_num, $stageLoc->co_line, $stageLoc->co_rel, $ship_selected_co->qty_staged, 'COShip');

                        $getCreatedDate = StageLoc::where('co_line', $ship_selected_co->co_line)
                            ->where('whse_num', $ship_selected_co->whse_num)
                            ->where('stage_num', $ship_selected_co->stage_num)
                            ->where('co_num', $ship_selected_co->co_num)
                            ->where('lot_num', $ship_selected_co->lot_num)
                            ->where('qty_staged', '>', 0)
                            ->value('created_date');

                        // Update Matl Trans


                        $insertMatl = GeneralService::coshippingMatlTrans(config('icapt.transtype.co_shipping'), $stageLoc);



                        // Find the Stage Location and delete it (View table rows cant be deleted)
                        $deleteStageLoc = StageLoc::where('whse_num', $ship_selected_co->whse_num)->where('stage_num', $ship_selected_co->stage_num)->where('co_num', $ship_selected_co->co_num)->where('co_line', $stageLoc->co_line)->where('co_rel', $stageLoc->co_rel)->where('lot_num', $ship_selected_co->lot_num)->first();
                        // dd($deleteStageLoc);
                        $deleteStageLoc->delete($deleteStageLoc->id);
                        //echo "<pre>";
                        // print_R($stageLoc);

                    }
                    // $arr[] = $stagedOpenCoItems;
                }

                // Store into Shipment for each line
                $shipment = Shipment::create([
                    'shipment_id' => Shipment::orderBy('shipment_id', 'desc')->value('shipment_id') + 1,
                    'order_detail' => json_encode($ship_selected_co),
                    'qty_shipped' => $ship_selected_co->qty_staged * -1,
                    'uom' => $ship_selected_co->uom,
                    'co_num' => $ship_selected_co->co_num,
                    'co_line' => $ship_selected_co->co_line,
                    'lot_num' => $ship_selected_co->lot_num,
                    'trans_type' => "1",
                    'shipment_date' => Carbon::now() // UTC Timezone
                ]);

                $shipment->coitems()->syncWithoutDetaching($coitem->id);

                $ship_selected_co->{'shipment_id'} = $shipment->id;
            }
            // dd($selected_co,$stagedOpenCoItems);
            // Open for debugging
            $shipmentID = $shipment->shipment_id;
            $site_id = auth()->user()->site_id;



            //dd($arrInput,"ssss");
            //return BarcodeController::showLabelDefinitionMultiple($arrInput,111);
            // exit;
            // Create New Shipment ID
            // $shipment = Shipment::create([
            //     'shipment_id' => Shipment::orderBy('shipment_id', 'desc')->value('shipment_id') + 1,
            //     'order_detail' => json_encode($arrSelectedCO),
            //     'qty_shipped' => $total_qty_shipped,
            //     'uom' => $uom,
            // ]);

            // Store in table: coitem_shipment (Using syncWithoutDetaching to avoid duplicate data)
            // $shipment->coitems()->syncWithoutDetaching($coitem_ids);

           /** Here remove to bottom */

           if ($sap_error_block == 1) {
             // SAP Intergration

            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
            //dd("jsjsjs",$sap_single_bin);
            $getErpId = 0;
            if ($sap_trans_order_integration == 1) {
   
                $request->merge([
                    'selected_co' => $selected_co,
                    'getErpId' => $getErpId,
                    'shipmentID' => $shipmentID,
                    'arrStoreQtyConv' => $arrStoreQtyConv
   
                ]);
                //$result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv);
   
                if ($sap_single_bin == 1) {
                    $result = SiteConnectionService::postIntergrationTransCOShipping("CO Shipping", $selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv, $request);
                } else {
                    if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                        // Later Change to read from Matltrans
                        $result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv, $request);
                    } else {
                        $result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv, $request);
                    }
                }
               // dd($result);
                if ($result != 200) {
                    // Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
   
                    if ($sap_error_block == 1) {
                        DB::rollback();
   
                        // throw $e;
   
   
                       /* $data   = Session::get('data');
                        //$result = Session::get('results');
                        $extras = Session::get('sap_service');
                        $user = auth()->user();
   
                        $id =  DB::table('integration_logs')->insertGetId([
                            'trans_num' => 0,
                            'site_id' => $site_id,
                            'type' => 'sap',
                            'method' => 'post',
                            'process_name' => 'CO Shipping',
                            'post_from' => 'CoShippingController_ShipCo()',
                            'post_data' => json_encode($data),
                            'response' => $result,
                            'extras' =>  json_encode(['sap_service' => $extras]),
                            'status' => 2,
                            'created_by' => $user ? $user->name : "System",
                            'created_date' => now(),
                            'modified_date' => now(),
                        ]);
                        */
   
   
                        Session::forget('data');
                        Session::forget('results');
                        Session::forget('sap_service');
                        if ($result != 200) {
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                            throw ValidationException::withMessages([__('error.mobile.sap_error_contact'). $result]);
                        
                        } else {
                            Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                        }
                        return app('App\Http\Controllers\RouteController')->BackButton();
                        //return redirect()->route('GenerateCOShipment');
                    }
                    else{
                        if ($sap_trans_order_integration == 1) {
                           if ($result != 200) {
                               Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                               throw ValidationException::withMessages([__('error.mobile.sap_error_contact'). $result]);
                           } else {
                               Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                           }
                       } else {
                           Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                       }
                    }
                    // return redirect()->back();
                }


                $gotSAP =2;
            }
            else{
                $gotSAP =1;
            }

        }
        /*$arrStoreStangingLinesId = array();
        foreach ($selected_co as $stageLoc) {
            $getRealtrans[$stageLoc->co_line][] =  StagingLine::where('co_num', $stageLoc->co_num)->where('shipment_id', 0)->where('co_line', $stageLoc->co_line)->where('site_id', $site_id)->where('to', $stageLoc->stage_num)->where('lot_num', $stageLoc->lot_num)->get();
            foreach ($getRealtrans as $co_line => $value) {
                foreach ($value as $key => $vsteps) {
                    foreach ($vsteps as $fkey => $v) {
                        $arrStoreStangingLinesId[@$v->id] = @$v->id;
                    }
                }
            }
        }

        // count and update
        $cDataStagingLines = count($arrStoreStangingLinesId ?? []);
        if ($cDataStagingLines > 0) {
            foreach ($arrStoreStangingLinesId as $staginglinesid => $id) {
                $updatedStagingLines = DB::table('staging_lines')
                    ->where([['id', $id], ['shipment_id', 0]])
                    ->update(['shipment_id' => $shipmentID, 'modified_date' => now()->format('Y-m-d 00:00:00'), 'modified_by' => auth()->user()->name]);
            }
        }*/


            if (config('icapt.special_modules.enable_pallet')) {
                //delete LPN
                $destroyLPNItem = ContainerItem::whereIn('lpn_num', $arrLpn)->delete();
                $destroyLPN = Container::whereIn('lpn_num', $arrLpn)->delete();
            }

            // Update staging lines
            //      foreach ($stagedOpenCoItems as $stageLoc) {
            //         $getRealtrans =  StagingLine::where('co_num', $stageLoc->co_num)->where('shipment_id',0)->where('co_line', $stageLoc->co_line)->where('site_id',$site_id)->where('to',$stageLoc->stage_num)->get();
            //       // dd($getRealtrans,$shipmentID);
            //         foreach($getRealtrans as $key){
            //            //dd($key->whse_num);
            //            if(@$key->lot_num){
            //                $updatedStagingLines = DB::table('staging_lines')
            //                ->where([['whse_num', $key->whse_num],['to', $key->to],['co_num', $key->co_num],['co_line', $key->co_line], ['shipment_id', 0],['lot_num', $key->lot_num]])
            //                ->update(['shipment_id' => $shipmentID, 'modified_date' => now()->format('Y-m-d 00:00:00'), 'modified_by' => auth()->user()->name]);

            //            }
            //            else{
            //            $updatedStagingLines = DB::table('staging_lines')
            //            ->where([['whse_num', $key->whse_num],['to', $key->to],['co_num', $key->co_num],['co_line', $key->co_line], ['shipment_id', 0]])
            //            ->update(['shipment_id' => $shipmentID, 'modified_date' => now()->format('Y-m-d 00:00:00'), 'modified_by' => auth()->user()->name]);
            //            }
            //         }
            //    }


            // Move here

           

            //dd('Stop',$stagedOpenCoItems,$getRealtrans,$shipmentID);


            //    Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
            // if ($sap_trans_order_integration == 1) {
            //     if ($result != 200) {
            //         Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
            //         throw ValidationException::withMessages([__('error.mobile.sap_error_contact'). $result]);
            //     } else {
            //         Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
            //     }
            // } else {
            //     Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
            // }


            DB::commit();

            if ($stageLoc->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($stageLoc);

                // Generate barcode
                $input = BarcodeController::GetCustOrdLabelData($request->co_num, $stageLoc->co_line, $request->co_rel, $request->qty_staged, $request->uom, $request->whse_num, $request->stage_num, $stageLoc->lot_num, $check_expiry_date, null, 'CustOrdShipping');
            } else {
                // Generate barcode
                $input = BarcodeController::GetCustOrdLabelData($request->co_num, $stageLoc->co_line, $request->co_rel, $request->qty_staged, $request->uom, $request->whse_num, $request->stage_num, $request->lot_num, null, null, 'CustOrdShipping');
            }

           
        } catch (Exception $e) {
            // dd($e,session('modulename'));


            DB::rollback();
            // throw $e;

            Session::put('errormsg', $e->getMessage());
            return redirect()->route('GenerateCOShipment');
            // return app('App\Http\Controllers\RouteController')->BackButton();
        }

         // SAP Intergration

         $tparm = new TparmView;
         $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
         $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');
         //dd("jsjsjs",$sap_single_bin);
         $getErpId = 0;
         // $result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv,$request);

         //dd($sap_trans_order_integration);
        // dd($sap_trans_order_integration);

         // Without Block
         if ($sap_error_block != 1) {
         if ($sap_trans_order_integration == 1) {

             $request->merge([
                 'selected_co' => $selected_co,
                 'getErpId' => $getErpId,
                 'shipmentID' => $shipmentID,
                 'arrStoreQtyConv' => $arrStoreQtyConv

             ]);
             //$result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv);
             
             if ($sap_single_bin == 1) {
                 $result = SiteConnectionService::postIntergrationTransCOShipping("CO Shipping", $selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv, $request);
             } else {
                 if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                     // Later Change to read from Matltrans
                     $result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv, $request);
                 } else {
                     $result = SapCallService::postMultiCOShipping($selected_co, $request->cust_num, $getErpId, $shipmentID, $arrStoreQtyConv, $request);
                 }
             }
            // dd($result);
             if ($result != 200) {
                 // Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');

                 if ($sap_error_block == 1) {
                     //DB::rollback();

                     // throw $e;


                    /* $data   = Session::get('data');
                     //$result = Session::get('results');
                     $extras = Session::get('sap_service');
                     $user = auth()->user();

                     $id =  DB::table('integration_logs')->insertGetId([
                         'trans_num' => 0,
                         'site_id' => $site_id,
                         'type' => 'sap',
                         'method' => 'post',
                         'process_name' => 'CO Shipping',
                         'post_from' => 'CoShippingController_ShipCo()',
                         'post_data' => json_encode($data),
                         'response' => $result,
                         'extras' =>  json_encode(['sap_service' => $extras]),
                         'status' => 2,
                         'created_by' => $user ? $user->name : "System",
                         'created_date' => now(),
                         'modified_date' => now(),
                     ]);
                     */


                     Session::forget('data');
                     Session::forget('results');
                     Session::forget('sap_service');
                     if ($result != 200) {
                         Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                     } else {
                         Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                     }
                     return app('App\Http\Controllers\RouteController')->BackButton();
                     //return redirect()->route('GenerateCOShipment');
                 }
                 else{
                     if ($sap_trans_order_integration == 1) {
                        if ($result != 200) {
                            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                            //throw ValidationException::withMessages([__('error.mobile.sap_error_contact'). $result]);
                        } else {
                            Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                        }
                    } else {
                        Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                    }
                 }
                 // return redirect()->back();
             }
            // else{
            //     if ($sap_trans_order_integration == 1) {
            //        if ($result != 200) {
            //            Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
            //            //throw ValidationException::withMessages([__('error.mobile.sap_error_contact'). $result]);
            //        } else {
            //            Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
            //        }
            //    } else {
            //        Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
            //    }
           // }
            
            }
         }

         $arrStoreStangingLinesId = array();
         foreach ($selected_co as $stageLoc) {
             $getRealtrans[$stageLoc->co_line][] =  StagingLine::where('co_num', $stageLoc->co_num)->where('shipment_id', 0)->where('co_line', $stageLoc->co_line)->where('site_id', $site_id)->where('to', $stageLoc->stage_num)->where('lot_num', $stageLoc->lot_num)->get();
             foreach ($getRealtrans as $co_line => $value) {
                 foreach ($value as $key => $vsteps) {
                     foreach ($vsteps as $fkey => $v) {
                         $arrStoreStangingLinesId[@$v->id] = @$v->id;
                     }
                 }
             }
         }

         // count and update
         $cDataStagingLines = count($arrStoreStangingLinesId ?? []);
         if ($cDataStagingLines > 0) {
             foreach ($arrStoreStangingLinesId as $staginglinesid => $id) {
                 $updatedStagingLines = DB::table('staging_lines')
                     ->where([['id', $id], ['shipment_id', 0]])
                     ->update(['shipment_id' => $shipmentID, 'modified_date' => now()->format('Y-m-d 00:00:00'), 'modified_by' => auth()->user()->name]);
             }
         }
        

           if ($sap_trans_order_integration == 1) {
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                    //throw ValidationException::withMessages([__('error.mobile.sap_error_contact'). $result]);
                } else {
                    Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
                }
            } else {
                Alert::success('Success', __('success.processed', ['process' => __('Shipment ID: ' . $shipment->shipment_id . '. Customer Order Shipping')]));
            }
        
         $tparm = new TparmView;
         $print_label = $tparm->getTparmValue('CustOrdShipping', 'print_label');

         if ($print_label == 1) {
             //return BarcodeController::showLabelDefinition($input);
             return BarcodeController::showLabelDefinitionMultiple($arrInput, $shipment->shipment_id);
         } else {

             return app('App\Http\Controllers\RouteController')->BackButton();
         }



    }

    public function validation(Request $request)
    {

        $vinput = $request->all();

        foreach ($vinput as $name => $value) {
            if ($name == "co_num") {
                $model = new StageLoc();
                $result = $model->COExists($value);
            }
            if ($name == "cust_num") {
                $model = new Customer();
                $result = $model->exists($value);
            }
            if ($name == "stage_num" || $name == "loc_num") {
                $model = new StageLoc();
                $result = $model->exists($value);
            }

            if ($result == true) {
                return "true";
            } else {
                return "false";
            }
        }
    }

    public function checkExpiryDateCOShipping(Request $request)
    {
        // Send error if lot_num is expired
        $copickview = new CoPickView;
        $checkCoPickView = $copickview->with('lot:lot_num,expiry_date')
            ->select('lot_num', 'item_num')
            ->where('whse_num', $request->whse_num)
            ->where('stage_num', $request->stage_num)
            ->where('co_num', $request->co_num)
            ->where('rel_status', '!=', 'C')
            ->where('qty_staged', '>', 0)
            ->whereHas('lot', function ($q) {
                $q->where('expiry_date', '!=', null)->where('expiry_date', '<', now()->format('Y-m-d 00:00:00'));
            })
            ->first();

        if ($checkCoPickView) {
            $checkCoPickView->expiry_date = $checkCoPickView->lot->expiry_date;
            return $checkCoPickView;
        } else {
            return 'false';
        }
    }

    public function changeCustNum(Request $request)
    {
        $cust = Customer::where('cust_num', $request->cust_num)->first();
        return $cust->cust_name;
    }
}
