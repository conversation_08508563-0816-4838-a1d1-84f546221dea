@extends('layout.app')
@section('head')
@include('report.style')
@endsection
@section('content')
<div class="margin-header">
    <div class="card-header-title ">
        <table class="title-header">
            <tr>
                <td class="title-division"><h4 class="title">{{__('admin.title.co_status')}}</h4></td>
            </tr>
        </table>
    </div>
    <div>
        <form class="form-group" id="co_status_report" name="co_status_report" method="POST" action="{{route('printcostatusrep')}}">
            @csrf
            <div class="default-button">
                <button id="filter" type="button" class="displayExcel-button btn btn-primary button-padding" style="background-color:#37BC9B;"><i class="icon-square-plus"></i> {{__('admin.button.generate')}}</button></td>
                <button id="btn-refresh" type="button" class="btn btn-primary button-padding" onclick="document.getElementById('inv_trans_by_item_report').reset(); document.getElementById('from_date').value = null; return false;">
                    <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
                </button>
                <button type="submit" id="print" class="btn btn-info button-padding">
                    <i class="icon-print"></i> {{__('admin.button.print')}}
                </button>
            </div>
            <div class="table-content">
                <table align="center">
                    <tr>
                        <td width='100px'><label for="item_num">{{ __('admin.label.item_num') }}</label></td>
                        <td width='300px'><input type="text" name="from_item_num" id="from_item_num" tabindex="1" class="form-control border-primary" placeholder="From Item"></td>
                        <td width='75px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','from_item_num','item_num','from_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='65px'><label> To</label></td>
                        <td width='300px'><input type="text" name="to_item_num" id="to_item_num" tabindex="6" class="form-control border-primary" placeholder="To Item"></td>
                        <td width='100px'>
                            <button type="button" name="{{__('admin.list.items')}}" onClick="selection('/getItem','to_item_num','item_num','to_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="co_num">{{ __('admin.label.co') }}</label></td>
                        <td><input type="text" name="from_co_num" id="from_co_num" tabindex="2" class="form-control border-primary" placeholder="From CO Number"></td>
                        <td>
                            <button type="button" name="{{ __('admin.label.co') }}"  onClick="selection('/getCoNum','from_co_num','co_num','from_co_num');modalheader(this.id,'Customer Orders');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td> <input type="text" name="to_co_num" id="to_co_num" tabindex="7" class="form-control border-primary" placeholder="To CO Number" ></td>
                        <td>
                            <button type="button" name="{{ __('admin.label.co') }}"  onClick="selection('/getCoNum','to_co_num','co_num','to_co_num');modalheader(this.id,'Customer Orders');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="cust_num">{{__('admin.label.cust_num')}}</label></td>
                        <td><input type="text" name="from_cust_num" id="from_cust_num" tabindex="3" class="form-control border-primary" placeholder="From Customer Code" ></td>
                        <td>
                            <button type="button" name="From Customer" onClick="selection('/getCust','from_cust_num','cust_num','from_cust_num');modalheader(this.id,'Customer Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                        <td width='50px'><label> To</label></td>
                        <td><input type="text" name="to_cust_num" id="to_cust_num" tabindex="8" class="form-control border-primary" placeholder="To Customer Code"></td>
                        <td>
                            <button type="button" name="To Customer" onClick="selection('/getCust','to_cust_num','cust_num','to_cust_num');modalheader(this.id,'Customer Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td><label for="due_date">{{ __('admin.label.due_date') }}</label></td>
                        <td><input type="text" autocomplete="off" id="from_due_date" tabindex="4" class="form-control border-primary input-group from_due_date" name="from_due_date" placeholder="From Due Date"></td>
                        <td>&nbsp;</td>
                        <td><label> To</label></td>
                        <td><input type="text" autocomplete="off" id="to_due_date" tabindex="9" class="form-control border-primary input-group to_due_date" name="to_due_date" placeholder="To Due Date" value="{{ old('due_date') }}"></td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td><label for="status">{{__('admin.label.status')}} &nbsp;</label></td>
                        <td>
                            <select tabindex="5" class="form-control border-primary" name="status" id="status">
                                {{-- <option value="" selected>Select Status</option> --}}
                                <option value="All" selected>&nbsp;All</option>
                                <option value="Fully Shipped">&nbsp;Fully Shipped</option>
                                <option value="Partially Shipped">&nbsp;Partially Shipped</option>
                                <option value="Unshipped">&nbsp;Unshipped</option>
                            </select>
                        </td>
                        <td></td>
                        {{-- Checkbox for Past Due --}}
                        <td width='65px'><label for="past_due">{{__('admin.option.past_due')}}: &nbsp;</label></td>
                        <td>
                            <label class="custom-control custom-checkbox">
                            <input type="checkbox" name="past_due" id="past_due" tabindex="10" class="custom-control-input">
                            <span class="custom-control-indicator"></span>
                            <span class="custom-control-description"></span>
                        </td>
                        <td></td>
                    </tr>
                </table>

            </div>
        </form>
         <hr>
                <div class="card-block card-dashboard">
                      <div class="table-content">
                    @include('report.inv.costatus.list')
                      </div>
                </div>
    </div>
</div>

@push('scripts')
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/gh/jeffreydwalter/ColReorderWithResize@9ce30c640e394282c9e0df5787d54e5887bc8ecc/ColReorderWithResize.js"></script>
@endpush

@include('util.js-datatable')

<script type="text/javascript">

    var defaultColumns= ['co_num','co_line','cust_num','cust_name','due_date','item_num','item_desc','qty_ordered','qty_picked',
            'qty_shipped','qty_returned','qty_required','uom'];

    if(localStorage.getItem('columnsetting_COStatusReport') == null){
        localStorage.setItem('columnsetting_COStatusReport', JSON.stringify(defaultColumns));
    }

    //Get Cookies Columns
    var getItem = localStorage.getItem('columnsetting_COStatusReport');
    var columnsList = JSON.parse(getItem);

    var arrColumns=[];
    for(var i = 0; i < columnsList.length; i++){
        if(columnsList[i]=='cust_num' || columnsList[i]=='cust_name' || columnsList[i]=='uom'){
            arrColumns.push({data:columnsList[i], name:columnsList[i], orderable:false});
        }else if(columnsList[i]=='qty_ordered' || columnsList[i]=='qty_picked' || columnsList[i]=='qty_shipped' || columnsList[i]=='qty_returned' || columnsList[i]=='qty_required'){
            arrColumns.push({data:columnsList[i], name:columnsList[i], class:'dt-right'});
        }else if (columnsList[i]=='due_date') {
            arrColumns.push({data:columnsList[i], name:columnsList[i], searchable: false});
        }else{
            arrColumns.push({data:columnsList[i], name:columnsList[i]});
        }
    }

    //onClick "Generate" button
    $('.displayExcel-button').click(function() {
        $('.default-button').css('padding-right', '220px');
    });

    jQuery(function($){
        $("#co_status_recort").validate({
            onchange:true,
            rules:{
                from_item_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_item_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_co_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_co_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_cust_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_cust_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
            },
            messages:{
                from_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                to_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                from_co_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.co_num') ]) }}"
                },
                to_co_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.co_num') ]) }}"
                },
                from_cust_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                },
                to_cust_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.cust_num') ]) }}"
                },
            },
        });
    });

    window.onload=function(){
        // document.getElementById("filter").click();
    };


    $(document).ready(function(){
        $('#filter').click(function(){
           getTable();
 $(".dataTable thead input:not(#mass-chk)").on( 'keyup', function (e) {
            if (e.keyCode == 13) {
                console.log($(this).parent().index());
                $('.dataTable').DataTable().column($(this).parent().index()).search(this.value).draw();
            }
        });
                $('#btn-refresh').click(function(){
                    $('input').each(function() {
                        $(this).val('');
                    });
                   $('input[type="checkbox"]').each(function() {
                        $(this).prop('checked', false); // Uncheck the checkbox
                    });
                    $('#co_status_report')[0].reset();
                  $('.dataTable').DataTable().columns().search('');
        $('.dataTable').DataTable().clear().draw();
                    //$('.dataTable').DataTable().ajax.reload();
                });

                var colreorder = new $.fn.dataTable.ColReorder(table);

                var colvis = new $.fn.dataTable.ColVis( table );
                $(colvis.button() ).insertAfter('div.dataTables_length');

                table.on('column-reorder.dt.mouseup column-resize.dt.mouseup', function(event) {
                    $(".buttons-excel").css('width','');
                    $("div").find('.ColVis').remove();
                    var colvis = new $.fn.dataTable.ColVis( table );
                    $(colvis.button() ).insertAfter('div.dataTables_length');

                    var arrColumns = [];
                    for(i=0;i<colvis.s.dt.aoColumns.length;i++){
                        arrColumns.push(colvis.s.dt.aoColumns[i].data);
                    }

                    // Save Cookies
                    var urlgo = '<?php echo route('save-cookies');?>';
                    var token = '<?php echo csrf_token() ?>';
                    $.ajax({
                                "url": urlgo,
                                "data": {
                                    "datasetting": arrColumns,
                                    "tableName": colvis.s.dt.nTable.id,
                                    "_token": token,
                                },
                                "dataType": 'json',
                                "type": 'POST',
                                "success": function (resp) {
                                    console.log(resp);
                                },
                            });

                    localStorage.setItem('columnsetting'+'_'+colvis.s.dt.nTable.id, JSON.stringify(arrColumns)); //stringify object and store
                });
            ;
        });

        $(window).resize(function (e) {
            $("#COStatusReport").DataTable().columns.adjust();
        });

        function getTable(){
            unit_quantity_format = "{{$unit_quantity_format}}";

            let ajaxMethod = {
                url : '{!! route('costatusrep.data') !!}',
                method: 'get',
                data: function (d) {
                    d.from_item_num = $('#from_item_num').val();
                    d.to_item_num = $('#to_item_num').val();
                    d.from_co_num = $('#from_co_num').val();
                    d.to_co_num = $('#to_co_num').val();
                    d.from_cust_num = $("#from_cust_num").val();
                    d.to_cust_num = $('#to_cust_num').val();
                    d.from_due_date = $('#from_due_date').val(); // Pass along start date and end date here
                    d.to_due_date = $('#to_due_date').val();
                    d.status = $('#status').val();
                    if ($('#past_due').is(":checked")) {
                        d.past_due = 'on';
                    }
                    else {
                        d.past_due = 'off';
                    }
                    console.log(d.past_due);
                },
                dataSrc: function (json) {
                    var return_data = new Array();
                    for(var i=0; i<json.data.length; i++){
                        // Convert null values to zero
                        json.data[i].qty_picked = json.data[i].qty_picked || 0;
                        return_data.push({
                        'co_num': json.data[i].co_num,
                        'co_line': json.data[i].co_line,
                        'cust_num'  : json.data[i].cust_num,
                        'cust_name'  : json.data[i].cust_name,
                        'due_date' : json.data[i].due_date,
                        'item_num' : json.data[i].item_num,
                        'item_desc' : json.data[i].item_desc,
                        'qty_ordered' : numberFormatPrecision(json.data[i].qty_ordered,unit_quantity_format),
                        'qty_picked' : numberFormatPrecision(json.data[i].qty_picked,unit_quantity_format),
                        'qty_shipped' : numberFormatPrecision(json.data[i].qty_shipped,unit_quantity_format),
                        'qty_returned' : numberFormatPrecision(json.data[i].qty_returned,unit_quantity_format),
                        'qty_required' : numberFormatPrecision(json.data[i].qty_required,unit_quantity_format),
                        'uom' : json.data[i].uom,
                        });
                    }
                    return return_data;
                }
            };

            generateTable('COStatusReport', arrColumns, ajaxMethod, 'CustomerOrderStatus', {serverSide: true, searching: true});

            $(".dataTable thead input:not(#mass-chk)").on('keyup', function (e) {
                if (e.keyCode == 13) {
                    $('.dataTable').DataTable().column($(this).parent().index()).search(this.value).draw();
                }
            });
        }
    });
</script>
<style>
    .title-header{
        width:100%;}
    .title{
        padding-top:2px;
        font-size: 22px !important;
        font-weight: bold;}
    .title-division{
        width:50%;
        float:left;}
    .default-button{
        float:right;
        margin-top:-59.4px;
        padding-right:0px;
        position: relative;}
    .button-padding{
        margin:5px 0.5px;}
    .table-content{
        margin-top:15px;}
    .card-block{
        margin-top: -18px;
        padding-right: 0rem;
        padding-left: 0rem;}
    /* Export button */
    div.dt-buttons{
        float: right !important;
        margin-top: -54.5px;}
    .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle){
        border-bottom-right-radius: 0.18rem !important;
        border-top-right-radius: 0.18rem !important;}
    .btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
        border-bottom-left-radius: 0.18rem !important;
        border-top-left-radius: 0.18rem !important;}
    div.dt-buttons.btn-group>a.btn-secondary{
        width: 110px !important;
        margin-left: 4px !important;}
    a.btn.btn-success{
        border-color:#4FC3F7;
        background-color:#4FC3F7;}
    label{
        margin-bottom: 0.1rem !important;}
    .margin-header input.form-control.border-primary{
        padding-left:10px;
        height: 25px;}
    select#status.form-control.border-primary{
        padding-left: 2px !important;
        height: 25px;}
    .form-group{
        margin-top:1rem;}
    form td{
        padding: 2px 1px;}
    .table th, .table td{
        padding: 0.2rem 0.2rem !important;}
    .table td{
        border-bottom: 1px solid white !important;}
    div.dataTables_length>label{
        width: 110px !important;}
    button.ColVis_Button>span{
        font-size: 9pt;}
    /* table header */
    div.card-block>table#COStatusReport thead>tr:first-child>th, div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        background-color: rgb(220,220,220);}
    div.dataTables_scrollBody>table>thead>tr.row>th{
        background-color: white;}
    table#datatable.table.table-bordered.table-hover.nowrap.datatable.no-footer td>a>button.btn.btn-primary{
        padding: 0.1rem 0.2rem !important;}
    div.dataTables_wrapper div.dataTables_paginate{
        margin-bottom: 3rem !important;}
    li.paginate_button.page-item.active>a {
        font-size: 9pt;}
    .dt-center {
        text-align: center;}
    .dt-right {
        text-align: right;}
    .dt-buttons {
        padding-bottom: 15px;}
    .dataTables_length {
        float:left;
        padding-top: 0.5em;
        padding-bottom: 5px;}
     div.dataTables_info {
        padding-top: 0.85em;
        position:absolute;}
    div.dataTables_paginate {
        padding-top: 0.85em;
        right:0px;}
</style>

@include('util.selection')
@include('util.datepicker')
@endsection
