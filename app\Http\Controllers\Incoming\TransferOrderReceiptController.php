<?php

namespace App\Http\Controllers\Incoming;

use App\Services\SapCallService;
use Illuminate\Http\Request;
use App\SapTransferOrders;
use App\SAPb1\SAPClient;
use App\SAPb1\Filters\Equal;
use App\SAPb1\Service;
use App\SAPb1\Config;
use App\Services\CallHttpService;
use Illuminate\Support\Facades\Crypt;
use App\ReasonCode;
use App\SiteSetting;
use App\TransferLine;
use App\TransferOrderLinesSubline;
use App\Item;
use App\Loc;
use App\LotLoc;
use App\Lot;
use App\AlternateBarcode;
use App\Services\GeneralService;
use App\Container;
use App\ContainerItem;
use App\ItemLoc;
use App\Services\PalletService;
use App\UomConv;
use DB;
use Alert;
use App\Exports\TransferOrderExport;
use App\Http\Controllers\BarcodeController;
use App\TransferOrder;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use App\View\TparmView;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Carbon;
use App\Http\Controllers\Controller;
use App\Services\LotService;
use App\Services\UOMService;
use App\Services\TOService;
use Illuminate\Support\Facades\Log;

use App\Services\SapApiCallService;
use App\Services\SiteConnectionService;
use App\SiteConnection;
use App\Services\PreassignLotsService;
use App\Services\CatchWeightService;
use Illuminate\Support\Facades\Cache;
use Exception;

class TransferOrderReceiptController extends Controller
{
    use \App\Traits\HasDefaultLoc;

    public function __construct()
    {
        $this->middleware('auth');
        //$this->middleware('can:hasCoReturn');
    }


    public function index()
    {

        if (!\Gate::allows('hasToReceipt')) {
            return view('errors.404')->with('page', 'error');
        }

        $checkPlan = ['AX-MT-STR-M', 'AX-MT-STR-A', 'AX-MT-FREE-M'];
        $plan = SiteSetting::select('plan_code')->where('site_id', auth()->user()->site_id)->first();
        if (in_array($plan->plan_code, $checkPlan)) {
            return view('errors.404v2')->with('page', 'error');
        }

        $tparm = new TparmView();
        $tparm = $tparm->getTparmValue('TransferOrderReceipt', 'enable_warehouse');

        $lpnDef = PalletService::getDefaultLpnTransaction('TO Receipt');

        return view('Receiving.toreceipt.index')->with('tparm', $tparm)->with('def_lpn', $lpnDef);
    }

    public function transferOrderItemList(Request $request)
    {

        if (!\Gate::allows('hasToReceipt')) {
            return view('errors.404')->with('page', 'error');
        }
        $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'nullable|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
        ]);

        // Send error if trn_num's status is not open
        // $checkTrnNum = TransferOrder::where('trn_num',$request->trn_num)->where('status', '!=', "C")->exists();
        // if (!$checkTrnNum) {
        //     throw ValidationException::withMessages(['trn_num' => 'TO-'.$request->trn_num.' cannot be proceed due to status is completed/closed']);
        // }

        $checkWhse = TransferOrder::where('trn_num', $request->trn_num)->where('site_id', auth()->user()->site_id)->first();
        //$checkWhse = TransferLine::where('trn_num', $request->trn_num)->where('site_id', auth()->user()->site_id)->first();
        if ($checkWhse->to_whse != $request->whse_num) {

            throw ValidationException::withMessages(['trn_num' => 'TO [' . $request->trn_num . '] does not match Whse [' . $request->whse_num . ']']);
        }
        //dd($checkWhse,$request->whse_num);

        $itemList = new TransferLine();
        $whse_num = $request->whse_num;
        $trn_num = $request->trn_num;
        $item_num = $request->item_num;
        $trn_line = $request->trn_line;
        if ($request->item_num != null) {
            $itemList = $itemList->select('item_num', 'trn_line')->where('to_whse', $whse_num)->where('trn_num', $trn_num)->where('item_num', $item_num)->where('line_stat', 'O')->get();
            if (count($itemList) == 1) {
                $trn_line = $itemList->first()->trn_line;
                $request = new \Illuminate\Http\Request();
                $request->replace(['trn_line' => $trn_line, 'item_num' => $item_num, 'trn_num' => $trn_num]);
                return $this->receiveTransferOrder($request);
            }
        }

        // $from_whse = TransferLine::where('trn_num', $request->trn_num)->value('from_whse');

        //dd($whse_num);
        $to_loc = TransferLine::where('trn_num', $request->trn_num)->first();

        if ($request->pick_by == 'pallet') {
            return view('Receiving.toreceipt.newtoreceiptpalletlist', compact('whse_num', 'trn_line', 'trn_num', 'item_num'))
                ->with('from_whse', @$to_loc->from_whse)
                ->with('to_whse', @$to_loc->to_whse)
                ->with('to_loc', @$request->toLoc);;
        }

        return view('Receiving.toreceipt.newtolist', compact('whse_num', 'trn_line', 'trn_num', 'item_num'));
    }

    public function receiveTransferOrder(Request $request)
    {

        if (!\Gate::allows('hasToReceipt')) {
            return view('errors.404')->with('page', 'error');
        }
        $sublines_details = "";
        $getLot = "";

        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $to_details = TransferLine::with('item')->with('lot')
            ->where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            //->where('item_num',$request->item_num)
            ->first();

        // Check if item is catch weight enabled
        $item = Item::select('lot_tracked', 'catch_weight', 'catch_weight_tolerance')->where('item_num', $to_details->item_num)->first();
        //dd($to_details,$request);
        $defaults = $this->getLocByRankReceipt($to_details->to_whse, $to_details->item_num);

        // Get the qty_shipped from transferoder_lines_sublines
        //  $sublines_details = TransferOrderLinesSubline::select("*",DB::raw("SUM(qty_receivable) as qty_receivable"),DB::raw("SUM(qty_shipped) as qty_shipped"),DB::raw("SUM(qty_loss) as qty_loss"))
        // ->where('trn_num',$request->trn_num)
        // ->where('trn_line',$request->trn_line)
        // ->where('item_num',$request->item_num)
        // //->groupBy('trn_lot')
        // ->first();

        $sublines_details = TransferLine::where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            ->where('item_num', $to_details->item_num)->first();

        $sub_sublines_details = TransferOrderLinesSubline::select("*",DB::raw("SUM(qty_received) as qty_received"), DB::raw("SUM(qty_receivable) as qty_receivable"), DB::raw("SUM(qty_shipped) as qty_shipped"), DB::raw("SUM(qty_loss) as qty_loss"))
            ->where('trn_num', $request->trn_num)
            ->where('trn_line', $request->trn_line)
            ->where('lpn_num', "")
            //->where('item_num',$request->item_num)
            ->where('item_num', $to_details->item_num)
            ->first();

        //dd($sublines_details,$sub_sublines_details);
        if ($sublines_details == null) {
            // $getLot = Lot::select('expiry_date')->where("item_num",$request->item_num)->first();
        } else {
            $getLot = Lot::select('expiry_date')->where("item_num", $request->item_num)->where("lot_num", $sublines_details->trn_lot)->first();
        }

        $tparm = new TparmView;
        $disable_create_new_item_location = $tparm->getTparmValue('TransferOrderReceipt', 'disable_create_new_item_location');
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $batch_id = generateBatchId("TransferOrderReceipt");

        // dd($to_details, $sublines_details, $getLot);

        // $last_doc_num = DB::table('sap_to_batch_sync')
        // ->where('trn_num', $request->trn_num)
        // ->where('sync_status', 1)
        // ->where('TO_type', 0)
        // ->orderBy('id', 'desc')
        // ->where('site_id', auth()->user()->site_id)
        // ->pluck('doc_num')
        // ->first();

        // Determine view based on catch weight
        $view = $item->catch_weight ? 'Receiving.toreceipt.process_cw' : 'Receiving.toreceipt.process';

        // Additional parameters for catch weight
        $allow_over_receive = $tparm->getTparmValue('TransferOrderReceipt', 'allow_over_receive');
        $printLabel = $tparm->getTparmValue('TransferOrderReceipt', 'print_label');
        $printerOptions = [];

        return view($view)
            ->with('to_details', $to_details)
            ->with('ItemList', $to_details) // For catch weight compatibility
            ->with('defaults', $defaults)
            ->with('sublines_details', $sublines_details)
            ->with('sub_sublines_details', $sub_sublines_details)
            ->with('getLot', $getLot)
            ->with('disable_create_new_item_location', $disable_create_new_item_location)
            ->with('unit_quantity_format', $unit_quantity_format)
            ->with('sap_trans_order_integration', $sap_trans_order_integration)
            ->with('batch_id', $batch_id)
            ->with('allow_over_receive', $allow_over_receive)
            ->with('printLabel', $printLabel)
            ->with('printerOptions', $printerOptions)
            ->with('unitQuantityFormat', $unit_quantity_format);
        //->with('last_doc_num',$last_doc_num);
    }

    public function processTransferReceive(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        //dd($request);
        $request = validateSansentiveValue($request);
        $reason_code = $request->reason_code;

        $transLine = new TransferLine();
        $transLine = $transLine->where('trn_num', $request->trn_num)->where('trn_line', $request->trn_line)->first();

        // Verifying TOLine exist
        if (!$transLine) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->trn_num . '-' . $request->trn_line . ']'])]);
        }

        $transferorder = new TransferOrder;
        // Throw error if TO has no transit location
        $checkTransitLoc = $transferorder->where('trn_num', $transLine->trn_num)->whereIn('trn_loc', ['', null])->exists();
        if ($checkTransitLoc) {
            throw ValidationException::withMessages([__('error.mobile.no_transit_loc', ['resource' => $request->trn_num])]);
        }

        // $request = validateSansentiveValue($request);
        $request->validate([
            'item_num' => 'required|exists:items,item_num,item_status,1,site_id,' . auth()->user()->site_id,
            'whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        ], [
            'item_num.exists' => __('error.mobile.processinactive', ['resource' => __('mobile.list.items')]),
            'whse_num.exists' =>  __('error.mobile.processinactive', ['resource' => __('mobile.list.warehouses')]),
        ]);

        // Store in Location
        $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->first();
        $fromLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->trn_num)->value('trn_loc');
        $site_id = auth()->user()->site_id;
        // If not exist, store it
        if (!$checkLoc) {
            $loc = new Loc;
            $loc->whse_num = $request->whse_num;
            $loc->loc_num = $request->loc_num;
            $loc->loc_type = "T";
            $loc->loc_status = 1;
            $loc->save();
        } else {
            if ($checkLoc->loc_status == 0) {
                throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
            }
        }


        $trn_line = $request->ref_line;
        $trn_num = $request->ref_num;
        $item_num = $request->item_num;
        $transDate = Carbon::now()->toDateTimeString();

        if ($request->qty <= 0 && $request->qty_loss <= 0)
            throw ValidationException::withMessages(['qty' => 'Qty to Receive or Qty Lost should be more than 0']);


        $to_uniqekey = base64_encode($request->whse_num . $request->loc_num . $request->trn_num . $request->trn_line . auth()->user()->name) ?? NULL;
        $lockKey = 'insert_lock_' . $to_uniqekey;
        $lock = Cache::lock($lockKey, 10); // Lock for 10 seconds

        if (!$lock->get()) {
            throw ValidationException::withMessages(['Another user is currently editing this record. Please try again later']);
        }

        DB::beginTransaction();
        try {

            Session::put('timestamp', $transDate);
            $request->base_uom = null;
            $executeTransferOrderReceipt = GeneralService::executeTransferOrderReceipt($request);

            $toLosRequest = clone $request;
            $toLossRequest = UOMService::convertTORequestForQtyLost($toLosRequest);
            $request->merge(['qtlloss_conv' => @$toLossRequest->qty_loss_conv]);
            //dd($request,$toLossRequest);
            if (!$executeTransferOrderReceipt) {

                $request = new \Illuminate\Http\Request();
                $request->replace(['trn_line' => $trn_line, 'item_num' => $item_num, 'trn_num' => $trn_num]);

                return $this->transferOrderItemList($request);
            }
            $erp_ID = TransferLine::select('erp_ID')->where('trn_num', $request->trn_num)
                ->where('trn_line', $request->trn_line)
                ->where('site_id', auth()->user()->site_id)
                ->value('erp_ID');

            // DB::table('sap_to_batch_sync')->insert([
            //     'trn_num' => $request->trn_num,
            //     'trn_line' => $request->trn_line,
            //     // 'po_rel' => $request->ref_release ?? 0,
            //     'qty_loss'=>$request->qty_loss,
            //     'uom_loss'=>$request->uom_loss,
            //     'reason_code'=>$request->reason_code,
            //     'erp_ID' => $erp_ID,
            //     'from_whse' => $request->whse_num,
            //     'loc_num' => $request->loc_num,
            //     'lot_num' => $request->lot_num,
            //     'item_num' => $request->item_num,
            //     'qty_received' => $request->qty,
            //     'qty_received_uom' => $request->uom,
            //     'doc_num' => $request->document_num,
            //     'sync_status' => 1,
            //     'site_id' => auth()->user()->site_id,
            //     'created_by' => auth()->user()->name,
            //     'modified_by' => auth()->user()->name,
            //     'created_date' => now(),
            //     'modified_date' => now(),
            // ]);



            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

            // SAP Integration
            // && $reasonCode && $reasonCode->sync_status=="Y"
            $reasonCode = ReasonCode::where('reason_num', $reason_code)->where('reason_class', 'TOLoss')->first();

            $qtyLossCheck = $request->qty_loss ? ($reasonCode && $reasonCode->sync_status == "Y" ? true : false) : true;
            //        dd($qtyLossCheck,$request->qty_loss,$reasonCode);


            LotService::updateLot("TO Receipt", $request);

            // update preassign lots
            // $uom_conv = UomConv::convert($request->uom, $request->qty, $request->item_num, null, null, null);
            // PreassignLotsService::updatePreassignLot('to', $request->ref_num, $request->ref_line, $request->item_num, $request->lot_num, auth()->user()->site_id, $uom_conv['qty']);

            //Future webhook here
            Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Receipt')]));


            $arrJsonEncodeParameters = json_encode($request->except('_token'));
            DB::table('sap_to_batch_sync')->insert([
                'trn_num' => $request->trn_num,
                'trn_line' => $request->trn_line,
                'qty_loss' => $request->qty_loss,
                'uom_loss' => $request->uom_loss,
                'reason_code' => $request->reason_code,
                'erp_ID' => $erp_ID,
                'from_whse' => $request->whse_num,
                'to_whse' => $request->whse_num,
                'loc_num' => $request->loc_num,
                'lot_num' => $request->lot_num,
                'item_num' => $request->item_num,
                'TO_type' => 0,
                'qty_received' => $request->qty,
                'qty_received_uom' => $request->uom,
                'doc_num' => $request->document_num,
                'sync_status' => 1,
                //'status' => 0,
                'site_id' => auth()->user()->site_id,
                'created_by' => auth()->user()->name,
                'modified_by' => auth()->user()->name,
                'created_date' => now(),
                'modified_date' => now(),
                'json_parameters' => $arrJsonEncodeParameters
            ]);


            // SAP Integration
            if ($sap_trans_order_integration == 1 && $request->last_receive == "Yes" && $qtyLossCheck) {

                //$res =SapCallService::postTOReceipt($request,$reason_code,$erp_ID,$fromLoc,$site_id);
                if ($sap_single_bin == 1) {
                    $result = SiteConnectionService::postIntergrationTrans('TO Receipt', $request);
                } else {
                    if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                        //dd(11, config('icapt.enable_sap_ap_readfrom_maltrans'));
                        // Later Change to read from Matltrans
                        $result = SapCallService::postStockTransferFromMaltrans('TO Receipt', $request);
                        //$result = SapCallService::postTOReceipt($request,$reason_code,$erp_ID,$fromLoc,$site_id);
                    } else {



                        if (config('icapt.enable_sap_resync')) {

                            $result = SapCallService::postTOReceiptResync($request, $reason_code, $erp_ID, $fromLoc, $site_id);
                        } else {

                            $result = SapCallService::postTOReceipt($request, $reason_code, $erp_ID, $fromLoc, $site_id);
                        }
                    }
                }

                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }


                //need to check TO Header close or not
                $transdetails = TransferOrder::where('trn_num', $request->trn_num)->where('site_id', auth()->user()->site_id)->first();
                if (@$transdetails) {
                    if ($transdetails->status == 'C') {
                        //SapCallService::postNotificationAndClose($transdetails,0,$request->qty);
                    }
                }




            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        } finally {
            sleep(1);
            $lock->release();
        }

        Session::put('modulename', 'TOReceipt');
        Session::put('trn_num', $trn_num);
        Session::put('trn_line', $trn_line);

        // Generate barcode
        $transType = 'TransferOrderReceipt';

        if ($request->lot_num != null) {
            $check_expiry_date = LotService::getExpiryDate($request);

            // Generate barcode
            $input = BarcodeController::GetTOReceiptLabelData($request->whse_num, $request->trn_num, $request->trn_line, $request->item_num, null, $request->loc_num, $request->lot_num, $request->qty, $request->uom, $check_expiry_date, $transDate, $transType, $request->from_whse, $request->to_whse);
        } else {
            // Generate barcode
            $input = BarcodeController::GetTOReceiptLabelData($request->whse_num, $request->trn_num, $request->trn_line, $request->item_num, null, $request->loc_num, $request->lot_num, $request->qty, $request->uom, null, $transDate, $transType, $request->from_whse, $request->to_whse);
        }

        $tparm = new TparmView;
        $print_label = $tparm->getTparmValue('TransferOrderReceipt', 'print_label');

        if ($print_label == 1) {
            return BarcodeController::showLabelDefinition($input);
        } else {
            return redirect()->route('transferOrderItemList', ['whse_num' => $request->whse_num, 'trn_num' => $request->trn_num, 'item_num' => '']);
            // return app('App\Http\Controllers\RouteController')->BackButton();
        }
    }

    public function processTransferReceiveCW(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        // Validate TO
        $trn_num = $request->ref_num;
        $trn_line = $request->ref_line;
        if (empty($trn_num) || empty($trn_line))
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.label.transfer_order') ]) ]);
        }

        $transLine = TransferLine::where('trn_num', $trn_num)->where('trn_line', $trn_line)->first();
        if (!$transLine)
        {
            throw ValidationException::withMessages([ __('error.mobile.does_not_exists', ['resource' => __('admin.label.transfer_order') ]) ]);
        }

        // Throw error if TO has no transit location
        $transferorder = new TransferOrder;
        $checkTransitLoc = $transferorder->where('trn_num', $transLine->trn_num)->whereIn('trn_loc', ['', null])->exists();
        if ($checkTransitLoc) {
            throw ValidationException::withMessages([__('error.mobile.no_transit_loc', ['resource' => $trn_num])]);
        }

        // Get the qty_receivable from transferoder_lines_sublines
        $sub_sublines_details = TransferOrderLinesSubline::select("*",DB::raw("SUM(qty_received) as qty_received"), DB::raw("SUM(qty_receivable) as qty_receivable"), DB::raw("SUM(qty_shipped) as qty_shipped"), DB::raw("SUM(qty_loss) as qty_loss"))
            ->where('trn_num', $trn_num)
            ->where('trn_line', $trn_line)
            ->where('lpn_num', "")
            ->where('item_num', $transLine->item_num)
            ->first();

        // Get Tolerance and UOM
        $tolerance = $sub_sublines_details->qty_receivable ?? ($transLine->qty_shipped - $transLine->qty_received);
        $tolerance_uom = $transLine->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $request->merge([
            'qty' => array_sum($request->arr_qty ?? []),
            'base_uom' => $transLine->uom
        ]);

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        // Store in Location
        $checkLoc = Loc::where('loc_num', $request->loc_num)->where('whse_num', $request->whse_num)->first();
        $fromLoc = TransferOrder::select('trn_loc')->where('trn_num', $request->ref_num)->value('trn_loc');
        $site_id = auth()->user()->site_id;
        // If not exist, store it
        if (!$checkLoc) {
            $loc = new Loc;
            $loc->whse_num = $request->whse_num;
            $loc->loc_num = $request->loc_num;
            $loc->loc_type = "T";
            $loc->loc_status = 1;
            $loc->save();
        } else {
            if ($checkLoc->loc_status == 0) {
                throw ValidationException::withMessages([__('error.mobile.processinactive', ['resource' => __('mobile.list.locations')])]);
            }
        }

        DB::beginTransaction();
        try {
            Session::put('timestamp', Carbon::now()->toDateTimeString());

            // Use catch weight service to update item location and material transactions
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans($request, $tolerance_uom, config('icapt.transtype.to_receipt_to'), "TO Receipt");

            // Update transfer order line quantities
            $uom_conv = UomConv::convert($request->uom, $request->qty, $request->item_num, null, null, null);
            $executeTransferOrderReceipt = GeneralService::executeTransferOrderReceipt($request);

            if (!$executeTransferOrderReceipt) {
                throw ValidationException::withMessages(['qty' => 'Failed to process transfer order receipt']);
            }

            // Update Lot
            LotService::updateLot("TO Receipt", $request);

            DB::commit();

            Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Receipt')]));

            Session::put('modulename', 'TOReceipt');
            Session::put('trn_num', $trn_num);
            Session::put('trn_line', $trn_line);

            // Generate barcode and redirect
            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('TransferOrderReceipt', 'print_label');

            if ($print_label == 1) {
                $transDate = Carbon::now()->toDateTimeString();
                $transType = 'TransferOrderReceipt';
                $input = BarcodeController::GetTOReceiptLabelData($request->whse_num, $request->ref_num, $request->ref_line, $request->item_num, null, $request->loc_num, $request->lot_num, $request->qty, $request->uom, null, $transDate, $transType, $transLine->from_whse, $transLine->to_whse);
                return BarcodeController::showLabelDefinition($input);
            } else {
                return redirect()->route('transferOrderItemList', ['whse_num' => $request->whse_num, 'trn_num' => $request->ref_num, 'item_num' => '']);
            }

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function TOList(Request $request)
    {

        if ($request->ajax()) {
            $output = '';
            $query = $request->get('query');
            $trn_num = $request->get('trn_num');
            $whse_num = $request->whse_num;
            $item_num = $request->get('item_num');
            $trn_line = $request->get('trn_line');
            if ($query != '') {
                $po_item = new TransferLine();
                $data = TransferLine::with('item')->where('trn_num', $trn_num)->where('to_whse', $whse_num)
                    ->where('item_num', 'like', '%' . $item_num . '%')
                    ->whereRaw('IFNULL(qty_shipped,0) - IFNULL(qty_received,0) - IFNULL(qty_loss,0) > 0')
                    ->where('line_stat', '!=', 'C')
                    ->where(function ($q) use ($query) {
                        $q->orwhere('trn_line', 'like', '%' . $query . '%')
                            ->orWhere('item_num', 'like', '%' . $query . '%')
                            ->orderBy('trn_num');
                    })
                    ->whereHas('item', function ($q) {
                        return $q->where('item_status', 1);
                    })
                    ->orderByRaw('cast(trn_line as unsigned) ASC');
                if ($trn_line != "")
                    $data = $data->where('trn_line', $trn_line);
                $data = $data->get();
            } else {
                $data = TransferLine::with('item')->where('trn_num', $trn_num)->where('to_whse', $whse_num)
                    ->where('item_num', 'like', '%' . $item_num . '%')
                    ->whereRaw('IFNULL(qty_shipped,0) - IFNULL(qty_received,0) - IFNULL(qty_loss,0) > 0')
                    ->where('line_stat', '!=', 'C')
                    ->whereHas('item', function ($q) {
                        return $q->where('item_status', 1);
                    })
                    ->orderByRaw('cast(trn_line as unsigned) ASC')
                    ->orderBy('trn_num');
                if ($trn_line != "")
                    $data = $data->where('trn_line', $trn_line);
                $data = $data->get();
            }

            $total_row = $data->count();

            if ($total_row > 0) {
                foreach ($data as $row) {
                    $desc = new Item();
                    $desc = $desc->getItemDesc($row->item_num);
                    $altBarCode = AlternateBarcode::where('item_num', $row->item_num)->where('site_id', auth()->user()->site_id)->get();
                    $output .= '
                        <form class="form form-horizontal" method="GET" action="/home/<USER>/to-receipt/receive">
                            <input type="hidden" name="_token" value="' . csrf_token() . '">
                            <div class="row border border-primary" id="mybox" onclick="javascript:this.parentNode.submit();">
                                <div class="col-xs-12">
                                    <table style="width: 100%;">
                                        <input type="hidden" value="' . $row->trn_num . '" name="trn_num">
                                        <input value="' . $row->trn_line . '" name="trn_line" type="hidden">
                                        <tr>
                                            <td width="70px"><label for="trn_line">' . __('mobile.label.trn_line') . '</label></td>
                                            <td>
                                                <input type="hidden" value="' . $row->trn_line . '" name="trn_line" readonly class="form-control border-primary" >
                                                <span class="form-control border-primary pseudoinput">' . $row->trn_line . '</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><label for="item_num">' . __('mobile.label.item_num') . '</label></td>
                                            <td>';
                    foreach ($altBarCode as $barCode) {
                        $output .= '<span style="display: none"> ' . $barCode->alternate_barcode . ' </span>';
                    }
                    $output .= '
                                                <input readonly type="hidden" class="form-control border-primary" id="item_num" name="item_num" value="' . $row->item_num . '">
                                                <span class="form-control border-primary pseudoinput">' . $row->item_num . '</span>
                                            </td><td>&nbsp;</td>
                                        </tr>
                                        <tr>
                                            <td></td>
                                            <td><textarea readonly type="text" class="form-control border-primary" id="item">' . $desc . '</textarea>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </form>
                    ';
                }
            } else {
                $output = "
                <tr>
                <td align='center' colspan='4'>Record not found</td>
                </tr>
                ";
            }

            $data = array(
                'table_data'  => $output,
                'total_data'  => $total_row
            );

            echo json_encode($data);
        }
    }

    public function TOvalidation(Request $request)
    {
        $vinput = $request->all();

        foreach ($vinput as $name => $value) {
            if ($name == "to_num" || $name == "ref_num" || $name == "trn_num") {
                $model = new TransferOrder();
                if ($request->whse_num != "") {
                    $result = $model->where('from_whse', $request->whse_num)->where('status', 'O')->where('trn_num', $value)->first();

                    // $result = $model->where('whse_num', $request->whse_num)->exists($value);
                } else {
                    $result = $model->where('status', 'O')->where('trn_num', $value)->exists();
                }
            }

            if ($name == "to_line" || $name == "ref_line" || $name == "trn_line") {
                $model = new TransferLine();
                $result = $model->exists($value);
            }

            if ($name == "item") {
                $model = new Item();
                $result = $model->exists($value);
            }

            if ($result == true) {
                return "true";
            } else {
                return "false";
            }
        }
    }

    // Process of to receipt by pallet
    public function receiveTOByPallet(Request $request)
    {

        $transDate = Carbon::now()->toDateTimeString();
        DB::beginTransaction();
        try {
            // Get Pallet Loc
            $lpnNum = $request->lpn_num_field;
            $toLossMatlTrans = "No"; // check if there are any loss qty.
            $getTotalLineTo = TransferLine::where('trn_num', $request->trn_num)->count();
            $getPalletLoc = Container::where('lpn_num', $request->lpn_num_field)->first();
            for ($i = 1; $i <= $request->count; $i++) {
                $trn_line = 'ref_line_' . $i;
                $lpn_line = 'lpn_line_' . $i;
                $item = 'item_' . $i;
                $item = utf8_encode(base64_decode($request->$item));
                $item = htmlspecialchars_decode($item);
                $item_desc = 'item_desc_' . $i;
                $uom = 'qty_transact_uom_' . $i;
                $qty_receivable = 'qty_transact_' . $i;
                $qty_to_receive = 'qty_input_' . $i;
                $uom_to_receive = 'qty_input_uom_' . $i;
                $qty_to_loss = 'qty_to_loss_' . $i;
                $uom_to_loss = 'qty_to_loss_uom_' . $i;
                $rCode = 'reason_code_' . $i;
                $each_lpn_line = explode(",", $request->$lpn_line);

                // check to_loc freeze
                $check_to_loc = ItemLoc::where('whse_num', $request->whse_num)->where('loc_num', $request->to_loc)->where('item_num', $item)->value('freeze');
                if ($check_to_loc == 'Y') {
                    throw ValidationException::withMessages([__('error.mobile.loc_freeze', ['resource' => $item, 'resource2' => $request->to_loc])]);
                }

                //Get Item qty bal
                $itemQty = ItemLoc::select('qty_available')->where('whse_num', $request->whse_num)->where('item_num', $item)->where('loc_num', $getPalletLoc->loc_num)->first();
                $getLotNum = ContainerItem::where('lpn_num', $request->lpn_num_field)->where('item_num', $request->$item)->value('lot_num');
                if ($request->$trn_line != "") {
                    if (count($each_lpn_line) > 1) {
                        foreach ($each_lpn_line as $each_line) {
                            $lpn_each_qty = 'lpn_each_qty' . $each_line;
                            $record[$i][$each_line] =
                                [
                                    'whse_num' => $request->whse_num,
                                    'trn_num' => $request->trn_num,
                                    'trn_line' => $request->$trn_line,
                                    'lpn_num' => $lpnNum,
                                    'lpn_line' => $request->$lpn_line,
                                    'loc_num' => $getPalletLoc->loc_num,
                                    'to_loc' => $request->to_loc,
                                    'lot_num' => $getLotNum,
                                    'item_num' => $item,
                                    'item_desc' => $request->$item_desc,
                                    'qty_receivable' => $request->$qty_receivable,
                                    'uom' => $request->$uom,
                                    'qty_input' => $request->$lpn_each_qty,
                                    'base_uom' => $request->$uom_to_receive,
                                    'qty_to_loss' => $request->$qty_to_loss,
                                    'uom_to_loss' => $request->$uom_to_loss,
                                    'each_lpn_line' => $each_line,
                                ];

                            $toReceipt[$i][$each_line] =
                                [
                                    'site_id' => auth()->user()->site_id,
                                    'whse_num' => $request->whse_num,
                                    'trn_num' => $request->trn_num,
                                    'trn_line' => $request->$trn_line,
                                    'item_num' => $item,
                                    'qty_receivable' => $request->$qty_receivable,
                                    'qty_receivable_conv' => $request->$qty_receivable,
                                    'base_uom' => $request->$uom,
                                    'loc_num' => $request->to_loc,
                                    'lot_num' => $getLotNum,
                                    'qty_input' => $request->$lpn_each_qty,
                                    'uom' => $request->$uom_to_receive,
                                    'lpn_num' => $lpnNum,
                                    'qty_to_loss' => $request->$qty_to_loss,
                                    'uom_to_loss' => $request->$uom_to_loss,
                                    'reason_code' => $request->$rCode,
                                    'lpn_line' => $request->$lpn_line,
                                    'each_lpn_line' => $each_line,
                                ];
                        }
                    } else {
                        $record[$i] =
                            [
                                'whse_num' => $request->whse_num,
                                'trn_num' => $request->trn_num,
                                'trn_line' => $request->$trn_line,
                                'lpn_num' => $lpnNum,
                                'lpn_line' => $request->$lpn_line,
                                'loc_num' => $getPalletLoc->loc_num,
                                'to_loc' => $request->to_loc,
                                'lot_num' => $getLotNum,
                                'item_num' => $item,
                                'item_desc' => $request->$item_desc,
                                'qty_receivable' => $request->$qty_receivable,
                                'uom' => $request->$uom,
                                'qty_input' => $request->$qty_to_receive,
                                'base_uom' => $request->$uom_to_receive,
                                'qty_to_loss' => $request->$qty_to_loss,
                                'uom_to_loss' => $request->$uom_to_loss,
                            ];

                        $toReceipt[$i] =
                            [
                                'site_id' => auth()->user()->site_id,
                                'whse_num' => $request->whse_num,
                                'trn_num' => $request->trn_num,
                                'trn_line' => $request->$trn_line,
                                'item_num' => $item,
                                'qty_receivable' => $request->$qty_receivable,
                                'qty_receivable_conv' => $request->$qty_receivable,
                                'base_uom' => $request->$uom,
                                'loc_num' => $request->to_loc,
                                'lot_num' => $getLotNum,
                                'qty_input' => $request->$qty_to_receive,
                                'uom' => $request->$uom_to_receive,
                                'lpn_num' => $lpnNum,
                                'qty_to_loss' => $request->$qty_to_loss,
                                'uom_to_loss' => $request->$uom_to_loss,
                                'reason_code' => $request->$rCode,
                                'lpn_line' => $request->$lpn_line,
                            ];
                    }


                    // if($request->$qty_to_loss > 0){
                    //     $toLossMatlTrans = "Yes";
                    //     $toLoss[$i] =
                    //         [
                    //             'whse_num' => $request->whse_num,
                    //             'trn_num' => $request->trn_num,
                    //             'trn_line' => $request->$trn_line,
                    //             'lpn_num' => $lpnNum,
                    //             'lpn_line' => $request->$lpn_line,
                    //             'loc_num' => $getPalletLoc->loc_num,
                    //             'lot_num' => $getLotNum,
                    //             'item_num' => $request->$item,
                    //             'item_desc' => $request->$item_desc,
                    //             'qty_receivable' => $request->$qty_receivable,
                    //             'uom' => $request->$uom,
                    //             'qty_input' => $request->$qty_to_receive,
                    //             'base_uom' => $request->$uom_to_receive,
                    //             'qty_to_loss' => $request->$qty_to_loss,
                    //             'uom_to_loss' => $request->$uom_to_loss,
                    //             'reason_code' => $request->$rCode,
                    //         ];
                    // }
                }
            }
            // dd($record);
            if ($toLossMatlTrans == "Yes") {
                // $toLossResponse = TransferOrderReceiptController::processPalletToTrans(config('icapt.transtype.to_lost'),$toLoss);
            }
            $sendResponseFrom = TransferOrderReceiptController::processPalletToTrans(config('icapt.transtype.to_receipt_from'), $record);

            $sendResponseTo = TransferOrderReceiptController::processPalletToTrans(config('icapt.transtype.to_receipt_to'), $record);
            $updateTo = TransferOrderReceiptController::palletToProcess($toReceipt);

            // Update Pallet Status
            if ($updateTo == 'true') {
                $getLpn = Container::where('lpn_num', $lpnNum)->update(['status' => 'Open', 'loc_num' => $request->to_loc]);
            } else {
                throw ValidationException::withMessages(['Something Wrong happened.']);
            }

            LotService::updateLot("TO Receipt", $request);

            Alert::success('Success', __('success.processed', ['process' => __('Transfer Order Receipt')]))->persistent('Close');
            Session::put('modulename', 'TOReceipt');
            Session::put('trn_num', $request->trn_num);
            Session::put('trn_line', $request->$trn_line);

            DB::commit();

            // SAP Connected
            // SAP Intergration
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            //&& ($getPalletLoc->loc_num!=$request->trn_loc)
            if ($sap_trans_order_integration == 1) {
                $lpnnum = $request->lpn_num_field;
                $ref_num = $request->trn_num;
                $transferorder = new TransferOrder;
                $erp_ID = TransferOrder::select('erp_ID')->where('trn_num', $request->trn_num)->value('erp_ID');
                $result = SapCallService::postPalletTOReceipt($lpnnum, 'TO Receipt', $erp_ID);
                if ($result != 200) {
                    Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                }
            }




            // Generate barcode
            $transType = 'TransferOrderReceipt';

            if ($request->lot_num != null) {
                $check_expiry_date = LotService::getExpiryDate($request);

                // Generate barcode
                $input = BarcodeController::GetTOReceiptLabelData($request->whse_num, $request->trn_num, $request->$trn_line, $request->$item, null, $request->to_loc, $request->lot_num, $request->$qty_to_receive, $request->$uom_to_receive, $check_expiry_date, $transDate, $transType, $request->from_whse, $request->to_whse);
            } else {
                // Generate barcode
                $input = BarcodeController::GetTOReceiptLabelData($request->whse_num, $request->trn_num, $request->$trn_line, $request->$item, null, $request->to_loc, $request->lot_num, $request->$qty_to_receive, $request->$uom_to_receive, null, $transDate, $transType, $request->from_whse, $request->whse_num);
            }

            $tparm = new TparmView;
            $print_label = $tparm->getTparmValue('TransferOrderReceipt', 'print_label');

            if ($print_label == 1) {
                return BarcodeController::showLabelDefinition($input);
            } else {
                return redirect()->route('transferOrderItemList', ['whse_num' => $request->whse_num, 'trn_num' => $request->trn_num, 'item_num' => '']);
            }
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public function processPalletToTrans($transtype, $record)
    {

        // $recod_key = array_keys($record);
        // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $record[$recod_key[0]]['lpn_num'])->orderBy('lpn_line', 'ASC')->get();


        // $arrGetTrnLine = array();
        // $arrGetTrnLinebaseuom = array();
        // $arrGetTrnLineuom = array();
        // $arrGetItemDesc  = array();

        // $arrLossuom = array();
        // $arrQtyLoss  = array();




        // // Get the line by item_num
        // foreach($record as $key => $recordkey)
        // {
        //     $arrGetTrnLine[$recordkey['item_num'].$recordkey['base_uom']] = $recordkey['trn_line'];
        //     $arrGetTrnLinebaseuom[$recordkey['item_num'].$recordkey['base_uom']] = $recordkey['base_uom'];
        //     $arrGetTrnLineuom[$recordkey['item_num'].$recordkey['base_uom']] = $recordkey['uom'];
        //     $arrGetItemDesc[$recordkey['item_num'].$recordkey['base_uom']] = $recordkey['item_desc'] ?? null;

        //     $arrQtyLoss[$recordkey['item_num'].$recordkey['base_uom']] = $recordkey['qty_to_loss'];
        //     $arrLossuom[$recordkey['item_num'].$recordkey['base_uom']] = $recordkey['uom_to_loss'];
        // }

        //  'whse_num' => $request->whse_num,
        // 'trn_num' => $request->trn_num,
        // 'trn_line' => $request->$trn_line,
        // 'lpn_num' => $lpnNum,
        // 'lpn_line' => $request->$lpn_line,
        // 'loc_num' => $getPalletLoc->loc_num,
        // 'to_loc' => $request->to_loc,
        // 'lot_num' => $getLotNum,
        // 'item_num' => $request->$item,
        // 'item_desc' => $request->$item_desc,
        // 'qty_receivable' => $request->$qty_receivable,
        // 'uom' => $request->$uom,
        // 'qty_input' => $request->$qty_to_receive,
        // 'base_uom' => $request->$uom_to_receive,
        // 'qty_to_loss' => $request->$qty_to_loss,
        // 'uom_to_loss' => $request->$uom_to_loss,


        // "whse_num" => "Main"
        // "from_whse" => "MAINWH"
        // "trn_num" => "FA-160001"
        // "trn_line" => "1"
        // "lpn_num" => "FA-160001"
        // "lpn_line" => "1"
        // "from_loc" => "A1"
        // "loc_num" => "Transit"
        // "lot_num" => "1"
        // "item_num" => "FA-160001"
        // "item_desc" => "FA-160001"
        // "qty_req" => "200.0000"
        // "base_uom" => "EA"
        // "qty_input" => "200"
        // "uom" => "EA"


        // $arrStoreData = array();
        // $intIndex = 1;
        // foreach($getLpnDetailsQTY as $dataDatails)
        // {
        //     $arrStoreData[$intIndex]['whse_num'] = $record[$recod_key[0]]['whse_num'];

        //     $arrStoreData[$intIndex]['trn_num'] = $record[$recod_key[0]]['trn_num'];
        //     $arrStoreData[$intIndex]['trn_line'] = $arrGetTrnLine[$dataDatails->item_num.$dataDatails->uom];
        //     $arrStoreData[$intIndex]['lpn_num'] = $record[$recod_key[0]]['lpn_num'];
        //     $arrStoreData[$intIndex]['lpn_line'] = $dataDatails->lpn_line;
        //     $arrStoreData[$intIndex]['loc_num'] = $record[$recod_key[0]]['loc_num'];
        //     $arrStoreData[$intIndex]['to_loc'] = $record[$recod_key[0]]['to_loc'];
        //     $arrStoreData[$intIndex]['lot_num'] = $dataDatails->lot_num;
        //     $arrStoreData[$intIndex]['item_num'] = $dataDatails->item_num;

        //     $arrStoreData[$intIndex]['item_desc'] = $arrGetItemDesc[$dataDatails->item_num.$dataDatails->uom];
        //     $arrStoreData[$intIndex]['qty_receivable'] = $dataDatails->qty_contained;
        //     $arrStoreData[$intIndex]['base_uom'] = $arrGetTrnLinebaseuom[$dataDatails->item_num.$dataDatails->uom];
        //     $arrStoreData[$intIndex]['qty_input'] =$dataDatails->qty_contained;
        //     $arrStoreData[$intIndex]['uom'] = $arrGetTrnLineuom[$dataDatails->item_num.$dataDatails->uom];

        //     $arrStoreData[$intIndex]['qty_to_loss'] = $arrQtyLoss[$dataDatails->item_num.$dataDatails->uom];
        //     $arrStoreData[$intIndex]['uom_to_loss'] = $arrLossuom[$dataDatails->item_num.$dataDatails->uom];


        //     $intIndex++;
        // }
        //dd("sss",$record,$arrStoreData);

        //foreach($arrStoreData as $datas){

        // New Function
        $recod_key = array_keys($record);
        $arrStoreData = array();
        $intIndex = 1;
        $intLpnIndex = 1;
        foreach ($record as $dataDatails) {
            //check if contains more than 1 lpn line
            if (count($dataDatails) > 1) {
                // foreach($dataDatails as $key => $lpnDet){
                $arrStoreData[$intLpnIndex]['whse_num'] = $dataDatails['whse_num'];
                $arrStoreData[$intLpnIndex]['trn_num'] = $dataDatails['trn_num'];
                $arrStoreData[$intLpnIndex]['trn_line'] = $dataDatails['trn_line'];
                $arrStoreData[$intLpnIndex]['lpn_num'] = $dataDatails['lpn_num'];
                $arrStoreData[$intLpnIndex]['lpn_line'] = $dataDatails['lpn_line'];
                $arrStoreData[$intLpnIndex]['loc_num'] = $dataDatails['loc_num'];
                $arrStoreData[$intLpnIndex]['to_loc'] = $dataDatails['to_loc'];
                $arrStoreData[$intLpnIndex]['lot_num'] = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->where('lpn_line', $dataDatails['lpn_line'])->value('lot_num') ?? '';
                $arrStoreData[$intLpnIndex]['item_num'] = $dataDatails['item_num'];

                $arrStoreData[$intLpnIndex]['item_desc'] = $dataDatails['item_desc'];
                $arrStoreData[$intLpnIndex]['qty_receivable'] = $dataDatails['qty_receivable'];
                $arrStoreData[$intLpnIndex]['base_uom'] = $dataDatails['base_uom'];
                $arrStoreData[$intLpnIndex]['qty_input'] = $dataDatails['qty_input'];
                $arrStoreData[$intLpnIndex]['uom'] = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->where('lpn_line', $dataDatails['lpn_line'])->value('uom') ?? '';

                $intLpnIndex++;
                //}
            } else {
                // Check LPN Line
                $getLPNCount = explode(",", $dataDatails['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataDatails['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetail) {

                    $arrStoreData[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $arrStoreData[$intLpnIndex]['trn_num'] = $record[$intIndex]['trn_num'];
                    $arrStoreData[$intLpnIndex]['trn_line'] = $record[$intIndex]['trn_line'];
                    $arrStoreData[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
                    $arrStoreData[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;
                    $arrStoreData[$intLpnIndex]['loc_num'] = $record[$intIndex]['loc_num'];
                    $arrStoreData[$intLpnIndex]['to_loc'] = $record[$intIndex]['to_loc'];
                    $arrStoreData[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
                    $arrStoreData[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];

                    $arrStoreData[$intLpnIndex]['item_desc'] = $record[$intIndex]['item_desc'];
                    $arrStoreData[$intLpnIndex]['qty_receivable'] = $record[$intIndex]['qty_receivable'];
                    $arrStoreData[$intLpnIndex]['base_uom'] = $record[$intIndex]['base_uom'];
                    $arrStoreData[$intLpnIndex]['qty_input'] = $record[$intIndex]['qty_input'];
                    $arrStoreData[$intLpnIndex]['uom'] = $getLpnDetail->uom;

                    $intLpnIndex++;
                }
            }
            $intIndex++;
        }

        // dd($record);
        foreach ($arrStoreData as $datas) {
            // if($transtype == config('icapt.transtype.to_lost')){
            //     $transData = new Request($datas);
            //     $sendResponse = PalletService::palletMatlTrans($transtype,$transData);
            // }
            // else{
            if ($datas['qty_input'] > 0) {
                $transData = new Request($datas);
                $sendResponse = PalletService::palletMatlTrans($transtype, $transData);
            }
            // }
        }
        return 'true';
    }

    public function palletToProcess($record)
    {

        $recod_key = array_keys($record);
        // $getLpnDetailsQTY = ContainerItem::where('lpn_num', $record[$recod_key[0]]['lpn_num'])->orderBy('lpn_line', 'ASC')->get();

        // $arrGetTrnLine = array();
        // $arrGetTrnLinebaseuom = array();
        // $arrGetTrnLineuom = array();
        // $arrGetItemDesc  = array();

        // $arrLossuom = array();
        // $arrQtyLoss  = array();

        // // Get the line by item_num
        // foreach($record as $key => $recordkey)
        // {
        //     $arrGetTrnLine[$recordkey['item_num'].$recordkey['uom']] = $recordkey['trn_line'];
        //     $arrGetTrnLinebaseuom[$recordkey['item_num'].$recordkey['uom']] = $recordkey['base_uom'];
        //     $arrGetTrnLineuom[$recordkey['item_num'].$recordkey['uom']] = $recordkey['uom'];
        //     $arrGetItemDesc[$recordkey['item_num'].$recordkey['uom']] = $recordkey['item_desc'] ?? null;


        //     $arrQtyLoss[$recordkey['item_num'].$recordkey['uom']] = $recordkey['qty_to_loss'];
        //     $arrLossuom[$recordkey['item_num'].$recordkey['uom']] = $recordkey['uom_to_loss'];


        // }


        // $index = 0;
        // foreach($getLpnDetailsQTY as $dataline){
        //     $recordMashup[$index]['site_id'] = $dataline->site_id;
        //     $recordMashup[$index]['whse_num'] = $record[$recod_key[0]]['whse_num'];

        //     $recordMashup[$index]['trn_num'] = $record[$recod_key[0]]['trn_num'];
        //     $recordMashup[$index]['trn_line'] = $arrGetTrnLine[$dataline->item_num.$dataline->uom];
        //     $recordMashup[$index]['item_num'] = $dataline->item_num;
        //     $recordMashup[$index]['qty_receivable'] = $dataline->qty_contained;

        //     $recordMashup[$index]['qty_receivable_conv'] = $dataline->qty_contained;
        //     $recordMashup[$index]['base_uom'] =  $arrGetTrnLinebaseuom[$dataline->item_num.$dataline->uom];
        //     $recordMashup[$index]['loc_num'] = $record[$recod_key[0]]['loc_num'];
        //     $recordMashup[$index]['lot_num'] = $dataline->lot_num;
        //     $recordMashup[$index]['qty'] = $dataline->qty_contained;
        //     $recordMashup[$index]['uom'] =  $arrGetTrnLineuom[$dataline->item_num.$dataline->uom];
        //     $recordMashup[$index]['lpn_num'] = $record[$recod_key[0]]['lpn_num'];

        //     $recordMashup[$index]['qty_to_loss'] = $arrQtyLoss[$dataline->item_num.$dataline->uom];
        //     $recordMashup[$index]['uom_to_loss'] = $arrLossuom[$dataline->item_num.$dataline->uom];
        //     $recordMashup[$index]['reason_code'] = $record[$recod_key[0]]['reason_code'];




        //     $index++;
        // }

        // New Function
        $intIndex = 1;
        $intLpnIndex = 1;
        // dd($record);
        foreach ($record as $dataline) {
            //check if contains more than 1 lpn line
            if (count($dataline) > 1) {
                // foreach($dataline as $key => $lpnlineDet){
                $recordMashup[$intLpnIndex]['site_id'] = $dataline['site_id'];
                $recordMashup[$intLpnIndex]['whse_num'] = $dataline['whse_num'];
                $recordMashup[$intLpnIndex]['trn_num'] = $dataline['trn_num'];
                $recordMashup[$intLpnIndex]['trn_line'] = $dataline['trn_line'];
                $recordMashup[$intLpnIndex]['lpn_num'] = $dataline['lpn_num'];
                $recordMashup[$intLpnIndex]['lpn_line'] = $dataline['each_lpn_line'] ?? $dataline['lpn_line'];
                $recordMashup[$intLpnIndex]['loc_num'] = $dataline['loc_num'];
                $recordMashup[$intLpnIndex]['lot_num'] = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('lpn_line', $dataline['lpn_line'])->value('lot_num') ?? '';
                $recordMashup[$intLpnIndex]['item_num'] = $dataline['item_num'];

                $recordMashup[$intLpnIndex]['qty_receivable'] = $dataline['qty_receivable'];
                $recordMashup[$intLpnIndex]['qty_receivable_conv'] = $dataline['qty_receivable'];
                $recordMashup[$intLpnIndex]['qty'] = $dataline['qty_input'];
                $recordMashup[$intLpnIndex]['base_uom'] = $dataline['base_uom'];
                $recordMashup[$intLpnIndex]['uom'] = ContainerItem::where('lpn_num', $dataline['lpn_num'])->where('lpn_line', $dataline['lpn_line'])->value('uom') ?? '';

                $intLpnIndex++;
                //}
            } else {
                // Check LPN Line
                $getLPNCount = explode(",", $dataline['lpn_line']);
                $getLpnDetailsQTY = ContainerItem::where('lpn_num', $dataline['lpn_num'])->whereIn('lpn_line', $getLPNCount)->orderBy('lpn_line', 'ASC')->get();
                foreach ($getLpnDetailsQTY as $getLpnDetail) {

                    $recordMashup[$intLpnIndex]['site_id'] = $record[$intIndex]['site_id'];
                    $recordMashup[$intLpnIndex]['whse_num'] = $record[$intIndex]['whse_num'];
                    $recordMashup[$intLpnIndex]['trn_num'] = $record[$intIndex]['trn_num'];
                    $recordMashup[$intLpnIndex]['trn_line'] = $record[$intIndex]['trn_line'];
                    $recordMashup[$intLpnIndex]['lpn_num'] = $getLpnDetail->lpn_num;
                    $recordMashup[$intLpnIndex]['lpn_line'] = $getLpnDetail->lpn_line;
                    $recordMashup[$intLpnIndex]['loc_num'] = $record[$intIndex]['loc_num'];
                    $recordMashup[$intLpnIndex]['lot_num'] = $getLpnDetail->lot_num;
                    $recordMashup[$intLpnIndex]['item_num'] = $record[$intIndex]['item_num'];

                    $recordMashup[$intLpnIndex]['qty_receivable'] = $record[$intIndex]['qty_receivable'];
                    $recordMashup[$intLpnIndex]['qty_receivable_conv'] = $record[$intIndex]['qty_receivable'];
                    $recordMashup[$intLpnIndex]['qty'] = $record[$intIndex]['qty_input'];
                    $recordMashup[$intLpnIndex]['base_uom'] = $record[$intIndex]['base_uom'];
                    $recordMashup[$intLpnIndex]['uom'] = $getLpnDetail->uom;

                    $intLpnIndex++;
                }
            }
            $intIndex++;
        }


        // dd($recordMashup);
        // $rank = app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($recordMashup[0]['whse_num']), base64_encode($recordMashup[0]['item_num']), base64_encode($recordMashup[0]['trn_loc']));

        //dd($rank,$recordMashup[0]['whse_num'],$recordMashup[0]['item_num'],$recordMashup[0]['trn_loc']);
        $toArr = [];
        $fromArr = [];
        // $index = 0;
        $i = 0;


        //dd($record);
        $countData = count($recordMashup);
        $dataRow = 0;
        foreach ($recordMashup as $datas) {

            $loss_qty = 0;
            $transData = new Request($datas);
            $transData = validateSansentiveValue($transData);
            $transData['ref_num'] = $transData->trn_num;
            $transData['ref_line'] = $transData->trn_line;
            $transData['qty'] = $transData->qty;
            $requestTo = clone $transData;

            $transData['lot_num'] = $transData->lot_num;

            $baseuom = Item::where('item_num', $requestTo['item_num'])->where('site_id', auth()->user()->site_id)->value('uom');;
            $selectuom = $requestTo['base_uom'];
            $lineuom = $requestTo['base_uom'];
            $qty = $requestTo['qty'];
            $item_num = $requestTo['item_num'];
            // Conversion for TO item
            // $to_qty = UOMService::convertTORequest($transData);
            $convertUom = UomConv::convertUOM($baseuom, $lineuom, $selectuom, $qty, $item_num, '', '', '');
            if ($requestTo['qty_to_loss'] > 0) {
                $lossbaseuom = $requestTo['uom'];
                $lossselectuom = $requestTo['uom_to_loss'];
                $losslineuom = $requestTo['uom_to_loss'];
                $lossqty = $requestTo['qty_to_loss'];
                $convertUomToLoss = UomConv::convertUOM($lossbaseuom, $losslineuom, $lossselectuom, $lossqty, $item_num, '', '', '');
                $loss_qty = $convertUomToLoss['conv_qty_to_line']['qty'];
            }
            // dd($convertUom, $baseuom, $lineuom, $selectuom, $qty, $item_num);
            $to_qty = $convertUom['conv_qty_to_base']['qty'];
            $receipt_qty = $convertUom['conv_qty_to_line']['qty'];

            // dd($convertUom, $baseuom, $lineuom, $selectuom, $qty, $item_num);

            if ($requestTo['qty'] > 0) {
                $updateTransOrder = GeneralService::updateTransOrderReceipt($requestTo['trn_num'], $requestTo['trn_line'], $receipt_qty, $loss_qty);
                // dd($requestTo);
                //Update to Location
                // $updateItemLocation = GeneralService::updateItemLocationQty($requestTo['whse_num'], $requestTo['loc_num'], $requestTo['item_num'], $to_qty, $requestTo['lot_num'], $convertUom['conv_qty_to_base']['uom'], 0, null);
                $toArr = [
                    'whse_num' => $requestTo['whse_num'],
                    'loc_num' => $requestTo['loc_num'],
                    'lot_num' => $requestTo['lot_num'],
                    'item_num' => $requestTo['item_num'],
                    'qty_conv' => $to_qty,
                    'uom_conv' => $convertUom['conv_qty_to_base']['uom'],
                    'rank' => app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($updateTransOrder->to_whse), base64_encode($updateTransOrder->item_num), base64_encode($updateTransOrder->TransferOrder->trn_loc))
                ];

                $updateToItemLocation = PalletService::updateItemLocLotQty($fromArr = [], $toArr, 0, 'To TO Receipt');



                //$updateTransOrderSublines = GeneralService::updateTransOrderLotLocReceipt($requestTo['loc_num'], $requestTo['lot_num'], $requestTo['trn_num'], $requestTo['trn_line'], $to_qty, $loss_qty);
                $updateTransOrderSublines = GeneralService::updateTransOrderLotLocReceiptPallet($requestTo['trn_num'], $requestTo['trn_line'], $qty, $qty_loss = 0, $requestTo['lpn_num']);

                //updateTransOrderLotLocReceiptPallet($trn_num, $trn_line, $qty, $qty_loss = 0,$requestTo['lpn_num'])
                $TransitLocQtyOnHand = TOService::TransitQtyOnHand($updateTransOrder->TransferOrder, $updateTransOrder->item_num);
                $itemloc = new ItemLoc;

                // Check if the Transit Location is in Item Location
                $checkTransitLoc = $itemloc
                    ->where('whse_num', $updateTransOrder->to_whse)
                    ->where('loc_num', $updateTransOrder->TransferOrder->trn_loc)
                    ->where('item_num', $updateTransOrder->item_num)
                    ->first();
                // dd($updateTransOrder, $TransitLocQtyOnHand, $checkTransitLoc);
                $fromArr = [
                    'whse_num' => $updateTransOrder->to_whse,
                    'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                    'lot_num' =>  $transData['lot_num'],
                    'item_num' => $updateTransOrder->item_num,
                    'qty_conv' => $to_qty,
                    'uom_conv' => $updateTransOrder->uom,

                ];

                $toArr = [];

                $updateFromItemLocation = PalletService::updateItemLocLotQty($fromArr, $toArr = [], 0, 'From TO Receipt');

                // if ($checkTransitLoc) {
                //     // Update the Transit Location's qty on hand
                //     $resultItemLoc = $itemloc->updateOrCreate(
                //         [
                //             'whse_num' => $updateTransOrder->to_whse,
                //             'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                //             'item_num' => $updateTransOrder->item_num,
                //         ],
                //         [
                //             'qty_on_hand' => $TransitLocQtyOnHand,
                //         ]
                //     );
                // }
                // // Create the Transit Location
                // else {
                //     $resultItemLoc = $itemloc->updateOrCreate(
                //         [
                //             'whse_num' => $updateTransOrder->to_whse,
                //             'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                //             'item_num' => $updateTransOrder->item_num,
                //         ],
                //         [
                //             'qty_on_hand' => $TransitLocQtyOnHand,
                //             'uom' => $updateTransOrder->uom,
                //             'rank' => app('App\Http\Controllers\ApiController')->getnextRank(base64_encode($updateTransOrder->to_whse), base64_encode($updateTransOrder->item_num), base64_encode($updateTransOrder->TransferOrder->trn_loc)),
                //         ]
                //     );
                // }

                // if ($resultItemLoc->qty_on_hand <= 0) {
                //     $resultItemLoc->delete();
                // }
                // In transline only 1 lot , shd follow reques lot
                // if ($updateTransOrderSublines->trn_lot) {
                //     // LotLoc for original for deduct
                //     $TransitLotLocQtyOnHand = LotLoc::select('qty_on_hand')->where('whse_num', $updateTransOrder->to_whse)->where('item_num', $updateTransOrder->item_num)
                //         ->where('lot_num', $updateTransOrderSublines->trn_lot)->where('loc_num', $updateTransOrder->TransferOrder->trn_loc)->value('qty_on_hand');

                //     $TransitLotLocQtyOnHand = $TransitLotLocQtyOnHand - $to_qty - $loss_qty;
                //     $lotloc = new LotLoc;
                //     $resultItemlotloc = $lotloc->updateOrCreate([
                //         'whse_num' => $updateTransOrder->to_whse,
                //         'loc_num' => $updateTransOrder->TransferOrder->trn_loc,
                //         'item_num' => $updateTransOrder->item_num,
                //         'lot_num' => $updateTransOrderSublines->trn_lot,
                //     ], [
                //         'qty_on_hand' => $TransitLotLocQtyOnHand,
                //         'uom' => $updateTransOrder->uom,
                //     ]);

                //     if ($resultItemlotloc->qty_on_hand <= 0) {
                //         $resultItemlotloc->delete();
                //     }
                // }
            }
            $dataRow++;
        }
        if ($countData == $dataRow) {
            return 'true';
        } else {
            return 'false';
        }
    }
}
