@extends('layout.mobile.app')

@section('content')
@section('title', __('Transfer Order Shipping - CW'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$ItemList->from_whse"
            :itemnum="$ItemList->item_num"
            :itemdesc="$ItemList->item_desc"
            :refnum="$ItemList->trn_num"
            :refline="$ItemList->trn_line"
            :qtybalance="$ItemList->qty_required ?? 0"
            :qtybalanceuom="$ItemList->uom"
            :submiturl="route('runTOShippingCWProcess')"
            :catch-weight-tolerance="$ItemList->item->catch_weight_tolerance"
            :disable-create-new-item-loc="0"
            :allow-over="$allow_over_ship"
            :line-uom="$ItemList->uom"
            :print-label="$printLabel"
            transtype="to"
            trans-type="TOShipping"
            :incoming="false">

            <input type="hidden" name="non_inv" id="non_inv" value="false">
            <input type="hidden" name="ref_release" id="ref_release" class="form-control border-primary" value="{{ $ItemList->sequence }}">
            <input type="hidden" name="to_whse" id="to_whse" class="form-control border-primary" value="{{ $ItemList->to_whse }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num',$def_loc) }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.trn_num') }} : {{ $ItemList->trn_num }} | {{ $ItemList->trn_line }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack()
    {
        var URLRedirect = "{{$url}}";
        url = URLRedirect.replace(/&amp;/g, "&");
        window.location.href   =  url;
    }

    $(document).ready(function() {
        $("#lot_num").on('change', function() {
            if ($("#lot_num").val()) {
                $.ajax({
                    url: '{{ route('getVendLot') }}',
                    type: 'GET',
                    data: {
                        lot_num: $("#lot_num").val(),
                    },
                    success: function(data) {
                        display('/displayLotQuantityCoPick',
                            'item_num,whse_num,loc_num,lot_num,qtybalance,qtybalanceuom,cust_num,qtybalanceuom,vend_num',
                            'qty_available,max_qty_input'
                        );
                    }
                });
            }
        });
    });
</script>

@endsection()
