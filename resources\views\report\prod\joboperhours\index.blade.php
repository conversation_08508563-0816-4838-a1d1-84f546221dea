
@extends('layout.app')
@include('report.style')
@section('content')
<div class="margin-header">
    <div class="card-header-title ">
        <table class="title-header">
            <tr>
                <td class="title-division" ><h4 class="title" id="basic-layout-icons">{{ __('admin.title.job_oper_report') }}</h4></td>
            </tr>
        </table>
    </div>
    <div>
        <form class="form-group" id="job_oper_report" name="job_oper_report" method="POST" action="{{route('printjoboperhours')}}">
        @csrf
        <div class="default-button">
            <button id="filter" type="button" class="displayExcel-button btn btn-primary button-padding" style="background-color:#37BC9B;"><i class="icon-square-plus"></i> {{__('admin.button.generate')}}</button></td>
            <button id="btn-refresh" type="button" class="btn btn-primary button-padding" onclick="document.getElementById('inv_trans_by_item_report').reset(); document.getElementById('from_date').value = null; return false;">
                <i class="icon-refresh2"></i> {{__('admin.button.reset')}}
            </button>
            <button type="submit" id="print" class="btn btn-info button-padding">
                <i class="icon-print"></i> {{__('admin.button.print')}}
            </button>
        </div>
        <table align="center">
            <tr>
                <td width='100px'><label for="date">{{ __('admin.label.start_date_plan') }}</label></td>
                <td width='300px'><input autocomplete="off" id="from_start_date" tabindex="1" class="form-control border-primary input-group from_date" name="from_start_date" placeholder="From Date"> <!--  value="{{ old('start_date') }}" required> --></td>
                <td width='50px'>&nbsp;</td>
                <td width='50px'><label>&ensp; To</label></td>
                <td width='300px'><input autocomplete="off" id="to_start_date" tabindex="7" class="form-control border-primary input-group to_date" name="to_start_date" placeholder="To Date" value="{{ old('start_date') }}"></td>
            </tr>
            <tr>
                <td width='100px'><label for="date">{{ __('admin.label.end_date_plan') }}</label></td>
                <td width='300px'><input autocomplete="off" id="from_date" tabindex="2" class="form-control border-primary input-group from_date" name="from_date" placeholder="From Date"> <!--  value="{{ old('due_date') }}" required> --></td>
                <td width='50px'>&nbsp;</td>
                <td width='50px'><label>&ensp; To</label></td>
                <td width='300px'><input autocomplete="off" id="to_date" tabindex="8" class="form-control border-primary input-group to_date" name="to_date" placeholder="To Date" value="{{ old('due_date') }}"></td>
            </tr>
            <tr>
                <td><label for="job_num">{{ __('admin.label.job_num') }}</label></td>
                <td><input type="text" name="from_job_num" id="from_job_num" tabindex="3" class="form-control border-primary" placeholder="From Job Order"></td>
                <td>
                    <button type="button" name="Job Order" tabindex="-1" onClick="selection('/getCompletedJobSuffixSorted','whse_num,from_job_num','job_num','from_job_num');modalheader(this.id,'Job Orders');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    <input type="hidden" name="whse_num" id="whse_num" tabindex="-1" class="form-control border-primary" placeholder="From Job Order" value="{{auth()->user()->getCurrWhse()}}">
                </td>
                <td width='50px'><label>&ensp; To</label></td>
                <td><input type="text" name="to_job_num" id="to_job_num" tabindex="9" class="form-control border-primary" placeholder="To Job Order"></td>
                <td>
                    <button type="button" name="Job Order" tabindex="-1" onClick="selection('/getCompletedJobSuffixSorted','whse_num,to_job_num','job_num','to_job_num');modalheader(this.id, 'Job Orders');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    <input type="hidden" name="whse_num" id="whse_num" tabindex="-1" class="form-control border-primary" placeholder="To Job Order" value="{{auth()->user()->getCurrWhse()}}">
                </td>
            </tr>
            <tr>
                <td><label for="job_status">{{ __('admin.label.status') }}</label></td>
                <td colspan="4">
                    <input type="radio" name="job_status" value="all" id="job_status" tabindex="4" checked> {{ __('admin.label.all') }} &nbsp;&nbsp;&nbsp;
                    <input type="radio" name="job_status" value="Open" id="job_status"> {{ __('admin.label.open') }} &nbsp;&nbsp;&nbsp;
                    <input type="radio" name="job_status" value="Released" id="job_status"> {{ __('admin.label.released') }} &nbsp;&nbsp;
                    <input type="radio" name="job_status" value="Completed" id="job_status"> {{ __('admin.label.completed') }}<br>
                </td>
            </tr>
            <tr>
                <td><label for="item_num">{{ __('admin.label.item_num') }}</label></td>
                <td><input type="text" name="from_item_num" id="from_item_num" tabindex="5" class="form-control border-primary" placeholder="From Item"></td>
                <td>
                    <button type="button" name="{{__('admin.list.items')}}" tabindex="-1" onClick="selection('/getItem','from_item_num','item_num','from_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                </td>
                <td width='50px'><label>&ensp; To</label></td>
                <td><input type="text" name="to_item_num" id="to_item_num" tabindex="10" class="form-control border-primary" placeholder="To Item"></td>
                <td>
                    <button type="button" name="{{__('admin.list.items')}}" tabindex="-1" onClick="selection('/getItem','to_item_num','item_num','to_item_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                </td>
            </tr>
            <tr>
                <td><label for="product_code">{{ __('admin.label.product_code') }}</label></td>
                <td><input type="text" name="from_product_code" id="from_product_code" tabindex="6" class="form-control border-primary" placeholder="From Product Code"></td>
                <td>
                    <button type="button" name="Product Code" tabindex="-1" onClick="selection('/getProdCode','from_product_code', 'product_code', 'from_product_code');modalheader(this.id, 'Product Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                </td>
                <td width='50px'><label>&ensp; To</label></td>
                <td><input type="text" name="to_product_code" id="to_product_code" tabindex="11" class="form-control border-primary" placeholder="To Product Code"></td>
                <td>
                    <button type="button" name="Product Code" tabindex="-1" onClick="selection('/getProdCode','to_product_code', 'product_code', 'to_product_code');modalheader(this.id, 'Product Codes');" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                </td>
            </tr>
        </table>
        <hr>
        </form>
        <div class="card-block card-dashboard">
            @include('report.prod.joboperhours.list')
        </div>
    </div>
</div>

@push('scripts')
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
    <script src="https://cdn.jsdelivr.net/gh/jeffreydwalter/ColReorderWithResize@9ce30c640e394282c9e0df5787d54e5887bc8ecc/ColReorderWithResize.js"></script>
@endpush


@include('util.js-datatable')

<script type="text/javascript">

    var defaultColumns= ['job_num','suffix', 'job_status', 'item_num','item_desc','qty_released','qty_completed',
            'qty_scrapped','uom','setup_time','run_time','machine_time','tot','ave_labour_run_time_per_piece',
            'ave_machine_run_time_per_piece','ave_total_run_per_piece','plan_labour_per_piece','plan_machine_per_piece'];

    if(localStorage.getItem('columnsetting_JobOperHourReport') == null){
        localStorage.setItem('columnsetting_JobOperHourReport', JSON.stringify(defaultColumns));
    }

    //Get Cookies Columns
    var getItem = localStorage.getItem('columnsetting_JobOperHourReport');
    var columnsList = JSON.parse(getItem);

    var arrColumns=[];
    for(var i = 0; i < columnsList.length; i++){
        if(columnsList[i]=='job_num' || columnsList[i]=='suffix' || columnsList[i]=='job_status' || columnsList[i]=='item_num' || columnsList[i]=='item_desc'){
            arrColumns.push({data:columnsList[i], name:columnsList[i]});
        }else if(columnsList[i]=='uom'){
            arrColumns.push({data:columnsList[i], name:columnsList[i], width:'5%'});
        }else{
            arrColumns.push({data:columnsList[i], name:columnsList[i], class:'dt-right'});
        }
    }

    //onClick "Generate" button
    $('.displayExcel-button').click(function() {
        $('.default-button').css('padding-right', '220px');
    });

    jQuery(function($){
        $("#job_oper_report").validate({
            onchange:true,
            rules:{
                from_job_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_job_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_item_num:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_item_num:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                },
                from_product_code:{
                    remote:{
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
                to_product_code:{
                    remote: {
                        url: "{{ route('validation') }}",
                        type: "post",
                        data: { _token: $('input[name="_token"]').val() }
                    }
                }
            },
            messages:{
                from_job_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.job_num') ]) }}"
                },
                to_job_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.job_num') ]) }}"
                },
                from_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                to_item_num:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.item_num') ]) }}"
                },
                from_product_code:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                },
                to_product_code:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('admin.label.product_code') ]) }}"
                }
            },
        });
    });

    $(document).ready(function() {
        getFilterReset(getTable,arrColumns);
        // $('#filter').click(function(){

        //     getTable();

        //     $('#btn-refresh').click(function(){
        //         $('input').each(function() {
        //             $(this).val('');
        //         });
        //         $('.dataTable').DataTable().ajax.reload();
        //         // $('.dataTable').DataTable().$('select').each(function() {
        //         //     $(this).val('');
        //         // })
        //         // $('.dataTable').DataTable().columns().search('');
        //         // $('.dataTable').DataTable().draw();
        //     });

        //     var colreorder = new $.fn.dataTable.ColReorder(table);

        //     var colvis = new $.fn.dataTable.ColVis( table );
        //     $(colvis.button() ).insertAfter('div.dataTables_length');

        //     table.on('column-reorder.dt.mouseup column-resize.dt.mouseup', function(event) {
        //         $(".buttons-excel").css('width','');
        //         $("div").find('.ColVis').remove();
        //         var colvis = new $.fn.dataTable.ColVis( table );
        //         $(colvis.button() ).insertAfter('div.dataTables_length');

        //         var arrColumns = [];
        //         for(i=0;i<colvis.s.dt.aoColumns.length;i++){
        //             arrColumns.push(colvis.s.dt.aoColumns[i].data);
        //         }

        //         // Save Cookies
        //         var urlgo = '<?php echo route('save-cookies');?>';
        //         var token = '<?php echo csrf_token() ?>';
        //         $.ajax({
        //                     "url": urlgo,
        //                     "data": {
        //                         "datasetting": arrColumns,
        //                         "tableName": colvis.s.dt.nTable.id,
        //                         "_token": token,
        //                     },
        //                     "dataType": 'json',
        //                     "type": 'POST',
        //                     "success": function (resp) {
        //                         console.log(resp);
        //                     },
        //                 });

        //         localStorage.setItem('columnsetting'+'_'+colvis.s.dt.nTable.id, JSON.stringify(arrColumns)); //stringify object and store
        //     });
        // });

        // $("#JobOperHourReport thead input:not(#mass-chk)").on( 'keyup', function (e) {
        //     if (e.keyCode == 13) {
        //         $('#JobOperHourReport').DataTable().column($(this).parent().index() + ':visible').search(this.value).draw();
        //     }
        // });

        $(window).resize(function (e) {
            $("#JobOperHourReport").DataTable().columns.adjust();
        });
        function getTable(){
            unit_quantity_format = "{{$unit_quantity_format}}";

            let ajaxMethod = {
                url : '{!! route('joboperhourrep.data') !!}',
                method: 'get',
                data: function (d) {
                    d.from_start_date = $('#from_start_date').val();
                    d.to_start_date = $('#to_start_date').val();
                    d.from_date = $('#from_date').val();
                    d.to_date = $('#to_date').val();
                    d.from_job_num = $('#from_job_num').val();
                    d.to_job_num = $('#to_job_num').val();
                    d.job_status = $("input[name='job_status']:checked").val();
                    d.from_item_num = $('#from_item_num').val();
                    d.to_item_num = $('#to_item_num').val();
                    d.from_product_code = $("#from_product_code").val();
                    d.to_product_code = $('#to_product_code').val();
                },
                dataSrc: function (json) {
                    var result = new Array();
                    for (i = 0; i < json.data.length; i++) {
                        if(json.data[i].qty_completed == 0){
                            console.log();
                            json.data[i].ave_labour_run_time_per_piece = "NA";
                            json.data[i].ave_machine_run_time_per_piece = "NA";
                            json.data[i].ave_total_run_per_piece = "NA";
                        }
                        else{
                            json.data[i].ave_labour_run_time_per_piece = numberFormatPrecision(json.data[i].ave_labour_run_time_per_piece,5);
                            json.data[i].ave_machine_run_time_per_piece = numberFormatPrecision(json.data[i].ave_machine_run_time_per_piece,5);
                            json.data[i].ave_total_run_per_piece = numberFormatPrecision(json.data[i].ave_total_run_per_piece,5);
                        }
                        result.push({
                            'job_num': json.data[i].job_num,
                            'suffix': json.data[i].suffix,
                            'job_status': json.data[i].job_status,
                            'item_num': json.data[i].item_num,
                            'item_desc': json.data[i].item_desc,
                            'qty_released': numberFormatPrecision(json.data[i].qty_released,unit_quantity_format),
                            'qty_completed': numberFormatPrecision(json.data[i].qty_completed,unit_quantity_format),
                            'qty_scrapped': numberFormatPrecision(json.data[i].qty_scrapped,unit_quantity_format),
                            'uom': json.data[i].uom,
                            'setup_time': numberFormatPrecision(json.data[i].setup_time,5),
                            'run_time': numberFormatPrecision(json.data[i].run_time,5),
                            'machine_time': numberFormatPrecision(json.data[i].machine_time,5),
                            'tot': numberFormatPrecision(json.data[i].tot,5),
                            'ave_labour_run_time_per_piece': json.data[i].ave_labour_run_time_per_piece,
                            'ave_machine_run_time_per_piece': json.data[i].ave_machine_run_time_per_piece,
                            'ave_total_run_per_piece': json.data[i].ave_total_run_per_piece,
                            'plan_labour_per_piece': numberFormatPrecision(json.data[i].labour_hours_per_piece,5),
                            'plan_machine_per_piece': numberFormatPrecision(json.data[i].machine_hours_per_piece,5),
                        });
                    }
                    return result;
                }
            };

            generateTable('JobOperHourReport', arrColumns, ajaxMethod, 'JobOperationHours', {serverSide: true, searching: true});

            $(".dataTable thead input:not(#mass-chk)").on('keyup', function (e) {
                if (e.keyCode == 13) {
                    $('.dataTable').DataTable().column($(this).parent().index()).search(this.value).draw();
                }
            });
            $(".dataTable thead select:not(#mass-chk)").on('change', function (e) {
                $('.dataTable').DataTable().column($(this).parent().index()).search(this.value).draw();
            });
        }
    });
</script>

<style>
    .title-header{
        width:100%;}
    .title{
        padding-top:2px;
        font-size: 22px !important;
        font-weight: bold;}
    .title-division{
        width:50%;
        float:left;}
    .default-button{
        float:right;
        margin-top:-59.4px;
        padding-right:0px;
        position: relative;}
    .button-padding{
        margin:5px 0.5px;}
    .card-block{
        overflow:auto;
        margin-top: -18px;
        padding-right: 0rem;
        padding-left: 0rem;}
    /* Export button */
    div.dt-buttons{
        float: right !important;
        margin-top: -54.5px;}
    .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle){
        border-bottom-right-radius: 0.18rem !important;
        border-top-right-radius: 0.18rem !important;}
    .btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
        border-bottom-left-radius: 0.18rem !important;
        border-top-left-radius: 0.18rem !important;}
    div.dt-buttons.btn-group>a.btn-secondary{
        width: 110px !important;
        margin-left: 4px !important;}
    a.btn.btn-success{
        border-color:#4FC3F7;
        background-color:#4FC3F7;}
    label{
        margin-bottom: 0.1rem !important;}
    .margin-header input.form-control.border-primary{
        padding-left:10px;
        height: 25px;}
    .form-group{
        margin-top:1rem;
        margin-bottom: 5px;}
    form td{
        padding: 2px 1px;}
    .table{
        max-width: none;}
    .table th, .table td{
        padding: 0.2rem 0.2rem !important;}
    div.dataTables_length>label{
        width: 110px !important;}
    button.ColVis_Button>span{
        font-size: 9pt;}
    /* table header */
    table.dataTable thead>tr>th.sorting_asc, table.dataTable thead>tr>th.sorting_desc,
    table.dataTable thead>tr>th.sorting, table.dataTable thead>tr>td.sorting_asc, table.dataTable thead>tr>td.sorting_desc,
    table.dataTable thead>tr>td.sorting div.card-block>table#JobOperHourReport thead>tr:first-child>th,
    div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        padding-right: 25px !important;
    }
    div.card-block>table#JobOperHourReport>thead>tr:first-child>th, div.dataTables_scrollHeadInner>table>thead>tr:first-child>th{
        background-color: rgb(220,220,220);}
    div.dataTables_scrollBody>table#JobOperHourReport>thead>tr.first-child>th{
        background-color: white;}
    table#datatable.table.table-bordered.table-hover.nowrap.datatable.no-footer td>a>button.btn.btn-primary{
        padding: 0.1rem 0.2rem !important;}
    div.dataTables_wrapper div.dataTables_paginate{
        margin-bottom: 3rem !important;}
    li.paginate_button.page-item.active>a {
        font-size: 9pt;}
    .dt-center {
    text-align: center;}
    .dt-right {
        text-align: right;}
    .dt-buttons {
        padding-bottom: 15px;}
    .dataTables_length {
        float:left;
        margin-left: 10px;
        padding-top: 0.5em;
        padding-bottom: 5px;}
     div.dataTables_info {
        padding-top: 0.85em;
        position:absolute;}
    div.dataTables_paginate {
        padding-top: 0.85em;
        right:0px;}
</style>
@include('util.selection')
@include('util.datepicker')

@endsection
