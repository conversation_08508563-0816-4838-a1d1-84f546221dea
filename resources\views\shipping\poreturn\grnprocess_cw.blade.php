@extends('layout.mobile.app')

@section('content')
@section('title', __('GRN Return - CW'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$grn_item->whse_num"
            :itemnum="$grn_item->item_num"
            :itemdesc="$grn_item->item_desc"
            :refnum="$grn_item->grn_num"
            :refline="$grn_item->grn_line"
            :qtybalance="$grn_item->net_received ?? 0"
            :qtybalanceuom="$grn_item->uom"
            :submiturl="route('runGrnReturnCWProcess')"
            :catch-weight-tolerance="$grn_item->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="$disable_create_new_item_location ?? 0"
            :allow-over="$allow_over_return"
            :line-uom="$grn_item->uom"
            :print-label="0"
            transtype="grn"
            trans-type="GRNReturn"
            :incoming="false">

            <input type="hidden" name="vend_num" id="vend_num" value="{{ $grn_item->vend_num }}">
            <input type="hidden" name="non_inv" id="non_inv" value="{{ $non_inv ?? 0 }}">
            <input type="hidden" name="grn_num" id="grn_num" value="{{ $grn_item->grn_num }}">
            <input type="hidden" name="grn_line" id="grn_line" value="{{ $grn_item->grn_line }}">
            <input type="hidden" name="po_num" id="po_num" value="{{ $grn_item->po_num }}">
            <input type="hidden" name="po_line" id="po_line" value="{{ $grn_item->po_line }}">
            <input type="hidden" name="po_rel" id="po_rel" value="{{ $grn_item->po_rel }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num', $defaults['loc_num'] ?? '') }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                @if(($grn_item->item->lot_tracked ?? 0) == 1)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control-custom" for="vend_lot">{{ __('mobile.label.vend_lot') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" maxlength="30" id="vend_lot" value="{{old('vend_lot', $defaults['vend_lot'] ?? '')}}" class="form-control border-primary" placeholder="{{__('mobile.placeholder.vend_lot')}}" name="vend_lot">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control-custom" for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" maxlength="30" id="lot_num" value="{{old('lot_num', $defaults['lot_num'] ?? '')}}" class="form-control border-primary" placeholder="{{__('mobile.placeholder.lot_num')}}" name="lot_num">
                            </div>
                        </div>
                        <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" id="getRequest4" name="{{ __('mobile.list.lots') }}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLotNoPicking','item_num,whse_num,loc_num,lot_num','lot_num','lot_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                        </div>
                    </div>
                @endif
                
                <div class="form-group row" id="reason">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control-custom required" for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" value="{{ old('reason_code') }}" name="reason_code" id="reason_code" class="form-control border-primary" placeholder="Reason" onchange="clickSelf(this.id)">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="getRequest5" name="{{ __('mobile.list.reasons') }}" 
                            onClick="selectionwithcheckreasoncode('/getReasonCode/POReturn', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{__('admin.menu.reason_codes')}}');" 
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal">
                            <i class="icon-search"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.grn_num') }}: {{ $grn_item->grn_num }} | {{ $grn_item->grn_line }}
                            <br>
                            {{ __('mobile.label.po_num') }}: {{ $grn_item->po_num }} | {{ $grn_item->po_line }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack() {
        var URLRedirect = "{{$url ?? ''}}";
        if (URLRedirect) {
            url = URLRedirect.replace(/&amp;/g, "&");
            window.location.href = url;
        } else {
            window.history.back();
        }
    }

    $(document).ready(function() {
        // Handle lot number change to update vendor lot
        $("#lot_num").on('change', function() {
            if ($("#lot_num").val()) {
                $.ajax({
                    url: '{{ route('getVendLot') }}',
                    type: 'GET',
                    data: {
                        lot_num: $("#lot_num").val(),
                    },
                    success: function(data) {
                        if (data && data.vend_lot) {
                            $("#vend_lot").val(data.vend_lot);
                        }
                    }
                });
            }
        });
    });
</script>

@endsection()
