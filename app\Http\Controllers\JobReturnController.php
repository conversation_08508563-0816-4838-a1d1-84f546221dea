<?php

namespace App\Http\Controllers;

use App\Services\SapCallService;
use Illuminate\Http\Request;
use App\Job;
use App\JobRoute;
use App\Warehouse;
use App\Loc;
use App\Services\GeneralService;
use App\Item;
use Alert;
use App\Exports\JobReceiptExport;
use App\Exports\JobReceiptExportV2;
use App\ItemLoc;
use App\LotLoc;
use App\MatlTrans;
use App\Services\LotService;
use Maatwebsite\Excel\Facades\Excel;
use App\View\TparmView;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Carbon;
use Illuminate\Validation\ValidationException;
use App\ReasonCode;
use App\Services\MatltransService;
use App\Services\SiteConnectionService;
use App\Services\SapApiCallService;
use App\Services\PreassignLotsService;
use App\Services\CatchWeightService;
use App\UomConv;
use DB;
use Exception;

class JobReturnController extends Controller
{

    // public function __construct()
    // {
    //     $this->middleware('can:hasJobReturn');
    // }

    public function index(Request $request)
    {
        if (!\Gate::allows('hasJobReturn')) {
            return view('errors.404')->with('page', 'error');;
        }
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $batch_id = generateBatchId("Job Return");

        // Check if this is a catch weight item request
        if ($request->item_num && $request->job_num && $request->suffix && $request->oper_num) {
            $item = Item::where('item_num', $request->item_num)->first();

            if ($item && $item->catch_weight == 1) {
                // Get job details for catch weight processing
                $job_order = Job::where('job_num', $request->job_num)
                    ->where('suffix', $request->suffix)
                    ->first();

                if (!$job_order) {
                    throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->job_num . '-' . $request->suffix . ']'])]);
                }

                // Get job route details
                $job_route = JobRoute::where('job_num', $request->job_num)
                    ->where('suffix', $request->suffix)
                    ->where('oper_num', $request->oper_num)
                    ->first();

                if (!$job_route) {
                    throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->job_num . '-' . $request->suffix . '-' . $request->oper_num . ']'])]);
                }

                // Get catch weight parameters
                $tparms = new TparmView;
                $disable_create_new_item_location = $tparms->getTparmValue('JobReturn', 'disable_create_new_item_location');
                $allow_over_return = $tparms->getTparmValue('JobReturn', 'allow_over_return');
                $printLabel = $tparms->getTparmValue('JobReturn', 'print_label');

                return view('production.jobreturn.process_cw')
                    ->with('batch_id', $batch_id)
                    ->with('item', $item)
                    ->with('job_order', $job_order)
                    ->with('job_route', $job_route)
                    ->with('disable_create_new_item_location', $disable_create_new_item_location)
                    ->with('allow_over_return', $allow_over_return)
                    ->with('printLabel', $printLabel)
                    ->with('unit_quantity_format', $unit_quantity_format);
            }
        }

        return view('production.jobreturn.process')->with('unit_quantity_format', $unit_quantity_format)->with('sap_trans_order_integration', $sap_trans_order_integration)->with('batch_id', $batch_id);
    }

    public function process(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }
        $request = validateSansentiveValue($request);

        // Check if item is catch weight enabled
        $item = Item::where('item_num', $request->item_num)->first();
        if ($item && $item->catch_weight == 1) {
            // Redirect to catch weight processing with proper parameters
            $queryParams = [
                'item_num' => $request->item_num,
                'job_num' => $request->job_num,
                'suffix' => $request->suffix,
                'oper_num' => $request->oper_num
            ];

            return redirect()->route('JobReturn', $queryParams);
        }

        $checkJobNum = JobRoute::with('job')->where('job_num', $request->job_num)->where('suffix', $request->suffix)->where('oper_num', $request->oper_num)->first();

        // Verifying Job exist
        if (!$checkJobNum) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->job_num . '-' . $request->suffix . '-' . $request->oper_num . ']'])]);
        }
        $tparm = new TparmView;
        $sap_require_check_online = $tparm->getTparmValue('System', 'sap_check_online_connection');

        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');

        if($sap_require_check_online==1 && $sap_trans_order_integration==1){
            // Checking SAP Server
            $site_id = auth()->user()->site_id;
            $checkConnection = SapApiCallService::getSQPServerConnection($site_id,null,'Job Return',1);

            if($checkConnection > 2 ){
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_server_down'))->persistent('Dismiss');
                return redirect()->back();
            }

        }

        // Store into table: matl_trans
        MatlTrans::create([
            'trans_type' => 'Job Return',
            'item_num' => $request->item_num,
            'uom' => $request->uom,
            'qty' => - ($request->qty_to_return),
            'trans_uom' => $request->uom,
            'trans_qty' => - ($request->qty_to_return),
            'whse_num' => $request->whse_num,
            'loc_num' => $request->loc_num,
            'lot_num' => $request->lot_num,
            'ref_num' => $request->job_num,
            'suffix' => $request->suffix,
            'ref_line' => $request->oper_num,
            'reason_code' => $request->reason_code,
            'batch_id'=> $batch_id,
        ]);
        // Add qty_to_return inside table: job_routes
        $jobroute_builder = JobRoute::where('job_num', $request->job_num)
            ->where('suffix', $request->suffix)
            ->where('oper_num', $request->oper_num);

        $jobroute = (clone $jobroute_builder)
            ->first();

        if ($jobroute) {
            $jobroute->qty_returned = $jobroute->qty_returned + $request->qty_to_return;
            $jobroute->qty_completed = $jobroute->qty_completed - $request->qty_to_return;
            $jobroute->save();
        }

        // Update qty_to_return and job_status inside table: jobs
        $sum_qty_returned = (clone $jobroute_builder)
            ->groupBy('job_num')
            ->sum('qty_returned');

        Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->update([
            'qty_returned' => $sum_qty_returned,
            'job_status' => 'R',
        ]);
        // $this->updateJobRoute($request);

        // $request['trans_type'] = 'Job Return';
        // $request['qty'] = -1 *  ($request->qty_to_return);
        // $request['trans_qty'] = -1 * ($request->qty_to_return);
        // $request['ref_line'] = $request->ref_line;


        // $newTrans =  MatltransService::postTransaction($request);

        // Add qty_to_return inside table: item_locs / lot_locs
        $itemloc = ItemLoc::where('whse_num', $request->whse_num)
            ->where('loc_num', $request->loc_num)
            ->where('item_num', $request->item_num)
            ->first();

        if ($itemloc) {
            $itemloc->qty_on_hand = $itemloc->qty_on_hand - $request->qty_to_return;
            $itemloc->save();
        }

        if ($request->lot_num) {
            $lotloc = LotLoc::where('whse_num', $request->whse_num)
                ->where('loc_num', $request->loc_num)
                ->where('item_num', $request->item_num)
                ->where('lot_num', $request->lot_num)
                ->first();

            if ($lotloc) {
                $lotloc->qty_on_hand = $lotloc->qty_on_hand - $request->qty_to_return;
                $lotloc->save();
            }
        }
        $tparm = new TparmView;
        $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        $tparm = new TparmView;
        $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

        $request->merge([
            'sap_single_bin' => $sap_single_bin
        ]);

        // Get the Reason Code
        $reasonCode = ReasonCode::where('reason_num', $request->reason_code)->where('reason_class', 'JobReturn')->first();

        // update preassign lots
        PreassignLotsService::updatePreassignLot('job', $request->job_num, $request->suffix, $request->item_num, $request->lot_num, auth()->user()->site_id, ($request->qty_to_return * -1));

        if ($sap_trans_order_integration == 1 && $reasonCode && $reasonCode->sync_status == "Y") {
            $enable_miscissue = config('icapt.enable_sap_miscissue_to_drafts');
            //$result = SapCallService::postJobReturns($request);
            if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                // Later Change to read from Matltrans
                //$result = SapCallService::postJobReturns($request);
                if ($sap_single_bin == 1) {
                    $result = SiteConnectionService::postIntergrationTrans("Job Return", $batch_id = 0);
                } else {
                    $result = SapCallService::postInventoryGenExits("Job Return",0,$request);
                }
            } else {

                if(config('icapt.enable_sap_resync')){
                    //dd($request);
                    $result = SapCallService::postJobReturnsResync($request, $enable_miscissue);
                }
                else{
                    $result = SapCallService::postJobReturns($request, $enable_miscissue);
                }



            }
            if ($result != 200) {
                Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
            }
        }
        // Redirect success
        Alert::success('Success', __('success.processed', ['process' => __('Job Return')]));

        return back();
    }

    public function JobReturnCWProcess(Request $request)
    {
        $batch_id = $request->batch_id;

        if ($batch_id && checkBatchIdExists($batch_id)) {
            throw ValidationException::withMessages([__('error.admin.batch_id_exists')]);
        }

        $request = validateSansentiveValue($request);

        // Get job and item details
        $job_order = Job::where('job_num', $request->ref_num)
            ->where('suffix', $request->ref_line)
            ->first();

        if (!$job_order) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => '[' . $request->ref_num . '-' . $request->ref_line . ']'])]);
        }

        // Get job route details for operation
        $oper_num = $request->oper_num ?? $request->ref_line2 ?? '10'; // Try different parameter names
        $job_route = JobRoute::where('job_num', $request->ref_num)
            ->where('suffix', $request->ref_line)
            ->where('oper_num', $oper_num)
            ->first();

        if (!$job_route) {
            throw ValidationException::withMessages([__('error.mobile.notexist', ['resource' => 'Job Route [' . $request->ref_num . '-' . $request->ref_line . '-' . $oper_num . ']'])]);
        }

        // Get tolerance and UOM for catch weight validation (returnable quantity)
        $tolerance = $job_route->qty_completed - $job_route->qty_returned;
        $tolerance_uom = $job_order->uom;

        // Validate Catch Weight Data
        CatchWeightService::validateCatchWeightData($request, $tolerance, $tolerance_uom);

        $request->merge([
            'qty' => array_sum($request->arr_qty ?? []),
            'base_uom' => $job_order->uom,
            'qty_to_return' => array_sum($request->arr_qty ?? []),
            'job_num' => $request->ref_num,
            'suffix' => $request->ref_line,
            'oper_num' => $oper_num
        ]);

        DB::beginTransaction();
        try {
            // Use CatchWeightService to handle inventory updates
            $updateItemLocation = CatchWeightService::updateItemLocLotNMatlTrans(
                $request,
                $tolerance_uom,
                'Job Return',
                "Job Return"
            );

            // Update job route quantities
            if ($job_route) {
                $job_route->qty_returned = $job_route->qty_returned + $request->qty_to_return;
                $job_route->qty_completed = $job_route->qty_completed - $request->qty_to_return;
                $job_route->save();
            }

            // Update job quantities
            $sum_qty_returned = JobRoute::where('job_num', $request->job_num)
                ->where('suffix', $request->suffix)
                ->sum('qty_returned');

            Job::where('job_num', $request->job_num)
                ->where('suffix', $request->suffix)
                ->update([
                    'qty_returned' => $sum_qty_returned,
                    'job_status' => 'R',
                ]);

            // Update preassign lots
            PreassignLotsService::updatePreassignLot(
                'job',
                $request->job_num,
                $request->suffix,
                $request->item_num,
                $request->lot_num ?? null,
                auth()->user()->site_id,
                ($request->qty_to_return * -1)
            );

            DB::commit();

            // SAP Integration (if enabled)
            $tparm = new TparmView;
            $sap_trans_order_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
            $sap_single_bin = $tparm->getTparmValue('System', 'sap_single_bin');

            if ($sap_trans_order_integration == 1) {
                $reasonCode = ReasonCode::where('reason_num', $request->reason_code)
                    ->where('reason_class', 'JobReturn')
                    ->first();

                if ($reasonCode && $reasonCode->sync_status == "Y") {
                    $enable_miscissue = config('icapt.enable_sap_miscissue_to_drafts');

                    if (config('icapt.enable_sap_ap_readfrom_maltrans')) {
                        if ($sap_single_bin == 1) {
                            $result = SiteConnectionService::postIntergrationTrans("Job Return", $batch_id = 0);
                        } else {
                            $result = SapApiCallService::postInventoryGenExits("Job Return", 0, $request);
                        }
                    } else {
                        if (config('icapt.enable_sap_resync')) {
                            $result = SapApiCallService::postJobReturnsResync($request, $enable_miscissue);
                        } else {
                            $result = SapApiCallService::postJobReturns($request, $enable_miscissue);
                        }
                    }

                    if ($result != 200) {
                        Alert::error(__('error.mobile.sap_error'), __('error.mobile.sap_error_contact') . $result)->persistent('Dismiss');
                    }
                }
            }

            Alert::success('Success', __('success.processed', ['process' => __('Job Return')]));

            return back();

        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    public static function jobReturnValidation($request)
    {
        $errors = [];
        $result = ValidationController::checkJobRouteValidation($request);

        if ($result !== true) {
            $errors['job_num'] = $result;
        }
        $result = ValidationController::checkTransitPickingLocValidtion($request, "loc_num", true, false, false);

        if ($result !== true) {
            $errors['loc_num'] = $result;
        }
        return $errors;
    }
    public function updateJobRoute($request)
    {
        $jobroute_builder = JobRoute::where('job_num', $request->job_num)
            ->where('suffix', $request->suffix)
            ->where('oper_num', $request->oper_num);

        $jobroute = (clone $jobroute_builder)
            ->first();

        if ($jobroute) {
            $jobroute->qty_returned = $jobroute->qty_returned + $request->qty_to_return;
            $jobroute->qty_completed = $jobroute->qty_completed - $request->qty_to_return;
            $jobroute->save();
        }

        // Update qty_to_return and job_status inside table: jobs
        $sum_qty_returned = (clone $jobroute_builder)
            ->groupBy('job_num')
            ->sum('qty_returned');

        Job::where('job_num', $request->job_num)->where('suffix', $request->suffix)->update([
            'qty_returned' => $sum_qty_returned,
            'job_status' => 'R',
        ]);
    }
}
