# Catch Weight Feature Implementation for CO Picking

## Overview
This document outlines the implementation of catch weight functionality for Customer Order (CO) Picking in the ICAPT system. The implementation follows the same pattern used in other modules like PO Receipt and TO Shipping.

## Files Modified/Created

### 1. New View Template
**File:** `resources/views/shipping/copick/process_cw.blade.php`
- Created a new Blade template for catch weight CO picking
- Uses the existing `catch-weight-form` component
- Includes CO-specific hidden fields (co_rel, cust_num, stage_num, etc.)

### 2. Route Addition
**File:** `routes/custom/mobile.php`
- Added new route: `POST /home/<USER>/co-picking/catch-weight/process`
- Route name: `runCoPickCWProcess`
- Maps to `CoPickController@runCoPickCWProcess`

### 3. Controller Updates
**File:** `app/Http/Controllers/Outgoing/CoPickController.php`

#### Added Imports:
```php
use App\Services\CatchWeightService;
use App\Services\CoPickService;
use App\SiteSetting;
use Timezone;
```

#### Modified `CoPickingProcess` Method:
- Added catch weight detection logic
- Conditionally renders either `process_cw.blade.php` or `process.blade.php`
- Added required variables for catch weight view

#### Added New Method `runCoPickCWProcess`:
- Validates catch weight data using `CatchWeightService`
- Processes multiple lots with individual quantities
- Updates inventory (reduces quantities for picking)
- Updates CO item quantities
- Creates material transactions
- Handles tolerance validation

### 4. Service Updates
**File:** `app/Services/CatchWeightService.php`

#### Modified `updateItemLocLotNMatlTrans` Method:
- Added logic to handle CO picking (negative inventory adjustment)
- Differentiates between incoming and outgoing transactions
- For CO Pick: reduces inventory (`-$request->qty_conv`)
- For other transactions: increases inventory (`$request->qty_conv`)

### 5. Test Implementation
**File:** `tests/Feature/CoPickCatchWeightTest.php`
- Comprehensive test suite covering:
  - Catch weight item detection and view rendering
  - Successful catch weight processing
  - Tolerance validation
  - Multiple lot handling
  - Inventory updates verification

## How It Works

### 1. Item Detection
When a user navigates to CO picking process:
1. System checks if the item has `catch_weight = 1`
2. If true, redirects to catch weight form (`process_cw.blade.php`)
3. If false, shows standard picking form (`process.blade.php`)

### 2. Catch Weight Form
The catch weight form allows users to:
- Scan or enter multiple lot numbers
- Enter individual quantities for each lot
- Validate against catch weight tolerance
- Display running totals

### 3. Processing Logic
When catch weight form is submitted:
1. **Validation**: Validates all lots, quantities, and tolerance
2. **Inventory Update**: Reduces inventory for each lot/location
3. **CO Update**: Updates CO item with total picked quantity
4. **Material Transactions**: Creates audit trail entries
5. **Redirect**: Returns to CO picking list with success message

### 4. Key Features
- **Multiple Lots**: Supports picking from multiple lots in single transaction
- **Tolerance Validation**: Enforces catch weight tolerance settings
- **Inventory Accuracy**: Properly reduces inventory quantities
- **Audit Trail**: Creates complete material transaction records
- **Error Handling**: Comprehensive validation and error messages

## Usage Example

1. **Setup**: Create item with `catch_weight = 1` and `catch_weight_tolerance = 5.0`
2. **CO Creation**: Create customer order with catch weight item
3. **Picking Process**: Navigate to CO picking
4. **Automatic Detection**: System detects catch weight item and shows special form
5. **Data Entry**: User enters lot numbers and actual weights
6. **Validation**: System validates against tolerance (±5%)
7. **Processing**: System updates inventory and CO quantities
8. **Completion**: User receives confirmation and returns to picking list

## Integration Points

### Database Tables Affected:
- `item_locs` - Inventory quantities reduced
- `lot_locs` - Lot-specific quantities reduced  
- `coitems` - CO line quantities updated
- `matl_trans` - Material transaction records created
- `lots` - Lot information updated if needed

### Configuration Dependencies:
- Item must have `catch_weight = 1`
- Item should have `catch_weight_tolerance` value set
- System parameter `COPick.print_label` for label printing
- System parameter `CustOrdShipping.allow_over_ship` for over-picking

## Error Handling
- Validates lot existence in specified location
- Checks quantity against tolerance limits
- Prevents over-picking beyond available inventory
- Handles database transaction rollback on errors
- Provides user-friendly error messages

## Future Enhancements
- Integration with barcode scanning for lot numbers
- Real-time weight scale integration
- Advanced reporting for catch weight variances
- Mobile app optimization for warehouse operations

This implementation provides a complete catch weight solution for CO picking that maintains consistency with existing system patterns while providing the specialized functionality required for variable weight items.
