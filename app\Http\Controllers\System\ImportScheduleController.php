<?php

namespace App\Http\Controllers\System;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ImportScheduleController extends Controller
{
    /**
     * Display the File Import form.
     */
    public function index()
    {
        return view('system.import_schedule.index');
    }

    /**
     * Get the list of objects for the dropdown (placeholder data for now).
     */
    public function getObjects()
    {
        $modulesEnabled = \App\Services\ImportService::getCodeList();
        $objects = [];
        
        // Get the list of objects with defined column mappings
        $supportedObjects = [
            'MAINTENANCE_ITEM' => 'Item',
            'MAINTENANCE_VENDOR' => 'Vendor',
            'MAINTENANCE_CUSTOMER' => 'Customer',
            'MAINTENANCE_PRODUCTCODE' => 'Product Code',
            'MAINTENANCE_UOM' => 'UOM',
            'MAINTENANCE_WAREHOUSE' => 'Warehouse',
            'MAINTENANCE_EMPLOYEE' => 'Employee'
        ];
        
        foreach ($supportedObjects as $key => $name) {
            if (isset($modulesEnabled[$key])) {
                $objects[] = ['id' => $key, 'name' => $name];
            }
        }
        
        return response()->json($objects);
    }

    // New: Get target columns for mapping UI
    public function getColumns(Request $request)
    {
        // Normalize the object key by trimming and converting to uppercase
        $objectKey = strtoupper(trim($request->query('object')));
        
        \Log::info('getColumns called', [
            'object' => $objectKey,
            'query_params' => $request->query()
        ]);
        
        if (empty($objectKey)) {
            return response()->json([
                'success' => false,
                'error' => 'No object specified',
                'columns' => []
            ], 400);
        }
        
        // Define columns for each object type
        $columnsMap = [
            'MAINTENANCE_ITEM' => [
                ['name' => 'item_num', 'display' => 'Item Number', 'type' => 'string', 'max_length' => 50, 'required' => true],
                ['name' => 'item_desc', 'display' => 'Description', 'type' => 'string', 'max_length' => 100, 'required' => true],
                ['name' => 'uom', 'display' => 'UOM', 'type' => 'string', 'max_length' => 10, 'required' => true],
                ['name' => 'product_code', 'display' => 'Product Code', 'type' => 'string', 'max_length' => 30, 'required' => false],
                ['name' => 'lot_tracked', 'display' => 'Lot Tracked', 'type' => 'boolean', 'required' => false],
                ['name' => 'unit_weight', 'display' => 'Unit Weight', 'type' => 'decimal', 'precision' => 30, 'scale' => 6, 'required' => false],
                ['name' => 'item_status', 'display' => 'Item Status', 'type' => 'boolean', 'required' => false]
            ],
            'MAINTENANCE_VENDOR' => [
                ['name' => 'vendor_num', 'display' => 'Vendor Number', 'type' => 'string', 'max_length' => 50, 'required' => true],
                ['name' => 'vendor_name', 'display' => 'Vendor Name', 'type' => 'string', 'max_length' => 255, 'required' => true],
            ],
            'MAINTENANCE_CUSTOMER' => [
                ['name' => 'customer_num', 'display' => 'Customer Number', 'type' => 'string', 'max_length' => 50, 'required' => true],
                ['name' => 'customer_name', 'display' => 'Customer Name', 'type' => 'string', 'max_length' => 255, 'required' => true],
            ],
            // Add more object types with their columns
            'MAINTENANCE_PRODUCTCODE' => [
                ['name' => 'product_code', 'display' => 'Product Code', 'type' => 'string', 'max_length' => 50, 'required' => true],
                ['name' => 'description', 'display' => 'Description', 'type' => 'string', 'max_length' => 255, 'required' => true],
            ],
            'MAINTENANCE_UOM' => [
                ['name' => 'uom_code', 'display' => 'UOM Code', 'type' => 'string', 'max_length' => 10, 'required' => true],
                ['name' => 'description', 'display' => 'Description', 'type' => 'string', 'max_length' => 100, 'required' => true],
            ],
            'MAINTENANCE_WAREHOUSE' => [
                ['name' => 'warehouse_code', 'display' => 'Warehouse Code', 'type' => 'string', 'max_length' => 20, 'required' => true],
                ['name' => 'description', 'display' => 'Description', 'type' => 'string', 'max_length' => 100, 'required' => true],
            ],
            'MAINTENANCE_EMPLOYEE' => [
                ['name' => 'employee_id', 'display' => 'Employee ID', 'type' => 'string', 'max_length' => 20, 'required' => true],
                ['name' => 'name', 'display' => 'Name', 'type' => 'string', 'max_length' => 100, 'required' => true],
            ],
            // Add more object types as needed
        ];

        // Check if the object type exists in our map
        if (!array_key_exists($objectKey, $columnsMap)) {
            return response()->json([
                'success' => false,
                'error' => 'Object type not supported for import',
                'columns' => []
            ], 422);
        }

        $columns = $columnsMap[$objectKey];
        
        \Log::info('Returning columns', [
            'object' => $objectKey,
            'column_count' => count($columns),
            'columns' => $columns
        ]);
        
        return response()->json([
            'success' => true,
            'columns' => $columns
        ]);
    }

    /**
     * Execute the import with the provided mapping
     */
    public function executeImport(Request $request)
    {
        $validated = $request->validate([
            'object' => 'required|string',
            'mapping' => 'required|array',
            'preview' => 'required|array',
            'template_name' => 'nullable|string|max:100',
            'save_template' => 'boolean',
        ]);

        // Normalize object key
        $objectKey = strtoupper(trim($validated['object']));
        
        // Get the mapping configuration
        $mapping = $validated['mapping'];
        $previewData = $validated['preview'];
        
        // Validate required fields are mapped
        $requiredFields = collect($this->getColumns(new Request(['object' => $objectKey]))->getData(true)['columns'] ?? [])
            ->where('required', true)
            ->pluck('name')
            ->toArray();
            
        $mappedFields = array_keys($mapping);
        $missingRequired = array_diff($requiredFields, $mappedFields);
        
        if (!empty($missingRequired)) {
            return response()->json([
                'success' => false,
                'error' => 'The following required fields are not mapped: ' . implode(', ', $missingRequired)
            ], 422);
        }

        try {
            // Generate a unique import ID
            $importId = uniqid('import_');
            
            // Save the import data to a temporary file
            $importData = [
                'object' => $objectKey,
                'mapping' => $mapping,
                'data' => $previewData,
                'created_by' => auth()->id(),
                'created_at' => now()->toDateTimeString(),
            ];
            
            $fileName = "imports/{$importId}.json";
            Storage::disk('local')->put($fileName, json_encode($importData));
            
            // Dispatch the import job
            ProcessImportJob::dispatch(
                $importId,
                $fileName,
                $objectKey,
                $request->user()->id,
                $validated['template_name'] ?? null,
                $validated['save_template'] ?? false
            );
            
            return response()->json([
                'success' => true,
                'message' => 'Import has been queued for processing',
                'import_id' => $importId,
                'status_url' => route('system.import-schedule.status', $importId)
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Import execution failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'error' => 'Failed to process import: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get import status
     */
    public function getImportStatus($importId)
    {
        $status = Cache::get("import_status_{$importId}", [
            'status' => 'pending',
            'processed' => 0,
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'errors' => []
        ]);
        
        return response()->json($status);
    }

    // New: Analyse mapping for AI-assisted suggestion
    public function analyseMapping(Request $request)
    {
        // Normalize the object key by trimming and converting to uppercase
        $objectKey = strtoupper(trim($request->input('object')));
        $preview = $request->input('preview'); // Expecting array of rows, first row is header
        
        // Log the raw request data for debugging
        \Log::info('analyseMapping called', [
            'object_key' => $objectKey,
            'object_type' => gettype($objectKey),
            'preview_count' => is_array($preview) ? count($preview) : 0,
            'all_input' => $request->all(),
            'request_headers' => $request->headers->all()
        ]);
        
        // Log the available object types for debugging
        $availableObjects = array_keys([
            'MAINTENANCE_ITEM',
            'MAINTENANCE_VENDOR',
            'MAINTENANCE_CUSTOMER',
            'MAINTENANCE_PRODUCTCODE',
            'MAINTENANCE_UOM',
            'MAINTENANCE_WAREHOUSE',
            'MAINTENANCE_EMPLOYEE'
        ]);
        \Log::info('Available object types', ['types' => $availableObjects]);
        
        if (!$objectKey || !$preview || !is_array($preview) || count($preview) < 1) {
            $error = 'Missing object or preview data';
            \Log::error($error, [
                'has_object' => !empty($objectKey),
                'has_preview' => !empty($preview),
                'is_array' => is_array($preview),
                'preview_count' => is_array($preview) ? count($preview) : 0
            ]);
            return response()->json(['error' => $error], 422);
        }
        // Get target columns
        $columnsRequest = new Request(['object' => $objectKey]);
        $response = $this->getColumns($columnsRequest);
        $responseData = $response->getData(true);
        
        \Log::info('Target columns response', [
            'status' => $response->status(),
            'response_data' => $responseData
        ]);
        
        // Check if we got valid columns from the response
        if (empty($responseData['success']) || empty($responseData['columns']) || !is_array($responseData['columns'])) {
            $error = $responseData['error'] ?? 'Failed to get target columns';
            \Log::error('Invalid target columns response', [
                'error' => $error,
                'response' => $responseData
            ]);
            return response()->json(['error' => $error], 500);
        }
        
        $targetColumns = $responseData['columns'];
        \Log::info('Using target columns', ['count' => count($targetColumns)]);
        // Get source columns from first row of preview (header)
        $sourceColumns = $preview[0];
        
        // Log the structure for debugging
        \Log::info('Target columns:', ['targetColumns' => $targetColumns]);
        \Log::info('Source columns:', ['sourceColumns' => $sourceColumns]);
        
        // AI suggestion: map by name similarity (case-insensitive, underscores/space ignored)
        $mapping = [];
        foreach ($targetColumns as $target) {
            if (!isset($target['name'])) {
                \Log::error('Invalid target column format:', ['target' => $target]);
                continue;
            }
            
            $suggested = null;
            $targetNorm = strtolower(str_replace(['_', ' '], '', $target['name']));
            
            foreach ($sourceColumns as $srcCol) {
                $srcNorm = strtolower(str_replace(['_', ' '], '', $srcCol));
                if ($targetNorm === $srcNorm) {
                    $suggested = $srcCol;
                    break;
                }
            }
            if (!$suggested) {
                // Try partial match
                foreach ($sourceColumns as $srcCol) {
                    $srcNorm = strtolower(str_replace(['_', ' '], '', $srcCol));
                    if (strpos($srcNorm, $targetNorm) !== false || strpos($targetNorm, $srcNorm) !== false) {
                        $suggested = $srcCol;
                        break;
                    }
                }
            }
            
            // Add to mapping with all target column properties
            $mapping[] = [
                'target' => [
                    'name' => $target['name'],
                    'display' => $target['display'] ?? $target['name'],
                    'type' => $target['type'] ?? 'string',
                    'max_length' => $target['max_length'] ?? null,
                    'required' => $target['required'] ?? false
                ],
                'suggested_source' => $suggested,
            ];
            
            \Log::debug('Mapped column:', [
                'target' => $target['name'],
                'suggested' => $suggested
            ]);
        }
        return response()->json([
            'targetColumns' => $targetColumns,
            'sourceColumns' => $sourceColumns,
            'mapping' => $mapping,
            'sampleRows' => array_slice($preview, 1, 5),
        ]);
    }

    /**
     * Handle file upload and return preview (row count + first 5 rows).
     */
    public function uploadPreview(Request $request)
    {
        $request->validate([
            'importFile' => 'required|file|mimes:xlsx,xls,csv',
        ]);

        $file = $request->file('importFile');
        if (!$file->isValid() || $file->getSize() == 0) {
            return response()->json(['error' => 'File is empty or invalid.'], 422);
        }

        $extension = $file->getClientOriginalExtension();
        $rows = [];
        $rowCount = 0;
        try {
            if (in_array($extension, ['xlsx', 'xls'])) {
                $data = \Maatwebsite\Excel\Facades\Excel::toArray(new \App\Imports\PreviewImport, $file);
                $sheet = $data[0] ?? [];
                $rowCount = count($sheet);
                $rows = array_slice($sheet, 0, 5);
            } else if ($extension === 'csv') {
                $handle = fopen($file->getRealPath(), 'r');
                while (($row = fgetcsv($handle)) !== false) {
                    $rows[] = $row;
                    $rowCount++;
                    if ($rowCount >= 5) break;
                }
                // Count total rows
                $rowCount = 0;
                rewind($handle);
                while (fgetcsv($handle) !== false) {
                    $rowCount++;
                }
                fclose($handle);
            }
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to read file: ' . $e->getMessage()], 500);
        }
        return response()->json([
            'rowCount' => $rowCount,
            'preview' => $rows,
        ]);
    }
}
