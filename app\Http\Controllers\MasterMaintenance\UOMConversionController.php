<?php

namespace App\Http\Controllers\MasterMaintenance;

use Alert;
use App\Item;
use App\UomConv;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Controllers\ImportController;
use App\Http\Requests\FileImportRequest;
use App\DataTables\Master\UOMConversionDataTable;
use Illuminate\Validation\ValidationException;
use App\View\TparmView;
use App\Services\ExportService;

class UOMConversionController extends Controller
{

    private $name = 'MAINTENANCE_UOMCONV'; // 'UOMConversion';

    public function __construct()
    {

        $this->middleware('auth', ['except' => ['convertToBase', 'itemexist', 'checkcustomer', 'checkvendor', 'checkitem', 'checkglobal', 'check', 'exist', 'getbaseUOM']]);
        $this->middleware('can:hasUOMConversionMaintenance', ['except' => ['convertToBase', 'itemexist', 'checkcustomer', 'checkvendor', 'checkitem', 'checkglobal', 'check', 'exist', 'getbaseUOM']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(UOMConversionDataTable $uomConversionDataTable)
    {
        $tparm = new TparmView;
        // $qty = 30; //15EA
        // // CO Checking
        // // 1 EA = 2 SETS
        // // 1 EA = 10 MM
        // $item_num = "YNitem22101502";
        // $type_mode = "PalletBuilder";
        // $baseuom = "EA";
        // $selectuom = "BOX";
        // $lineuom = "MM";
        // // 30 EA1 = ? EA2
        // $cust_num = "";
        // $vend_num = "";

        // // PO Checking
        //  $item_num = "YNitem22101801";
        // //  $type_mode = "PO Receipt";
        //  $baseuom = "EA";
        //  $selectuom = "EA1";
        //  $lineuom = "EA";
        //  $vend_num = 594;

        // // TO / JOb Checking
        //  $item_num = "5012";
        //  $type_mode = "Misc Issue";
        //  $baseuom = "unit";
        //  $selectuom = "unit";
        //  $lineuom = "pkt";
        //  $vend_num = "";

        // // $arrData =   UomConv::UomConvert($baseuom, $selectuom, $lineuom, $qty, $item_num, $cust_num, $vend_num, $type_mode);
        // // conv uom =set, base uom =ea, qty=30
        // $arrBaseQty = UomConv::convert($selectuom, $qty, $item_num, $cust_num, $vend_num, $baseuom);
        // //qty=15, frm uom =sets, convuom=mm
        // $arrLineQty = UomConv::convertBeta($selectuom,"", $qty, $item_num, $cust_num, $vend_num, $baseuom);
        // dd( $arrBaseQty, $arrLineQty);


        $sap_integration = $tparm->getTparmValue('System', 'sap_trans_order_integration');
        return $uomConversionDataTable->render('MasterMaintenance.uomconversion.table', compact('sap_integration'));
    }

    public function import(FileImportRequest $request)
    {
        return ImportController::pushData('UOMConversionImport', $request, $this->name, auth()->user(), 'UOM Conversion');
    }

    public function export(UOMConversionDataTable $uomConversionDataTable)
    {
        $export = new ExportService($uomConversionDataTable, $this->name, "UOM Conversion");
        return $export->handleExport();
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $tparm = new TparmView;
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        return view('MasterMaintenance.uomconversion.add')->with('unit_quantity_format',$unit_quantity_format);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //Check UOMConversionObserver for validation on create.

        $request = validateSansentiveValue($request);
        $record = $request->except('_token');
        // dd($request);

        // New Function
        $baseuom = $request->uom_from;
        $convertUom = $request->uom_to;
        $type = $request->conv_type;
        $cust_num = $request->cust_num ?? null;
        $vend_num = $request->vend_num ?? null;
        $item_num = $request->item_num;
        $vend_num = $request->vend_num;
        $cust_num = $request->cust_num;
        $conv_factor = $request->conv_factor;
        $uom_conv = new UomConv();

        // Check if the Uom conv combination is already exist or not.

        if ($type == "I") {
            $checkUomConv = $uom_conv->where('uom_from', $baseuom)->where('uom_to', $convertUom)->where('conv_type', $type)->where('item_num', $item_num)->first();
        } else if ($type == "C") {
            $checkUomConv = $uom_conv->where('uom_from', $baseuom)->where('uom_to', $convertUom)->where('conv_type', $type)->where('item_num', $item_num)->where('cust_num', $cust_num)->first();
        } else if ($type == "V") {
            $checkUomConv = $uom_conv->where('uom_from', $baseuom)->where('uom_to', $convertUom)->where('conv_type', $type)->where('item_num', $item_num)->where('vend_num', $vend_num)->first();
        } else {
            $checkUomConv = $uom_conv->where('uom_from', $baseuom)->where('uom_to', $convertUom)->where('conv_type', $type)->first();
        }

        if (!empty($checkUomConv)) {
            throw ValidationException::withMessages([__('error.admin.exists_combination')]);
        }

        $reverseUomConv = $uom_conv->where('uom_from', $convertUom)->where('uom_to', $baseuom)->where('item_num', $item_num)->where('vend_num', $vend_num)->where('cust_num', $cust_num)->where('site_id', auth()->user()->site_id)->first();
        if (!empty($reverseUomConv)) {
            $ori_conv_factor = $reverseUomConv->conv_factor;
            $reverse_conv_factor = 1 / $reverseUomConv->conv_factor;
            // substr($str, 0, 7)
            $reverse_conv_factor_max_decimal = substr($reverse_conv_factor, 0, 8);
            if ($conv_factor != $reverse_conv_factor_max_decimal) {
                throw ValidationException::withMessages([__('error.admin.convert_uom', ['original_factor' => $ori_conv_factor, 'reverse_factor' => $reverse_conv_factor_max_decimal])]);
            }
        }
        $result = $uom_conv->create($record);

        return redirect()->route('UOMConversion')->with('successmsg', __(
            'success.addedv2',
            ['resource' => __('UOM Conversion'), 'name' => $result->name]
        ));
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(UomConv $uom_conv)
    {

        $tparm = new TparmView();
        $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $uom_conv->conv_factor  = number_format( $uom_conv->conv_factor , $unit_quantity_format, '.', '');
     
       // dd($uom_conv->conv_factor );
        return view('MasterMaintenance.uomconversion.view')->with('uomconversiondetails', $uom_conv);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(UomConv $uom_conv)
    {
          $tparm = new TparmView();
         $unit_quantity_format = $tparm->getTparmValue('System', 'decimal_setting');
        $uom_conv->conv_factor  = number_format( $uom_conv->conv_factor , $unit_quantity_format, '.', '');

       
        return view('MasterMaintenance.uomconversion.edit')->with('uomconversiondetails', $uom_conv);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $from, $to, $type, $i = "", $k = "")
    {
        //Check UOMConversionObserver for validation on update.

        $request = validateSansentiveValue($request);
        $record = $request->except('_token');

        $uom_conv = new UomConv();

        if ($type == "I") {
            $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->where('item_num', $i)->first();
        } else if ($type == "C") {
            $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->where('item_num', $i)->where('cust_num', $k)->first();
        } else if ($type == "V") {
            $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->where('item_num', $i)->where('vend_num', $k)->first();
        } else {
            $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->first();
        }

        if (!$uomconversiondetails) {
            return redirect(route('UOMConversion'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.uom_conversion'),'resource1' => __('admin.button.update'), 'resource2' => "$from - $to"]));
        }
        //Validation for same type amd uom conv
        $checkUomConv = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->first();
        if ($checkUomConv == true) {
            throw ValidationException::withMessages([__('error.admin.exists_combination')]);
        }

        $uomconversiondetails->update($record);

        return redirect()->route('UOMConversion')
            ->with('successmsg', __(
                'success.updated',
                ['resource' => __('UOM Conversion'), 'name' => $uomconversiondetails->name]
            ));
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($uom_from)
    {
        //Check UOMConversionObserver for validation on delete.

        $uom_conv = new UomConv();
        $uomconversiondetails = $uom_conv->where('uom_from', $uom_from)->delete();
        return $this->index();
    }

    public function uomapi(UomConv $uom)
    {
        //get all
        $uoms = $uom->all();
        ini_set('memory_limit', '-1');
        set_time_limit(0);

        return json_encode($uoms);
    }

    public function ap_delete(Request $request)
    {
        //Check UOMConversionObserver for validation on delete.

        $myString = $request->id;
        $myArray = explode(',', $myString);
        UomConv::destroy($myArray);

        //if no row selected or request is null
        if ($myString == "null" || $myString == "") {
            return redirect(route('UOMConversion'))->with('errormsg', __('error.admin.selectone'));
        }
        //if >1 selected and successful
        else {
            return redirect(route('UOMConversion'))->with('successmsg', __('success.deleted', ['resource' => __('UOM Conversion')]));
        }
    }

    public static function getbaseUOM($item, $um = "", $cust = "", $vend = "")
    {
        $uoml = new UomConv();
        $uom = $uoml->select('uom_from')->where('item_num', $item)->first();

        if ($uom)
            return $uom->uom_from;
        elseif ($um == '')
            return 'empty';
        else
            return $um;
    }

    public function exist($uom_from, $uom_to, $conv_type)
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->where('conv_type', $conv_type)->first();

        if ($data != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function check($uom_from, $uom_to)
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->first();

        if ($data->uom_from != $data->uom_to) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function checkglobal($uom_from, $uom_to)
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->where('conv_type', 'G')->first();
        $data2 = UomConv::where('uom_from', $uom_to)->where('uom_to', $uom_from)->where('conv_type', 'G')->first();

        if ($data = null && $data2 != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function checkitem($uom_from, $uom_to, $conv_type, $item_num)
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->where('conv_type', $conv_type)->where('item_num', $item_num)->first();
        $data2 = UomConv::where('uom_to', $uom_from)->where('uom_from', $uom_to)->where('conv_type', $conv_type)->where('item_num', $item_num)->first();

        if ($data = null && $data2 != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function checkvendor($uom_from, $uom_to, $item_num, $vend_num, $id)
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->where('conv_type', 'V')->where('item_num', $item_num)->where('vend_num', $vend_num)->where('id', "!=", $id)->first();
        $data2 = UomConv::where('uom_to', $uom_from)->where('uom_from', $uom_to)->where('conv_type', 'V')->where('item_num', $item_num)->where('vend_num', $vend_num)->where('id', "!=", $id)->first();

        if ($data == null && $data2 == null) {
            return 'not exist';
        } else {
            return 'exist';
        }
    }

    public function checkcustomer($uom_from, $uom_to, $item_num, $cust_num)
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->where('conv_type', 'C')->where('item_num', $item_num)->where('cust_num', $cust_num)->first();
        $data2 = UomConv::where('uom_to', $uom_from)->where('uom_from', $uom_to)->where('conv_type', 'C')->where('item_num', $item_num)->where('cust_num', $cust_num)->first();

        if ($data != null && $data2 != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function itemexist($uom_from, $uom_to, $conv_type, $item_num, $id = "")
    {
        $data = UomConv::where('uom_from', $uom_from)->where('uom_to', $uom_to)->where('conv_type', $conv_type)->where('item_num', $item_num)->where('id', '!=', $id)->first();

        if ($data != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }

    public function convertToBase($uom, $qty, $item_num, $cust_num = '', $vend_num = '')
    {
        $conv = UomConv::convert($uom, $qty, $item_num, $cust_num, $vend_num);
        $item_uom = Item::where('item_num', $item_num)->pluck('uom')->first();

        if ($item_uom == $conv['uom'])
            return $conv['qty'];
        else
            return '';
    }

    /* *************************************************
    -- Author         : Afiq
    -- Create date    : 19/04/2023
    -- Description    : Add UOM Conversion Reversal.
    -- Method         : POST
    -- Source File(s) : UOMConversionController.php
    -- URL            : /maintenance-invty/master-files-invty/uom-conversions/add
    -- --------------------------------------------------------

    =============================================================================================================================
        'Rev      Project Code     Modified By - Date           Description

        '-----    ------------     --------------------         ----------------------
        'R1       Gitlab #1376     Afiq  - 19/04/2023           Add reversal uom conv.

    ==============================================================================================================================*/

    public function update2(Request $request)
    {
        $request = validateSansentiveValue($request);
        // $record = $request->all();
        $baseuom = $request->_uom_from;
        $convertUom = $request->_uom_to;
        $type = $request->_conv_type;
        $item_num = $request->_item_num;
        $vend_num = $request->_vend_num;
        $cust_num = $request->_cust_num;
        $conv_factor = $request->conv_factor;

        $record = (['conv_factor' => $conv_factor]);

        $conv = new UomConv();
        $oriUomConvert = $conv->where('uom_from', $convertUom)
            ->where('uom_to', $baseuom)
            // ->where('item_num', $item_num)
            // ->where('vend_num', $vend_num)
            // ->where('cust_num', $cust_num)
            ->where('site_id', auth()->user()->site_id);


        if ($item_num)
            $oriUomConvert = $oriUomConvert->where('item_num', $item_num);
        if ($cust_num)
            $oriUomConvert = $oriUomConvert->where('cust_num', $cust_num);
        if ($vend_num)
            $oriUomConvert = $oriUomConvert->where('vend_num', $vend_num);
        $oriUomConvert = $oriUomConvert->first();
// dd($oriUomConvert);
        $uomconversiondetails = $conv->where('uom_from', $baseuom)
            ->where('uom_to', $convertUom)->where('site_id', auth()->user()->site_id);
        if ($item_num)
            $uomconversiondetails = $uomconversiondetails->where('item_num', $item_num);
        if ($cust_num)
            $uomconversiondetails = $uomconversiondetails->where('cust_num', $cust_num);
        if ($vend_num)
            $uomconversiondetails = $uomconversiondetails->where('vend_num', $vend_num);

        $uomconversiondetails = $uomconversiondetails->first();
        // dd($uomconversiondetails);
        // $uomconversiondetails->where('item_num', $item_num)
        // ->where('vend_num', $vend_num)
        // ->where('cust_num', $cust_num)
        // ->where('site_id', auth()->user()->site_id)->first();
        // dd($request->all(), $uomconversiondetails, $oriUomConvert);
        if (!$uomconversiondetails) {
            return redirect(route('UOMConversion'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.uom_conversion'), 'resource1' => __('admin.button.update'), 'resource2' => "$baseuom - $convertUom"]));
        }
        // dd($uomconversiondetails);
        if (!empty($oriUomConvert)) {
            $ori_conv_factor = $oriUomConvert->conv_factor;
            $reverse_conv_factor = 1 / $oriUomConvert->conv_factor;
            if ($conv_factor != $reverse_conv_factor) {
                throw ValidationException::withMessages([__('error.admin.convert_uom', ['original_factor' => $ori_conv_factor, 'reverse_factor' => $reverse_conv_factor])]);
            } else {
                // $uomconversiondetails = $conv->where('uom_from', $baseuom)->where('uom_to', $convertUom)->where('item_num', $item_num)->where('vend_num', $vend_num)->where('cust_num', $cust_num)->where('site_id', auth()->user()->site_id)->first();
                // dd($uomconversiondetails);
                if (!empty($uomconversiondetails)) {
                    $result = $uomconversiondetails->update($record);
                } else {
                    throw ValidationException::withMessages([__('error.admin.exists_update_combination')]);
                }
            }
        } else {
            // $uomconversiondetails = $conv->where('uom_from', $baseuom)->where('uom_to', $convertUom)->where('item_num', $item_num)->where('vend_num', $vend_num)->where('cust_num', $cust_num)->where('site_id', auth()->user()->site_id)->first();
            // dd($uomconversiondetails);
            if (!empty($uomconversiondetails)) {
                $result = $uomconversiondetails->update($record);
            } else {
                throw ValidationException::withMessages([__('error.admin.exists_update_combination')]);
            }
        }


        // $uom_conv = new UomConv();

        // dd($request);

        // $from= $request->_uom_from;
        //         $to= $request->_uom_to;
        // $type= $request->_conv_type;
        // $i= $request->_item_num;

        // if ($type == "I") {
        //     $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->where('item_num', $i)->first();
        // } else if ($type == "C") {
        //     $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->where('item_num', $i)->where('cust_num',$request->_cust_num)->first();
        // } else if ($type == "V") {
        //     $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->where('item_num', $i)->where('vend_num', $request->vend_num)->first();
        // } else {
        //     $uomconversiondetails = $uom_conv->where('uom_from', $from)->where('uom_to', $to)->where('conv_type', $type)->first();
        // }


        // $checkUomConv = $uom_conv->where('uom_from',$from)->where('uom_to',$to)->where('conv_type',$type)->first();
        // if($checkUomConv->id!=$request->id)
        // {
        //     throw ValidationException::withMessages([__('error.admin.exists_update_combination')]);
        // }

        // //dd($checkUomConv->id);
        // $uomconversiondetails->update($record);

        return redirect()->route('UOMConversion')
        ->with('successmsg', __(
            'success.updated',
            ['resource' => __('UOM Conversion'), 'name' => $uomconversiondetails->name]
        ));
    }
}
