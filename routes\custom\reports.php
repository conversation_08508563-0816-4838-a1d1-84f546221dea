<?php
Auth::routes();
Route::group(['middleware' => ['auth']], function () {
    /*******************
          Inventory
     ********************/
    // Inventory Transactions by Item
    Route::get('/reports/inventory/inventory-trans-by-item', 'Report\InvTransReportController@index')->name('invtransrep');
    Route::get('/reports/inventory/invtransrep-data', 'Report\InvTransReportController@data')->name('invtransrep.data');
    Route::post('/reports/inventory/inventory-trans-by-item', 'Report\InvTransReportController@print')->name('printinvtransrep');
    // Route::get('/reports/invtransrep-table', 'InvTransReportController@table')->name('invtransrep.table');

    // Inventory Balance
    Route::get('/reports/inventory/inventory-balance', 'Report\InvBalanceReportController@index')->name('invbalrep');
    Route::get('/reports/inventory/invbalrep-data', 'Report\InvBalanceReportController@data')->name('invbalrep.data');
    Route::post('/reports/inventory/inventory-balance', 'Report\InvBalanceReportController@print')->name('printinvbalrep');
    // Route::get('/reports/invbalrep-table', 'Report\InvBalanceReportController@table')->name('invbalrep.table');

    // Inventory Expiry (renamed to Lot Expiry)
    Route::get('/reports/inventory/lot-expiry', 'Report\InvExpiryReportController@index')->name('invexprep');
    Route::get('/reports/inventory/invexprep-data', 'Report\InvExpiryReportController@data')->name('invexprep.data');
    Route::post('/reports/inventory/lot-expiry', 'Report\InvExpiryReportController@print')->name('printinvexprep');
    Route::get('/reports/inventory/lot-expiry-dashboard', 'Report\InvExpiryReportController@lotNearViewDashboard')->name('lotNearViewDashboard');
    Route::get('/reports/inventory/lot-expiry-dashboard-data', 'Report\InvExpiryReportController@dataDashboard')->name('lotNearViewDashboard.data');

    // Inventory Balance
    Route::get('/reports/inventory/inventory-valuation', 'Report\InvValuationReportController@index')->name('invvalrep');
    Route::get('/reports/inventory/invvalrep-data', 'Report\InvValuationReportController@data')->name('invvalrep.data');
    Route::post('/reports/inventory/inventory-valuation', 'Report\InvValuationReportController@print')->name('printinvvalrep');
    // Route::get('/reports/invbalrep-table', 'Report\InvBalanceReportController@table')->name('invbalrep.table');

    // Summary of Live Stock and Incoming Report
    Route::get('/reports/inventory/summary-live-stock-income', 'Report\LiveStockIncomReportController@index')->name('summlivestockincom');
    Route::get('/reports/inventory/summary-live-stock-income-data', 'Report\LiveStockIncomReportController@data')->name('summlivestockincom.data');
    Route::post('/reports/inventory/summary-live-stock-income-export', 'Report\LiveStockIncomReportController@export')->name('summlivestockincom.export');

    // Live Stock Report
    Route::get('/reports/inventory/live-stock', 'Report\LiveStockReportController@index')->name('livestock');
    Route::get('/reports/inventory/live-stock-data', 'Report\LiveStockReportController@data')->name('livestock.data');
    Route::post('/reports/inventory/live-stock-export', 'Report\LiveStockReportController@export')->name('livestock.export');

    // Forecast Report
    Route::get('/reports/inventory/forecast', 'Report\ForecastReportController@index')->name('forecast');
    Route::get('/reports/inventory/forecast-data', 'Report\ForecastReportController@data')->name('forecast.data');
    Route::post('/reports/inventory/forecast-export', 'Report\ForecastReportController@export')->name('forecast.export');


    // Stock Allocation Report
    Route::get('/reports/inventory/stock-allocation', 'Report\StockAllocationReportController@index')->name('stockallocation');
    Route::get('/reports/inventory/stock-allocation-data', 'Report\StockAllocationReportController@data')->name('stockallocation.data');
    Route::post('/reports/inventory/stock-allocation-export', 'Report\StockAllocationReportController@export')->name('stockallocation.export');

    // Purchase Order Receipt History
    Route::get('/reports/inventory/po-receipt-history', 'Report\POLinesReportController@index')->name('polinesrep');
    Route::get('/reports/inventory/polinesrep-data', 'Report\POLinesReportController@data')->name('polinesrep.data');
    Route::post('/reports/inventory/po-receipt-history', 'Report\POLinesReportController@print')->name('printpolinesrep');
    // Route::get('/reports/polinesrep-table', 'POLinesReportController@table')->name('polinesrep.table');

    // Customer Order Shipment History
    Route::get('/reports/inventory/co-shipment-history', 'Report\SOLinesReportController@index')->name('solinesrep');
    Route::get('/reports/inventory/solinesrep-data', 'Report\SOLinesReportController@data')->name('solinesrep.data');
    Route::post('/reports/inventory/co-shipment-history', 'Report\SOLinesReportController@print')->name('printsolinesrep');
    // Route::get('/reports/solinesrep-table', 'SOLinesReportController@table')->name('solinesrep.table');

    // Re-order Item
    Route::get('/reports/inventory/reorder-report', 'Report\ReorderItemReportController@index')->name('reorder_itemrep');
    Route::get('/reports/inventory/reorder-item-data', 'Report\ReorderItemReportController@data')->name('reorder_itemrep.data');
    Route::post('/reports/inventory/reorder-report', 'Report\ReorderItemReportController@print')->name('printreorder_itemrep');
    Route::get('/reports/inventory/reorder-report-dashboard', 'Report\ReorderItemReportController@itemBelowReorderviewDashboard')->name('itemBelowReorderviewDashboard');
    Route::get('/reports/inventory/reorder-report-dashboard-data', 'Report\ReorderItemReportController@dataDashboard')->name('itemBelowReorderviewDashboard.data');

    // Pallet Summary
    Route::any('/reports/inventory/pallet-summary-report', 'Report\PalletSummaryReportController@index')->name('pallet_summary_report');
    Route::any('/reports/inventory/pallet-summary-report/export', 'Report\PalletSummaryReportController@index')->name('pallet_summary_report.export');
    Route::post('/reports/inventory/pallet-summary-report/print', 'Report\PalletSummaryReportController@print')->name('pallet_summary_report.print');

    // Purchase Order Status
    Route::get('/reports/inventory/po-status', 'Report\POStatusReportController@index')->name('postatusrep');
    Route::get('/reports/inventory/postatusrep-data', 'Report\POStatusReportController@data')->name('postatusrep.data');
    Route::post('/reports/inventory/po-status', 'Report\POStatusReportController@print')->name('printpostatusrep');
    Route::get('/reports/inventory/po-status-dashboard', 'Report\POStatusReportController@poviewDashboard')->name('poviewDashboard');
    Route::get('/reports/inventory/po-status-dashboard-data', 'Report\POStatusReportController@dataDashboard')->name('poviewDashboard.data');

    // Customer Order Status
    Route::get('/reports/inventory/co-status', 'Report\COStatusReportController@index')->name('costatusrep');
    Route::get('/reports/inventory/costatusrep-data', 'Report\COStatusReportController@data')->name('costatusrep.data');
    Route::post('/reports/inventory/co-status', 'Report\COStatusReportController@print')->name('printcostatusrep');
    Route::get('/reports/inventory/co-status-dashboard', 'Report\COStatusReportController@coviewDashboard')->name('coviewDashboard');
    Route::get('/reports/inventory/co-status-dashboard-data', 'Report\COStatusReportController@dataDashboard')->name('coviewDashboard.data');

    // CO Picking vs Shipping Status
    Route::get('/reports/inventory/co-picking-shipping-status', 'Report\COPickingShippingStatusReportController@index')->name('copickingshippingstatusrep');
    Route::get('/reports/inventory/copickingshippingstatusrep-data', 'Report\COPickingShippingStatusReportController@data')->name('copickingshippingstatusrep.data');
    Route::post('/reports/inventory/co-picking-shipping-status', 'Report\COPickingShippingStatusReportController@print')->name('printcopickingshippingstatusrep');

    // Material Issued
    Route::get('/reports/inventory/material-issued', 'Report\ProdIssueReportController@index')->name('prodissuerep');
    Route::get('/reports/inventory/prodissuerep-data', 'Report\ProdIssueReportController@data')->name('prodissuerep.data');
    Route::post('/reports/inventory/material-issued', 'Report\ProdIssueReportController@print')->name('printprodissuerep');
    // Route::get('/reports/prodissuerep-table', 'ProdIssueReportController@table')->name('prodissuerep.table');
    // Material Requirement
    Route::get('/reports/inventory/material-required', 'Report\MatlReqReportController@index')->name('matl_req');
    Route::any('/reports/inventory/material-req-data', 'Report\MatlReqReportController@data')->name('matl_req.data');
    Route::post('/reports/inventory/material-required', 'Report\MatlReqReportController@print')->name('print_matl_req');
    Route::get('/reports/inventory/material-req-filter', 'Report\MatlReqReportController@filter')->name('matl_req.filter');

    Route::get('/reports/inventory/material-required-jmrr', 'Report\MatlReqReportController@agrgtIndex')->name('matl_req_agrgt');
    Route::any('/reports/inventory/material-req-agrgt-data', 'Report\MatlReqReportController@agrgtData')->name('matl_req_agrgt.data');
    Route::post('/reports/inventory/material-required-agrgt', 'Report\MatlReqReportController@agrgtPrint')->name('print_matl_req_agrgt');

    // Production Completion Transactions
    Route::get('/reports/inventory/item-production-completion-summary', 'Report\ProdComplReportController@index')->name('prodcomplrep');
    Route::get('/reports/inventory/prodcomplrep-data', 'Report\ProdComplReportController@data')->name('prodcomplrep.data');
    Route::post('/reports/inventory/item-production-completion-summary', 'Report\ProdComplReportController@print')->name('printprodcomplrep');
    // Route::get('/reports/prodcomplrep-table', 'ProdComplReportController@table')->name('prodcomplrep.table');

    // Production
    Route::get('/reports/production/job-operation-hours', 'Report\JobOperHourReportController@index')->name('joboperhourrep'); // Job Operation Hours
    Route::get('/reports/production/joboperhourrep-data', 'Report\JobOperHourReportController@data')->name('joboperhourrep.data');
    Route::post('/reports/production/job-operation-hours', 'Report\JobOperHourReportController@print')->name('printjoboperhours');

    Route::get('/reports/production/job-processing-hours-by-operation', 'Report\JobProcessingHoursReportController@index')->name('jobprocesshoursbyoper'); // Job Prcessing Hours by Operation
    Route::get('/reports/production/jobprocesshoursbyoper-data', 'Report\JobProcessingHoursReportController@data')->name('jobprocesshoursbyoper.data');
    Route::post('/reports/production/job-processing-hours-by-operation', 'Report\JobProcessingHoursReportController@print')->name('printjobprocesshoursbyoper');

    Route::get('/reports/production/item-scrap-summary', 'Report\ItemScrapSummaryReportController@index')->name('itemscrapsumrep');   // Item Scrap Summary
    Route::get('/reports/production/itemscrapsumrep-data', 'Report\ItemScrapSummaryReportController@data')->name('itemscrapsumrep.data');
    Route::post('/reports/production/item-scrap-summary', 'Report\ItemScrapSummaryReportController@print')->name('printitemscrapsumrep');
    Route::get('/reports/production/item-scrap-summary-dashboard', 'Report\ItemScrapSummaryReportController@itemScappedviewDashboard')->name('itemScappedviewDashboard');
    Route::get('/reports/production/item-scrap-summary-dashboard-data', 'Report\ItemScrapSummaryReportController@dataDashboard')->name('itemScappedviewDashboard.data');

    Route::get('/reports/production/scrap-reasons', 'Report\JobScrapReasonReportController@index')->name('jobscraprsnrep');    // Scrap Reasons
    Route::post('/reports/production/scrap-reasons', 'Report\JobScrapReasonReportController@print')->name('printJobscraprsnrep');
    Route::get('/reports/production/jobscraprsnrep-data', 'Report\JobScrapReasonReportController@data')->name('jobscraprsnrep.data');
    Route::get('/reports/production/machine-status-summary', 'Report\MachineStatSummaryReportController@index')->name('machinestatsumrep');    // Machine Status Summary
    Route::post('/reports/production/machine-status-summary', 'Report\MachineStatSummaryReportController@print')->name('printMachinestatsumrep');
    Route::get('/reports/production/machinestatsumrep-data', 'Report\MachineStatSummaryReportController@data')->name('machinestatsumrep.data');
    Route::get('/reports/production/job-scrap-summary-by-oper', 'Report\JobScrapSummaryByOperReportController@index')->name('jobscrapsumbyoperrep');    // Job Scrap Summary by Route
    Route::any('/reports/production/jobscrapsumbyoperrep-data', 'Report\JobScrapSummaryByOperReportController@data')->name('jobscrapsumbyoperrep.data');
    Route::post('/reports/production/job-scrap-summary-by-oper', 'Report\JobScrapSummaryByOperReportController@print')->name('printjobscrapsumbyoperrep');

    Route::get('/reports/production/job-order-status', 'Report\JobOrderStatusReportController@index')->name('joborderstatrep');
    Route::get('/reports/production/joborderstatrep-data', 'Report\JobOrderStatusReportController@data')->name('joborderstatrep.data');
    Route::post('/reports/production/job-order-status', 'Report\JobOrderStatusReportController@print')->name('printjoborderstatrep');

    Route::get('/reports/production/labour-status-summary', 'Report\LabourStatSummaryReportController@index')->name('labourstatsumrep');    // Machine Status Summary
    Route::post('/reports/production/labour-status-summary', 'Report\LabourStatSummaryReportController@print')->name('printLabourstatsumrep');
    Route::get('/reports/production/labourstatsumrep-data', 'Report\LabourStatSummaryReportController@data')->name('labourstatsumrep.data');

    Route::get('/reports/production/product-efficiency', 'Report\ProductEfficiencyReportController@index')->name('produceffrep');    // Product Efficiency
    Route::get('/reports/production/product-efficiency-data', 'Report\ProductEfficiencyReportController@data')->name('produceffrep.data');
    Route::post('/reports/production/product-efficiency', 'Report\ProductEfficiencyReportController@print')->name('printproduceffrep');

    Route::get('/reports/production/slitting-efficiency', 'Report\SlittingEfficiencyReportController@index')->name('slittingeffrep');    // Slitting Efficiency
    Route::get('/reports/production/slitting-efficiency-data', 'Report\SlittingEfficiencyReportController@data')->name('slittingeffrep.data');
    Route::post('/reports/production/slitting-efficiency', 'Report\SlittingEfficiencyReportController@print')->name('printslittingeffrep');

    Route::any('/reports/production/wip', 'Report\WIPReportController@index')->name('wiprep');    // WIP Report
    Route::post('/reports/production/wip/export', 'Report\WIPReportController@index');
    Route::post('/reports/production/wip-print', 'Report\WIPReportController@print')->name('printwiprep');

    // Customer Return Status
    Route::any('/reports/inventory/co-return-status', 'Report\CoReturnStatusController@index')->name('coReturnStatus');
    Route::post('/reports/inventory/co-return-status/export', 'Report\CoReturnStatusController@export')->name('coReturnStatus.export');
    
    // Customer Return Transaction
    Route::any('/reports/inventory/co-return-transaction', 'Report\CoReturnTransController@index')->name('coReturnTrans');
    Route::post('/reports/inventory/co-return-transaction/export', 'Report\CoReturnTransController@export')->name('coReturnTrans.export');

    // Transaction
    Route::get('/reports/transaction/print-job-sheet', 'Report\TransactionReportController@index')->name('jobsheet');
    Route::get('/reports/transaction/print-job-sheet/{id}/{suffix?}', 'Report\TransactionReportController@print')->name('jobsheetsingle');

    Route::get('/reports/transaction/preassigned-lot-labels', 'Report\TransPreassignedlotReportController@index')->name('preassignedlotlabels');
    Route::get('/reports/transaction/preassigned-lot-labels/data', 'Report\TransPreassignedlotReportController@data')->name('preassignedlotlabels.data');
    Route::post('/reports/transaction/preassigned-lot-labels/print', 'Report\TransPreassignedlotReportController@print')->name('preassignedlotlabels.print');


    Route::post('/reports/transaction/print-job-sheet', 'Report\TransactionReportController@print')->name('printJobSheet');
    Route::get('/reports/transaction/printJobSheet-data', 'Report\TransactionReportController@data')->name('transreport.data');
    Route::get('/reports/transaction/print-pick-list', 'Report\TransPickListReportController@index')->name('picklist');
    Route::get('/reports/transaction/print-pick-list/{pick_num}', 'Report\TransPickListReportController@print')->name('printPickListSingle');

    Route::post('/reports/transaction/print-pick-list', 'Report\TransPickListReportController@print')->name('printPickList');
    Route::get('/reports/transaction/printPickList-data', 'Report\TransPickListReportController@data')->name('transpickreport.data');
    Route::get('/reports/transaction/print-grn', 'Report\TransGRNReportController@index' )->name('grn');
    Route::post('/reports/transaction/print-grn', 'Report\TransGRNReportController@print')->name('transgrnreport.print');
    Route::get('/reports/transaction/printGRN-data', 'Report\TransGRNReportController@data')->name('transgrnreport.data');

    // Packing List
    Route::get('/reports/inventory/packing-list', 'Report\PackingListController@index')->name('packlist');
    Route::get('/reports/inventory/packing-list-data', 'Report\PackingListController@data')->name('packlist.data');
    Route::post('/reports/inventory/packing-list-print', 'Report\PackingListController@print')->name('packlist.print');

    // Stok Move Verification
    Route::any('/reports/inventory/stock-move-verification', 'Report\StockMoveVerificationController@index')->name('stockMoveVerif');
    Route::post('/reports/inventory/stock-move-verification/export', 'Report\StockMoveVerificationController@export')->name('stockMoveVerif.export');

    // Customer Order Pick
    Route::any('/reports/inventory/co-pick', 'Report\COPickController@index')->name('coPick');
    Route::any('/reports/inventory/co-pick/data', 'Report\COPickController@data')->name('coPick.data');
    Route::post('/reports/inventory/co-pick/export', 'Report\COPickController@export')->name('coPickReport.export');
    Route::post('/reports/inventory/co-pick/print', 'Report\COPickController@print')->name('coPick.print');


    // Dashboard
    // Order Due - Inventory
    Route::get('/reports/inventory/order-due-dashboard', 'Report\InvOrderDueReportController@invIndex')->name('inv.orderDueDashboard');
    Route::get('/reports/inventory/order-due-dashboard-data', 'Report\InvOrderDueReportController@invData')->name('inv.orderDueDashboard.data');
    // Order Due - Production
    Route::get('/reports/production/order-due-dashboard', 'Report\ProdOrderDueReportController@prodIndex')->name('prod.orderDueDashboard');
    Route::get('/reports/production/order-due-dashboard-data', 'Report\ProdOrderDueReportController@prodData')->name('prod.orderDueDashboard.data');
    // Current Running Job
    Route::get('/reports/production/running-job-dashboard', 'Report\CurrRunningJobReportController@index')->name('runningJob');
    Route::get('/reports/production/running-job-dashboard-data', 'Report\CurrRunningJobReportController@data')->name('runningJob.data');
    // Current Running Job
    Route::get('/reports/production/running-machine-dashboard', 'Report\CurrRunningMachineReportController@index')->name('runningMachine');
    Route::get('/reports/production/running-machine-dashboard-data', 'Report\CurrRunningMachineReportController@data')->name('runningMachine.data');
    // Current Running Job
    Route::get('/reports/production/downtime-dashboard', 'Report\CurrDowntimeReportController@index')->name('downtime');
    Route::get('/reports/production/downtime-dashboard-data', 'Report\CurrDowntimeReportController@data')->name('downtime.data');

    // Job Material Pick
    Route::any('/reports/production/job-matl-pick', 'Report\JobMaterialPickController@index')->name('jobMatlPick');
    Route::any('/reports/production/job-matl-pick/data', 'Report\JobMaterialPickController@data')->name('jobMatlPick.data');
    Route::post('/reports/production/job-matl-pick/export', 'Report\JobMaterialPickController@export')->name('jobMatlPick.export');
    Route::post('/reports/production/job-matl-pick/print', 'Report\JobMaterialPickController@print')->name('jobMatlPick.print');
});

if (env('APP_ENV') == "production") {
    URL::forceScheme('https');
}
