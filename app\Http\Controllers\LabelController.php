<?php

namespace App\Http\Controllers;

use DB;
use App;
use PDF;
use Gate;
use Alert;
use App\Label;
use App\Module;
use Carbon\Carbon;
use App\SiteSetting;
use Milon\Barcode\DNS2D;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\DataTables\Master\LabelDataTable;
use App\DataTables\Master\ObjectLabelDataTable;
use App\DefaultLabel;
use App\Http\Controllers\Super\SuperController;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Storage;
use Printing;
use Dompdf\Dompdf;

class LabelController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('can:hasBarcode');
    }

    private $page = 'labellist';
    public static $placeholders = [
        "Inventory Label" => [
            'Label Name' => "%label_name%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Loc Num' => "%loc_num%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'Doc Num' => "%document_num%",

            'Unit Weight' => "%unit_weight%",

            'Unit Length' => "%unit_length%",

            'Unit Width' => "%unit_width%",

            'Unit Height' => "%unit_height%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",
        ],
        "CO Shipping Label" => [
            'Label Name' => "%label_name%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Loc Num' => "%loc_num%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'CO Num' => "%co_num%",

            'Customer Num' => "%cust_num%",

            'Customer Name' => "%cust_name%",

            'Unit Weight' => "%unit_weight%",

            'Unit Length' => "%unit_length%",

            'Unit Width' => "%unit_width%",

            'Unit Height' => "%unit_height%",

            'Customer Order Address' => "%cust_address%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",

        ],
        "Customer Return Label" => [
            'Label Name' => "%label_name%",
            'Return Num' => "%return_num%",
            'Return Line' => "%return_line%",
            'Customer Num' => "%cust_num%",
            'Customer Name' => "%cust_name%",
            'Warehouse' => "%whse_num%",
            'Loc Num' => "%loc_num%",
            'Item Num' => "%item_num%",
            'Item Desc' => "%item_desc%",
            'Lot Num' => "%lot_num%",
            'Expiry Date' => "%expiry_date%",
            'Qty To Return' => "%qty_to_return%",
            'UOM' => "%uom%",
            'Site Name' => "%site_name%",
            'Box Num' => "%bn%",
            'Total Box' => "%tb%",
            'Trans Date' => "%trans_date%",
            'Qty In Box' => "%qib%",
            'Print Date' => "%print_date%",


        ],
        "PO Receipt Label" => [
            'Label Name' => "%label_name%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Loc Num' => "%loc_num%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'PO Num' => "%po_num%",

            'Vend Name' => "%vend_name%",

            'Vend Lot' => "%vend_lot%",

            'Unit Weight' => "%unit_weight%",

            'Unit Length' => "%unit_length%",

            'Unit Width' => "%unit_width%",

            'Unit Height' => "%unit_height%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",
        ],
        "Job Material Issue Label" => [
            'Label Name' => "%label_name%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Loc Num' => "%loc_num%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'Job Num' => "%job_num%",

            'Suffix' => "%suffix%",

            'Product' => "%Product%",

            'Doc Num' => "%document_num%",

            'Unit Weight' => "%unit_weight%",

            'Unit Length' => "%unit_length%",

            'Unit Width' => "%unit_width%",

            'Unit Height' => "%unit_height%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",
        ],
        "Job Receipt Label" => [
            'Label Name' => "%label_name%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Loc Num' => "%loc_num%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'Job Num' => "%job_num%",

            'Suffix' => "%suffix%",

            'Product' => "%Product%",

            'Doc Num' => "%document_num%",

            'Unit Weight' => "%unit_weight%",

            'Unit Length' => "%unit_length%",

            'Unit Width' => "%unit_width%",

            'Unit Height' => "%unit_height%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",
        ],
        "TO Shipping Label" => [
            'Label Name' => "%label_name%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Loc Num' => "%loc_num%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'TO Num' => "%to_num%",

            'TO Line' => "%to_line%",

            'From Whse' => "%from_whse%",

            'To Whse' => "%to_whse%",

            'Unit Weight' => "%unit_weight%",

            'Unit Length' => "%unit_length%",

            'Unit Width' => "%unit_width%",

            'Unit Height' => "%unit_height%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Doc Num' => "%document_num%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",
        ],
        "Pallet Label" => [

            'Label Name' => "%label_name%",

            'LPN' => "%lpn_num%",

            'Whse' => "%whse_num%",

            'Loc Num' => "%loc_num%",

            'Single Item' => "%sgl_item%",

            'Customer Num' => "%cust_num%",

            'Cust Name' => "%cust_name%",

            'Creation Date' => "%creation_date%",

            'Ref Num' => "%ref_num%",

            'Ref Line' => "%ref_line%",

            'Item Num' => "%item_num%",

            'Item Desc' => "%item_desc%",

            'Lot Num' => "%lot_num%",

            'Expiry Date' => "%expiry_date%",

            'Qty In Box' => "%qib%",

            'UOM' => "%uom%",

            'Site Name' => "%site_name%",

            'Company Name' => "%company_name%",

            'Box Num' => "%bn%",

            'Total Box' => "%tb%",

            'Print Date' => "%print_date%",

            'Trans Date' => "%trans_date%",
            'Whse' => "%whse_num%",

        ],

    ];
    public static $module_mapping = [
        "Inventory Label" => [
            'CustOrdPicking',
            'CustOrdShipping',
            'Inventory Label',
            'JobLabour',
            'JobMaterialIssue',
            'JobMaterialReturn',
            'JobReceipt',
            'MachineRun',
            'MiscIssue',
            'MiscReceipt',
            // 'Pallet',
            'PickList',
            'PickNShip',
            'POReceipt',
            'Putaway',
            'StockMove',
            'TranOrderShipping',
            'TransferOrderReceipt',
            'WIPMove',
        ],
        "CO Shipping Label" => [
            'CustOrdPicking',
            'CustOrdShipping',
            'PickList',
            'PickNShip',

        ],
        "Customer Return Label" => [
            'CustomerReturn'
        ],
        "PO Receipt Label" => [

            'POReceipt',

        ],
        "Job Material Issue Label" => [

            'JobMaterialIssue',
            'JobMaterialReturn',

        ],
        "Job Receipt Label" => [
            'JobLabour',
            'JobReceipt',
            'MachineRun',
            'WIPMove',
        ],
        "TO Shipping Label" => [
            'TranOrderShipping',
        ],
        "Pallet Label" => [
            'CustOrdPicking',
            'CustOrdShipping',
            'JobReceipt',
            'Pallet',
            'POReceipt',
            'TranOrderShipping',
            'TransferOrderReceipt',

        ],
    ];
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(LabelDataTable $labelDataTable)
    {
        // $printers = Printing::printers();

        // // $api = app(\Rawilk\Printing\Api\PrintNode\PrintNode::class);
        // // $api->setApiKey("-OpqJCpU_GzYoNNrRiJp7_KuPH6p6spWU7h482ypVRM");
        // // $whoami = $api->printers();
        // $printerId = "72435199";
        // $res =  Printing::newPrintTask()
        //     ->printer($printerId)
        //     ->file(public_path('Inventory Label_AXA_TEST.pdf'))
        //     ->send();

        // dd($res);
        if (!Gate::allows('hasBarcode')) {
            return view('errors.404v2')->with('page', 'error');
        }

        $PickListModule = Module::where('modulename', 'PickList')->first();

        $now = Carbon::now()->toDateTimeString();

        if (!$PickListModule) {
            $PickListModule = new Module();
            $PickListModule->modulename = "PickList";
            $PickListModule->description = "PickList";
            $PickListModule->created_date = $now;
            $PickListModule->modified_date = $now;
            $PickListModule->save();
        }

        // return view('admin.label.index')->with('page',$this->page);
        return $labelDataTable->render('admin.label.table');
    }
    public function objectLabel(ObjectLabelDataTable $labelDataTable)
    {

        if (!Gate::allows('hasObjectLabel')) {
            return view('errors.404v2')->with('page', 'error');
        }


        $now = Carbon::now()->toDateTimeString();



        // return view('admin.label.index')->with('page',$this->page);
        return $labelDataTable->render('admin.object_label.table');
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $types = QrcodeController::$types;
        // To control starter plan drop down
        $plan = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->first();
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'print_node')->first();

        $planId = customDecrypt($plan->plan_id);
        if ($planId == 1 || $planId == 4) {
            if (isset($types['Job Material Issue Label'])) {
                unset($types['Job Material Issue Label']);
            }
            if (isset($types['Job Material Label'])) {
                unset($types['Job Material Label']);
            }

            // unset($types['Job Material Label']);

            unset($types['TO Shipping Label']);
            unset($types['Job Receipt Label']);
        }
        if ($planId == 7) {

            unset($types['TO Shipping Label']);
        }
        if (!config('icapt.special_modules.enable_pallet') || $planId == 1 || $planId == 4 || $planId == 7) {
            unset($types['Pallet Label']);
        }
        $bartender = config('icapt.bartender');
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();
        $use_bartender = false;
        if ($bartender || $bartender_cloud_setting) {
            $use_bartender = true;
        }
        return view('admin.label.add_multi')
            ->with('page', $this->page)
            ->with('types', $types)
            ->with('use_bartender', $use_bartender)
            ->with('print_node_setting', $print_node_setting);
    }

    public function createpost()
    {
        $label = new Label();
        return view('admin.label.add')->with('page', $this->page);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $validateArray = [
            'label_name' => 'required',
            'type' => 'required',
            'height' => 'numeric|min:10',
            'width' => 'numeric',
            'margin_left' => 'numeric',
            'margin_right' => 'numeric',
            'margin_top' => 'numeric',
            'margin_bottom' => 'numeric',
            // 'file_name' => 'required',
        ];
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'print_node')->first();

        if ($print_node_setting) {
            $validateArray['paper_size'] = "required";
        }
        // dd($validateArray);
        $data = request()->validate($validateArray);

        $labelOld = Label::where('label_name', $request->label_name)->first();
        if ($labelOld)
            throw ValidationException::withMessages([__('error.admin.exists', ["resource" => "Label Name", "name" => $request->label_name])]);


        $saved = Label::create($data);

        //if >1 selected and successful
        if ($saved) {
            return redirect(route('editlabel', $saved))->with('successmsg', 'Label [' . $request->label_name . '] has been added successfully.');
        }
    }
    public function getPlaceholders(Request $request)
    {
        $type = $request->type;
        //    dd($type);
        $data = self::$placeholders[$type];
        return response()->json($data);
    }
    public function edit(Request $request, $labelId)
    {


        $label = new Label;
        $labels = $label->findOrFail($labelId);
        $label_default = Label::where('type', $labels->type)->where('is_default', 1)->first();
        // dd($labels->type);
        $labels->bodydefault = $label_default ? $label_default->raw_content : "P";
        $label_template = DefaultLabel::where('type', $labels->type)->first();


        // $labels->bodydefault = DB::table('labels')->select('raw_content')->where('label_name', $labels->label_name)->where('site_id', auth()->user()->site_id)->value('raw_content');
        $modulelist = Module::orderBy('modulename')->get();
        $module = $labels->modules()->get();
        $module_mapping = SuperController::$modules_labels;
        $module_mapped = isset($module_mapping[$labels->type]) ? $module_mapping[$labels->type] : [];

        $plan = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->first();
        $planId = customDecrypt($plan->plan_id);
        $planArr = [];
        if ($planId == 7) {
            $planArr = ['MachineRun', 'TransferOrderReceipt', 'TranOrderShipping'];
        }

        //dd($labels->type,$module_mapping);
        $module_mapped = $module_mapping[$labels->type];
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'print_node')->first();
        $bartender = config('icapt.bartender');
        $bartender_cloud_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'simple')->where('active', 1)->where('connector', 'bartender_cloud')->first();
        $use_bartender = false;
        if ($bartender || $bartender_cloud_setting) {
            $use_bartender = true;
        }

        // $module = $label_default->modules()->get();
        //dd($module,$module_mapped);
        return view('admin.label.editor')->with(compact('labels'))
            ->with('module', $module)
            ->with('use_bartender', $use_bartender)
            ->with('module_mapped', $module_mapped)
            ->with('label_template', $label_template)

            // ->with(compact('modulelist', $modulelist))->with('page', $this->page);
            ->with(compact('modulelist', 'planId', 'planArr', 'print_node_setting'))->with('page', $this->page);
    }
    public function addObjectLabel(Request $request)
    {

        $labels = Label::all();


        // $labels->bodydefault = DB::table('labels')->select('raw_content')->where('label_name', $labels->label_name)->where('site_id', auth()->user()->site_id)->value('raw_content');
        $modulelist = Module::orderBy('modulename')->get();



        $module_mapping = SuperController::$modules_labels;
        // dd($module_mapping);
        // $default_check=true;

        //dd($labels->type,$module_mapping)

        // $module = $label_default->modules()->get();
        //dd($module,$module_mapped);
        return view('admin.object_label.add')
            ->with('modules_mapped', $module_mapping)
            ->with('labels', $labels)
            // ->with(compact('modulelist', $modulelist))->with('page', $this->page);
            ->with(compact('modulelist'))->with('page', $this->page);
    }
    public function validateObjectLabel(Request $request)
    {


        $exist =  DB::table('label_modules')->where('modulename', $request->modulename)->where('is_default', 1)->first();

        if ($exist)
            return "true";
        else
            return "false";
        // dd($request->all());
    }
    public function storeObjectLabel(Request $request)
    {
        $label_id = $request->label_name;
        $validate_object = DB::table('label_modules')->where('modulename', $request->modulename)->where('label_id', $label_id)->where('site_id', auth()->user()->site_id)
            ->first();
        // dd($validate_object, $request->dropdown, $label_id);
        if ($validate_object != null) {
            // Throw error if object already exists
            // Label Name and Object already exists. Please select a different combination.
            throw ValidationException::withMessages([__('error.admin.exists_object_label')]);
        }
        if ($request->is_default) {
            DB::table('label_modules')->where('modulename', $request->modulename)->where('is_default', 1)->update(['is_default' => 0]);
        }
        $module = Module::find($request->modulename);
        $module->labels()->syncWithoutDetaching([$label_id => ['site_id' => auth()->user()->site_id, 'is_default' => $request->is_default]]);

        $label = Label::findOrFail($label_id);
        // dd($request->all());

        return redirect()->route('objectLabel')->with('successmsg', __(
            'success.addedv2',
            ['resource' => __('Object Label'), 'name' => $request->modulename . " - " . $label->label_name]
        ));
        // dd($request->all());
    }
    public function editObjectLabel(Request $request, $id)
    {


        $object_label = DB::table('label_modules')->find($id);
        // dd($label);
        // $label= Label::find($object_label->label_id);
        $labels = Label::findOrFail($object_label->label_id);


        // $labels->bodydefault = DB::table('labels')->select('raw_content')->where('label_name', $labels->label_name)->where('site_id', auth()->user()->site_id)->value('raw_content');
        $modulelist = Module::orderBy('modulename')->get();
        $module = $labels->modules()->get();

        $object_label_default = DB::table('label_modules')->where('is_default', 1)
            ->where('modulename', $object_label->modulename)
            ->where('id', "!=", $object_label->id)
            ->first();
        // dd($object_label_default);
        $default_check = false;
        if ($object_label_default)
            $default_check = true;

        $module_mapping = SuperController::$modules_labels;
        $module_mapped = isset($module_mapping[$labels->type]) ? $module_mapping[$labels->type] : [];

        // $default_check=true;

        //dd($labels->type,$module_mapping);
        $module_mapped = $module_mapping[$labels->type];

        // dd($module_mapping);
        // $module = $label_default->modules()->get();
        //dd($module,$module_mapped);
        return view('admin.object_label.edit')->with(compact('labels'))
            ->with('module', $module)
            ->with('module_mapped', $module_mapped)
            ->with('object_label', $object_label)
            ->with('default_check', $default_check)
            // ->with(compact('modulelist', $modulelist))->with('page', $this->page);
            ->with(compact('modulelist'))->with('page', $this->page);
    }
    public function updateObjectLabel(Request $request, $id)
    {
        $object_label = DB::table('label_modules')->find($id);
        // dd($label);
        // $label= Label::find($object_label->label_id);
        $labels = Label::findOrFail($object_label->label_id);
        if ($request->is_default) {
            DB::table('label_modules')->where('modulename', $object_label->modulename)->where('is_default', 1)->update(['is_default' => 0]);
            DB::table('label_modules')->where('id', $id)->update(['is_default' => 1]);
        } else {
            DB::table('label_modules')->where('id', $id)->update(['is_default' => 0]);
        }
        return redirect()->route('objectLabel')->with('successmsg', __(
            'success.updated',
            ['resource' => __('Object Label'), 'name' => $object_label->modulename . " - " . $labels->label_name]
        ));
        // dd($request->all());
    }
    public function editor(Request $request, $labelId)
    {
        $label = new Label;
        $labels = $label->findOrFail($labelId);
        $labels->bodydefault = DB::table('labels')->select('raw_content')->where('label_name', $labels->label_name)->where('site_id', auth()->user()->site_id)->value('raw_content');
        $modulelist = Module::all();
        $module = $labels->modules()->get();
        //dd($modulelist);
        return view('admin.label.edit')->with(compact('labels'))
            ->with('module', $module)
            ->with(compact('modulelist'))->with('page', $this->page);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $labelId
     * @return Response
     * @throws ValidationException
     */
    public function update(Request $request, int $labelId)
    {

        $label = Label::where('id', $labelId)->first();

        if (!$label) {
            return redirect(route('labellist'))->with('errormsg', __('error.mobile.notexist2', ['model' => __('admin.label.label_name'), 'resource1' => __('admin.button.update'), 'resource2' => $labelId]));
        }
        $validateData
            = [
                'label_name' => 'required',
                'type' => 'required',
                'height' => 'numeric|min:10',
                'width' => 'numeric',
                'margin_left' => 'numeric',
                'margin_right' => 'numeric',
                'margin_top' => 'numeric',
                'margin_bottom' => 'numeric',
                'rows' => 'required|min:1|max:9',
                'columns' => 'required|min:1|max:9'

                // 'height' => 'numeric'
            ];
        $print_node_setting = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('active', 1)->where('type', 'simple')->where('connector', 'print_node')->first();

        if ($print_node_setting) {
            $validateArray['paper_size'] = "required";
        }
        $label = Label::findOrFail($labelId);
        $this->validate(request(), $validateData);
        $labelOld = Label::where('label_name', $request->label_name)->where('id', "!=", $labelId)->first();
        if ($labelOld)
            throw ValidationException::withMessages([__('error.admin.exists', ["resource" => "Label Name", "name" => $request->label_name])]);
        if ($request->margin_right + $request->margin_left > $request->width) {
            throw ValidationException::withMessages("Margin left and Margin Right cannot be more than the width.");
        }
        if ($request->margin_top + $request->margin_bottom > $request->height) {
            throw ValidationException::withMessages("Margin left and Margin Right cannot be more than the height.");
        }
        // $label_json = json_decode($request->raw_content);

        // if (!$label_json) {
        //     Alert::error('Warning', 'Invalid format.', 'warning');
        //     return back();
        // }

        // $validate = Label::where('label_name', $request->label_name)->where('site_id', auth()->user()->site_id)
        // ->exists();

        // Check the label name before updating
        // if ($request->label_name != $label->label_name) {

        //     if ($validate) {
        //         // Throw error if label name already exists
        //         throw ValidationException::withMessages([__('error.admin.exists', ['resource' => __('admin.label.label_name'), 'name' => $request->label_name])]);
        //     }
        // }
        $record = $request->except('_token', 'type');
        $label->content = $this->convertJson($request['raw_content']);
        // $label->update($request->all());
        $label->update($record);
        // if (!$label->content) {
        //     Alert::error('Warning', 'Invalid format.', 'warning');
        //     return back();
        // } else {
        //     $label->update($request->all());
        // }
        // dd($request->redirect_back);
        if ($request->redirect_back == "1") {
            return redirect()->back()->with('successmsg', __(
                'success.updated',
                ['resource' => __('Label'), 'name' => $label->label_name]
            ));
        }

        return redirect(route('labellist'))->with('successmsg', __(
            'success.updated',
            ['resource' => __('Label'), 'name' => $label->label_name]
        ));
    }
    public function updateOld(Request $request, int $labelId)
    {
        $label = Label::findOrFail($labelId);
        $this->validate(request(), [
            'label_name' => 'required',
            'height' => 'numeric|min:10',
            'width' => 'numeric',
            'height' => 'numeric'
        ]);

        $label_json = json_decode($request->raw_content);

        if (!$label_json) {
            Alert::error('Warning', 'Invalid format.', 'warning');
            return back();
        }

        $validate = Label::where('label_name', $request->label_name)->where('site_id', auth()->user()->site_id)
            ->exists();

        // Check the label name before updating
        if ($request->label_name != $label->label_name) {

            if ($validate) {
                // Throw error if label name already exists
                throw ValidationException::withMessages([__('error.admin.exists', ['resource' => __('admin.label.label_name'), 'name' => $request->label_name])]);
            }
        }

        $label->content = $this->convertJson($request['raw_content']);

        if (!$label->content) {
            Alert::error('Warning', 'Invalid format.', 'warning');
            return back();
        } else {
            $label->update($request->all());
        }

        return redirect(route('labellist'))->with('successmsg', __(
            'success.updated',
            ['resource' => __('Label'), 'name' => $label->label_name]
        ));
    }

    public function display($labelId)
    {
        $modulelist = Module::all();
        $labels = Label::findOrFail($labelId);
        $module = $labels->modules()->get();
        return view('admin.label.view')->with(compact('labels'))
            ->with('module', $module)
            // ->with(compact('modulelist', $modulelist))->with('page', $this->page);
            ->with(compact('modulelist'))->with('page', $this->page);
    }

    public function preview(Label $label)
    {
        //Rewrite this.
        // 1. Get Template->
        $template = $label['content'];

        if ($label['content_html'] != "") {
            $template = $label['content_html'];
        }

       // dd($template);
        $site = SiteSetting::where('site_id', @auth()->user()->site_id)->first();

        // 2. Replace placeholders
        $datetime = Timezone::convertFromUTC(now(), auth()->user()->getSiteTimezone(), SiteSetting::getOutputDateFormat() . ' H:i:s');

        $input['site_name'] = @$site->site_name;
        $input['label_name'] = $label['label_name'];
        $input['expiry_date'] = Carbon::now()->toDateString();
        $input['item_num'] = 'ITEM00019';
        $input['item_desc'] = 'Description of Item';
        $input['loc_num'] = 'LOC-ABCD';
        $input['lot_num'] = 'LOT-EFGH';
        $input['from_whse'] = 'WHSE-ABCD';
        $input['to_whse'] = 'WHSE-EFGH';
        $input['whse_num'] = 'WHSE-ABCD';

        $input['job_num'] = 'JOB0001';
        $input['suffix'] = '0000';
        $input['oper_num'] = "10";

        $input['po_num'] = 'PO0001';
        $input['co_num'] = 'CO0001';
        $input['trn_num'] = 'TO0001';
        $input['to_num'] = 'TO0001';
        $input['to_line'] = 'LINE0001';

        $input['trn_line'] = 'LINE0001';
        $input['cust_name'] = 'Customer Name';
        $input['cust_num'] = 'Customer Name';
        $input['vend_num'] = 'VEND0001';
        $input['vend_name'] = 'Vendor Sdn Bhd';
        $input['vend_lot'] = 'VEND_LOT';
        $input['uom'] = 'EA';
        $input['cust_address'] = 'Customer Address';

        // $input['trans_date'] = Carbon::now()->toDateString();
        $input['trans_date'] = $datetime;
        $input['trans_datetime'] = Carbon::now()->toDateTimeString();
        $input['boxnum'] = 1;
        $input['bn'] = 1;
        $input['qtyinbox'] = 50;
        $input['qib'] = 50;
        $input['totalbox'] = 1;
        $input['tb'] = 1;
        $input['labelperbox'] = (int) 1;
        $input['document_num'] = 'DOC-ABCD';

        $input['lpn_num'] = 'LPN0001';
        $input['creation_date'] = $datetime;


        $input['unit_weight'] = 10111;
        $input['unit_length'] = 20;
        $input['unit_width'] = 30;
        $input['unit_height'] = 40;

        $input['return_num'] = 'RC0001';
        $input['return_line'] = 1;

        $label_name = 'Label Name';

        //dd($input);

        $text = preg_replace("/\r|\n/", "", $input);
        list($keys, $values) = Arr::divide($text);

        $placeholders = array();
        foreach ($keys as $key)
            $placeholders[] = '%' . $key . '%';

        $dnsd = new DNS2D();

        $item_qrcode = $dnsd->getBarcodeSVG($input['item_num'], "QRCODE", 20, 20, "black", true);
        $loc_qrcode = $dnsd->getBarcodeSVG($input['loc_num'], "QRCODE", 20, 20, "black", true);
        $from_whse_qrcode = $dnsd->getBarcodeSVG($input['from_whse'], "QRCODE", 20, 20, "black", true);
        $to_whse_qrcode = $dnsd->getBarcodeSVG($input['to_whse'], "QRCODE", 20, 20, "black", true);

        if ($input['lot_num']) {
            $lot_qrcode = $dnsd->getBarcodeSVG($input['lot_num'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%lot_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($lot_qrcode) . '" height="40px" />', $template);
        } else {
            $template = str_replace('%lot_qrcode%', '', $template);
        }

        if ($input['vend_lot']) {
            $vend_lot_qrcode = $dnsd->getBarcodeSVG($input['vend_lot'], "QRCODE", 40, 40, "black", true);
            $template = str_replace('%vendlot_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($vend_lot_qrcode) . '" height="40px" />', $template);
        } else {
            $template = str_replace('%vendlot_qrcode%', '', $template);
        }

        if (array_key_exists('job_num', $input)) {
            $job_qrcode = $dnsd->getBarcodeSVG($input['job_num'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%job_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($job_qrcode) . '" height="40px" />', $template);
        } else {
            $template = str_replace('%job_qrcode%', '', $template);
        }

        if (array_key_exists('suffix', $input)) {
            $suffix_qrcode = $dnsd->getBarcodeSVG($input['suffix'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%suffix_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($suffix_qrcode) . '" height="40px" />', $template);
        } else {
            $template = str_replace('%job_qrcode%', '', $template);
        }

        if (array_key_exists('from_whse', $input)) {
            $from_whse_qrcode = $dnsd->getBarcodeSVG($input['from_whse'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%from_whse_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($from_whse_qrcode) . '" height="40px" />', $template);

            $to_whse_qrcode = $dnsd->getBarcodeSVG($input['to_whse'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%to_whse_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($to_whse_qrcode) . '" height="40px" />', $template);
        } else {
            $template = str_replace('%TO_qrcode%', '', $template);
            $template = str_replace('%TO_line_qrcode%', '', $template);
        }

        if (array_key_exists('trn_num', $input)) {
            $TO_qrcode = $dnsd->getBarcodeSVG($input['trn_num'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%TO_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($TO_qrcode) . '" height="40px" />', $template);

            $TO_line_qrcode = $dnsd->getBarcodeSVG($input['trn_line'], "QRCODE", 20, 20, "black", true);
            $template = str_replace('%TO_line_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($TO_line_qrcode) . '" height="40px" />', $template);
        } else {
            $template = str_replace('%TO_qrcode%', '', $template);
            $template = str_replace('%TO_line_qrcode%', '', $template);
        }

        $template = str_replace($placeholders, $values, $template);
        $template = str_replace('%item_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($item_qrcode) . '" height="40px"  />', $template);
        $template = str_replace('%loc_qrcode%', '<img src="data:image/svg+xml;base64,' . base64_encode($loc_qrcode) . '" height="40px" />', $template);
        $template = str_replace('%label_name%', $label_name, $template);
        $site_settings = SiteSetting::select('*')->where('site_id', auth()->user()->site_id)->first();
        $company_info = json_decode($site_settings->company_info, true);
        $company_name = isset($company_info['company_name']) ? $company_info['company_name'] : "";
        $input['site_name'] = $site_settings->site_name;
        $input['label_name'] = $label->label_name;
        $input['print_date'] = $datetime;
        $input['company_name'] = $company_name;
        // dd($input);
        // dd($company_info);

        $template = str_replace('%expiry_date%', $input['expiry_date'], $template);
        $template = str_replace('%site_name%', $site_settings->site_name, $template);
        $template = str_replace('%label_name%', $label->label_name, $template);
        $company_info = json_decode($site_settings->company_info, true);
        $company_name = isset($company_info['company_name']) ? $company_info['company_name'] : "";
        $template = str_replace('%company_name%', $company_name, $template);

        $template = str_replace('%print_date%', $datetime, $template);

        $template = str_replace('%trans_date%', $datetime, $template);


        //$template = str_replace('%unit_weight%', $input['unit_weight'], $template);
        // dd($template);
        $template = QrcodeController::replacePlaceholders($template, $input);

        $template = QrcodeController::handleQrCode($template, $input);
        
        $template = QrcodeController::handleQrCodeImage($template, $input);


        
       $template = QrcodeController::handlPrimaryImage($template, $input);


        $header_icon = "<link rel='shortcut icon' type='image/x-icon' href='/images/logo/icapt-small-red2.png'>";

        $labels[] = $template;
        // dd($label->width);
        // $width = 259;
        // $height = 136.9;
        // $label->width = $width;
        // $label->height = $height;
        // 3. Display in PDF
        $icon = '<title>' . config('app.name', 'ICAPT') . '</title><link rel="shortcut icon" type="image/x-icon" href="/images/logo/icapt-small-red2.png"><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>';
        $body = "$icon<body style='width:" . $label->width . "mm;height:" . $label->height . "mm;'>

        $template</body>";
        echo $body;
        exit;
        $labels = [1 => $template];
        // $labels = [1 => $template];
        // $template = "Print Testing";
        // // Create a new Dompdf instance
        // $dompdf = new Dompdf();


        // $dompdf->setPaper(array(
        //     0, 0, $width, $height
        // ), 'portrait');


        // // Set document information
        // // $dompdf->setPaper('90mmx60mm', 'portrait'); // Set page width and height in millimeters (A4 size in this example)
        // // $dompdf->set_option('isPhpEnabled', true);


        // $html = View('Barcode.template', compact('labels'))->render();
        // // Render the content
        // $dompdf->loadHtml($html);



        // // Render the PDF
        // $dompdf->render();



        // // Output the PDF as a download
        // $dompdf->stream('pdf_with_specific_page_size.pdf');
        // exit;

        $pdf = PDF::loadView('Barcode.template', compact('labels'))
            ->setOption('page-width', $label->width . "mm")
            ->setOption('page-height', $label->height . "mm");
        //  ->setOption('margins', 0);
        $path = public_path('');
        $fileName = $input['label_name'] . "_" . auth()->user()->site_id . ".pdf";
        if (file_exists($path . '/' . $fileName)) {
            unlink($path . '/' . $fileName);
        }

        $pdf->save($path . '/' . $fileName);

        dd($pdf);
        // Storage::put('public/invoice.pdf', $pdf->output());
        // $pdf->download($fileName);
        // $url = config('icapt.app_url.real_url');
        // $url = config('app.url');
        // dd($url);
        echo 'https://docs.google.com/viewerng/viewer?url=' . $url . '/' . $fileName;
        return redirect('https://docs.google.com/viewerng/viewer?url=' . $url . '/' . $fileName);

        return $pdf->stream();
    }


    private function convertJson($raw_content)
    {

        $label_json = json_decode($raw_content);
        if (!$label_json)
            return "";

        $property = [
            'size',
            'content',
            'style',
        ];

        $char = ['%', '-', '/', '<small>', '</small>', '<br>', '</br>', '<br/>', '<br />', '<br >'];

        $arrContent = array();

        $attribute = [
            'label_name',
            'site_name',
            'Item',
            'item_num',
            'item_qrcode',
            'Desc',
            'item_desc',
            'Loc',
            'loc_qrcode',
            'loc_num',
            'Lot',
            'lot_qrcode',
            'lot_num',
            'Expiry',
            'Date',
            'expiry_date',
            'CO',
            'co_num',
            'Cust',
            'cust_name',
            'PO',
            'po_num',
            'Vendor',
            'vend_name',
            'vend_lot',
            'uom',
            'boxnum',
            'bn',
            'tb',
            'totalbox',
            'trans_date',
            'TO',
            'trn_num',
            'TO',
            'Line',
            'trn_line',
            'Job',
            'job_qrcode',
            'job_num',
            'Suffix',
            'suffix',
            'suffix_qrcode',
            'qtyinbox',
            'qib',
            'Qty',
            'in',
            'Box',
            'loc_qrcodeloc_num',
            'lot_qrcodelot_num',
            'From',
            'Whse',
            'from_whse_qrcode',
            'from_whse_qrcodefrom_whse',
            'from_whse',
            'To',
            'to_whse_qrcode',
            'to_whse_qrcodeto_whse',
            'to_whse',
            'Shipment',
            'Id',
            'ID',
            'shipmentId',
            'Product',
            'Doc',
            'document_num',
            'unit_weight',
            'unit_length',
            'unit_width',
            'unit_height',
            'return_num',
            'return_line',

        ];

        $html = '<div class="container">';

        foreach ($label_json as $label_data) {
            foreach ($label_data as $data) {

                $content = str_replace($char, "", $data->content);
                $arrContent = explode(" ", $content);

                foreach ($arrContent as $key => $strData) {
                    if ($strData != "") {
                        if (!in_array($strData, $attribute)) {
                            return false;
                        }
                    }
                }
            }
        }

        foreach ($label_json as $rows) {
            $html .= '<div class="row">';
            foreach ($rows as $col) {
                foreach ($col as $key => $col_value) {
                    if (!in_array($key, $property)) {
                        return false;
                    }
                }

                $html .= '<div class="col-xs-' . $col->size . '" style="' . $col->style . '">';
                $html .= $col->content;
                $html .= '</div>';
            }
            $html .= '</div>';
        }
        $html .= '</div>';

        return $html;
    }

    //delete label
    public function ap_delete(Request $request)
    {
        $label = new Label();
        $myString = $request->id;
        $myArray = explode(',', $myString);
        Label::destroy($myArray);
        //if no row selected or request is null
        if ($myString == "null" || $myString == "") {
            return redirect(route('labellist'))->with('errormsg', __('error.admin.selectone'))
                ->with('page', $this->page);
        }
        //if >1 selected and successful
        else {
            return redirect(route('labellist'))->with('successmsg', __('success.deleted', ['resource' => __('Label')]))
                ->with('page', $this->page);
        }
    }

    //select all label
    public function ap_index(Label $label)
    {
        //get all
        $labels = $label->all();

        foreach ($labels as $label) {
            $label->created_date = Timezone::convertFromUTC($label->created_at, auth()->user()->getSiteTimezone(), 'Y-m-d H:i:s');
            $label->modified_date = Timezone::convertFromUTC($label->modified_date, auth()->user()->getSiteTimezone(), 'Y-m-d H:i:s');
        }

        ini_set('memory_limit', '-1');
        set_time_limit(0);
        return json_encode($labels);
    }

    public function add_module(Request $request, $label_id)
    {
        $validate_object = DB::table('label_modules')->where('modulename', $request->dropdown)->where('label_id', $label_id)->where('site_id', auth()->user()->site_id)
            ->first();
        // dd($validate_object, $request->dropdown, $label_id);
        if ($validate_object != null) {
            // Throw error if object already exists
            throw ValidationException::withMessages([__('error.admin.exists', ['resource' => __('admin.label.object'), 'name' => $request->dropdown])]);
        }

        $module = Module::find($request->dropdown);
        $module->labels()->syncWithoutDetaching([$label_id => ['site_id' => auth()->user()->site_id]]);

        $label = Label::findOrFail($label_id);

        // Check whether object already exist in any label

        return App::make('redirect')->back()->with('successmsg', __(
            'success.updated',
            ['resource' => __('Label'), 'name' => $label->label_name]
        ));
    }


    public function add_module_new(Request $request, $label_id)
    {
        $module = Module::find($request->dropdown);

        $validate_object = DB::table('label_modules')->where('modulename', $request->dropdown)->where('site_id', auth()->user()->site_id)
            ->first();

        if ($validate_object != null) {
            // Throw error if object already exists
            throw ValidationException::withMessages([__('error.admin.exists', ['resource' => __('admin.label.object'), 'name' => $request->dropdown])]);
        }


        $module->labels()->syncWithoutDetaching($label_id);

        $label = Label::findOrFail($label_id);

        // Check whether object already exist in any label

        return App::make('redirect')->back()->with('successmsg', __(
            'success.updated',
            ['resource' => __('Label'), 'name' => $label->label_name]
        ));
    }

    public function del_module($label_id, $modulename)
    {

        $module = Module::find($modulename);
        $module->labels()->detach($label_id);
        return App::make('redirect')->back()->with('successmsg', __(
            'success.deleted',
            ['resource' => __('Object'), 'name' => $module->module_name]
        ));
    }

    public function checkLabelName($label_name = "")
    {
        $label = Label::where('label_name', $label_name)->where('site_id', auth()->user()->site_id)->first();

        if ($label != null) {
            return 'exist';
        } else {
            return 'not exist';
        }
    }
    public function test()
    {
        $apiKey = "-OpqJCpU_GzYoNNrRiJp7_KuPH6p6spWU7h482ypVRM";
        header("Content-Type:text/plain");
        $printerId = "72442662";

        $credentials = new \PrintNode\Credentials\ApiKey($apiKey);

        $client = new \PrintNode\Client($credentials);
        // $printers = $client->viewPrinters();

        // if (is_array($printers)) {
        //     foreach ($printers as $printerId => $printer) {
        //         echo $printer;
        //     }
        // }
        // exit;
        $printJob = new \PrintNode\Entity\PrintJob($client);
        //  914
        //                             [1] => 483
        $options = [
            'paper' => '90x60',
            'fit_to_page' => true,
            // 'rotate'=>0,
        ];
        $filePAth = public_path('YNInventory Label_AXA_TEST.pdf');
        $printJob->title = 'My Test Printjob';
        $printJob->source = 'PrintNode Example 2 Script';
        $printJob->printer = $printerId;
        $printJob->contentType = 'pdf_base64';
        $printJob->options = $options;
        $printJob->addPdfFile($filePAth);

        $printJobId = $client->createPrintJob($printJob);
        echo $printJobId;
        exit;

        return view('admin.label.test')->with('page', $this->page);
    }
    public function uploadFiles(Request $request)
    {
        $fileName = $request->file('file')->getClientOriginalName();
        $path = $request->file('file')->storeAs('label_uploads', $fileName, 'public');
        // $imgpath = request()->file($fileName)->store('label_uploads', 'public');

        return response()->json_encode(['location' => $path]);

        /*$imgpath = request()->file('file')->store('uploads', 'public');
        return response()->json(['location' => "/storage/$imgpath"]);*/
    }
}
