@extends('layout.mobile.app')

@section('content')
@section('title', __('Job Material Return - CW'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$jobmatl->whse_num"
            :itemnum="$jobmatl->matl_item"
            :itemdesc="$jobmatl->matl_desc"
            :refnum="$jobmatl->job_num"
            :refline="$jobmatl->oper_num"
            :qtybalance="$jobmatl->qty_returnable ?? 0"
            :qtybalanceuom="$jobmatl->uom"
            :submiturl="route('runJobMatlReturnCWProcess')"
            :catch-weight-tolerance="$jobmatl->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :allow-over="$allow_over_return"
            :line-uom="$jobmatl->uom"
            :print-label="$printLabel"
            transtype="job"
            trans-type="JobMaterialReturn"
            :incoming="true">

            <input type="hidden" name="non_inv" id="non_inv" value="false">
            <input type="hidden" name="ref_release" id="ref_release" class="form-control border-primary" value="{{ $jobmatl->sequence }}">
            <input type="hidden" name="suffix" id="suffix" class="form-control border-primary" value="{{ $jobmatl->suffix }}">

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num',$def_loc) }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.job_num') }} : {{ $jobmatl->job_num }} | {{ $jobmatl->suffix }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack()
    {
        var URLRedirect = "{{$url}}";
        url = URLRedirect.replace(/&amp;/g, "&");
        window.location.href   =  url;
    }

    $(document).ready(function() {
        $("#lot_num").on('change', function() {
            if ($("#lot_num").val()) {
                // Change qty returnable based on selected Lot for job material return
                $.ajax({
                    url: '{{ route('getIssuedLotQty') }}',
                    type: 'GET',
                    data: {
                        from_module: 'Job Material Issue',
                        whse_num: $("#whse_num").val(),
                        ref_num: $("#ref_num").val(),
                        ref_line: $("#ref_line").val(),
                        ref_release: $("#ref_release").val(),
                        lot_num: $("#lot_num").val(),
                    },
                    success: function(data) {
                        // Update the quantity balance based on the selected lot
                        $("#qtybalance").val(data);
                        display('/displayLotQuantityCoPick',
                            'item_num,whse_num,loc_num,lot_num,qtybalance,qtybalanceuom,cust_num,qtybalanceuom,vend_num',
                            'qty_available,max_qty_input'
                        );
                    }
                });
            }
        });
    });
</script>

@endsection()
