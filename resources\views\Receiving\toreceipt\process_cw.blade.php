@extends('layout.mobile.app')

@section('content')
@section('title', __('Transfer Order Receipt - CW'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 6px;}

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$to_details->to_whse"
            :itemnum="$to_details->item_num"
            :itemdesc="$to_details->item->item_desc ?? ''"
            :refnum="$to_details->trn_num"
            :refline="$to_details->trn_line"
            :qtybalance="$sub_sublines_details->qty_receivable ?? ($to_details->qty_shipped - $to_details->qty_received)"
            :qtybalanceuom="$to_details->uom"
            :submiturl="route('processTransferReceiveCW')"
            :catch-weight-tolerance="$to_details->item->catch_weight_tolerance ?? 0"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :allow-over="$allow_over_receive"
            :line-uom="$to_details->uom"
            :print-label="$printLabel"
            transtype="to"
            trans-type="TOReceipt"
            :incoming="true">

            @if($sap_trans_order_integration==1)
                {{-- Document num --}}
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="document_num">{{__('mobile.label.doc')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="document_num" id="document_num" autocomplete="off" class="form-control border-primary" placeholder="{{__('mobile.placeholder.doc')}}" value="{{ old('document_num') }}" maxlength="30">
                        </div>
                    </div>
                </div>

                {{-- Last Receive? --}}
                <input type="hidden" name="last_receive" value="Yes" id="last_receive">
            @endif

            <div class="form-group row pt-1">
                <label class="col-xs-3 col-md-3 col-lg-3 label-control-custom required" for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                <div class="col-xs-7 col-md-7 col-lg-7">
                    <div class="input-group">
                        <input type="text" id="loc_num" class="form-control border-primary" placeholder="{{__('mobile.placeholder.loc_num')}}" name="loc_num" value="{{old('loc_num', $defaults->loc_num ?? '') }}" maxlength="30">
                        <span id="locnumnotexist"></span>
                        <span id="checkLoc"></span>
                    </div>
                </div>
                <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                    <button type="button" name="{{__('mobile.list.locations')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionNull('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                </div>
            </div>

            <x-slot name="additionalFields">
                @if($to_details->item->lot_tracked == 1)
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control-custom" for="trn_lot">{{ __('mobile.label.lot_num') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" maxlength="50" id="trn_lot" value="{{old('trn_lot', $sublines_details->trn_lot ?? '')}}" class="form-control border-primary" placeholder="{{__('mobile.placeholder.lot_num')}}" name="trn_lot" >
                            </div>
                        </div>
                        <div class="col-xs-1 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{__('mobile.list.lots')}}" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal" onclick="selectionTONum('/getLotToUnitNum','item_num,trn_num,trn_line','trn_lot','trn_lot');modalheader(this.id,this.name)"><i class="icon-search"></i></button>
                        </div>
                    </div>
                @endif
                <div class="form-group row">
                    <div class="input-group">
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom required" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.to_num') }} : {{ $to_details->trn_num }} | {{ $to_details->trn_line }}
                        </p>
                        <p class="col-xs-12 col-md-12 col-lg-12 label-control-custom" style="word-break: break-all; word-wrap: break-word">
                            {{ __('mobile.label.from_whse') }} : {{ $to_details->from_whse }} → {{ $to_details->to_whse }}
                        </p>
                    </div>
                </div>
            </x-slot>

        </x-catch-weight-form>
    </div>
</div>

<script>
    function historyBack()
    {
        window.history.go(-1);
    }
</script>

@endsection()
