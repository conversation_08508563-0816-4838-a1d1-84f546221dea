<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Jobs\WebhookApiServiceJob;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Services\QB_Client;
use App\QuickbookSetting;
use Illuminate\Support\Facades\Log;
use App\Services\QuickbookService;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Crypt;
use App\Services\GeneralService;
use App\Services\SiteConnectionService;
use PhpParser\Node\Stmt\TryCatch;

class QuickbookController extends Controller
{

    public function connect(Request $request)
    {

        //        dd($request->qb);
        $request = validateSansentiveValue($request);
        $request->validate([
            //            'qb_realmId' => 'required',
            'qb_whse_num' => 'required|exists:warehouses,whse_num,whse_status,1,site_id,' . auth()->user()->site_id,
        ], [
            //            'qb_realmId.required' => __('error.admin.required', ['resource' => __('admin.label.quickbook_app_id')]),
            'qb_whse_num.required' => __('error.admin.required', ['resource' => __('mobile.list.warehouses')]),
            'qb_whse_num.exists' => __('error.admin.processinactive', ['resource' => __('mobile.list.warehouses')]),
        ]);

        $qb_setting = QuickbookSetting::where('site_id', auth()->user()->site_id)->first();

        if (!$qb_setting) {
            $qb_setting = new QuickbookSetting();
            $qb_setting->site_id = auth()->user()->site_id;
        }

        //            $qb_setting->active = 1;
        //            $qb_setting->realmId = encrypt($request->qb_realmId);
        $qb_setting->whse_num = $request->qb_whse_num;
        $qb_setting->save();
        $client = new QB_Client($qb_setting);

        $authUrl = $client->getAuthorizationURL("code", "RandomState");

        return redirect()->away($authUrl);

        return redirect(route('connection_settings'))->with(['errormsg' => "QuickBooks Settings not found."]);
    }

    public function disconnect(Request $request)
    {

        //        \App\SiteConnection::where('connector', 'quickbook')->update(['active' => 0]);
        $qb_setting = QuickbookSetting::where('site_id', auth()->user()->site_id)->first();

        $response = QB_Client::revokeToken($qb_setting);
        if ($response === false) {
            return redirect(route('connection_settings'))->with(['errormsg' => "Token cannot be revoked."]);
        }
        $site_connectionObj = \App\SiteConnection::where('site_id', auth()->user()->site_id)->where('type', 'erp')->where('connector', 'quickbook')->first();
        if ($site_connectionObj) {
            $site_connectionObj->active = 0;
            $site_connectionObj->save();
        }

        if ($qb_setting) {
            $qb_setting->active = 0;

            $qb_setting->access_token = "";
            $qb_setting->refresh_token = "";
            $qb_setting->access_token_expires_in = strtotime('now') - 10;
            $qb_setting->refresh_token_expires_in = strtotime('now') - 10;
            $qb_setting->save();
        }
        return redirect(route('connection_settings'))->with(['successmsg' => "QuickBooks account has been successfully disconnected."]);
    }

    public function disconnected(Request $request)
    {
        $realmID = $request->realmId;
        // $qb_setting = QuickbookSetting::all()->filter(function ($row) use ($realmID) {
        //             if (Crypt::decrypt($row->realmId) == $realmID) {
        //                 return $row;
        //             }
        //         })->first();
        $qb_setting = QuickbookSetting::where('realmId', $realmID)->first();

        if ($qb_setting) {
            $qb_setting->active = 0;

            $qb_setting->access_token = "";
            $qb_setting->refresh_token = "";
            $qb_setting->access_token_expires_in = strtotime('now') - 10;
            $qb_setting->refresh_token_expires_in = strtotime('now') - 10;
            $qb_setting->save();
        }
        return view('quickbooks.disconnect');
    }

    public function getToken(Request $request)
    {

        $qb_setting = QuickbookSetting::where('site_id', auth()->user()->site_id)->first();
        if (!$qb_setting) {
            return redirect(route('connection_settings'))->with(['errormsg' => "QuickBooks Settings not found."]);
        }

        if (isset($_GET["error"])) {
            return redirect(route('connection_settings'))->with(['errormsg' => "QuickBooks Connection Failed."]);
        }
        $code = $_GET["code"];
        $responseState = $_GET['state'];

        $grant_type = "authorization_code";
        // $client_id = $qb_setting->client_id;
        // $client_secret = $qb_setting->client_secret;
        // $company_id = $qb_setting->app_id;
        $realmID = $request->realmId;
        $qb_setting->realmId = $realmID;

        // $qb_setting->realmId = encrypt("$realmID");
        $qb_setting->save();
        //
        $result = QB_Client::getAccessToken($code, $grant_type);
        // dd($result);
        if (isset($result['access_token'])) {

            $endpoint = "companyinfo/" . $realmID;
            $customerData = QB_Client::getApiCallTokenized($endpoint, $qb_setting, $result['access_token'], "Company Info", "getToken");

            if (!isset($customerData->CompanyInfo))
                return redirect(route('connection_settings'))->with(['errormsg' => "Error: Failed to connect with QuickBooks. Kindly confirm the Company ID is correct."]);

            //                return redirect(route('connection_settings'))->with('errormsg', "Error: Failed to connect with QuickBooks. Kindly confirm the Company ID is correct.");
            //            throw ValidationException::withMessages(["Error: Failed to connect with QuickBooks. Kindly confirm the Company ID is correct."]);

            $site_connectionObj = \App\SiteConnection::where('type', 'erp')->where('connector', 'quickbook')->first();
            if (!$site_connectionObj) {
                if (!$site_connectionObj) {
                    $site_connectionObj = new \App\SiteConnection();
                    $site_connectionObj->type = 'erp';
                    $site_connectionObj->connector = "quickbook";
                }
            }
            $axacuteProcesses = SiteConnectionService::$axacuteProcesses['erp']['quickbook'];
            $qbProcess = [];
            foreach ($axacuteProcesses as $key => $elm) {
                $qbProcess[] = $key;
            }
            $site_connectionObj->processes = json_encode($qbProcess);
            $site_connectionObj->active = 1;
            $site_connectionObj->save();

            $qb_setting->active = 1;
            $qb_setting->access_token = $result['access_token'];
            $qb_setting->refresh_token = encrypt($result['refresh_token']);
            $qb_setting->access_token_expires_in = strtotime('now') + intval($result['expires_in']);
            $qb_setting->refresh_token_expires_in = strtotime('now') + intval($result['x_refresh_token_expires_in']);

            $qb_setting->save();
            $_SESSION['access_token'] = $result['access_token'];

            return redirect(route('connection_settings'))->with(['successmsg' => "QuickBooks account has been added successfully."]);
        }

        //record them in the session variable
        //        $grant_type = 'refresh_token';
        //        $result = $client->refreshAccessToken($grant_type, $result['refresh_token']);
        //        dd($result);
        return redirect(route('connection_settings'))->with(['errormsg' => "Error: Failed to connect with QuickBooks. Access Token could not found."]);
    }


    public function webhookNotification(Request $request)
    {


        $webhookToken = "107de755-ffd2-411f-9688-b7e95ac1bcaf";
        // $singatureHash = "";
        // if (isset($_SERVER['HTTP_INTUIT_SIGNATURE'])) {
        //     $singatureHash = bin2hex(base64_decode($_SERVER['HTTP_INTUIT_SIGNATURE']));
        // }
        //        $output = 'Received a request at ' . date("D M j G:i:s", $_SERVER['REQUEST_TIME']) . ' at ' . $_SERVER['HTTP_HOST'] . ' from ' . $_SERVER['HTTP_USER_AGENT'] . PHP_EOL;
        //        Log::debug(json_encode($output));

        // $bodyContent = $request->getContent();

        // $payloadHash = hash_hmac('sha256', $bodyContent, $webhookToken);

        //        if ($payloadHash == $singatureHash) {
        //            $output .= PHP_EOL . 'Request is verified' . PHP_EOL;
        //        } else {
        //            $output .= PHP_EOL . "Unable to verify request, using a token of '" . $webhookToken . "' the payload hash was " . $payloadHash . ' while the intuit signature was ' . $singatureHash . PHP_EOL;
        //        }
        $realmID = "";
        $payload = @file_get_contents('php://input');
        $data = json_decode($payload, true);
        // GeneralService::addIntegerationLog("QuickBooks", "QuickBooksEventNotifications", "received", "notification", $data, [], 1, "post", "AXA");

        $eventNotifications = $data['eventNotifications'];
        // dd($eventNotifications);
        // GeneralService::addIntegerationLog("quickbooks", "Webhook", "webhookNotification", [], $eventNotifications, [], 1, "get", "AXA_TEST");

        foreach ($eventNotifications as $eventNotification) {

            $realmID = $eventNotification['realmId'];
            $entities = $eventNotification['dataChangeEvent']['entities'];
            $qb_setting = QuickbookSetting::where('realmId', $realmID)->first();

            if ($qb_setting) {
                foreach ($entities as $entity) {

                    WebhookApiServiceJob::dispatch($entity, $qb_setting->site_id);

                    // $type = $entity['name'];
                    // $id = $entity['id'];
                    // $operation = $entity['operation'];
                    // GeneralService::addIntegerationLog("QuickBooks", "QuickBooksEventNotifications", $operation, $entity, [], [], 1, "post", $qb_setting->site_id);


                    // if ($operation == "Create" || $operation == "Update") {
                    //     //                dd($id);
                    //     @QuickbookService::syncEntity($id, $type, $realmID, $operation);
                    // }
                }
            }
        }

        return response()->json(['message' => 'This request succeeded.'], 200);

        //        Log::debug("nd $realmID");
        exit();
    }
    public function test(Request $request)
    {
        if (config('icapt.environment') == "TestEnvironment") {
            $apiBaseUrl = "https://sandbox-quickbooks.api.intuit.com/v3/company/";
        } else {
            $apiBaseUrl = "https://quickbooks.api.intuit.com/v3/company/";
        }
        dd($apiBaseUrl, config('icapt.environment'), env('QB_TEST'));
        $realmID = "4620816365221394370";
        //        dd(encrypt($realmID));
        //        $realmID = encrypt($realmID);
        // $qb = QuickbookSetting::all()->filter(function ($row) use ($realmID) {
        //     if ($row->realmId != "" && Crypt::decrypt($row->realmId) == $realmID) {
        //                 return $row;
        //             }
        //         })->first();
        $qb = QuickbookSetting::all();
        $qbo = null;
        // dd(decrypt("eyJpdiI6IkVkRHZzR2JxMlFPdmpwZStcL1k3dzF3PT0iLCJ2YWx1ZSI6IjdDNGpycXFCODd0c21SNVJqMDdaUFNDNUpnR3hrS0h5WHZcL2Y3dTRPNzhBPSIsIm1hYyI6IjgwYjcyN2IwMzMzOWJmMThiYTMzYzNh"));
        foreach ($qb as $qbElm) {


            try {
                $dec =  decrypt($qbElm->realmId);
                echo "$qbElm->site_id";
                if ($dec == $realmID) {
                    $qbo = $qbElm;
                    dd($qbo);
                }
            } catch (\Throwable $th) {
                //throw $th;
                // dd($qbElm);
            }
        }
        dd('no luck');

        $qb_setting = QuickbookSetting::where('realmId', $realmID)->first();
        dd($qb_setting, $realmID);
        @self::syncAccount($qb_setting, $operation);

        $qb_setting = QuickbookSetting::where('site_id', "AXA_TEST")->first();
        //dd($qb_setting);
        //        $data = QuickbookService::syncCustomerOrder("155", $qb_setting);
        $endpoint = "companyinfo/" . decrypt($qb_setting->realmId) . "1";
        //        $data = QuickbookService::updateItem($qb_setting, "Garden Supplies3y");
        //        dd($data);
        //        $endpoint = "purchaseorder/155";
        $customerData = QB_Client::getApiCall($endpoint, $qb_setting);

        if (!isset($customerData->CompanyInfo))
            dd("err");
        dd($customerData->CompanyInfo);
    }
}
