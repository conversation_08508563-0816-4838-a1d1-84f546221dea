<?php

namespace App\Imports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;


class BaseImport implements ToCollection,WithHeadingRow, SkipsOnError, WithChunkReading, WithBatchInserts
{
    use Importable,SkipsErrors;

    protected $rowcount = 0;
    public $verifyHeader = true;
    public $truncate = false;

    public function collection(Collection $rows)
    {
    }

    public function getRowCount(): int
    {
        return $this->rowcount;
    }

    public function filterEmpty(Collection $rows)
    {
        // $rows = array_map(array($this, 'filterEmptyArray'), $rows->toArray());
        // return array_filter($rows);

        $rows = $rows->toArray();
        // Remove only rows that are completely empty (but keep keys like 'uom' even if empty)
        $rows = array_filter($rows, function ($row) {
            return array_filter($row, function ($value) {
                return $value !== null && $value !== '';
            });
        });

        return $rows;
    
    }

    public function filterEmptyArray($array){
        return array_filter($array, 'strlen');
    }

    public function chunkSize(): int
    {
        return 10000;
    }

    public function batchSize(): int
    {
        return 10000;
    }
}

?>
