<?php

namespace App\Http\Controllers\MasterMaintenance;

use App\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\helpers;
use Gate;

class MenuController extends Controller
{

    public function __construct()
    {
        //$this->middleware('auth');
        //$this->middleware('can:hasSysadmin');
    }
    public function getPlan()
    {
        // Query the site settings
        $plan_id = SiteSetting::select('plan_id')->where('site_id', auth()->user()->site_id)->where('status', 1)->value('plan_id');
        $plan_id = customDecrypt($plan_id);
        return $plan_id;
    }

    public function masterMenu()
    {

        $menuName = 'Master Files';

        $plan_id = $this->getPlan();

        //dd($plan_id);

        $col_num = 4;
        $module_name = DB::table('groups')->select('name', 'route')->where('module', "Maintenance")->where('submodule', "Master Files")->orderBy('sequence', 'ASC')->get()->toArray();

        foreach ($module_name as $key => $value) {

            $submenu[] = [
                'title' => $value->name,
                'route' => route($value->route),
                'icon' => 'icon-command2'
            ];
        }

        $arrLineDisplayLine1 = [];
        if (config('icapt.special_modules.alternate_barcode')) {
            $arrLine1 = ["Product Codes", "Count Groups", "UOMs", "Items", "UOM Conversions", "Alternate Barcodes", "Customers", "Shipping Zones", "Vendors"];
        } else {
            $arrLine1 = ["Product Codes", "Count Groups", "UOMs", "Items", "UOM Conversions", "Customers", "Shipping Zones", "Vendors"];
        }

        foreach ($submenu as $item) {
            if (in_array($item['title'], $arrLine1)) {
                $arrLineDisplayLine1[$item['title']]['title'] = $item['title'];
                $arrLineDisplayLine1[$item['title']]['route'] = $item['route'];
                // Primary = purple color
                $arrLineDisplayLine1[$item['title']]['color'] = 'primary';
                if ($item['title'] == "Customers" || $item['title'] == "Shipping Zones" || $item['title'] == "Vendors") {
                    $arrLineDisplayLine1[$item['title']]['color'] = 'secondary';
                }
            }
        }

        $arrLineDisplayLine2 = [];

        // Free without Zone
        if ($plan_id == 7) {
            $arrLine2 = ["Warehouses", "Locations", "Item Warehouses", "Item Locations", "Lots", "Item Lot Locations"];
        } else if ($plan_id == 2 || $plan_id == 3 || $plan_id == 5 || $plan_id == 6) {
            if (config('icapt.special_modules.enable_pallet')) {
                $arrLine2 = ["Warehouses", "Zones", "Locations", "Item Warehouses", "Item Locations", "Lots", "Item Lot Locations", "Pallets"];
            } else {
                $arrLine2 = ["Warehouses", "Zones", "Locations", "Item Warehouses", "Item Locations", "Lots", "Item Lot Locations"];
            }
        } else {
            $arrLine2 = ["Warehouses", "Zones", "Locations", "Item Warehouses", "Item Locations", "Lots", "Item Lot Locations"];
        }




        foreach ($submenu as $item) {
            if (in_array($item['title'], $arrLine2)) {
                $arrLineDisplayLine2[$item['title']]['title'] = $item['title'];
                $arrLineDisplayLine2[$item['title']]['route'] = $item['route'];
                // Danger = red color
                $arrLineDisplayLine2[$item['title']]['color'] = 'danger';
            }
        }

        $arrLineDisplayLine3 = [];
        // plan_id =1 & 4 = Started Plan
        if (config('icapt.client_prefix') == "OceanCash") {
            // dd($plan_id);
            if ($plan_id != 1 && $plan_id != 4) {

                if ($plan_id == 7 || $plan_id == 2 || $plan_id == 5) {
                    $arrLine3 = ["Indirect Tasks", "Reason Codes", "Bundle Builder", "Employees", "Work Centers", "Bill of Materials",'Packing List', "Package Type"];
                } else {
                    $arrLine3 = ["Indirect Tasks", "Reason Codes", "Bundle Builder", "Employees", "Machines", "Work Centers", "Bill of Materials",'Packing List', "Package Type"];
                }
            } else {
                $arrLine3 = ["Employees", "Reason Codes", "Bundle Builder"];
            }

            // Sort $submenu to add Bundle Builder below Reason Code.
            usort($submenu, function ($a, $b) use ($arrLine3) {
                $pos_a = array_search($a['title'], $arrLine3);
                $pos_b = array_search($b['title'], $arrLine3);
                return $pos_a - $pos_b;
            });
        } else {
            if ($plan_id != 1 && $plan_id != 4) {

                if ($plan_id == 7 || $plan_id == 2 || $plan_id == 5) {
                    $arrLine3 = ["Employees", "Work Centers", "Indirect Tasks", "Reason Codes", "Bill of Materials", "Package Type"];
                } else {
                    $arrLine3 = ["Employees", "Machines", "Work Centers", "Indirect Tasks", "Reason Codes", "Bill of Materials", "Package Type"];
                }
            } else {
                $arrLine3 = ["Employees", "Reason Codes"];
            }
        }

        foreach ($submenu as $item) {
            if (in_array($item['title'], $arrLine3)) {

                $arrLineDisplayLine3[$item['title']]['title'] = $item['title'];
                $arrLineDisplayLine3[$item['title']]['route'] = $item['route'];
                // Info = blue color
                $arrLineDisplayLine3[$item['title']]['color'] = 'info';
                if ($item['title'] == "Indirect Tasks" || $item['title'] == "Reason Codes" || $item['title'] == "Package Type" || $item['title'] == "Bundle Builder") {
                    $arrLineDisplayLine3[$item['title']]['color'] = 'warning';
                }
            }
        }

        return view('MasterMaintenance.menu', compact('submenu', 'menuName'))->with('plan_id', $plan_id)->with('col_num', $col_num)->with('arrLineDisplayLine1', $arrLineDisplayLine1)->with('arrLineDisplayLine2', $arrLineDisplayLine2)->with('arrLineDisplayLine3', $arrLineDisplayLine3);
    }

    public function transMenu()
    {

        $menuName = 'Transaction Maintenance';

        $plan_id = $this->getPlan();

        if ($plan_id != 1 &&  $plan_id != 4) {
            $col_num = 4;
            $module_name = DB::table('groups')->select('name', 'route')->where('module', "Maintenance")->where('submodule', "Transactions")->orderBy('sequence', 'ASC')->get()->toArray();
        } else {
            $col_num = 6;
            $module_name = DB::table('groups')
                ->select('name', 'route')
                ->where('category', "Inventory")
                ->where('submodule', "Transactions")
                ->orderBy('sequence', 'ASC')
                ->get()
                ->toArray();
        }

        // Additional Menu
        $additional_module_name = DB::table('groups')
            ->select('name', 'route')
            ->whereIn('code', ['UTILITIES_LOTEXPIRYUPDATE','MAINTENANCE_CODUEDATEUPDATER', 'INVENTORY_COUNT_UNMATCHED'])
            ->orderBy('sequence', 'ASC')
            ->get()
            ->toArray();

        $additional_module_name_poduedate = DB::table('groups')
            ->select('name', DB::raw('"PODueDateUpdater" AS route'))
            ->whereIn('code', ['MAINTENANCE_PODUEDATEUPDATER'])
            ->orderBy('sequence', 'ASC')
            ->get()
            ->toArray();

        $module_name = array_merge($module_name, $additional_module_name);
        $index_need_to_add = null;
        $secondary_index = null;

        foreach($module_name as $key => $value) {
            if ($value->name == 'Purchase Orders')
            {
                $secondary_index = $key;
            }
            if ($value->name == 'Goods Receiving Notes')
            {
                $index_need_to_add = $key;
                break;
            }
        }

        if ($index_need_to_add == null)
        {
            $index_need_to_add = $secondary_index;
        }

        $module_name = array_merge(
            array_slice($module_name, 0, $index_need_to_add + 1, true),
            $additional_module_name_poduedate,
            array_slice($module_name, $index_need_to_add + 1, count($module_name) - 1, true)
        );

        foreach ($module_name as $key => $value) {
            if ($value->route == 'allocation.index' || $value->route == 'picklistTest') {
                $value_before_after_dash = explode(' - ', $value->name);
                $value->name = $value_before_after_dash[0];
            }

            $submenu[$value->name] = [
                'title' => $value->name,
                'route' => route($value->route),
                'icon' => 'icon-command2'
            ];
        }

        $arrLineDisplayLine1 = [];

        if (config('icapt.enable_customer_returns')) {



        if (config('icapt.enable_grn')) {
            if ($plan_id == 1 || $plan_id == 4 || $plan_id == 7) {
                $arrLine1 = ["Purchase Orders", "Goods Receiving Notes", "Purchase Order Line Due Date Updater", "Customer Orders","Customer Returns", "Customer Order Line Due Date Updater"];
            } else {
                $arrLine1 = ["Purchase Orders", "Goods Receiving Notes", "Purchase Order Line Due Date Updater", "Customer Orders", "Customer Returns","Transfer Orders", "Customer Order Line Due Date Updater"];
            }
        } else {
            if ($plan_id == 1 || $plan_id == 4 || $plan_id == 7) {
                $arrLine1 = ["Purchase Orders", "Purchase Order Line Due Date Updater", "Customer Orders","Customer Returns"];
            } else {
                $arrLine1 = ["Purchase Orders", "Purchase Order Line Due Date Updater", "Customer Orders","Customer Returns", "Transfer Orders", "Customer Order Line Due Date Updater"];
            }
        }
    } else {

        if (config('icapt.enable_grn')) {
            if ($plan_id == 1 || $plan_id == 4 || $plan_id == 7) {
                $arrLine1 = ["Purchase Orders", "Goods Receiving Notes", "Purchase Order Line Due Date Updater", "Customer Orders", "Customer Order Line Due Date Updater"];
            } else {
                $arrLine1 = ["Purchase Orders", "Goods Receiving Notes", "Purchase Order Line Due Date Updater", "Customer Orders","Transfer Orders", "Customer Order Line Due Date Updater"];
            }
        } else {
            if ($plan_id == 1 || $plan_id == 4 || $plan_id == 7) {
                $arrLine1 = ["Purchase Orders", "Purchase Order Line Due Date Updater", "Customer Orders"];
            } else {
                $arrLine1 = ["Purchase Orders", "Purchase Order Line Due Date Updater", "Customer Orders", "Transfer Orders", "Customer Order Line Due Date Updater"];
            }
        }

    }

        //dd($submenu,$arrLine1);

        foreach ($submenu as $item) {
            if (in_array($item['title'], $arrLine1)) {
                $arrLineDisplayLine1[$item['title']]['title'] = $item['title'];
                $arrLineDisplayLine1[$item['title']]['route'] = $item['route'];
                // Danger = red color
                $arrLineDisplayLine1[$item['title']]['color'] = 'danger';

                if (config('icapt.enable_grn')) {
                    if ($item['title'] == "Purchase Orders" || $item['title'] == "Goods Receiving Notes") {
                        $arrLineDisplayLine1[$item['title']]['color'] = 'primary';
                    }
                } else {
                    if ($item['title'] == "Purchase Orders") {
                        $arrLineDisplayLine1[$item['title']]['color'] = 'primary';
                    }
                    if ($item['title'] == "Purchase Order Line Due Date Updater") {
                        $arrLineDisplayLine1[$item['title']]['color'] = 'primary';
                    }
                }
            }
        }

        $arrLineDisplayLine2 = [];
        $arrLine2 = ["Allocation", "Pick List", "Inventory Count", "Inventory Count Unmatched", "Lot Expiry Date Update"];
        foreach ($submenu as $item) {
            if (in_array($item['title'], $arrLine2)) {
                $arrLineDisplayLine2[$item['title']]['title'] = $item['title'];
                $arrLineDisplayLine2[$item['title']]['route'] = $item['route'];
                // Secondary = black color
                $arrLineDisplayLine2[$item['title']]['color'] = 'secondary';
            }
        }

        $arrLineDisplayLine3 = [];

        // plan is not starter
        if ($plan_id != 1 && $plan_id != 4) {
            //, "Labor & Machine"
            $arrLine3 = ["Job Orders", "Job Routes", "Job Materials", "Bill of Materials"];
        } else {
            $arrLine3 = ["Job Orders", "Job Routes", "Job Materials", "Bill of Materials"];
        }



        foreach ($submenu as $item) {
            if (in_array($item['title'], $arrLine3)) {
                $arrLineDisplayLine3[$item['title']]['title'] = $item['title'];
                $arrLineDisplayLine3[$item['title']]['route'] = $item['route'];
                // Warning = yellow color
                $arrLineDisplayLine3[$item['title']]['color'] = 'warning';
            }
        }

        return view('MasterMaintenance.menu')->with(compact('submenu', 'menuName'))->with('plan_id', $plan_id)->with('col_num', $col_num)->with('arrLineDisplayLine1', $arrLineDisplayLine1)->with('arrLineDisplayLine2', $arrLineDisplayLine2)->with('arrLineDisplayLine3', $arrLineDisplayLine3);
    }

    public function utilitiesMenu()
    {

        $menuName = __("admin.menu.utilities");
        $plan_id = $this->getPlan();
        if ($plan_id != 1 || $plan_id != 4) {
        $arrLineDisplayLine1 = array(
            array('title' => __('admin.menu.inventory_count_unmatched'), 'route' => route('inventoryCountUnmatched'), 'icon' => 'icon-command2', 'color' => 'info'),
          // array('title' => __('admin.menu.job_status_updater'), 'route' => route('jobStatusUpdater'), 'icon' => 'icon-command2', 'color' => 'warning'),
            array('title' => __('admin.menu.co_line_due_date_updater'), 'route' => route('CODueDateUpdater'), 'icon' => 'icon-command2', 'color' => 'danger'),
            array('title' => __('admin.menu.po_line_due_date_updater'), 'route' => route('PODueDateUpdater'), 'icon' => 'icon-command2', 'color' => 'primary'),
            array('title' => __('admin.menu.lot_expiry_date_update'), 'route' => route('LotExpiryDateUpdate'), 'icon' => 'icon-command2', 'color' => 'info'),

        );
    }
    else{
        $arrLineDisplayLine1 = array(
            array('title' => __('admin.menu.inventory_count_unmatched'), 'route' => route('inventoryCountUnmatched'), 'icon' => 'icon-command2', 'color' => 'info'),
            array('title' => __('admin.menu.job_status_updater'), 'route' => route('jobStatusUpdater'), 'icon' => 'icon-command2', 'color' => 'warning'),
            array('title' => __('admin.menu.co_line_due_date_updater'), 'route' => route('CODueDateUpdater'), 'icon' => 'icon-command2', 'color' => 'danger'),
            array('title' => __('admin.menu.po_line_due_date_updater'), 'route' => route('PODueDateUpdater'), 'icon' => 'icon-command2', 'color' => 'primary'),
            array('title' => __('admin.menu.lot_expiry_date_update'), 'route' => route('LotExpiryDateUpdate'), 'icon' => 'icon-command2', 'color' => 'info'),

        );
    }
        $plan_id = $this->getPlan();

        $col_num = 4;
        $arrLineDisplayLine2 = $arrLineDisplayLine3 = [];
        return view('MasterMaintenance.menu')->with(compact('arrLineDisplayLine1', 'arrLineDisplayLine2', 'arrLineDisplayLine3', 'col_num', 'menuName'));
    }

    public function historyMenu()
    {
        $menuName = 'History';

        $plan_id = $this->getPlan();

        if ($plan_id != 1 && $plan_id != 4) {
            $module_name = DB::table('groups')->select('name', 'route')->where('module', "History")->get()->toArray();
        } else {
            $module_name = DB::table('groups')->select('name', 'route')->where('module', "History")->where('category', '!=',  "Production")->get()->toArray();
        }

        foreach ($module_name as $key => $value) {

            $submenu[] = [
                'title' => $value->name,
                'route' => route($value->route),
                'icon' => 'icon-command2'
            ];
        }

        return view('MasterMaintenance.menu')->with(compact('submenu', 'menuName'));
    }

    public function inventoryMenu()
    {
        $menuName = 'Inventory Report';
        $arrLineDisplayLine2 = [];
        $arrLineDisplayLine3 = [];
        $col_num = 4;


        @$plan = \App\SiteSetting::select('plan_id')
            ->where('site_id', @auth()->user()->site_id)
            ->where('status', 1)
            ->value('plan_id');
        $plan = @customDecrypt($plan);

        $starterOrFree = $starterOnly = false;

        if ($plan == 1 || $plan == 4) {
            $starterOnly = true;
        }
        if ($plan == 1 || $plan == 4 || $plan == 7) {
            $starterOrFree = true;
        }


        $arrLineDisplayLine1 = array(
            array('title' => __('admin.title.inv_trans_by_item'), 'route' => route('invtransrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.po_history') . ' By Vendor', 'route' => route('polinesrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.po_status'), 'route' => route('postatusrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.stock_move_verif'), 'route' => route('stockMoveVerif'), 'icon' => 'icon-command2', 'color' => 'secondary'),

            // Open backin 2.16.0
             array('title' => __('admin.title.co_return_trans'), 'route' => route('coReturnTrans'), 'icon' => 'icon-command2', 'color' => 'secondary'),
        );

        // if(config('icapt.client_prefix') === "DCE"){
        //     array_push($arrLineDisplayLine1, array('title' => __('admin.title.inv_valuation'), 'route' => route('invvalrep'), 'icon' => 'icon-command2', 'color' => 'secondary'));
        //     array_push($arrLineDisplayLine2, array('title' => __('admin.title.summ_live_stock_incom'), 'route' => route('summlivestockincom'), 'icon' => 'icon-command2', 'color' => 'secondary'));
        // }

        $arrLineDisplayLine2 = array(
            array('title' => __('admin.title.inv_balance'), 'route' => route('invbalrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.co_history') . ' By Customer', 'route' => route('solinesrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.co_status'), 'route' => route('costatusrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.copicking_vs_shipping'), 'route' => route('copickingshippingstatusrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            array('title' => __('admin.title.copick'), 'route' => route('coPick'), 'icon' => 'icon-command2', 'color' => 'secondary'),
        );

        if (config('icapt.client_prefix') == "OceanCash") {
            $arrLineDisplayLine3 = array(
                // Inventory Expiry report renamed to Lot Expiry
                array('title' => __('admin.title.lot_expiry'), 'route' => route('invexprep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Reorder Report', 'route' => route('reorder_itemrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => __('admin.title.pack_list'), 'route' => route('packlist'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                   // open back at 2.16.0
                array('title' => __('admin.title.co_return_status'), 'route' => route('coReturnStatus'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
        } else {

            if (config('icapt.special_modules.enable_pallet') && !$starterOrFree) {
            $arrLineDisplayLine3 = array(
                // Inventory Expiry report renamed to Lot Expiry
                array('title' => __('admin.title.lot_expiry'), 'route' => route('invexprep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Reorder Report', 'route' => route('reorder_itemrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Pallet Summary Report', 'route' => route('pallet_summary_report'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                // open back at 2.16.0
                 array('title' => __('admin.title.co_return_status'), 'route' => route('coReturnStatus'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            }
            else{
                $arrLineDisplayLine3 = array(
                    // Inventory Expiry report renamed to Lot Expiry
                    array('title' => __('admin.title.lot_expiry'), 'route' => route('invexprep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                    array('title' => 'Reorder Report', 'route' => route('reorder_itemrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),

                  // open back at 2.16.0
                      array('title' => __('admin.title.co_return_status'), 'route' => route('coReturnStatus'), 'icon' => 'icon-command2', 'color' => 'secondary'),


                 // array('title' => 'Pallet Summary Report', 'route' => route('pallet_summary_report'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                );
            }
        }

        if (config('icapt.client_prefix') === "DCE") {
            array_push($arrLineDisplayLine1, array('title' => __('admin.title.inv_valuation'), 'route' => route('invvalrep'), 'icon' => 'icon-command2', 'color' => 'secondary'));
            array_push($arrLineDisplayLine2, array('title' => __('admin.title.summ_live_stock_incom'), 'route' => route('summlivestockincom'), 'icon' => 'icon-command2', 'color' => 'secondary'));
            array_push($arrLineDisplayLine3, array('title' => __('admin.title.live_stock'), 'route' => route('livestock'), 'icon' => 'icon-command2', 'color' => 'secondary'));
            array_push($arrLineDisplayLine1, array('title' => __('admin.title.forecast_rep'), 'route' => route('forecast'), 'icon' => 'icon-command2', 'color' => 'secondary'));
            array_push($arrLineDisplayLine3, array('title' => __('admin.title.stockallocation'), 'route' => route('stockallocation'), 'icon' => 'icon-command2', 'color' => 'secondary'));
        }

        return view('MasterMaintenance.menu')->with('col_num', $col_num)->with('arrLineDisplayLine1', $arrLineDisplayLine1)->with('menuName', $menuName)
            ->with('arrLineDisplayLine2', $arrLineDisplayLine2)->with('arrLineDisplayLine3', $arrLineDisplayLine3);
    }

    public function productionMenu()
    {
        $menuName = 'Production Report';

        $plan_id = $this->getPlan();
        $arrLineDisplayLine2 = [];
        $arrLineDisplayLine3 = [];
        $col_num = 4;

        //plan_id 7= Free
        // 5,2= Professional
        if ($plan_id != 7 && $plan_id != 5 && $plan_id != 2) {
            $arrLineDisplayLine1 = array(
                array('title' => 'Job Operation Hours', 'route' => route('joboperhourrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Machine Status Summary', 'route' => route('machinestatsumrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Job Order Status', 'route' => route('joborderstatrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Labour Status Summary', 'route' => route('labourstatsumrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            $arrLineDisplayLine2 = array(
                array('title' => 'Item Scrap Summary', 'route' => route('itemscrapsumrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Material Issued', 'route' => route('prodissuerep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Job Material Pick List Report', 'route' => route('jobMatlPick'), 'icon' => 'icon-command2', 'color' => 'secondary'),

                array('title' => 'Job Scrap Summary By Operation', 'route' => route('jobscrapsumbyoperrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );

            $arrLineDisplayLine3 = array(
                array('title' => 'Job Scrap Summary', 'route' => route('jobscraprsnrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Item Production Completion Summary', 'route' => route('prodcomplrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Job Processing Hours By Operation', 'route' => route('jobprocesshoursbyoper'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );

            if (config('icapt.client_prefix') === "OceanCash") {
                $arrLineDisplayLine2[] = array('title' => 'Product Efficiency', 'route' => route('produceffrep'), 'icon' => 'icon-command2', 'color' => 'secondary');
                $arrLineDisplayLine3[] = array('title' => 'Slitting Efficiency', 'route' => route('slittingeffrep'), 'icon' => 'icon-command2', 'color' => 'secondary');
            }

            if (config('icapt.client_prefix') === "MSSB") {
                $arrLineDisplayLine2[] = array('title' => 'WIP', 'route' => route('wiprep'), 'icon' => 'icon-command2', 'color' => 'secondary');
            }
        } else {
            // for starter/enterprise
            $arrLineDisplayLine1 = array(
                array('title' => 'Job Operation Hours', 'route' => route('joboperhourrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Job Order Status', 'route' => route('joborderstatrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Labour Status Summary', 'route' => route('labourstatsumrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            $arrLineDisplayLine2 = array(
                array('title' => 'Item Scrap Summary', 'route' => route('itemscrapsumrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                // array('title' => 'Aggregated Materials Requirement', 'route' => route('matl_req_agrgt'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                // array('title' => 'Job Materials Requirement by Operation', 'route' => route('matl_req'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Material Issued', 'route' => route('prodissuerep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Job Material Pick List Report', 'route' => route('jobMatlPick'), 'icon' => 'icon-command2', 'color' => 'secondary'),

                array('title' => 'Job Scrap Summary By Operation', 'route' => route('jobscrapsumbyoperrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            $arrLineDisplayLine3 = array(
                array('title' => 'Job Scrap Summary', 'route' => route('jobscraprsnrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Item Production Completion Summary', 'route' => route('prodcomplrep'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Job Processing Hours By Operation', 'route' => route('jobprocesshoursbyoper'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
        }
       // dd($plan_id);
        if ($plan_id != 1 &&  $plan_id != 4 ) {
            if (Gate::allows('hasMatlReqRep')) {
                $arrLineDisplayLine2[] = array('title' => 'Aggregated Materials Requirement', 'route' => route('matl_req'), 'icon' => 'icon-command2', 'color' => 'secondary');
            }
            if (Gate::allows('hasAgrgtMatlReqRep')) {
                $arrLineDisplayLine2[] = array('title' => 'Job Materials Requirement by Operation', 'route' => route('matl_req_agrgt'), 'icon' => 'icon-command2', 'color' => 'secondary');
            }
        }


        return view('MasterMaintenance.menu')->with('col_num', $col_num)->with('arrLineDisplayLine1', $arrLineDisplayLine1)->with('menuName', $menuName)
            ->with('arrLineDisplayLine2', $arrLineDisplayLine2)->with('arrLineDisplayLine3', $arrLineDisplayLine3);
    }

    public function transactionMenu()
    {

        $menuName = 'Transaction Report';
        $plan_id = $this->getPlan();
        // $submenu = array (
        //     array('title'=>'Print Job Sheet', 'route'=> route('jobsheet'), 'icon'=>'icon-command2'),
        // );
        $arrLineDisplayLine2 = [];
        $arrLineDisplayLine3 = [];

        if ($plan_id == 1 || $plan_id == 4) {
            $arrLineDisplayLine1 = array(
                array('title' => 'Print Pick List', 'route' => route('picklist'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            if (config('icapt.enable_grn')){
                $arrLineDisplayLine2 = array(
                    array('title' => 'Print GRN', 'route' => route('grn'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                );
            }

        } else {
            $arrLineDisplayLine1 = array(
                array('title' => 'Print Job Sheet', 'route' => route('jobsheet'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                array('title' => 'Preassigned Lot Labels', 'route' => route('preassignedlotlabels'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            $arrLineDisplayLine2 = array(
                array('title' => 'Print Pick List', 'route' => route('picklist'), 'icon' => 'icon-command2', 'color' => 'secondary'),
            );
            if (config('icapt.enable_grn')){
                $arrLineDisplayLine3 = array(
                    array('title' => 'Print GRN', 'route' => route('grn'), 'icon' => 'icon-command2', 'color' => 'secondary'),
                );
            }
        }

        // $arrLineDisplayLine3 = [];
        $col_num = 4;

        return view('MasterMaintenance.menu')->with('col_num', $col_num)->with('arrLineDisplayLine1', $arrLineDisplayLine1)->with('menuName', $menuName)
            ->with('arrLineDisplayLine2', $arrLineDisplayLine2)->with('arrLineDisplayLine3', $arrLineDisplayLine3);
    }
}
