@extends('layout.mobile.app')
@section('content')
@section('title', __('Transfer Order Receipt'))
<style>
    .card {
        box-shadow: 0px 0px 0px transparent;
        border: 1px solid transparent;
    }

    div.col-xs-2.col-md-1.col-lg-1,
    div.col-xs-2.col-md-1.col-lg-2 {
        margin-top: 5px;
    }
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="processTOReceiptform" name="processTOReceiptform"
            action="{{ route('processTransferReceive') }}" method="post">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input', ['type' => 'inventory'])

                @if (isset($batch_id))
                    <input type="hidden" name="batch_id" id="batch_id" value="{{ $batch_id }}" />
                @endif
                <input type="hidden" value="to" name="trans_type" id="trans_type">

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="to_num">{{ __('mobile.label.to_num') }} </label>
                    <div class="col-xs-5">
                        <div class="input-group">
                            <input readonly type="text" name="trn_num" id="trn_num"
                                class="form-control border-primary" value="{{ $to_details->trn_num }}"
                                placeholder="{{ __('mobile.placeholder.to_num') }}">
                        </div>
                    </div>
                    <div class="col-xs-2" style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input readonly type="text" name="trn_line" id="trn_line"
                            class="form-control border-primary" value="{{ $to_details->trn_line }}"
                            placeholder="{{ __('mobile.label.trn_line') }}">
                    </div>
                </div>
                <input readonly type="hidden" name="whse_num" id="whse_num" class="form-control border-primary"
                    value="{{ $to_details->to_whse }}" placeholder="{{ __('mobile.placeholder.whse_num') }}">
                {{-- <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="whse_num">{{ __('mobile.label.whse_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input readonly type="text" name="whse_num"  id="whse_num"  class="form-control border-primary" value="{{$to_details->to_whse}}" placeholder="{{ __('mobile.placeholder.whse_num') }}">
                            </div>
                        </div>
                    </div> --}}

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="item_num">{{ __('mobile.label.item_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input readonly type="text" name="item_num" id="item_num"
                                class="form-control border-primary" value="{{ $to_details->item_num }}"
                                placeholder="{{ __('mobile.placeholder.item_num') }}">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="item_desc"></label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <textarea readonly type="text" id="description" class="form-control border-primary">{{ $to_details->item->item_desc ?? null }}</textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="from_whse">{{ __('mobile.label.from_whse') }}</label>
                    <div class="col-xs-3 col-md-3 col-lg-3"
                        style="text-align:center;margin-left:1em;padding-left: 0px;padding-right: 6px;">
                        <div class="div-fromwhse input-group">
                            <input readonly type="text" name="from_whse" id="from_whse"
                                class="form-control border-primary" value="{{ $to_details->from_whse }}">
                        </div>
                    </div>
                    <div class="col-xs-1 col-md-2 col-lg-1"
                        style="text-align:center; margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <label for="to_whse">&nbsp;{{ __('mobile.label.to') }}</label>
                    </div>
                    <div class="col-xs-3 col-md-3 col-lg-3"
                        style="text-align:center;margin-left: -0.8em;padding-left: -0.8px;padding-right: 6px;">
                        <div class="div-towhse input-group">
                            <input readonly type="text" name="to_whse" id="to_whse"
                                class="form-control border-primary" value="{{ $to_details->to_whse }}">
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_shipped">{{ __('mobile.label.qty_shipped') }}</label>
                    <div class="col-xs-7 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input readonly type="text" name="qty_shipped" id="qty_shipped"
                                class="form-control border-primary"
                                value="{{ numberFormatPrecision(@$sub_sublines_details->qty_shipped, $unit_quantity_format) }}"
                                style="text-align:right" placeholder="{{ __('mobile.placeholder.qty_shipped') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input readonly type="text" id="base_uom" name="base_uom" value="{{ @$to_details->uom }}"
                            class="form-control border-primary" placeholder="Unit">
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_received">{{ __('mobile.label.qty_received') }}</label>
                    <div class="col-xs-7 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input readonly type="text" name="qty_received" id="qty_received"
                                class="form-control border-primary"
                                value="{{ numberFormatPrecision(@$sub_sublines_details->qty_received, $unit_quantity_format) }}"
                                style="text-align:right" placeholder="{{ __('mobile.placeholder.qty_received') }}">
                            <input type="hidden" id="qty_receivable"
                                value="{{ @$sub_sublines_details->qty_receivable }}">
                            <input type="hidden" id="qty_receivable_conv"
                                value="{{ @$sub_sublines_details->qty_receivable ?? 0 }}">


                                 <input type="hidden" id="qty_available_receipt"
                                value="{{ @$sub_sublines_details->qty_receivable }}">


                               
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input readonly type="text" value="{{ @$to_details->uom }}"
                            class="form-control border-primary" placeholder="Unit">
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_loss">{{ __('admin.label.qty_loss') }}</label>
                    <div class="col-xs-7 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input readonly type="text" name="qty_lost" id="qty_lost"
                                class="form-control border-primary"
                                value="{{ numberFormatPrecision(@$sub_sublines_details->qty_loss, $unit_quantity_format) }}"
                                style="text-align:right" placeholder="{{ __('admin.placeholder.qty_loss') }}">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2"
                        style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input readonly type="text" id="base_uom" name="base_uom"
                            value="{{ @$to_details->uom }}" class="form-control border-primary" placeholder="Unit">
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="loc_num">{{ __('mobile.label.loc_num') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="loc_num" id="loc_num" value="{{ old('loc_num') }}"
                                class="form-control border-primary" data-value="{{ @$sublines_details->loc_num }}"
                                onchange="clickSelf(this.id)" placeholder="{{ __('mobile.placeholder.loc_num') }}"
                                maxlength="30">
                            <span id="locnumnotexist"></span>
                            {{-- <span id="checkLoc"></span> --}}
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="{{ __('mobile.list.locations') }}"
                            onClick="selection('/getLocNoPicking','whse_num,loc_num','loc_num','loc_num');modalheader(this.id, this.name);"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                @if ($to_details->item->lot_tracked == 1)
                    <div class="form-group row" id="lotrow">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                            for="lot_num">{{ __('mobile.label.lot_num') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" onchange="clickSelf(this.id)" value="{{ old('lot_num') }}"
                                    data-value="{{ @$sublines_details->trn_lot }}" name="lot_num" id="lot_num"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.lot_num') }}" required maxlength="50">
                            </div>
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{ __('mobile.list.lots') }}"
                                onClick="selectionTONum('/getLotToUnitNum','item_num,trn_num,trn_line','trn_lot','lot_num');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                            {{-- <button type="button" name="Lot" id="lotbtn"
                                onclick="selectionNull('/getLotpreassign','whse_num,item_num,trans_type,trn_num,trn_line','lot_num','lot_num');modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button> --}}
                        </div>
                    </div>

                    {{-- <div class="form-group row">
                        <label
                            class="col-xs-3 col-md-2 col-lg-3 label-control @if ($to_details->item->expiry_tracked == 1) required @endif"
                            for="expiry_date">{{ __('mobile.label.expiry_date') }}</label>
                        <div class="col-xs-7 col-md-8 col-lg-7">
                            <div class="input-group">
                                <input type="text" @if ($to_details->item->expiry_tracked == 1) required @endif
                                    name="expiry_date" value="{{ old('expiry_date') }}" id="expiry_date"
                                    class="not_autop form-control border-primary min-today-date"
                                    placeholder="{{ __('mobile.label.expiry_date') }}" readonly>

                              <?php if (isset($getLot->expiry_date)) {
                                  echo 'readonly';
                              } ?> name="expiry_date" id="expiry_date" class="form-control border-primary min-today-date" placeholder="{{ __('mobile.label.expiry_date') }}">
                            </div>
                            <span id="expiry_date_info"></span>
                        </div>
                    </div> --}}
                @endif

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="qty">{{ __('mobile.label.qty_to_receive') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="text" inputmode="numeric" style="text-align:right" name="qty"
                                id="qty" value="{{ old('qty') }}"
                                class="form-control border-primary number-format"
                                placeholder="{{ __('mobile.placeholder.qty_to_receive') }}" required>
                            <input type="hidden" id="qty_conv" value="0">
                        </div>
                    </div>
                    @if ($sap_trans_order_integration == 1)
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input type="text" readonly name="uom" id="uom"
                                value="{{ old('uom', $to_details->uom) }}" class="form-control border-primary"
                                placeholder="Unit">
                        </div>
                    @else
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input type="text" name="uom" id="uom" value="{{ $to_details->uom }}"
                                value="{{ old('uom') }}" class="form-control border-primary" placeholder="Unit">
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                            <button type="button" readonly name="{{ __('mobile.list.uoms') }}"
                                onClick="selection('/getTOUOMConv','item_num,trn_num,trn_line','uom','uom');
                            modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                        for="qty_loss">{{ __('mobile.label.qty_loss') }}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input inputmode="numeric" type="text" style="text-align:right"
                                value="{{ old('qty_loss') }}" name="qty_loss" id="qty_loss"
                                class="form-control border-primary number-format"
                                placeholder="{{ __('mobile.placeholder.qty_loss') }}">
                            <input type="hidden" id="qty_loss_conv" value="0">
                        </div>
                    </div>
                    @if ($sap_trans_order_integration == 1)
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input readonly type="text" name="uom_loss" id="uom_loss"
                                value="{{ old('uom_loss', $to_details->uom) }}" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.uom') }}">
                        </div>
                    @else
                        <div class="col-xs-2 col-md-2 col-lg-2"
                            style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                            <input type="text" name="uom_loss" id="uom_loss"
                                value="{{ old('uom_loss', $to_details->uom) }}" class="form-control border-primary"
                                placeholder="{{ __('mobile.placeholder.uom') }}">
                        </div>
                        <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                            <button type="button" readonly name="{{ __('mobile.list.uoms') }}"
                                onClick="selection('/getTOUOMConv','item_num,trn_num,trn_line','uom','uom_loss');
                            modalheader(this.id, this.name);"
                                class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                                data-target="#myModal"><i class="icon-search"></i></button>
                        </div>
                    @endif

                </div>

                @if ($sap_trans_order_integration == 1)
                    {{-- Document num --}}
                    <div class="form-group row">
                        <label class="col-xs-3 col-md-2 col-lg-3 label-control"
                            for="document_num">{{ __('mobile.label.doc') }}</label>
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="document_num" id="document_num" autocomplete="off"
                                    class="form-control border-primary"
                                    placeholder="{{ __('mobile.placeholder.doc') }}"
                                    value="{{ old('document_num') }}" maxlength="30">
                            </div>
                        </div>
                    </div>

                    {{-- Last Receive? --}}
                    <input type="hidden" name="last_receive" value="Yes" id="last_receive">
                    {{-- <div class="form-group row">
                            <label class="col-xs-3 col-md-2 col-lg-3 label-control" for="last_receive">{{__('mobile.label.last_receive')}}</label>
                            <div class="col-xs-7 col-md-7 col-lg-7">
                                <div class="input-group">
                                    <input type="radio"  name="last_receive" value="Yes" id="last_receive" > {{ __('mobile.option.yes') }} &emsp;&emsp;&emsp;
                                    <input type="radio"  name="last_receive" value="No" id="last_receive_no" checked> {{ __('mobile.option.no') }}
                                </div>
                            </div>
                        </div> --}}
                @endif


                <div class="form-group row" id="reason" style="display:none;">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required"
                        for="reason_code">{{ __('mobile.label.reason_code') }}</label>
                    <div class="col-xs-7 col-md-8 col-lg-7">
                        <div class="input-group">
                            <input type="text" autocomplete="off" value="{{ old('reason_code') }}"
                                name="reason_code" id="reason_code" class="form-control border-primary"
                                placeholder="Reason">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" id="getRequest5" name="{{ __('mobile.list.reasons') }}"
                            onClick="selection('/getReasonCode/TOLoss', 'reason_code', 'reason_num', 'reason_code');modalheader(this.id, '{{ __('admin.menu.reason_codes') }}');"
                            class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal"
                            data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div><br>

                <div class="form-actions center">
                    <button type="button" action="{{ route('TOReceipt') }}" class="btn btn-warning mr-1"
                        onclick="window.history.go(-1); return false;">
                        <i class="icon-cross2"></i> {{ __('mobile.button.cancel') }}
                    </button>
                    <button type="next" id="process" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{ __('mobile.button.to_receipt_process') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
</div>

<style>
    .numberalign {
        text-align: center:right;
    }

    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>

@include('util.validate_uom')
<script>
    jQuery(function($) {
        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';
        $.validator.addMethod('minStrict', function(value, el, param) {
            return value > param;
        });
        $.validator.addMethod('exceed', function(value, el, param) {
            return param > 0;
        });
        $.validator.addMethod('qty_loss_validation', function(value, el, param) {
            var qty_receivable = parseFloat($("#qty_receivable").val().replace(/,/g, "") || 0);
            var qty_conv = parseFloat($("#qty_conv").val().replace(/,/g, "") || 0);
            var qty_loss_conv = parseFloat($("#qty_loss_conv").val().replace(/,/g, "") || 0);
            var balance = qty_receivable - qty_conv;
            var balance_final = Math.round(balance * 100) / 100;


            if (qty_loss_conv > balance_final) {

                return false;
            } else {

                return true;
            }
        });

        var errorMessage = "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.loc_num')]) }}";

         var errorMessageAvail = "{{ __('error.mobile.qty_notmoreorequalqty_available') }}";
        var errorMessageShiped = "{{ __('error.mobile.qty_notmoreorequalqty_shiped') }}";
 var errorMessageQtyLoss = "{{ __('error.mobile.qty_lossnotmorethan') }}";

        
        // $("#process").click(function(){
        // $(this).attr('disabled',true);
        // })
        $("#processTOReceiptform").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $("#process").attr('disabled', false);
            }
        });
        $("#processTOReceiptform").validate({
            onchange: true,

            rules: {

                /* loc_num:{
                     required: true,
                     remote: {
                         param: {
                             url: "{{ route('LocValidation') }}",
                             type: "post",
                             data: {
                                 _token: $('input[name="_token"]').val(),
                                 whse_num:
                                     function () {
                                         return $("#whse_num").val();
                                     }
                             },
                             dataFilter: function(data){
                                 if(data == 'true'){
                                     var result = 'true';
                                     $.ajax({
                                         url: '{{ route('checkLocNotTransit') }}',
                                         type: "GET",
                                         async: false,
                                         data: {
                                             whse_num: $("#whse_num").val(),
                                             loc_num: $("#loc_num").val(),
                                         },
                                         success: function(data){
                                             if (data == "transit") {
                                                 result = 'false';
                                                 errorMessage = "{{ __('error.mobile.transit_loc') }}";
                                             }
                                             else{
                                                 showNewLoc();
                                             }
                                         }
                                     });

                                     return result;
                                 }
                                 if (disable_create_new_item_location == 0) {
                                     return true;
                                 }
                                 errorMessage = "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.loc_num')]) }}";
                                 return false;
                             }
                         }
                     },
                 },*/


                loc_num: {
                    required: true,
                    remote: {
                        url: "{{ route('checkLocNotTransitpickLocs') }}",
                        type: "GET",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            whse_num: function() {
                                return $("#whse_num").val();
                            }
                        },
                        dataFilter: function(data) {
                            var data = JSON.parse(data);
                            console.log(data);
                            if (data.length > 0) {
                                if (data[0].pick_locs == 1 && data[0].pick_locs !== undefined) {
                                    $("#checkLoc").html("");
                                    $("#locnumnotexist").html('');
                                    //   $("#qty").val("");
                                    //   $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else if (data[0].loc_type == 'T') {
                                    $("#checkLoc").html("");
                                    $("#locnumnotexist").html('');
                                    // $("#qty").val("");
                                    // $("#qty_available").val("0");
                                    errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    // $(".submitloader").attr("disabled", true);
                                    return false;
                                } else {
                                    showNewLoc();
                                    return true;
                                }

                            } else {
                                var disable_create_new_item_location =
                                    '{{ $disable_create_new_item_location }}';

                                if (disable_create_new_item_location == 1) {
                                    // document.getElementById('loc_num-error').style.display = 'none';
                                    $("#checkLoc").html("");
                                    $("#locnumnotexist").html('');
                                    errorMessage = " {{ __('error.mobile.loc_not_exists') }}";
                                    // showNewLoc();
                                    //  $(".submitloader").attr("disabled", true);
                                    return false;

                                } else {
                                    // showNewLoc();
                                    var disable_create_new_item_location =
                                        '{{ $disable_create_new_item_location }}';
                                    if (disable_create_new_item_location == 1) {
                                        $(".submitloader").attr("disabled", true);
                                        $("#locnumnotexist").html(
                                            '<span style="color:red;">{{ __('mobile.message.notallow_new_item_location') }}</span>'
                                        );

                                    }
                                    // Disable create new loc = No = 0
                                    if (disable_create_new_item_location == 0) {

                                        $(".submitloader").attr("disabled", false);
                                        $("#locnumnotexist").html(
                                            '<i class="icon-info"></i> <small>{{ __('mobile.message.new_item_location') }}</small>'
                                        );

                                    }
                                    return true;
                                }

                            }
                        }
                    }
                },

                qty: {
                    required: true,
                    number: true,
                    number_size: true,
                    min_value: 0,
                    max_value: function() {
                        var qty_receivable = parseFloat($("#qty_receivable_conv").val().replace(
                            /,/g, ""));

                        if (isNaN(qty_receivable)) {
                            qty_receivable = 0;
                        }

                        return Math.max(0, qty_receivable);
                    }
                },
                qty_loss: {
                    required: false,
                    number: true,
                   // number_size: true,
                   // min_value: 0,
                    max_value: function()
                    {
                        var qtyreceive =  parseFloat($("#qty_conv").val().replace(
                            /,/g, "") || 0);
                        var qtyloss =  parseFloat($("#qty_loss_conv").val().replace(
                            /,/g, "") || 0);
                        var qty_receivable = parseFloat($("#qty_receivable_conv").val().replace(
                            /,/g, "") || 0);
                        var totalqty = qtyreceive + qtyloss;
                        if(totalqty > qty_receivable)
                        {
                            return qty_receivable;
                        }
                    }
                  /*  qty_loss_validation: function() {
                        var qty_receivable = parseFloat($("#qty_receivable_conv").val().replace(
                            /,/g, "") || 0);
                        return Math.max(0, qty_receivable);
                    }*/
                },
                reason_code: {
                    required: true,
                    remote: {
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            class: "MISC ISSUE"
                        }
                    }
                },
                lot_num: {
                    required: {
                        depends: function(element) {
                            return $("#lot_num").is(":visible");
                        }
                    },
                    remote: {
                        url: "{{ route('ExistingLotValidationTOReceipt') }}",
                        type: "post",
                        data: {
                            _token: $('input[name="_token"]').val(),
                            trn_num: function() {
                                return $("#trn_num").val();
                            },
                            trn_line: function() {
                                return $("#trn_line").val();
                            },
                            item_num: function() {
                                return $("#item_num").val();
                            },
                        }
                    }
                },
                uom: {
                    required: true,
                    uom_validation: [$('#item_num').val(), null, null, null, $('#base_uom').val()]
                    // remote:{
                    //         url: "{{ route('MiscValidation') }}",
                    //         type: "post",
                    //         data: { _token : $('input[name="_token"]').val() }
                    //     }
                },
                uom_loss: {
                    required: true,
                    uom_validation: [$('#item_num').val(), null, null, null, $('#base_uom').val()]
                    // remote:{
                    //         url: "{{ route('MiscValidation') }}",
                    //         type: "post",
                    //         data: { _token : $('input[name="_token"]').val() }
                    //     }
                },
            },
            messages: {
                loc_num: {
                    remote: function() {
                        return errorMessage;
                    }
                    // remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.loc_num')]) }}"
                },
                qty: {
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.placeholder.qty')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}",
                    min_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.placeholder.qty')]) }} {0}",
                    //max_value: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.placeholder.qty')]) }} {0}".

                    max_value: function()
                    {
                         var qty_receivable = parseFloat($("#qty_receivable_conv").val().replace(
                            /,/g, ""));
                        var qtykeyin = parseFloat($("#qty").val().replace(
                            /,/g, ""));

                        if (isNaN(qty_receivable)) {
                            qty_receivable = 0;
                        }
                        if(qtykeyin > qty_receivable)
                        {
                             var maxQty = parseFloat($('#qty_receivable_conv').val().replace(/,/g, ""));
                             return errorMessageShiped.replace(':resource_qty_available', maxQty); 
                        }

                        //return Math.max(0, qty_receivable);
                    }
                },
                qty_loss: {
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.label.qty')]) }}",
                    number_size: "{{ __('error.mobile.max_characters') }}",
                    //min_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.label.qty')]) }} {0}",
                    max_value: function()
                    {
                         var qtyreceive =  parseFloat($("#qty_conv").val().replace(
                            /,/g, "") || 0);
                        var qtyloss =  parseFloat($("#qty_loss_conv").val().replace(
                            /,/g, "") || 0);
                        var qty_receivable = parseFloat($("#qty_receivable_conv").val().replace(
                            /,/g, "") || 0);
                        var totalqty = qtyreceive + qtyloss;
        console.log(qtyreceive + " >> "+ qtyloss + " >> " + qty_receivable +" >> "+ totalqty);
                        if(totalqty > qty_receivable)
                        {
                            //var maxQty = parseFloat($('#qty_receivable_conv').val().replace(/,/g, ""));
                             return errorMessageQtyLoss; 
                        }
                    }
                    //qty_loss_validation: "{{__('error.mobile.qty_lossnotmorethan')}}",
                    //qty_loss_validation: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.placeholder.qty')]) }} {0}"
                },
                reason_code: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.reason_code')]) }}"
                },
                lot_num: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.lot_num')]) }}"
                },
                uom: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                },
                uom_loss: {
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom')]) }}"
                },
            },
            invalidHandler: function(event, validator) {
                // console.log(validator.numberOfInvalids());
            },
            submitHandler: function(form) {
                // ajaxurl ="{{ route('itemlocv', ['item_num', 'loc_num', 'whse_num']) }}";
                // url = ajaxurl.replace('item_num', $("#item_num").val());
                // url = url.replace('loc_num', $("#loc_num").val());
                // url = url.replace('whse_num', $("#whse_num").val());
                $("#process").attr('disabled', true);
                ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
                url = ajaxurl.replace('loc_num', btoa($("#loc_num").val()));
                $.get(url, function(data) {
                    if (data == 'not exist') {
                        var m = '{{ __('admin.message.surecreate') }}';
                        if ($('#lot_num').val() == "" || $('#lot_num').val() == null) {
                            m = m.replace(':resource', "Item Location");
                        } else {
                            m = m.replace(':resource', "Item Lot Location");
                        }
                        Swal.fire({
                            title: 'Warning',
                            text: m,
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#3085d6',
                            cancelButtonColor: '#d33',
                            confirmButtonText: 'Yes'
                        }).then((result) => {
                            if (result.value) {
                                $(".pageloader").css("display", "block");
                                $(".submitloader").attr("disabled", true);
                                form.submit();
                            } else {
                                $(".submitloader").attr("disabled", false);
                                return false;
                            }
                        });
                    } else {
                        if ($('#lot_num').val()) {
                            ajaxurl =
                                "{{ route('lotitemv', ['lot_num', 'item_num', 'whse_num']) }}";
                            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
                            url = url.replace('item_num', btoa($("#item_num").val()));
                            url = url.replace('whse_num', btoa($("#whse_num").val()));

                            $.get(url, function(data) {
                                if (data != 'exist') {
                                    var m =
                                        '{{ __('admin.message.surecreate') }}';
                                    m = m.replace(':resource',
                                        "{{ __('mobile.placeholder.lot_num') }}"
                                    );
                                    Swal.fire({
                                        title: 'Warning',
                                        text: m,
                                        icon: 'warning',
                                        showCancelButton: true,
                                        confirmButtonColor: '#3085d6',
                                        cancelButtonColor: '#d33',
                                        confirmButtonText: 'Yes'
                                    }).then((result) => {
                                        if (result.value) {
                                            $(".pageloader").css("display",
                                                "block");
                                            $(".submitloader").attr(
                                                "disabled", true);
                                            form.submit();
                                        } else {
                                            $(".submitloader").attr(
                                                "disabled", false);
                                            return false;
                                        }
                                    });
                                } else {
                                    $(".pageloader").css("display", "block");
                                    $(".submitloader").attr("disabled", true);
                                    form.submit();
                                }
                            });
                        } else {
                            $(".pageloader").css("display", "block");
                            $(".submitloader").attr("disabled", true);
                            form.submit();
                        }
                    }
                });
                return false;
            }
        });

        $("input:radio[name='last_receive']").change(function() {
            var _val = $(this).val();

            if (_val == "Yes") {

                Swal.fire({
                    title: 'Warning',
                    icon: 'warning',
                    html: '{!! __('admin.message.confirm_last_receive_to') !!}',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes'
                }).then((result) => {
                    if (result.value) {
                        return false;
                    } else {
                        $("#last_receive_no").prop("checked", true);
                    }
                });
            }
        });


    });

    function showNewLoc() {
        var disable_create_new_item_location = '{{ $disable_create_new_item_location }}';

        $("#loc_info").html("");
        $("#checkLoc").html("");
        $("#locnumnotexist").html('');

        // ajaxurl ="{{ route('itemlocv', ['item_num', 'loc_num', 'whse_num']) }}";
        // url = ajaxurl.replace('item_num', $("#item_num").val());
        // url = url.replace('loc_num', $("#loc_num").val());
        // url = url.replace('whse_num', $("#whse_num").val());
        ajaxurl = "{{ route('checklocmaster', ['loc_num']) }}";
        url = ajaxurl.replace('loc_num', btoa($("#loc_num").val()));
        $.get(url, function(data) {

            if (data == 'not exist') {
                // Disable create new loc = Yes = 1
                if (disable_create_new_item_location == 1) {
                    $(".submitloader").attr("disabled", true);
                    $("#locnumnotexist").html(
                        '<span style="color:red;">{{ __('mobile.message.notallow_new_item_location') }}</span>'
                    );

                }
                // Disable create new loc = No = 0
                if (disable_create_new_item_location == 0) {
                    $(".submitloader").attr("disabled", false);
                    $("#locnumnotexist").html(
                        '<i class="icon-info"></i> <small>{{ __('mobile.message.new_item_location') }}</small>'
                    );

                }

            } else {
                $(".submitloader").attr("disabled", false);
            }

        });
    }

    $(document).ready(function() {
        var unit_quantity_format = '{{ $unit_quantity_format }}';
        $("#item_num").on('change', function() {
            ajaxurl = "{{ route('item.expiry_tracked', 'item_num') }}";
            url = ajaxurl.replace('item_num', btoa($("#item_num").val()));

            $.get(url, function(data) {
                $("#expiry_date").prop('required', false);
                $("label[for='expiry_date']").removeClass('required');

                if (data['expiry_tracked'] == 1) {
                    // Set expiry_date's required to true
                    $("#expiry_date").prop('required', true);
                    $("label[for='expiry_date']").addClass('required');
                }
                if (data['default_shelf_life']) {
                    // Predefined expiry_date based on default_shelf_life. User can change it if they want to.
                    $('.min-today-date').datepicker('setDate', '+' + data[
                        'default_shelf_life'] + 'd');

                    // Default shelf life is put back when change to new lot that has no due date
                    $("#expiry_date").attr('data-date-default-shelf-life', $("#expiry_date")
                        .val());
                }
            });
        });

        $("#item_num").trigger('change');

        $("#expiry_date").on('change', function() {
            console.log($("#expiry_date").val());
            if ($("#expiry_date").val() != "") {
                // $("#expiry_date").prop('readonly',true);

                var ExpiryDate = $("#expiry_date").val();
                console.log(ExpiryDate);
                //   return false;
                var dateFormat = '{{ App\SiteSetting::first()->getJsInputDateFormat() }}';
                // console.log(dateFormat);

                validatedateformat(ExpiryDate, dateFormat, 'expiry_date');
            } else {
                $("#expiry_date").addClass('date-picker-only');
                $("#expiry_date").prop('readonly', false);
            }
        });

        $("#uom").on("change", function() {
            // if ($(this).val()) {
            //     displayConvQty('/displayQuantityConverted','base_uom,item_num,qty_receivable,uom','qty_receivable_conv');
            //     displayConvQty('/displayQuantityConverted','uom,item_num,qty,base_uom','qty_conv');
            // }

            if ($("#uom").val() != "") {
                if ($("#item_num").val() == "") {
                    // $("#qty_receivable_conv").val("");
                } else {
                    let validate = validateConvUOM($('#item_num').val(), null, null, null, $(
                        '#base_uom').val(), $("#uom").val());

                    validate.then(function(resp) {
                            // true
                        },
                        function(err) {
                            // false
                            $("#qty").val('');
                            $("#uom").val($("#base_uom").val());
                        }).finally(function() {
                             $("#qty").val('');
                              $("#qty-error").hide();
             displayConvQty('/displayQuantityConverted',
                            'base_uom,item_num,qty_receivable,uom', 'qty_available_receipt');
                   
            var qty_shipped  = parseFloat($("#qty_shipped").val().replace(/,/g, "") || 0);
            var qty_received = parseFloat($("#qty_received").val().replace(/,/g, "") || 0);
            var balance = qty_shipped - qty_received;



           $("#qty_receivable").val(balance);
                  displayConvQty('/displayQuantityConverted',
                            'base_uom,item_num,qty_receivable,uom', 'qty_receivable_conv');
                    });




                  
                }
            }
            // else{
            //     $("#qty_receivable").val('');
            // }


        });

        $("#uom_loss").on("change", function() {
            if ($(this).val()) {
                let validate = validateConvUOM($('#item_num').val(), null, null, null, $('#base_uom')
                    .val(), $("#uom_loss").val());

                validate.then(function(resp) {
                        // true
                    },
                    function(err) {
                        // false
                        $("#qty_loss").val('');
                        $("#uom_loss").val($("#base_uom").val());
                    }).finally(function() {


                         $("#qty_loss").val('');
                              $("#qty_loss-error").hide();
                                    $("#qty_loss_conv").val('');

                                displayConvQty('/displayQuantityConverted',
                            'base_uom,item_num,qty_receivable,uom', 'qty_available_receipt');
                   
            var qty_shipped  = parseFloat($("#qty_shipped").val().replace(/,/g, "") || 0);
            var qty_received = parseFloat($("#qty_received").val().replace(/,/g, "") || 0);
            var balance = qty_shipped - qty_received;
             $("#qty_receivable").val(balance);
             
                    displayConvQty('/displayQuantityConverted',
                        'uom_loss,item_num,qty_loss,base_uom', 'qty_loss_conv');
                });
            }
        });

        $("#qty").on("change", function() {

            if ($(this).val()) {
                displayConvQty('/displayQuantityConverted', 'uom,item_num,qty,base_uom', 'qty_conv');
            }
        });

        $("#qty_loss").on("input change", function() {

            if ($(this).val()) {
                displayConvQty('/displayQuantityConverted', 'uom_loss,item_num,qty_loss,base_uom',
                    'qty_loss_conv');
            } else {
                $("#qty_loss_conv").val('');
            }
        });
        $("#lot_num").on("change", function() {
            if ($(this).val()) {
                displayConvQtyShiped('/getLotToUnitNum', 'item_num,trn_num,trn_line', 'qty_shipped',
                    'lot_num');
            } else {
                $("#item_num").trigger('change');
            }
        });




        /* $("#loc_num").on("change", function(){
             $("#loc_info").html("");
            $("#checkLoc").html("");
            $("#locnumnotexist").html('');


            if ($("#loc_num").val() != null) {

                // Send error if manually type object that is transit location
                $.ajax({
                    url: '{{ route('checkLocNotTransitpickLocs') }}',
                    type: "GET",
                    data: {
                        whse_num: $("#whse_num").val(),
                        loc_num: $("#loc_num").val(),
                    },
                    success: function(data){
                       // alert(data[0].pick_locs);
                       // var data = JSON.parse(data);

                            if(data.length > 0){

                                if(data[0].pick_locs==1)
                                {

                                  //  $("#loc_info").html("");
                                   // $("#locnumnotexist").html('');
                                  //  $("#qty").val("");
                                  //  $("#qty_available").val("0");
                                    //errorMessage = "{{ __('error.mobile.validate_picking') }}";
                                    document.getElementById('loc_num-error').style.display = 'none';
                                    $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.validate_picking') }}</span>');
                                     $(".submitloader").attr("disabled", true);
                                    //return false;
                                }
                                else if(data[0].loc_type=='T')
                                {
                                    //$("#loc_info").html("");
                                    //$("#locnumnotexist").html('');
                                   // $("#qty").val("");
                                   // $("#qty_available").val("0");
                                    //errorMessage = "{{ __('error.mobile.validate_transit') }}";
                                    document.getElementById('loc_num-error').style.display = 'none';
                                    $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.validate_transit') }}</span>');
                                     $(".submitloader").attr("disabled", true);
                                    //return false;
                                }
                                else{
                                    showNewLoc();
                                    $("#checkLoc").html("");
                                    $(".submitloader").attr("disabled", false);
                                }

                        // if (data == "transit") {
                        //     $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.transit_loc') }}</span>');
                        //     $(".submitloader").attr("disabled", true);
                        // }
                        // else {
                        //     showNewLoc();
                        //     $("#checkLoc").html("");
                        //     $(".submitloader").attr("disabled", false);
                        // }
                            }
                            else{
                                document.getElementById('loc_num-error').style.display = 'none';
                                  $("#checkLoc").html('<span style="color:red;"> {{ __('error.mobile.loc_not_exists') }}</span>');
                                     $(".submitloader").attr("disabled", true);


                            }
                    }
                });
            }

              if ($("#loc_num").val() == "") {
                $("#loc_info").html("");
                $("#checkLoc").html("");
                $("#locnumnotexist").html('');
            }
        });
*/

































        $("#qty_loss").on('keypress change', function() {
            var qty = parseFloat($("#qty_loss").val().replace(/,/g, ""));
            if (qty > 0) {
                $('#reason').show();
            } else {
                $('#reason').hide();
            }
        });
    });
</script>

@include('errors.maxchar')
@include('util.selection')
@include('util.datepicker')
@endsection()
