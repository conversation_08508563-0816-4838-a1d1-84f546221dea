@extends('layout.mobile.app')
@section('title', __('mobile.nav.job_return') . ' - Catch Weight')
@section('head')
    <style>
        .card {
            box-shadow: 0px 0px 0px transparent;
            border: 1px solid transparent;
        }

        div.col-xs-2.col-md-1.col-lg-1,
        div.col-xs-2.col-md-1.col-lg-2 {
            margin-top: 5px;
        }
    </style>
@endsection
@section('content')

<div class="card-body collapse in">
    <div class="card-block-custom">
        <x-catch-weight-form
            :batch-id="$batch_id"
            :whsenum="$job_order->whse_num"
            :itemnum="$item->item_num"
            :itemdesc="$item->item_desc"
            :refnum="$job_order->job_num"
            :refline="$job_order->suffix"
            :qtybalance="($job_route->qty_completed - $job_route->qty_returned) ?? 0"
            :qtybalanceuom="$job_order->uom"
            :submiturl="route('JobReturnCWProcess')"
            :catch-weight-tolerance="$item->catch_weight_tolerance"
            :disable-create-new-item-loc="$disable_create_new_item_location"
            :allow-over="$allow_over_return"
            :line-uom="$job_order->uom"
            :print-label="$printLabel"
            trans-type="JobReturn"
            transtype="job"
            :incoming="false">
        </x-catch-weight-form>
    </div>
</div>

@endsection
