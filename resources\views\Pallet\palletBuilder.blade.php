@extends('layout.mobile.app')
@section('content')
@section('title', __('Pallet Builder'))
<style>
    .card{
        box-shadow:0px 0px 0px transparent;
        border: 1px solid transparent;}
    div.col-xs-2.col-md-1.col-lg-1, div.col-xs-2.col-md-1.col-lg-2{
        margin-top: 5px;}
</style>
<div class="card-body collapse in">
    <div class="card-block">
        <form class="form" autocomplete="off" id="postpalletbuilder" name="postpalletbuilder" action="{{route('PalletBuilderProcess')}}"  method="POST">
            <input type="hidden" name="batch_id" value="{{ $batch_id }}">
            @csrf
            <div class="form-body">
                @include('components.form.scan_input',['type'=>'inventory'])

                <input type="hidden"  name="transtype" id="picklabel" value="{{__('mobile.nav.pallet_builder')}}">
                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 label-control required ">{{__('mobile.label.lpn')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="lpn_num" id="lpn_num" class="form-control border-primary" value="" placeholder="{{__('mobile.placeholder.lpn')}}" required>
                            <span id="checkLpnNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button"  tabindex="-1"  name="{{__('mobile.list.lpn')}}" onClick="selection('/getLPNListRestric','whse_num,lpn_num','lpn_num','lpn_num');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control" for="whse_num">{{__('mobile.label.whse_num')}}</label>
                    {{-- @if($tparm == 1)
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" readonly name="whse_num" id="whse_num" value="" class="form-control border-primary" placeholder="{{__('admin.label.whse_num')}}">
                            </div>
                        </div> --}}
                        {{-- <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                            <button type="button" name="{{__('admin.label.whse_num')}}" onClick="selection('/getWhse','whse_num','whse_num','whse_num');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                        </div> --}}
                    {{-- @else --}}
                        <div class="col-xs-7 col-md-7 col-lg-7">
                            <div class="input-group">
                                <input type="text" name="whse_num" id="whse_num" value="" class="form-control border-primary" readonly>
                            </div>
                        </div>
                    {{-- @endif --}}

                </div>

                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control" for="location">{{__('mobile.label.loc_num')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="loc_num" id="loc_num" value="" class="form-control border-primary" readonly>
                        </div>
                    </div>
                </div>


                <div class="form-group row" id="item_num_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" for="item_num">{{__('mobile.label.item_num')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="item_num" id="item_num" value="" class="form-control border-primary" placeholder="{{ __('mobile.label.item_num') }}" required>
                            <span id="checkItemNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" name="{{__('mobile.list.item_num')}}"  onClick="selection('/getItemByWhse','whse_num','item_num','item_num');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="item_desc_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control" for="item_desc"></label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <textarea id="item_desc" tabindex="5" class="form-control border-primary" rows="2" name="item_desc" readonly></textarea>
                        </div>
                    </div>
                </div>






                {{-- <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" for="loc_num">{{__('mobile.label.from_loc')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="loc_num" id="loc_num" value="" class="form-control border-primary" placeholder="{{ __('mobile.label.from_loc') }}" required>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" name="From Loc" id="getRequest"  onClick="selection('/getItemLoc','whse_num,item_num,loc_num','loc_num','loc_num');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div> --}}




                <div class="form-group row">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" for="from_loc">{{__('mobile.label.from_loc')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text"  maxlength="30"  name="from_loc" id="from_loc" value="" class="form-control border-primary" placeholder="{{ __('mobile.label.from_loc') }}" required>
                            <span id="checkLoc"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" tabindex="-1" name="{{__('mobile.list.locations')}}" id="getRequest"  onClick="selection('/getItemLocLotPallet','whse_num,item_num,lot_num','loc_num','from_loc');modalheader(this.id,this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="lot_num_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" for="lot_num">{{__('mobile.label.lot_num')}}</label>
                    <div class="col-xs-7 col-md-7 col-lg-7">
                        <div class="input-group">
                            <input type="text" name="lot_num" id="lot_num" value="" class="form-control border-primary" placeholder="{{ __('mobile.label.lot_num') }}" maxlength="50" required>
                            <span id="checkLotNo"></span>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-2" style="margin-left:-0.8em; padding:0px;">
                        <button type="button" name="Lot" id="lotbtn" onclick="selectionMultiLineInput('/getLotLocExpiry','whse_num,from_loc,item_num,sortField,sortBy','lot_num','from_loc,lot_num');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-group row" id="qty_available_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control" for="qty_available">{{__('mobile.label.qty_available')}}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="text" name="qty_available" id="qty_available" class="form-control border-primary" value="" placeholder="{{ __('mobile.label.qty_available') }}" readonly/>
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2" style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" name="base_uom" id="base_uom" class="form-control border-primary" value="" placeholder="{{ __('mobile.label.uom') }}" readonly/>
                    </div>
                </div>


                <div class="form-group row" id="qty_to_put_field">
                    <label class="col-xs-3 col-md-2 col-lg-3 pr-0 label-control required" for="qty_to_put">{{__('mobile.label.qty_to_put')}}</label>
                    <div class="col-xs-5 col-md-6 col-lg-5">
                        <div class="input-group">
                            <input type="text" inputmode="numeric" name="qty_input" id="qty_input" class="form-control border-primary number-format" value="" placeholder="{{ __('mobile.label.qty_to_put') }}" required/>
                            <input type="hidden"  name="max_qty_input" id="max_qty_input" value="">
                            <input type="hidden"  name="conv_factor" id="conv_factor" value="1">
                        </div>
                    </div>
                    <div class="col-xs-2 col-md-2 col-lg-2" style="margin-left:-0.8em;padding-left: 0.2px;padding-right: 4px;">
                        <input type="text" maxlength="30" required name="uom" id="uom" class="form-control border-primary" value="" placeholder="{{ __('mobile.label.uom') }}" required/>
                    </div>
                    <div class="col-xs-2 col-md-1 col-lg-1" style=" padding:0px;">
                        <button type="button" name="UOMs" onclick="selection('/getItemUOMConv','item_num','uom','uom');modalheader(this.id, this.name);" class="btn-magnify btn-icon btn-magnify-class" data-toggle="modal" data-target="#myModal"><i class="icon-search"></i></button>
                    </div>
                </div>

                <div class="form-actions center">
                    <button type="submit" class="btn btn-primary submitloader">
                        <i class="icon-check2"></i> {{__('mobile.button.process')}}
                    </button>
                </div>
            </div>
        </form>
        @include('errors.maxchar')
    </div>
</div>

<style>
    form .form-group {
        margin-bottom: 0.2rem;
    }
</style>
<script>
    jQuery(function($){
        $.validator.addMethod('minStrict', function (value, el, param) {
            return value > param;
        });
        
        $("#postpalletbuilder").on("invalid-form.validate", function(event, validator) {
            var errors = validator.numberOfInvalids();
            if (errors) {
                $(".submitloader").attr('disabled', false);
            }
        });
        
        $("#postpalletbuilder").validate({
            onchange: true,
            rules:{
                qty_input:{
                    required: true,
                    number: true,
                    number_size: true,
                    min_value: 0,
                    max_value: function(){
                        var qty_receivable = parseFloat($("#max_qty_input").val().replace(/,/g,""));
                        return Math.max(0, qty_receivable);
                    }
                },
                uom:{
                    required: true,
                    remote:{
                        url: "{{ route('MiscValidation') }}",
                        type: "post",
                        data: { _token : $('input[name="_token"]').val() }
                    }
                },
            },
            messages:{
                qty_input:{
                    // number: "{{ __('error.mobile.numbersonly', ['resource' => __('mobile.placeholder.qty') ]) }}",
                    number_size: "{{__('error.mobile.max_characters')}}",
                    min_value: "{{ __('error.mobile.morethan', ['resource' => __('mobile.placeholder.qty') ]) }} {0}",
                    max_value: "{{ __('error.mobile.lessthan', ['resource' => __('mobile.placeholder.qty') ]) }} {0}"
                },
                uom:{
                    remote: "{{ __('error.mobile.notexist', ['resource' => __('mobile.label.uom') ]) }}"
                },
            },

            submitHandler: function(form) {
                $(".submitloader").attr('disabled',true);

                ajaxurl ="{{ route('getPalletDetailsRestric', ['lpn_num', 'whse_num'])}}";
                url = ajaxurl.replace('lpn_num', btoa($("#lpn_num").val()));
                url = url.replace('whse_num', whse_num);

                $.get(url, function(data){
                    if(data == 'not exist') {
                        $("#checkLpnNo").html('<span style="color:red;"> {{__('error.mobile.lpn_notexists')}}</span>');
                        // Alert.notexist('{{__('LPN')}}', $("#lpn_num").val());
                        $("#lpn_num").val('');
                        $(".submitloader").attr('disabled',false);
                        return false;
                    }
                    else if(data == 'not open'){
                        $("#checkLpnNo").html('<span style="color:red;"> {{__('mobile.message.lpn_not_open')}}</span>');
                        // Alert.warning('Pallet status is not Open.');
                        $("#lpn_num").val('');
                        $(".submitloader").attr('disabled',false);
                        return false;
                    }
                    else {
                        var dataWhr = data['whse_num'];
                        $("#whse_num").val(dataWhr);
                        $("#loc_num").val(data['loc_num']);
                    }
                })

                ajaxurl ="{{ route('getItemList', 'item_num')}}";
                url = ajaxurl.replace('item_num', btoa($("#item_num").val()));

                $.get(url, function(data){
                    if(data == 'false') {
                        $("#checkItemNo").html('<span style="color:red;"> {{__('error.mobile.itemnotexist')}}</span>');
                        // Alert.notexist('{{__('Item')}}', $("#item_num").val());
                        $("#item_num").val('');
                        $("#item_desc").val('');
                        $("#qty_contained").val('');
                        $("#base_uom").val('');
                        $("#from_loc").val('');
                        $("#uom").val('');
                        $("#qty_input").val('');

                        $(".submitloader").attr('disabled',false);
                        return false;
                    }
                    // else {
                    //     // check if the pallet is single item or not
                    //     ajaxurl ="{{ route('getPalletSingleItem', ['lpn_num','item_num'])}}";
                    //     url = ajaxurl.replace('lpn_num', $("#lpn_num").val());
                    //     url = url.replace('item_num', $("#item_num").val());
                    //     // url = ajaxurl.replace('lpn_num', lpn_num);
                    //     // url = url.replace('item_num', item_num);

                    //     $.get(url, function(singleItem){
                    //         if(singleItem == 'false') {
                    //             Alert.warning('{{__('LPN is for single item only')}}');
                    //             $("#item_num").val('');
                    //             $("#item_desc").val('');
                    //             $("#qty_contained").val('');
                    //             $("#base_uom").val('');
                    //             $("#from_loc").val('');
                    //             $("#uom").val('');
                    //             $("#qty_input").val('');

                    //         }
                    //         else {
                    //             display('/displayItemDesc','item_num','item_desc');
                    //             // if(data['lot_tracked'] == 1){
                    //             //     $("#lot_num_field").attr('hidden',false);
                    //             // }
                    //         }
                    //     })
                    // }
                });

                ajaxurl ="{{ route('lotexistpallet', ['item_num', 'whse_num', 'loc_num', 'lot_num'])}}";
                url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
                url = url.replace('whse_num', btoa($("#whse_num").val()));
                url = url.replace('loc_num', btoa($("#loc_num").val()));
                url = url.replace('lot_num', btoa($("#lot_num").val()));

                $.get(url, function(data){
                    if(data['result'] == 'false') {
                        console.log($("#item_num").val() +'..'+ $("#whse_num").val() +'..'+ loc_num +'..'+ $("#lot_num").val());
                        $("#checkLotNo").html('<span style="color:red;"> {{__('error.mobile.lot_notexists')}}</span>');
                        // Alert.notexist('{{__('mobile.label.lot_num')}}', $("#lot_num").val());
                        $("#qty_available").val('');
                        $("#base_uom").val('');
                        $("#uom").val('');
                        $("#max_qty_input").val('');
                        $("#lot_num").val('');

                        $(".submitloader").attr('disabled',false);
                        return false;
                    }
                    else{
                        $("#qty_available").val(data['qty_available']);
                        $("#base_uom").val(data['uom']);
                        $("#uom").val(data['uom']);
                        $("#max_qty_input").val(data['qty_available']);
                    }
                })


                var maxQty= $("#max_qty_input").val();
                var qty = parseFloat($("#qty_input").val());

                if(maxQty!==""){
                    if (qty > maxQty) {
                        Alert.warning('Qty to Put cannot be more than '+maxQty);
                        $(".submitloader").attr('disabled',false);
                        return false;
                    }
                    else{
                        $(".pageloader").css("display", "block");
                        $(".submitloader").attr("disabled", true);
                        setTimeout( function () {
                            form.submit();
                        }, 300);
                    }
                }
            }
        });
    });

    $(document).ready(function(){
        $("#lot_num_field").attr('hidden',true);
        $("#whse_num").on("change", function(){
            if($("#whse_num").val() != ''){
                ajaxurl ="{{ route('validateWhse', 'whse_num')}}";
                url = ajaxurl.replace('whse_num', btoa($("#whse_num").val()));

                $.get(url, function(data){
                    if(data == 'not exist') {
                        $("#whse_num").val('');
                        $("#loc_num").val('');
                        $("#lpn_num").val('');
                        Alert.notexist('{{__('admin.label.whse_num')}}', $("#whse_num").val());
                    }
                    else{
                        // $("#loc_num").val('');
                        $("#lpn_num").val('');
                    }
                });
            }
        });

        $("#lot_num").on("change", function(){
            var loc_num = 'null';
            if($("#from_loc").val() != ""){
                loc_num = $("#from_loc").val();
            }
            if($("#lot_num").val() != ''){
                ajaxurl ="{{ route('lotexistpallet', ['item_num', 'whse_num', 'loc_num', 'lot_num'])}}";
                url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
                url = url.replace('whse_num', btoa($("#whse_num").val()));
                url = url.replace('loc_num', btoa($("#from_loc").val()));
                url = url.replace('lot_num', btoa($("#lot_num").val()));
                $.get(url, function(data){
                    if(data['result'] == 'false') {
                        $("#checkLotNo").html('<span style="color:red;"> {{__('error.mobile.lot_notexists')}}</span>');
                        // Alert.notexist('{{__('mobile.label.lot_num')}}', $("#lot_num").val());
                        $("#qty_available").val('');
                        $("#base_uom").val('');
                        $("#uom").val('');
                        $("#max_qty_input").val('');
                        $("#lot_num").val('');
                    }
                    else{
                        $("#qty_available").val(data['qty_available']);
                        $("#base_uom").val(data['uom']);
                        $("#uom").val(data['uom']);
                        $("#max_qty_input").val(data['qty_available']);
                        $("#checkLotNo").html('');
                    }
                });
            }
        });

        // $("#qty_input").on("keyup", function(){
        //     calculateQtyLimit("null", $("#qty_available").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), $("#lot_num").val(), "", "", "", "");
        // });

        $("#from_loc").on("change", function(){

            if($("#from_loc").val() != ''){
                // Send error if manually type object that is transit location
                $.ajax({
                    url: '{{route('checkLocNotTransitpickLocs')}}',
                    type: "GET",
                    data: {
                        whse_num: $("#whse_num").val(),
                        loc_num: $("#from_loc").val(),
                    },
                    success: function(data){
                        $("#checkLoc").html("");
                    // console.log(data);
                        if(data.length > 0){
                            if(data[0].pick_locs==1 && data[0].pick_locs!==undefined)
                            {
                                $("#from_loc").val('');
                                $("#lpn_num").val('');
                                $("#item_count").val('');
                                $("#uom").val('');
                                $("#qty_available").val('');
                                $("#base_uom").val('');
                                $("#qty_input").val('');

                                // $("#checkLoc").html('<small style="color:red;"> {{__('error.mobile.no_picking_location')}}</small>');

                                 Alert.warning('{{__('error.mobile.no_picking_location')}}');
                                //  $(".submitloader").attr("disabled", true);
                            }
                            else if(data[0].loc_type=='T')
                            {
                                $("#from_loc").val('');
                                $("#lpn_num").val('');
                                $("#item_count").val('');
                                $("#uom").val('');
                                $("#qty_available").val('');
                                $("#base_uom").val('');
                                $("#qty_input").val('');
                                 //$("#checkLoc").html('<small style="color:red;"> {{__('error.mobile.transit_loc')}}</small>');
                                 Alert.warning('{{__('error.mobile.transit_loc')}}');
                                //  $(".submitloader").attr("disabled", true);
                            }
                            else{
                                var lot = $("#lot_num").val();
                               // alert(lot);
                                if(lot!="")
                                {
                                    // getItemLocLotPallet
                                    ajaxurl ="{{ route('getItemLocLotPallet', ['whse_num','item_num','lot_num'])}}";
                                    url = ajaxurl.replace('whse_num', btoa($("#whse_num").val()));
                                    url = url.replace('item_num', btoa($("#item_num").val()));
                                    url = url.replace('lot_num', btoa($("#lot_num").val()));
                                    var frm_loc = $("#from_loc").val();


                                    $.get(url, function(data){
                                        console.log(data);
                                        if(data == 'not exist'){
                                            Alert.notexist('{{__('mobile.label.from_loc')}}');
                                            $("#from_loc").val("");
                                        }
                                        else{
                                            for(i=0;i<data.length;i++){
                                                if(data[i]['loc_num']==frm_loc){
                                                    $("#from_loc").val(data[i]['loc_num']);
                                                    $("#qty_available").val(data[i]['qty_available']);
                                                    $("#base_uom").val(data[i]['uom']);
                                                    $("#uom").val(data[i]['uom']);
                                                    $("#max_qty_input").val(data[i]['qty_available']);
                                                    break;
                                                }
                                                else
                                                {
                                                    //$("#from_loc").val('');
                                                    $("#qty_available").val(0);
                                                    $("#base_uom").val('');
                                                    $("#uom").val('');
                                                    $("#max_qty_input").val('');

                                                }
                                            }
                                        }
                                    });
                                }
                                else{
                                    ajaxurl ="{{ route('getItemLoc', ['whse_num','item_num'])}}";
                                    url = ajaxurl.replace('whse_num', btoa($("#whse_num").val()));
                                    url = url.replace('item_num', btoa($("#item_num").val()));

                                    var frm_loc = $("#from_loc").val();

                                    // alert(url + "Reeve"  );
                                    $.get(url, function(data){
                                        console.log(data);
                                        if(data.length == 0){
                                            Alert.notexist('{{__('mobile.label.from_loc')}}');
                                            $("#from_loc").val("");
                                        }
                                        else{
                                            for(i=0;i<data.length;i++){
                                                if(data[i]['loc_num']==frm_loc){
                                                    $("#from_loc").val(data[i]['loc_num']);
                                                    $("#qty_available").val(data[i]['qty_available']);
                                                    $("#base_uom").val(data[i]['uom']);
                                                    $("#uom").val(data[i]['uom']);
                                                    $("#max_qty_input").val(data[i]['qty_available']);
                                                    break;
                                                }
                                                else
                                                {
                                                    $("#qty_available").val('0');
                                                    $("#base_uom").val('');
                                                    $("#uom").val('');
                                                    $("#max_qty_input").val('');
                                                }
                                            }
                                        }
                                    });
                                }
                            }
                        }
                        else
                        {
                            Alert.notexist('{{__('mobile.label.from_loc')}}');
                            // $("#checkLoc").html('<small style="color:red;"> {{__('error.mobile.loc_not_exists')}}</small>');
                            $("#from_loc").val('');
                            $("#lpn_num").val('');
                            $("#item_count").val('');
                            $("#qty_available").val('');
                            $("#base_uom").val('');
                            $("#qty_input").val('');
                            $("#uom").val('');
                            // showNewLoc();
                            //  $(".submitloader").attr("disabled", false);
                        }
                    }
                });
            }
        });

        $("#uom").on("change", function(){
            ajaxurl ="{{ route('getItemUOMConv', 'item_num')}}";
            url = ajaxurl.replace('item_num', btoa($("#item_num").val()));
            var selectuom = $("#uom").val();
            var uomConv = [];
            if(selectuom!=""){
                $.get(url, function(data){
                    console.log(JSON.stringify(data));
                    data.forEach(element => {
                        uomConv.push(element['uom']);
                    });

                    if(jQuery.inArray(selectuom, uomConv) != -1){
                         $("#qty_input").val('');
                        $("#qty_input-error").hide();
                        calculateQtyLimit("null", $("#qty_available").val(), $("#uom").val(), $("#item_num").val(), $("#whse_num").val(), $("#loc_num").val(), $("#lot_num").val(), "", "", "", "", "");
                    }
                    else{
                        $("#qty_input").val('');
                        $("#uom").val($("#base_uom").val());
                        Alert.notexist('{{__('admin.label.uom')}}', $("#uom").val());
                    }
                });
            }
        });

        $("#lpn_num").on("change", function(){
            if($("#lpn_num").val() != ''){
                var whse_num = $("#whse_num").val();
                ajaxurl ="{{ route('getPalletDetailsRestric', ['lpn_num', 'whse_num'])}}";
                url = ajaxurl.replace('lpn_num', btoa($("#lpn_num").val()));
                url = url.replace('whse_num', whse_num);

                $.get(url, function(data){
                    if(data == 'not exist') {
                        $("#checkLpnNo").html('<span style="color:red;"> {{__('error.mobile.lpn_notexists')}}</span>');
                        // Alert.notexist('{{__('LPN')}}', $("#lpn_num").val());
                        $("#lpn_num").val('');
                    }
                    else if(data == 'not open'){
                        $("#checkLpnNo").html('<span style="color:red;"> {{__('mobile.message.lpn_not_open')}}</span>');
                        // Alert.warning('Pallet status is not Open.');
                        $("#lpn_num").val('');
                    }
                    else {
                        var dataWhr = data['whse_num'];
                        $("#whse_num").val(dataWhr);
                        $("#loc_num").val(data['loc_num']);
                        $("#checkLpnNo").html('');
                    }
                });
            }
            else{
                $("#lpn_num").val('');
                $("#whse_num").val('');
                $("#loc_num").val('');
            }
        });

        $("#item_num").on("change", function(){
            //alert($("#item_num").val());
            if($("#item_num").val() != ''){
                ajaxurl ="{{ route('getItemList', 'item_num')}}";
                url = ajaxurl.replace('item_num', btoa($("#item_num").val()));

                $.get(url, function(data){
                    if(data == 'false') {
                        $("#checkItemNo").html('<span style="color:red;"> {{__('error.mobile.itemnotexist')}}</span>');
                        // Alert.notexist('{{__('Item')}}', $("#item_num").val());
                        $("#item_num").val('');
                        $("#item_desc").val('');
                        $("#qty_contained").val('');
                        $("#base_uom").val('');
                        $("#from_loc").val('');
                        $("#uom").val('');
                        $("#qty_input").val('');
                    }
                    else {
                        // check if the pallet is single item or not
                        ajaxurl ="{{ route('getPalletSingleItem', ['lpn_num','item_num'])}}";
                        url = ajaxurl.replace('lpn_num', $("#lpn_num").val());
                        url = url.replace('item_num', $("#item_num").val());

                        $.get(url, function(singleItem){
                            if(singleItem == 'false') {
                                Alert.warning('{{__('LPN is for single item only')}}');
                                $("#item_num").val('');
                                $("#item_desc").val('');
                                $("#qty_contained").val('');
                                $("#base_uom").val('');
                                $("#from_loc").val('');
                                $("#uom").val('');
                                $("#qty_input").val('');

                            }
                            else {
                                display('/displayItemDesc','item_num','item_desc');
                                $("#checkItemNo").html('');
                                if(data['lot_tracked'] == 1){
                                    $("#lot_num_field").attr('hidden',false);
                                }
                            }
                        });

                    }
                });
            }
            else{
                document.getElementById('item_num').value="";
                document.getElementById('item_desc').value="";
                document.getElementById('lot_num').value="";
                document.getElementById('qty_to_letdown').value="";
                document.getElementById('qty_contained').value="";
                document.getElementById('base_uom').value="";
                document.getElementById('uom').value="";
            }
        });

    });

    function showNewLot() {
        $("#loc_info").html('');
        $("#expiry_date_info").html("");
        $("#expiry_date").prop('readonly',false);

        if($("#lot_num").val()) {

            ajaxurl ="{{ route('lotitemv', ['lot_num','item_num','whse_num'])}}";
            url = ajaxurl.replace('lot_num', btoa($("#lot_num").val()));
            url = url.replace('item_num', btoa($("#item_num").val()));
            url = url.replace('whse_num', btoa($("#whse_num").val()));

            $.get(url, function(data){
                if(data == 'not exist') {
                    $("#loc_info").html('<i class="icon-info"></i> <small>{{ __('mobile.message.new_lot_location') }}</small>');
                }
            });

            $.ajax({
                url: '{{route('lot_item_expiry_date_exist')}}',
                type: "GET",
                data: {
                    lot_num: $("#lot_num").val(),
                    item_num: $("#item_num").val(),
                },
                success: function(data){
                    if(data) {
                        $("#expiry_date").val(data);
                        $("#expiry_date").prop('readonly',true);
                    }
                    else {
                        $("#expiry_date").prop('readonly',false);
                        $("#expiry_date").val($("#expiry_date").data('date-default-shelf-life'));
                    }
                }
            });

        }
        $("#lot_num").focus();
    }

</script>

@include('util.selection')
@include('Pallet.palletMobileValidation')
@include('util.convert_alternade_barcode_to_item')

@endsection
