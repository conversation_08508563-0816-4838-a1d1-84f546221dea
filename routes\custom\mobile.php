<?php
    Auth::routes();

    Route::group(['middleware' => ['auth']], function () {
    /*******************
           Inquiry
    ********************/
    // Item Inquiry
    Route::get('/home/<USER>/item-inquiry','Inquiry\ItemInquiryController@index')->name('ItemInquiry');
    Route::post('/home/<USER>/item-inquiry/item-details','Inquiry\ItemInquiryController@Inquiry')->name('ItemInquiryList');
    Route::get('/home/<USER>/item-inquiry/item-details','Inquiry\ItemInquiryController@Inquiry')->name('ItemInquiryList');

    Route::post('/home/<USER>/item-inquiry/lot-listing/{item}','Inquiry\ItemInquiryController@LotTracked')->name('ItemLotTracked');

    Route::get('/home/<USER>/item-inquiry/lot-listing/{item}/{whse}/{loc}','Inquiry\ItemInquiryController@GetLotTracked')->name('GetItemLotTracked');

    // Location Inquiry
    Route::get('/home/<USER>/location-inquiry','Inquiry\LocationInquiryController@index')->name('locinquiry');
    //Route::post('/home/<USER>/location-inquiry/location-inquiry-details','Inquiry\LocationInquiryController@inquiryitemdetails')->name('LocationItemDetails');
    //Route::post('/home/<USER>/location-inquiry/location-inquiry-details/lot-tracked/{whse}/{loc}','Inquiry\LocationInquiryController@LotTracked')->name('LocationLotTracked');
    Route::get('/home/<USER>/location-inquiry/location-inquiry-details/lot-tracked/{whse}/{loc}','Inquiry\LocationInquiryController@LotTracked')->name('LocationLotTracked');
    Route::get('/home/<USER>/location-inquiry/location-inquiry-details','Inquiry\LocationInquiryController@inquiryitemdetails')->name('LocationItemDetails');
    // Job Inquiry
    Route::get('/home/<USER>/job-inquiry','Inquiry\Job_InquiryController@index')->name('Job_Inquiry');
    Route::get('/home/<USER>/job-inquiry/details','Inquiry\Job_InquiryController@details')->name('Job_Inquiry.details');
    Route::get('/home/<USER>/job-inquiry/showJobInquiry/{id}/{id1}','Inquiry\Job_InquiryController@showJobInquiry')->name('showJobInquiry');
    Route::get('/home/<USER>/job-inquiry/filterJobInquiry/{id}','Inquiry\Job_InquiryController@filterJobInquiry')->name('filterJobInquiry');
    Route::get('/home/<USER>/job-inquiry/showDocNotes/{id}','Inquiry\Job_InquiryController@showDocNotes')->name('showDocNotes');

    // Job Attachment Inquiry
    Route::get('/home/<USER>/job-attachment-inquiry','Inquiry\JobInquiryController@index')->name('JobInquiry');
    Route::any('/home/<USER>/job-attachment-inquiry/details','Inquiry\JobInquiryController@inquiry')->name('JobInquiryList');
    Route::any('/home/<USER>/job-attachment-inquiry/details-new','Inquiry\JobInquiryController@inquirynew')->name('JobInquiryListNew');
    Route::post('/home/<USER>/job-attachment-inquiry/job-notes-and-docs/{ref_type}/{ref_num}/{ref_line?}/{ref_release?}','Inquiry\JobInquiryController@details')->name('JobInquiryDetails');
    Route::post('/home/<USER>/job-attachment-inquiry/job-notes-and-docs-new/{ref_type}/{ref_num}/{suffix}/{ref_line?}/{ref_release?}','Inquiry\JobInquiryController@detailsnew')->name('JobInquiryDetailsNew');

    /*******************
         Warehouse
    ********************/
    // Stock Move
    Route::get('/home/<USER>/stock-move','Warehouse\MoveController@index')->name('StockMove');
    Route::post('/home/<USER>/stock-move/process','Warehouse\MoveController@moveProcess')->name('moveProcess');

    Route::get('/home/<USER>/multi-stock-move/', 'Warehouse\MoveController@multiIndex')->name('multiStockMove');
    Route::post('/home/<USER>/multi-stock-move/process', 'Warehouse\MoveController@multiMoveProcess')->name('multiStockMoveProcess');



    // Misc Issue
    Route::get('/home/<USER>/misc-issue','Warehouse\MiscController@issue')->name('MiscIssue');
    Route::post('/home/<USER>/misc-issue/process','Warehouse\MiscController@issueprocess')->name('issueprocess');

    // Misc Receipt
    Route::get('/home/<USER>/misc-receipt','Warehouse\MiscController@receipt')->name('MiscReceipt');
    Route::post('/home/<USER>/misc-receipt/process','Warehouse\MiscController@receiptprocess')->name('receiptprocess');
    Route::post('/home/<USER>/misc-receipt/validation','Warehouse\MiscController@MiscValidation')->name('MiscValidation');

    // Inventory Count
    Route::get('/home/<USER>/inventory-count', 'CounterController@index')->name('counters');

    Route::get('/home/<USER>/inventory-count-batch', 'CounterController@batchIndex')->name('countersBatch');

    Route::get('/home/<USER>/inventory-count-batch/scan', 'CounterController@countersBatchScan')->name('countersBatchScan');

    Route::post('/home/<USER>/inventory-count-batch/scan', 'CounterController@postCountScan')->name('postCountScan');

    Route::get('/home/<USER>/inventory-count/counts/{counter}', 'CounterController@edit')->name('counters.edit');
    Route::put('/home/<USER>/inventory-count/counts/{counter}', 'CounterController@update')->name('counters.update');
    Route::post('/home/<USER>/inventory-count/counts/{counter}', 'CounterController@adminUpdate')->name('counters.update-admin');
    Route::get('/home/<USER>/inventory-count/countlist','CounterController@list')->name('counters.countlist');

    // Picklist OLD (Mobile)
//     Route::get('/home/<USER>/pick-list', 'Outgoing\PicklistPickController@index')->name('picks');
//     Route::get('/home/<USER>/pick-list/edit/{picklist_line}', 'Outgoing\PicklistPickController@edit')->name('picks.edit');
//     Route::put('/home/<USER>/pick-list/update/{picklist_line}', 'Outgoing\PicklistPickController@update')->name('picks.update');
//     Route::get('/home/<USER>/pick-list/allocate/{picklist_line}', 'Outgoing\PicklistPickController@allocate')->name('picks.allocate');
//     Route::post('/home/<USER>/pick-list/process/{picklist_line}', 'Outgoing\PicklistPickController@processAllocate')->name('picks.process-allocate');
//     Route::get('/home/<USER>/pick-list/list','Outgoing\PicklistPickController@list')->name('picks.list');
//     Route::get('/home/<USER>/pick-list/list/{picklist}','Outgoing\PicklistPickController@listItems')->name('picks.listitems');
//     Route::get('/home/<USER>/pick-list/sortByType/{id}','Outgoing\PicklistPickController@sortByType')->name('sortByType');        // Sort
//     Route::get('/home/<USER>/pick-list/checkExpiryDatePicklist','Outgoing\PicklistPickController@checkExpiryDatePicklist')->name('checkExpiryDatePicklist');

    // Pick List Test (Mobile)
    Route::get('/home/<USER>/pick-list', 'Warehouse\PicklistPickTestController@index')->name('picksTest');
    Route::get('/home/<USER>/pick-list/list','Warehouse\PicklistPickTestController@list')->name('picksTest.list');
    Route::get('/home/<USER>/pick-list/pick-list-details/{picklist}', 'Warehouse\PicklistPickTestController@picklistDetails')->name('picksTest.picklistDetails');
    Route::get('/home/<USER>/pick-list/pick-item/{picklist}', 'Warehouse\PicklistPickTestController@pickItem')->name('picksTest.pickItem');
    Route::get('/home/<USER>/pick-list/confirm-picking/{picklist}', 'Warehouse\PicklistPickTestController@confirmPick')->name('picksTest.confirmPick');
    Route::put('/home/<USER>/pick-list/update/{picklist}', 'Warehouse\PicklistPickTestController@update')->name('picksTest.update');
    Route::get('/home/<USER>/pick-list/manual-allocate/{picklist}', 'Warehouse\PicklistPickTestController@manualAllocate')->name('picksTest.manualAllocate');
    Route::put('/home/<USER>/pick-list/manual-allocate', 'Warehouse\PicklistPickTestController@updateManualAllocate')->name('picksTest.updateManualAllocate');
    Route::get('/home/<USER>/pick-list/picked-item/{picklist}', 'Warehouse\PicklistPickTestController@pickedItems')->name('picksTest.pickedItems');
    Route::get('/home/<USER>/pick-list/confirm-unpicking/{picklist}/{picklist_allocate}', 'Warehouse\PicklistPickTestController@unpick')->name('picksTest.unpick');
    Route::put('/home/<USER>/pick-list/updateUnpick/{picklist}/{picklist_allocate}', 'Warehouse\PicklistPickTestController@updateUnpick')->name('picksTest.updateUnpick');
    Route::get('/home/<USER>/pick-list/checkExpiryDatePicklist','Warehouse\PicklistPickTestController@checkExpiryDatePicklist')->name('checkExpiryDatePicklist');

     // Packing (Mobile)
     Route::get('/home/<USER>/packing', 'Warehouse\PackingController@index')->name('packingMenu');
     Route::get('/home/<USER>/packing/main', 'Warehouse\PackingController@packingIndex')->name('packingIndex');
     Route::post('/home/<USER>/packing/list', 'Warehouse\PackingController@packingDetails')->name('packingDetails');
     Route::get('/home/<USER>/packing/view', 'Warehouse\PackingController@viewPacking')->name('viewPacking');
     Route::get('/home/<USER>/packing/viewPackingList', 'Warehouse\PackingController@viewPackingList')->name('viewPackingList');
     Route::get('/home/<USER>/packing/viewProcess/{id}', 'Warehouse\PackingController@viewProcess')->name('viewProcess');
     Route::post('/home/<USER>/packing/viewProcess/{id}', 'Warehouse\PackingController@viewProcess')->name('viewProcess');
    //  Route::post('/home/<USER>/packing/viewProcess', 'Warehouse\PackingController@viewProcess')->name('viewProcess');
     Route::get('/home/<USER>/packing/viewList', 'Warehouse\PackingController@viewList')->name('viewList');
     Route::post('/home/<USER>/packing/editView', 'Warehouse\PackingController@editView')->name('editView');
     Route::post('/home/<USER>/packing/update', 'Warehouse\PackingController@update')->name('update');
     Route::post('/home/<USER>/packing/packingProcess', 'Warehouse\PackingController@packingProcess')->name('packingProcess');
     Route::get('/home/<USER>/packing/viewUnpack/{id}', 'Warehouse\PackingController@viewUnpack')->name('viewUnpack');
     Route::get('/home/<USER>/packing/viewPack', 'Warehouse\PackingController@viewPack')->name('viewPack');
     Route::get('/home/<USER>/packing/removeUnpack/{id}', 'Warehouse\PackingController@removeUnpack')->name('removeUnpack');
     Route::get('/home/<USER>/packing/packinglist_pack', 'Warehouse\PackingController@packinglist_pack')->name('packinglist_pack');
     Route::get('/home/<USER>/packing/packinglist_unpack', 'Warehouse\PackingController@packinglist_unpack')->name('packinglist_unpack');
     Route::get('/home/<USER>/packing/packList', 'Warehouse\PackingController@packList')->name('packList');
     Route::get('/home/<USER>/packing/packing_package/{id}', 'Warehouse\PackingController@packing_package')->name('packing_package');
     Route::get('/home/<USER>/packing/add_package', 'Warehouse\PackingController@add_package')->name('add_package');
     Route::get('/home/<USER>/packing/pack_package', 'Warehouse\PackingController@pack_package')->name('pack_package');
     Route::get('/home/<USER>/packing/edit_pack', 'Warehouse\PackingController@edit_pack')->name('edit_pack');
     Route::get('/home/<USER>/packing/print_package', 'Warehouse\PackingController@print_package')->name('print_package');
     Route::get('/home/<USER>/packing/viewPackage', 'Warehouse\PackingController@viewPackage')->name('viewPackage');
     Route::get('/home/<USER>/packing/packageList', 'Warehouse\PackingController@packageList')->name('packageList');
     Route::get('/home/<USER>/packing/viewPackageList', 'Warehouse\PackingController@viewPackageList')->name('viewPackageList');
     Route::get('/home/<USER>/packing/editPackage', 'Warehouse\PackingController@editPackage')->name('editPackage');
     Route::get('/home/<USER>/packing/printMenu', 'Warehouse\PackingController@printMenu')->name('printMenu');


    /*******************
         Incoming
    ********************/

    // PO Receipt
    Route::get('/home/<USER>/po-receipt/back-list','Incoming\POReceiveController@backshowPoReceiptLine')->name('backshowPoReceiptLine');

    Route::get('/home/<USER>/po-receipt/grn-back-list','Incoming\POReceiveController@backshowGRnLine')->name('backshowGRnLine');

    Route::get('/home/<USER>/po-receipt','Incoming\POReceiveController@index')->name('poReceive');
    Route::post('/home/<USER>/po-receipt/list','Incoming\POReceiveController@showPoLine')->name('showPoLine');
    Route::get('/home/<USER>/po-receipt/process','Incoming\POReceiveController@showPoProcess')->name('showPoProcess');
    Route::post('/home/<USER>/po-receipt/process','Incoming\POReceiveController@runPoProcess')->name('runPoProcess');
    Route::get('/home/<USER>/po-receipt/item-list', 'Incoming\POReceiveController@POItemList')->name('POItemList');
    Route::post('/home/<USER>/po-receipt/grn-line-list', 'Incoming\POReceiveController@showGrnLine')->name('showGrnLine');
    Route::get('/home/<USER>/po-receipt/grn-list', 'Incoming\POReceiveController@GRNList')->name('GRNList');
    Route::get('/home/<USER>/po-receipt/grn-item-list', 'Incoming\POReceiveController@GRNItemList')->name('GRNItemList');
    Route::get('/home/<USER>/po-receipt/grn-process','Incoming\POReceiveController@showGrnProcess')->name('showGrnProcess');
    Route::post('/home/<USER>/po-receipt/run-grn-process','Incoming\POReceiveController@runGrnProcess')->name('runGrnProcess');
    Route::get('/home/<USER>/po-receipt/grn-add','Incoming\POReceiveController@addGrn')->name('addGrn');
    Route::post('/home/<USER>/po-receipt/checkLPNPoReceipt','Incoming\POReceiveController@checkLPNPoReceipt')->name('checkLPNPoReceipt');
    Route::get('/home/<USER>/po-receipt/generate-lpn/{lpnNum?}/{record?}', 'Incoming\POReceiveController@LPNList')->name('LPNList');

    Route::get('/home/<USER>/po-receipt/checkLPNStatus', 'Incoming\POReceiveController@checkLPNStatus')->name('checkLPNStatus');

    // Catch Weight
    Route::post('/home/<USER>/po-receipt/catch-weight/process','Incoming\POReceiveController@runPoCWProcess')->name('runPoCWProcess');
    Route::post('/home/<USER>/po-receipt/catch-weight/run-grn-process','Incoming\POReceiveController@runGrnCWProcess')->name('runGrnCWProcess');

    Route::post('/home/<USER>/catch-weight','CatchWeightController@printCatchWeight')->name('printCatchWeight');

  // Put Away
    Route::get('home/incoming/put-away','Incoming\PutAwayController@index')->name('putaway');
    Route::post('home/inoming/put-away/process','Incoming\PutAwayController@process')->name('putawayprocess');

    // CO Return
    Route::get('/home/<USER>/co-return','Incoming\CoReturnController@index')->name('CoReturn');
    Route::any('home/incoming/co-return/details','Incoming\CoReturnController@CoReturnDetails')->name('CoReturnDetails');
    Route::post('/home/<USER>/co-return/details/process','Incoming\CoReturnController@ReturnCO')->name('ReturnCO');
    Route::get('/home/<USER>/co-return/details/process','Incoming\CoReturnController@CoReturnProcess')->name('CoReturnProcess');
    Route::get('/home/<USER>/co-return/list', 'Incoming\CoReturnController@ReturnList')->name('CoReturnList');


    // Customer Return
    Route::get('/home/<USER>/customer-return','Incoming\CustomerReturnController@index')->name('CustomerReturn');
    Route::any('home/incoming/customer-return/details','Incoming\CustomerReturnController@CustomerReturnDetails')->name('CustomerReturnDetails');
    Route::post('/home/<USER>/customer-return/details/process','Incoming\CustomerReturnController@ReturnCustomer')->name('ReturnCustomer');
    Route::get('/home/<USER>/customer-return/details/process','Incoming\CustomerReturnController@CustomerReturnProcess')->name('CustomerReturnProcess');
    Route::get('/home/<USER>/customer-return/list', 'Incoming\CustomerReturnController@ReturnCustomerList')->name('CustomerReturnList');


    Route::get('/home/<USER>/customer-return/customer-return-back-list','Incoming\CustomerReturnController@backshowCustomerReturnLineBack')->name('backshowCustomerReturnLineBack');



    // TO Receipt
    Route::get('/home/<USER>/to-receipt','Incoming\TransferOrderReceiptController@index')->name('TOReceipt');
    Route::get('/home/<USER>/to-receipt/list','Incoming\TransferOrderReceiptController@transferOrderItemList')->name('transferOrderItemList');
    Route::get('/home/<USER>/to-receipt/receive','Incoming\TransferOrderReceiptController@receiveTransferOrder')->name('receiveTransferOrder');
    Route::post('/home/<USER>/to-receipt/process','Incoming\TransferOrderReceiptController@processTransferReceive')->name('processTransferReceive');
    Route::get('/home/<USER>/to-receipt-pallet/listing','Incoming\TransferOrderReceiptController@receiveTOByPallet')->name('receiveTOByPallet');
    Route::get('/TOList', 'Incoming\TransferOrderReceiptController@TOList')->name('TOList');
    Route::post('/TOvalidation','Incoming\TransferOrderReceiptController@TOvalidation')->name('TOvalidation');
    Route::post('/TOvalidationWithStatus','Incoming\TransferOrderReceiptController@TOvalidationWithStatus')->name('TOvalidationWithStatus');

    // TO Receipt Catch Weight
    Route::post('/home/<USER>/to-receipt/catch-weight/process','Incoming\TransferOrderReceiptController@processTransferReceiveCW')->name('processTransferReceiveCW');

    /*******************
         Outgoing
    ********************/

    // PO Return

    Route::get('/home/<USER>/po-return', 'Outgoing\POReturnController@index')->name('poReturn');
    Route::post('/home/<USER>/po-return/list','Outgoing\POReturnController@showPoLine')->name('showPoLineReturn');
    Route::get('/home/<USER>/po-return/back-list','Outgoing\POReturnController@backshowPoLine')->name('backshowPoLineReturn');
    Route::get('/home/<USER>/po-return/process','Outgoing\POReturnController@showPoProcess')->name('showPoReturnProcess');
    Route::post('/POReturn','Outgoing\POReturnController@runPoProcess')->name('runPoReturnProcess');
    Route::get('/POReturnItemList', 'Outgoing\POReturnController@POItemList')->name('POReturnItemList');
    Route::post('/home/<USER>/grn-return/list','Outgoing\POReturnController@showGrnLine')->name('showGrnLineReturn');
    Route::get('/GRNReturnItemList', 'Outgoing\POReturnController@GRNItemList')->name('GRNReturnItemList');
    Route::get('/home/<USER>/grn-return/process','Outgoing\POReturnController@showGrnProcess')->name('showGrnReturnProcess');
    Route::post('/GRNReturn','Outgoing\POReturnController@runGrnProcess')->name('runGrnReturnProcess');

    Route::get('/home/<USER>/po-return/grn-back-list','Outgoing\POReturnController@backshowGRNLineReturn')->name('backshowGRNLineReturn');
    Route::post('/home/<USER>/po-return/catch-weight/process','Outgoing\POReturnController@runPoCWProcess')->name('runPoReturnCWProcess');
    Route::post('/home/<USER>/grn-return/catch-weight/process','Outgoing\POReturnController@runGrnCWProcess')->name('runGrnReturnCWProcess');

    // Pick & Ship
    Route::get('/home/<USER>/pick-and-ship','Outgoing\PickNShipController@index')->name('PickNShip');
    Route::post('/home/<USER>/pick-and-ship/list','Outgoing\PickNShipController@CoDetails')->name('CoDetails');
    Route::post('/home/<USER>/pick-and-ship/listing','Outgoing\PickNShipController@ShipCO')->name('ShipCO');
    Route::get('/home/<USER>/pick-and-ship/process','Outgoing\PickNShipController@PickNShipProcess')->name('PickNShipProcess');
    Route::get('/PickNShipList', 'Outgoing\PickNShipController@PickNShipList')->name('PickNShipList');
    Route::get('/checkExpiryDatePickNShip', 'Outgoing\PickNShipController@checkExpiryDatePickNShip')->name('checkExpiryDatePickNShip');

    // CO Picking
    Route::get('/home/<USER>/co-picking','Outgoing\CoPickController@index')->name('CoPick');
    Route::get('/home/<USER>/co-picking/list','Outgoing\CoPickController@CoPickingDetails')->name('CoPickingDetails');
    Route::post('/home/<USER>/co-picking/listing','Outgoing\CoPickController@PickCo')->name('PickCo');
    Route::get('/home/<USER>/co-picking/process','Outgoing\CoPickController@CoPickingProcess')->name('CoPickingProcess');
    Route::post('/home/<USER>/co-picking-pallet/listing','Outgoing\CoPickController@PickCobyPalletProcess')->name('PickCobyPalletProcess');
    Route::get('/CoPickList', 'Outgoing\CoPickController@CoPickList')->name('CoPickList');
    Route::post('/COvalidation','Outgoing\CoPickController@validation')->name('COvalidation');
    Route::post('/COOpenvalidation','Outgoing\CoPickController@COOpenvalidation')->name('COOpenvalidation');
    Route::get('/CoPalletvalidation/{whse_num}/{co_num}','Outgoing\CoPickController@CoPalletvalidation')->name('CoPalletvalidation');
    Route::get('/checkExpiryDateCOPicking','Outgoing\CoPickController@checkExpiryDateCOPicking')->name('checkExpiryDateCOPicking');
    Route::get('/changeStageLoc/{whse_num}', 'Outgoing\CoPickController@changeStageLoc')->name('changeStageLoc');

    // CO Picking Catch Weight
    Route::post('/home/<USER>/co-picking/catch-weight/process','Outgoing\CoPickController@runCoPickCWProcess')->name('runCoPickCWProcess');

    // CO Unpicking
    Route::get('/home/<USER>/co-unpicking','Outgoing\CoUnPickController@index')->name('CoUnPick');
    Route::get('/home/<USER>/co-unpicking/list','Outgoing\CoUnPickController@CoUnPickDetails')->name('CoUnPickDetails');
    Route::get('/home/<USER>/co-unpicking/process','Outgoing\CoUnPickController@CoUnPickingProcess')->name('CoUnPickingProcess');
    Route::post('/home/<USER>/co-unpicking/listing','Outgoing\CoUnPickController@UnPickCo')->name('UnPickCo');
    Route::get('/CoUnpickList', 'Outgoing\CoUnPickController@CoUnpickList')->name('CoUnpickList');
    Route::post('/home/<USER>/co-unpicking-pallet/list','Outgoing\CoUnPickController@UnpickCobyPalletProcess')->name('UnpickCobyPalletProcess');
    // Route::get('/home/<USER>/co-unpicking/details', 'Outgoing\CoUnpickController@CoUnpickList')->name('CoUnpickList');

    // CO Shipping
    Route::get('/home/<USER>/co-shipping','Outgoing\CoShippingController@index')->name('CoShipping');
    Route::get('/home/<USER>/co-shipping/validate', 'Outgoing\CoShippingController@CoShipValidate')->name('CoShipDetails');

    Route::get('/home/<USER>/co-shipping/list','Outgoing\CoShippingController@CoShipDetails')->name('CoShipDetails');
    Route::any('/home/<USER>/co-shipping/co-shipment', 'Outgoing\CoShippingController@GenerateCOShipment')->name('GenerateCOShipment');
    Route::post('/CoShipping','Outgoing\CoShippingController@ShipCo')->name('CoShip');
    Route::post('/COShipvalidation','Outgoing\CoShippingController@validation')->name('COShipvalidation');
    Route::delete('//home/<USER>/co-shipping/delete','Outgoing\CoShippingController@delete')->name('co_ship.delete');
    Route::get('/checkExpiryDateCOShipping','Outgoing\CoShippingController@checkExpiryDateCOShipping')->name('checkExpiryDateCOShipping');
    Route::get('/changeCustNum', 'Outgoing\CoShippingController@changeCustNum')->name('changeCustNum');

    // TO Shipping
    Route::get('/home/<USER>/to-shipping','Outgoing\TransferOrderShippingController@index')->name('TransferOrderShipping');
    Route::get('/home/<USER>/to-shipping/list','Outgoing\TransferOrderShippingController@showTOShipping')->name('showTOShippingList');
    Route::put('/home/<USER>/to-shipping/back-list','Outgoing\TransferOrderShippingController@backTOShipping')->name('backTOShippingList'); // To back to to shipping page
    Route::get('/home/<USER>/to-shipping/back-list','Outgoing\TransferOrderShippingController@backTOShipping')->name('backTOShippingList'); // To back to to shipping page

    Route::get('/home/<USER>/to-shipping/process', 'Outgoing\TransferOrderShippingController@process')->name('showTOShippingProcess');
    Route::put('/home/<USER>/to-shipping-pallet/listing','Outgoing\TransferOrderShippingController@shipTOByPallet')->name('shipTOByPallet');
    Route::post('/ShipTransferOrder','Outgoing\TransferOrderShippingController@ShipTransferOrder')->name('ShipTransferOrder');
    Route::get('/getTransItemLoc/{item?}/{whse?}/{loc?}','Outgoing\TransferOrderShippingController@getTransItemLoc');
    Route::get('/checkExpiryDateTOShipping','Outgoing\TransferOrderShippingController@checkExpiryDateTOShipping')->name('checkExpiryDateTOShipping');
    Route::get('/checkTransferOrderExist', 'Outgoing\TransferOrderShippingController@checkTransferOrderExist')->name('checkTransferOrderExist');
    Route::post('/home/<USER>/to-shipping/catch-weight/process','Outgoing\TransferOrderShippingController@runTOShippingCWProcess')->name('runTOShippingCWProcess');

    /*******************
            WIP
    ********************/

    // Job Matl Issue
    Route::get('/home/<USER>/job-material-issue','JobMaterialController@index')->name('JobMaterialIssue');
    Route::any('/home/<USER>/job-material-issue/list','JobMaterialController@showSelectionList')->name('showSelectionList');
    Route::get('/home/<USER>/job-material-issue/list/next','JobMaterialController@showSelectionListAfterPrintLabel')->name('showSelectionListAfterPrintLabel');
    Route::get('/home/<USER>/job-material-issue/details','JobMaterialController@jobMatlDetails')->name('jobMatlDetails');
    Route::post('/home/<USER>/job-material-issue/process','JobMaterialController@jobMatlProcess')->name('jobMatlProcess');
    Route::get('/JobList', 'JobMaterialController@JobList')->name('JobList');
    Route::post('/JobValidation','JobMaterialController@JobValidation')->name('JobValidation');
    Route::post('/checkJobQtyCompletedMoreThan0','JobMaterialController@checkJobQtyCompletedMoreThan0')->name('checkJobQtyCompletedMoreThan0');
    Route::get('/checkExpiryDateJobMaterialIssue','JobMaterialController@checkExpiryDateJobMaterialIssue')->name('checkExpiryDateJobMaterialIssue');
    Route::post('/home/<USER>/job-material-issue/catch-weight/process','JobMaterialController@runJobMatlIssueCWProcess')->name('runJobMatlIssueCWProcess');

    //Job Matl Issue bulk
    Route::get('/home/<USER>/batch-job-material-issue', 'JobMaterialController@batchIndex')->name('BatchJobMaterialIssue');
    Route::get('/home/<USER>/batch-job-material-issue/list', 'JobMaterialController@showBatchSelectionList')->name('showBatchSelectionList');
    Route::post('/home/<USER>/batch-job-material-issue/process', 'JobMaterialController@BatchJobMatlProcess')->name('batchJobMatlProcess');

    //Job Matl Issue By Job
    Route::get('/JobMaterialIssueByJob','JobMatlIssueByJobController@index')->name('JobMaterialIssueByJob');
    Route::post('/issueMatl','JobMatlIssueByJobController@issueMatl')->name('issueMatl');

    // Job Matl Return
    Route::get('/home/<USER>/job-material-return','JobMaterialController@unIssueIndex')->name('unIssueIndex');
    Route::any('/home/<USER>/job-material-return/list','JobMaterialController@showUnIssueSelectionList')->name('showUnIssueSelectionList');
    Route::get('/home/<USER>/job-material-return/details','JobMaterialController@unIssueJobDetails')->name('unIssueJobDetails');
    Route::post('/unissueJobMatlProcess','JobMaterialController@unissueJobMatlProcess')->name('unissueJobMatlProcess');
    Route::post('/home/<USER>/job-material-return/catch-weight/process','JobMaterialController@runJobMatlReturnCWProcess')->name('runJobMatlReturnCWProcess');
    Route::get('/JobUnissueList', 'JobMaterialController@JobUnissueList')->name('JobUnissueList');

    // Job Receipt
    Route::get('/home/<USER>/job-receipt','JobReceiptController@index')->name('JobReceipt');
    Route::post('/home/<USER>/job-receipt/print','JobReceiptController@JobReceiptProcess')->name('JobReceiptProcess');
    Route::post('/JobReceiptValidation','JobReceiptController@JobReceiptValidation')->name('JobReceiptValidation');
    Route::post('/home/<USER>/job-receipt/catch-weight/print','JobReceiptController@JobReceiptCWProcess')->name('JobReceiptCWProcess');

    // Job Return
    Route::get('/home/<USER>/job-return','JobReturnController@index')->name('JobReturn');
    Route::post('/home/<USER>/job-return','JobReturnController@process')->name('JobReturn.process');
    Route::post('/home/<USER>/job-return/catch-weight/process','JobReturnController@JobReturnCWProcess')->name('JobReturnCWProcess');

    // Labor Reporting
    Route::get('/home/<USER>/labor-reporting','LabourReportingController@index')->name('LabourReporting');
    Route::get('/home/<USER>/end-labor-reporting','LabourReportingController@endLabourReporting')->name('EndLabourReporting');
    Route::post('/home/<USER>/labor-reporting/next','LabourReportingController@nextLabourReporting')->name('nextLabourReporting');
    Route::post('/home/<USER>/end-labor-reporting/list','LabourReportingController@labourList')->name('LabourList');
    Route::post('/labourdetail/{id}','LabourReportingController@labourdetail')->name('LabourDetail');
    Route::get('/labourdetail/{id}','LabourReportingController@labourdetail')->name('LabourDetailGET');
    Route::post('/LabourValidation','LabourReportingController@LabourValidation')->name('labourValidation');
    Route::get('/employee/{emp_num}', 'LabourReportingController@checkEmployee')->name('employee'); //validate employee status
    Route::get('/checkEmp/{emp_num}','LabourReportingController@checkExist')->name('checkEmp');

    // Labor Reporting - Indirect Task
    Route::post('/storeIndirectStart','IndirectTaskController@storeIndirectStart')->name('storeIndirectStart');
    Route::post('/endIndirect','IndirectTaskController@endIndirect')->name('endIndirect');

    // Labor Reporting - Job Setup
    Route::post('/storeJobSetup','JobSetupController@storeJobSetup')->name('storeJobSetup');
    Route::post('/endJobSetup','JobSetupController@endJobSetup')->name('endJobSetup');

    // Labor Reporting - Job Run
    Route::post('/storeJobRun','JobRunController@storeJobRun')->name('storeJobRun');
    Route::post('/endJobRun','JobRunController@endJobRun')->name('endJobRun');
    Route::get('/validQtyMove','JobRunController@jobValidation')->name('validQtyMove');

    // Machine Run
    Route::get('/home/<USER>/machine-run-start', 'MachineRunController@indexMulti')->name('MachineRunStart');
    Route::get('/home/<USER>/machine-run-start/next', 'MachineRunController@MachineRunStartNext')->name('MachineRunStartNext');
    Route::post('/home/<USER>/machine-run-start/next', 'MachineRunController@storeMultiMachineRun')->name('storeMultiMachineRun');

    Route::get('/home/<USER>/machine-run-end', 'MachineRunController@MachineRunEnd')->name('MachineRunEnd');


    Route::get('/home/<USER>/machine-run-end/next', 'MachineRunController@MachineRunEndNext')->name('MachineRunEndNext');

    Route::get('/home/<USER>/machine-run-end/{id}', 'MachineRunController@MachineRunDetail')->name('MachineRunDetail');


    Route::get('/home/<USER>/downtime-start', 'MachineRunController@indexMultiDT')->name('MachineDownTimeStart');
    // Route::get('/home/<USER>/downtime-start/next', 'MachineRunController@MachineDownTimeStartNext')->name('MachineDownTimeStartNext');
    Route::post('/home/<USER>/downtime-start', 'MachineRunController@storeMultiMachineDowntime')->name('storeMultiMachineDowntime');
    Route::get('/home/<USER>/downtime-start/validate', 'MachineRunController@validateMultiMachineDowntime')->name('validateMultiMachineDowntime');

    Route::get('/home/<USER>/downtime-end', 'MachineRunController@indexMultiDTStop')->name('MachineDownTimeEnd');

    Route::post('/home/<USER>/downtime-end', 'MachineRunController@endMultiMachineDowntime')->name('endMultiMachineDowntime');

    Route::get('/home/<USER>/machine-run','MachineRunController@index')->name('MachineRun');
    Route::post('/home/<USER>/machine-run','MachineRunController@storeMachineRun')->name('storeMachineRun');
    Route::post('/endMachineRun','MachineRunController@endMachineRun')->name('endMachineRun');
    Route::get('/checkResource/{res_id}','MachineRunController@checkResource')->name('checkResource');
    Route::post('/nextMachineType','MachineRunController@nextMachineType')->name('nextMachineType');
    Route::get('/nextMachineType','MachineRunController@nextMachineType')->name('nextMachineType');
    Route::get('/home/<USER>/machine-run/stop','MachineRunController@showEndMachineRun')->name('showEndMachineRun');

    // WIP Move
    Route::get('/home/<USER>/wip-move','WIPMoveController@index')->name('WIPMove');
    Route::post('/WIPMove','WIPMoveController@runWIPMove')->name('runWIPMove');
    Route::post('/WIPMoveReverse/', 'WIPMoveController@runWIPMoveReverse')->name('runWIPMoveReverse');

    Route::get('/getWipMoveParm/{jobnum}/{oper_num}', 'WIPMoveController@getWipMoveParm')->name('getWipMoveParm');
    Route::get('/getWipMoveParm1/{jobnum}/{suffix}/{oper_num}', 'WIPMoveController@getWipMoveParm1')->name('getWipMoveParm1');

    // Print Label
    Route::get('/home/<USER>/print-inventory-label', 'BarcodeController@showInventoryLabel')->name('showInventoryLabel');
    Route::post('/home/<USER>/print-inventory-label/print','BarcodeController@processInventoryLabel')->name('processInventoryLabel');
    Route::get('/home/<USER>/print-co-shipping-label', 'BarcodeController@showCOShippingLabel')->name('showCOShippingLabel');
    Route::post('/home/<USER>/print-co-shipping-label/print','BarcodeController@processCOShippingLabel')->name('processCOShippingLabel');
    Route::get('/home/<USER>/print-po-receipt-label', 'BarcodeController@showPORcptLabel')->name('showPORcptLabel');
    Route::post('/home/<USER>/print-po-receipt-label/print','BarcodeController@processPORcptLabel')->name('processPORcptLabel');
    Route::get('/home/<USER>/print-job-material-issue-label', 'BarcodeController@showJobMatLabel')->name('showJobMatLabel');
    Route::post('/home/<USER>/print-job-material-issue-label/print','BarcodeController@processJobMatLabel')->name('processJobMatLabel');


    Route::get('/home/<USER>/print-job-receipt-label', 'BarcodeController@showJobRcptLabel')->name('showJobRcptLabel');
    Route::post('/home/<USER>/print-job-receipt-label/print','BarcodeController@processJobRcptLabel')->name('processJobRcptLabel');
    Route::get('/home/<USER>/print-to-shipping-label', 'BarcodeController@showTOShippingLabel')->name('showTOShippingLabel');    // TO Shipping Label
    Route::post('/home/<USER>/print-to-shipping-label/print','BarcodeController@processTOShippingLabel')->name('processTOShippingLabel');

    Route::get('/home/<USER>/print-to-pallet-label', 'BarcodeController@showPalletLabel')->name('showPalletLabel');    // TO Shipping Label
    Route::post('/home/<USER>/print-to-pallet-label/print', 'BarcodeController@processPalletLabel')->name('processPalletLabel');


    Route::any('/print_preview', 'BarcodeController@PrintPreviewLabelProcess')->name('PrintPreviewLabelProcess');
    Route::any('/print_preview_multi', 'BarcodeController@PrintPreviewLabelProcessMulti')->name('PrintPreviewLabelProcessMulti');
    Route::any('/print_preview_batch', 'BarcodeController@PrintPreviewLabelProcessBatch')->name('PrintPreviewLabelProcessBatch');
    Route::any('/print_preview_catch_weight', 'BarcodeController@printCatchWeightProcess')->name('printCatchWeightProcess');

    Route::post('/print','BarcodeController@PrintLabelProcess')->name('PrintLabelProcess');

    Route::post('/print_multiple_lines_bartender','BarcodeController@PrintMultiLinesBartenderLabelProcess')->name('PrintMultiLinesBartenderLabelProcess');

    Route::post('/printmultilines','BarcodeController@PrintMultiLabelProcess')->name('PrintMultiLabelProcess');
    /*******************
            Pallet
    ********************/
    Route::get('/home/<USER>/pallet-inquiry','PalletMobileController@showPalletInquiry')->name('PalletInquiry');
    Route::post('/home/<USER>/pallet-inquiry/details','PalletMobileController@palletInquiryDetails')->name('PalletInquiryDetails');
    Route::get('/home/<USER>/pallet-inquiry/details','PalletMobileController@palletInquiryDetails')->name('PalletInquiryDetails');
    Route::get('/home/<USER>/pallet-move','PalletMobileController@showPalletMove')->name('PalletMove');
    Route::get('/home/<USER>/pallet-letdown','PalletMobileController@showPalletLetdown')->name('PalletLetdown');
    Route::get('/home/<USER>/pallet-item-transfer','PalletMobileController@showPalletItemTransfer')->name('PalletItemTransfer');
    Route::get('/home/<USER>/pallet-builder','PalletMobileController@showPalletBuilder')->name('PalletBuilder');
    Route::get('/home/<USER>/pallet-destruction','PalletMobileController@showPalletDestruction')->name('PalletDestruction');



    Route::post('/home/<USER>/pallet-move/process','PalletMobileController@processPalletMove')->name('PalletMoveProcess');
    Route::post('/home/<USER>/pallet-letdown/process','PalletMobileController@processPalletLetdown')->name('PalletLetdownProcess');
    Route::post('/home/<USER>/pallet-item-transfer/process','PalletMobileController@PalletTransferProcess')->name('PalletTransferProcess');
    Route::post('/home/<USER>/pallet-builder/process','PalletMobileController@processPalletBuilder')->name('PalletBuilderProcess');
    Route::post('/home/<USER>/pallet-destruction/process','PalletMobileController@processPalletDestruction')->name('PalletDestructionProcess');

    Route::get('/fetchPalletMatch', 'PalletMobileController@fetchPalletMatch')->name('fetchPalletMatch');


    Route::get('/getLPNMatchUnMatchFeatures', 'PalletMobileController@getLPNMatchUnMatchFeatures')->name('getLPNMatchUnMatchFeatures');


    // Route::get('/home/<USER>/pallet-inquiry','PalletInquiryController@index')->name('PalletDestruction');
    // Route::post('/home/<USER>/pallet-inquiry/details','PalletInquiryController@details')->name('PalletDestructionDetails');


    /**************************
            Bundle Builder
    ***************************/
    Route::get('/home/<USER>/bundle-builder','BundleBuilderMobileController@index')->name('BundleMobile');
    Route::post('/home/<USER>/bundle-builder/store','BundleBuilderMobileController@store')->name('storeBundleMobile');

    });
    if(env('APP_ENV')=="production") {
        URL::forceScheme('https');
    }

?>
