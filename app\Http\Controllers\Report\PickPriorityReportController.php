<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\CustomerOrderItem;
use App\ItemWarehouse;
use Yajra\DataTables\DataTables;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PickPriorityReportController extends Controller
{
    /**
     * Display the Pick Priority report page.
     */
    public function index()
    {
        // Authorize using a generic report permission gate if it exists
        // if (!\Gate::allows('hasReport')) {
        //     return view('errors.404v2')->with('page', 'error');
        // }

        return view('report.pickpriority.index');
    }

    /**
     * AJAX source for DataTables – prioritised list of items to pick.
     */
    public function data(Request $request)
    {
        /*
         |--------------------------------------------------------------------------
         |  Core Selection Logic – v2 (typical WMS needs)
         |--------------------------------------------------------------------------
         |  1. Open customer-order lines (rel_status = O) with outstanding qty.
         |  2. Attach customer (cust_num, name) and shipment due-date.
         |  3. Attach default location → zone to enable zone-based picking.
         |  4. Attach current qty_available & compute shortage.
         |  5. Order by (a) due_date (soonest), (b) zone, (c) shortage, so a
         |     supervisor can wave-pick efficiently.
         |--------------------------------------------------------------------------
         */

        $today = Carbon::today();

        $sub = CustomerOrderItem::query()
            ->select([
                'coitems.id',
                'coitems.co_num',
                'coitems.co_line',
                'coitems.co_rel',
                'coitems.item_num',
                'coitems.item_desc',
                'coitems.due_date',
                DB::raw('(coitems.qty_released - coitems.qty_shipped) as qty_required'),
                'coitems.whse_num',
                'coitems.cust_num',
                'coitems.cust_name',
            ])
            ->where('coitems.rel_status', 'O')
            ->whereRaw('(coitems.qty_released - coitems.qty_shipped) > 0');

        // Join to inventory availability, location & zone
        $query = DB::query()->fromSub($sub, 'ci')
            ->leftJoin('item_warehouses as iw', function ($join) {
                // stock available in same warehouse
                $join->on('iw.item_num', '=', 'ci.item_num');
                $join->on('iw.whse_num', '=', 'ci.whse_num');
            })
            // default location assign for zone info
            ->leftJoin('item_locs as il', function ($join) {
                $join->on('il.item_num', '=', 'ci.item_num');
                $join->on('il.whse_num', '=', 'ci.whse_num');
            })
            ->leftJoin('locs as l', function ($join) {
                $join->on('l.loc_num', '=', 'il.loc_num');
                $join->on('l.whse_num', '=', 'il.whse_num');
            })
            ->leftJoin('zones as z', 'z.id', '=', 'l.zone_id')
            ->select([
                'ci.*',
                DB::raw('IFNULL(iw.qty_available, 0) as qty_available'),
                DB::raw('(ci.due_date) as due'),
                'ci.cust_num',
                'ci.cust_name',
                'il.loc_num',
                'z.zone_num',
                DB::raw('(ci.qty_required - IFNULL(iw.qty_available,0)) as shortage')
            ])
            // priority ordering: due date, zone, shortage
            ->orderByRaw('ci.due_date ASC')
            ->orderByRaw('z.zone_num IS NULL')
            ->orderBy('z.zone_num')
            ->orderByRaw('shortage ASC');

        return DataTables::of($query)
            ->editColumn('due', function ($row) {
                return $row->due ? Carbon::parse($row->due)->format('d/m/Y') : '';
            })
            ->editColumn('shortage', function($row){
                return number_format($row->shortage,2,'.','');
            })
            ->make(true);
    }
}
