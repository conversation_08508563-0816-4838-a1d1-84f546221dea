<?php

namespace App\Http\Controllers\Super;

use App\CountBatch;
use App\Customer;
use App\Group;
use App\Item;
use App\ItemLoc;
use App\Label;
use App\Loc;
use App\LotLoc;
use App\ProductCode;
use App\ReasonCode;
use App\SiteSetting;
use App\TransParm;
use App\Vendor;
use App\Warehouse;
use App\WorkCenter;
use App\UOM;
use App\User;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Rules\StrongPassword;
use LangleyFoxall\LaravelNISTPasswordRules\PasswordRules;
use Illuminate\Support\Facades\Storage;
use App\Jobs\ImportBackground;
use App\Imports\ProductCodeImport;
use App\Imports\UOMImport;
use App\Imports\ItemsImport;
use Alert;
use App\DefaultLabel;
use Excel;
use App\helpers;
use Camroncade\Timezone\Facades\Timezone;
use Illuminate\Validation\ValidationException;

class SuperController extends Controller
{

    public static $modules_labels = [
        'Inventory Label' => [
            'Inventory Label',
            'CustOrdPicking',
            'CustOrdShipping',
            'JobLabour',
            'JobMaterialIssue',
            'JobMaterialReturn',
            'JobReceipt',
            'MachineRun',
            'MiscReceipt',
            'MiscIssue',
            'PickList',
            'PickNShip',
            'POReceipt',
            'Putaway',
            'StockMove',
            'TranOrderShipping',
            'TransferOrderReceipt',
            'WIPMove'
        ],
        'CO Shipping Label' => [
            'CustOrdPicking',
            'CustOrdShipping',
            'PickList',
            'PickNShip',
        ],
        'Customer Return Label' => [
            'CustomerReturn',

        ],
        'PO Receipt Label' => [
            'POReceipt',
        ],
        'Job Receipt Label' => [
            'JobReceipt',
            'JobLabour',
            'MachineRun',
            'WIPMove',
        ],
        'Job Material Issue Label' => [
            'JobMaterialIssue',
            'JobMaterialReturn',
        ],
        'TO Shipping Label' => [
            'TranOrderShipping',

        ],
        'Pallet Label' => [
            'CustOrdPicking',
            'CustOrdShipping',
            'JobReceipt',
            'POReceipt',
            'MiscReceipt',
            'TranOrderShipping',
            'TransferOrderReceipt',
            'Pallet'
        ],
    ];
    public function index()
    {
        //
        //  $v= customCrypt('20');
          // $d = customDecrypt('eyJpdiI6IlpiSmlZTk9MejF2VnAxazBBQko5cEE9PSIsInZhbHVlIjoiWU42Y1NtNnVTNUFXYnBMSkFVeFhOZz09IiwibWFjIjoiZjBkMmU5OTJlY2FhYjhkY2Q2OGIwNTkwNDY2ZDY2NmRmMzE5ZWRhMGFhOTJhOTM5NWZjNDI5OGFmNjkzMGEwNCJ9');
         //  echo $d;
        //echo $v;
       //  exit;

        // echo "<br>";
        // echo customDeCrypt($v);
        // exit;
        // self::addLabelObjectsAll();
        // exit; testing

        if (auth()->user()->site_id != "AXA_TEST" && auth()->user()->site_id != "AXA" ) {
            return view('errors.404')->with('page', 'error');;
        }


        // load the packages
        $db_plan = DB::table('plans')->select('id', 'plan_code')->get()->toArray();

        $sites = DB::table('site_settings')->select('site_name', 'site_id', 'plan_code', 'plan_id', 'zoho_customer_id')->get()->toArray();

        //Need to put super admin permission here

        /*
          if(!Gate::allows('hasSuperAdmin')){
          return view('errors.404')->with('page','error'); ;
          }
         */
        // dd($db_plan);

        return view('admin.super')->with('db_plan', $db_plan)->with('sites', $sites);
    }
    public function migrate_labels_modules()
    {

        self::addLabelObjectsAll();
        return redirect()->back()->with('successmsg', 'Labels modules are reset to default.');

        // exit;
    }

    // Do reset Demo Data
    public function doResetDemoData()
    {

        // Read Excel File
        $arrData = array(
            '1' => array('locationfile' => '1. Import-Product code.xlsx', 'ImportName' => 'ProductCodeImport', 'CenterName' => 'ProductCode', 'lastName' => 'Product Codes'),
            '2' => array('locationfile' => '2. Import-UOM.xlsx', 'ImportName' => 'UOMImport', 'CenterName' => 'UOM', 'lastName' => 'UOMs'),
            '3' => array('locationfile' => '3. Import-Item.xlsx', 'ImportName' => 'ItemsImport', 'CenterName' => 'Item', 'lastName' => 'Items'),
            '4' => array('locationfile' => '4. Import-Alternate Barcode.xlsx', 'ImportName' => 'AlternateBarcodeImport', 'CenterName' => 'AlternateBarcode', 'lastName' => 'alternate-barcodes'),
            '5' => array('locationfile' => '5. Import-Customer.xlsx', 'ImportName' => 'CustomerImport', 'CenterName' => 'Customer', 'lastName' => 'Customers'),
            '6' => array('locationfile' => '6. Import-Vendor.xlsx', 'ImportName' => 'VendorImport', 'CenterName' => 'Vendor', 'lastName' => 'Vendors'),
            '7' => array('locationfile' => '7. Import-Warehouse.xlsx', 'ImportName' => 'WarehouseImport', 'CenterName' => 'Warehouse', 'lastName' => 'Warehouses'),
            '8' => array('locationfile' => '8. Import-Zones.xlsx', 'ImportName' => 'ZoneImport', 'CenterName' => 'Zone', 'lastName' => 'Zones'),
            '9' => array('locationfile' => '9. Import-Location.xlsx', 'ImportName' => 'LocationImport', 'CenterName' => 'Location', 'lastName' => 'Locations'),
            '10' => array('locationfile' => '10. Import-Item Warehouses.xlsx', 'ImportName' => 'ItemWarehouseImport', 'CenterName' => 'ItemWarehouse', 'lastName' => 'Item Warehouses'),
            '11' => array('locationfile' => '11. Import-Item Locations.xlsx', 'ImportName' => 'ItemLocationImport', 'CenterName' => 'ItemLocation', 'lastName' => 'Item Locations'),
            '12' => array('locationfile' => '12. Import-Lot.xlsx', 'ImportName' => 'LotImport', 'CenterName' => 'Lot', 'lastName' => 'Lots'),
            '13' => array('locationfile' => '13. Import-Item Lot Locations.xlsx', 'ImportName' => 'LotLocationImport', 'CenterName' => 'LotLocation', 'lastName' => 'Item Lot Locations'),
            '14' => array('locationfile' => '14. Import-Indirect Task.xlsx', 'ImportName' => 'TaskImport', 'CenterName' => 'Task', 'lastName' => 'Indirect Tasks'),
            '15' => array('locationfile' => '15. Import-Reason Code.xlsx', 'ImportName' => 'ReasonCodeImport', 'CenterName' => 'ReasonCode', 'lastName' => 'Reason Codes'),
            '16' => array('locationfile' => '16. Import-Employee.xlsx', 'ImportName' => 'EmployeeImport', 'CenterName' => 'Employee', 'lastName' => 'Employees'),
            '17' => array('locationfile' => '17. Import-Machine.xlsx', 'ImportName' => 'MachineImport', 'CenterName' => 'Machine', 'lastName' => 'Machines'),
            '18' => array('locationfile' => '18. Import-Workcenter.xlsx', 'ImportName' => 'WorkCenterImport', 'CenterName' => 'WorkCenter', 'lastName' => 'Work Centers'),
            '19' => array('locationfile' => '19. Import-BOM.xlsx', 'ImportName' => 'BomImport', 'CenterName' => 'Bom', 'lastName' => 'Bill of Materials'),
            '20' => array('locationfile' => '20. Import-Shipping Zones.xlsx', 'ImportName' => 'ShippingZoneImport', 'CenterName' => 'ShippingZone', 'lastName' => 'Shipping Zones'),
            '21' => array('locationfile' => '21. Import-Customer Order.xlsx', 'ImportName' => 'COImport', 'CenterName' => 'CO', 'lastName' => 'Customer Orders'),
            '22' => array('locationfile' => '22. Import-Purchase Order.xlsx', 'ImportName' => 'POImport', 'CenterName' => 'PO', 'lastName' => 'Purchase Orders'),
            '23' => array('locationfile' => '23. Import-Transfer Order.xlsx', 'ImportName' => 'TransferOrderImport', 'CenterName' => 'TransOrder', 'lastName' => 'Transfer Orders'),
            '24' => array('locationfile' => '24. Import-Job Order.xlsx', 'ImportName' => 'JobImport', 'CenterName' => 'Job', 'lastName' => 'Job Orders'),
            '25' => array('locationfile' => '25. Import-Job Routes.xlsx', 'ImportName' => 'JobRouteImport', 'CenterName' => 'JobRoute', 'lastName' => 'Job Routes'),
            '26' => array('locationfile' => '26. Import-Job Material.xlsx', 'ImportName' => 'JobMatlImport', 'CenterName' => 'JobMatl', 'lastName' => 'Job Materials'),
        );
        // Note* Zone Import need check . Item Warehouses [ need check ]
        foreach ($arrData as $key => $value) {
            $file = storage_path('download\\' . $value['locationfile']);
            dispatch(new ImportBackground($value['ImportName'], $file, $value['locationfile'], $value['CenterName'], auth()->user(), $value['lastName']));
        }

        echo "Done Import";
    }

    // Reset SA Password
    public function resetSASupportPassword(Request $request)
    {

        $type= "site_support";
        $siteID = $request->siteID;
            $getDataSite =  DB::table('site_settings')->where('site_id', $siteID)->first();
           // $timezone = $getDataSite->timezone;
            $now = Carbon::now()->toDateTimeString();


            //$now = getDateTimeConvertedToUTC($getnow);

            //dd($now,getDateTimeConverted($now));


        if($request->siteID=="All")
        {
            $user = User::where('type',$type)->get();
            foreach($user as $key)
            {
               $key->password = $request->password;
               $user->modified_date = $now;
               $key->save();
            }
        }
        else{

           //dd($now);

        $user = User::where('type',$type)->where('site_id',$request->siteID)->first();
        if ($request->password != null) {
            $user->password = $request->password;

        }
        $user->modified_date = $now;
        $user->save();
        }

        Alert::success('Success', 'Your password reset successfully', 'success');
        return redirect()->back();
       // }
    }

     // Add SA User
     public  function createSASupport(Request $request)
     {
        // dd($request);
         $siteID = $request->siteID;
         // Check on the SiteID
         $checkSAAcct = User::where('name','sa')->where('site_id', $siteID)->exists();
         $getDataSite =  DB::table('site_settings')->where('site_id', $siteID)->first();


         if($checkSAAcct==true)
         {
            //throw ValidationException::withMessages(["SA exists in this site ID"]);

            Alert::error('Error!', 'SA already exists in this site ID', 'failed');
            return redirect()->back();
         }
         else
         {
            // Create New User

           // dd('create SA');

            $plan_code = $getDataSite->plan_code;


            $plan_id = DB::table('plans')->select('id')->where('plan_code', $plan_code)->first();
            $db_plan = DB::table('plans')->select('id', 'plan_code')->get();

            $arrFilterPlans = array('AX-MT-STR-M' => '1', 'AX-MT-STR-A' => '4');
            $arrFilterPlanCode = array();
            foreach ($db_plan as $key) {
                if (array_key_exists($key->plan_code, $arrFilterPlans)) {
                    $arrFilterPlanCode[$key->id] = $key->plan_code;
                }
                if ($plan_code == $key->plan_code) {
                    $planID = $key->id;
                }
            }
            // password@123
            //$password = 'password@123';
            $password = $request->password;
            $site_id = $siteID;
            $timezone = $getDataSite->timezone;
            $now = Carbon::now()->toDateTimeString();
            //$now = getDateTimeConvertedToUTC($getnow);

            //dd($now,getDateTimeConverted($now));

             // Create Admin User  User::create(
        //  $createSaUser =User::create([
        //         'name' => 'sa',
        //         'description' => 'User Support Phitomas',
        //         'email' => '<EMAIL>',
        //         'password' => $password,
        //         'type' => 'site_support',
        //         'site_administration' => 'Yes',
        //         'homepage' => 'web',
        //         'timezone' => $timezone,
        //         'site_id' => $site_id,
        //         // 'created_by' => 'system',
        //         'status' => 'A',
        //         'created_date' => $now,
        //         'modified_date' => $now
        //     ]
        // );

        // if (!$createSaUser) {
        //     DB::rollback();
        // }
        $saEmail = "<EMAIL>";
        $uniqueKey = MD5($saEmail.$site_id);

        $createSaUser = User::create(
            [
                'name' => 'sa',
                'description' => 'Site Support',
                'email' => $saEmail,
                'password' => $password,
                'type' => 'site_support',
                'site_administration' => 'Yes',
                'homepage' => 'web',
                'timezone' => $timezone,
                'site_id' => $site_id,
                // 'created_by' => 'system',
                'unique_id' => $uniqueKey,
                'status' => 'A',
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createSaUser) {
            DB::rollback();
        }

        $saId = $createSaUser->id;

        // Give Admin Ext stuff
        DB::table('user_ext')->insert([
            ['user_id' => $saId, 'whse_num' => '']
        ]);

        $moduleSKU = DB::table('moduleskus')
            ->selectRaw('id, name')
            ->get();

        $arrGroup = array();

        // Check if plan code is Starter
        if (array_key_exists($planID, $arrFilterPlanCode)) {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->where('module_id', 1)
                ->get();
        } else {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->get();
        }

        foreach ($group_moduleSKU as $key => $value) {
            array_push($arrGroup, $value->group_id);
        }

        $group = DB::table('groups')
            ->selectRaw('id')
            ->whereIn('id', $arrGroup)
            ->get();

        $arr_user_groups_values = [];
        $arr_sa_user_groups_values = [];
        foreach ($group as $k => $value) {
            $grpId = $value->id;


            $arr_sa_user_groups_values[] = [
                'user_id' => $saId,
                'group_id' => $grpId,
                'created_date' => $now,
                'modified_date' => $now
            ];


        }



        if (count($arr_sa_user_groups_values) > 0) {
            DB::table('user_groups')->insert($arr_sa_user_groups_values);
        }

        //return redirect()->back()->with('successmsg', 'SA User Created!');
        Alert::success('Success', 'SA User Created!', 'success');
        return redirect()->back();
         }
     }


    // Add New Site
    public function exeNewSite(Request $request)
    {

        $request->validate([
            'site_id' =>
            [
                'required', 'alpha_dash', 'max:30', 'unique:site_settings',
            ],
            'email' =>
            [
                'required', 'max:30', 'unique:users',
            ],
            'password' => [new StrongPassword, PasswordRules::register(request()->name)],
        ]);

        // $db_plan = DB::table('plans')->select('id','plan_code')->get()->toArray();
        $plan_code = $request->plan_code;
        $plan_id = DB::table('plans')->select('id')->where('plan_code', $plan_code)->first();
        $db_plan = DB::table('plans')->select('id', 'plan_code')->get();

        $arrFilterPlans = array('AX-MT-STR-M' => '1', 'AX-MT-STR-A' => '4');
        $arrFilterPlanCode = array();
        foreach ($db_plan as $key) {
            if (array_key_exists($key->plan_code, $arrFilterPlans)) {
                $arrFilterPlanCode[$key->id] = $key->plan_code;
            }
            if ($plan_code == $key->plan_code) {
                $planID = $key->id;
            }
        }

        //        dd($arrFilterPlanCode);

        $site_id = $request->site_id;
        $company_name = $request->company_name;
        $site_name = $request->site_name;
        $timezone = $request->timezone;
        $adminUsername = $request->name;
        $adminEmail = $request->email;
        $max_inv_lic = customCrypt($request->max_inv_lic);



        $password = $request->password;

        // $first_name = $request->first_name;
        // $last_name = $request->last_name;
        //$password_confirmation = $request->password_confirmation;
        // $country = $request->country;
        // $state = $request->state;
        // $city = $request->city;
        // $street = $request->street;
        // $zip = $request->zip;
        // $qty = $request->qty;
        //dd($site_id,$company_name,$site_name,$timezone,$adminUsername,$adminEmail,$password,$max_inv_lic,$plan_code,$db_plan_id);

        $getnow = Carbon::now()->toDateTimeString();

        //$timezone = auth()->user()->timezone;
        $date = Timezone::convertFromUTC($getnow, $timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($getnow, $timezone, 'H:i:s');
        //$now = $date . ' ' . $time;

        $now = Carbon::now()->toDateTimeString();
        //$now =  Carbon::now()->toDateTimeString();
// dd($getnow,$now);
        // Insert into site setting
        $createSite = DB::table('site_settings')->insertOrIgnore(
            [
                'site_name' => $site_name,
                'site_id' => $site_id,
                'timezone' => $timezone,
                // 'zoho_event_id' => $zoho_event_id,
                // 'zoho_event_type' => $zoho_event_type,
                // 'zoho_subscription_id' => $zoho_subscription_id,
                // 'zoho_product_name' => $zoho_product_name,
                // 'zoho_customer_id' => $zoho_customer_id,
                'plan_code' => $plan_code,
                'plan_id' => $planID,
                'status' => 1,
                'max_user' => $max_inv_lic,
                'registered_user' => 1,
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        $siteID = DB::table('site_settings')->select('id')->where('site_id', $site_id)->value('id');

        // Add system setting to the newly created site id
        $sysSetArr = ['241', '243', '247']; // System setting id for number format. 241 = Unit Quantity Decimal Points, 243 = Quantity Per Decimal Points, 247 = Total Quantity Decimal Points

        $arr_tparm_sites_values = [];
        foreach ($sysSetArr as $setId) {
            $arr_tparm_sites_values[] = [
                'site_id' => $site_id,
                'tparm_id' => $setId,
                'tparm_value' => 2, // default value
            ];
            // $addSystemSet = DB::table('tparm_sites')->insert(
            //     [
            //         'site_id' => $site_id,
            //         'tparm_id' => $setId,
            //         'tparm_value' => 2, // default value
            //     ]
            // );
        }
        //dd($arr_tparm_sites_values);
        if (count($arr_tparm_sites_values) > 0) {
            $addSystemSet = DB::table('tparm_sites')->insert($arr_tparm_sites_values);
        }

        // // Adding default value
        // $TransParamsId = DB::table('trans_parms')->select('id')->where('tparm_name','item_warehouse_error')->where('tparm_module','CustomerOrder')->value('id');
        // if($TransParamsId > 0 )
        // {
        //     $arr_tparm_sites_item_values[] = [
        //         'site_id' => $site_id,
        //         'tparm_id' => $TransParamsId,
        //         'tparm_value' => 1, // default value
        //     ];
        //     DB::table('tparm_sites')->insert($arr_tparm_sites_item_values);
        // }
        // $TransParamsId = DB::table('trans_parms')->select('id')->where('tparm_name','item_warehouse_error')->where('tparm_module','PurchaseOrder')->value('id');
        // if($TransParamsId > 0 )
        // {
        //     $arr_tparm_sites_item_values[] = [
        //         'site_id' => $site_id,
        //         'tparm_id' => $TransParamsId,
        //         'tparm_value' => 1, // default value
        //     ];
        //     DB::table('tparm_sites')->insert($arr_tparm_sites_item_values);
        // }


        if (!$createSite) {
            DB::rollback();
        }
        $uniqueKey = MD5($adminEmail.$site_id);

        // Create Admin User
        $createAdminUser = User::create(
            [
                'name' => $adminUsername,
                'description' => 'Admin User',
                'email' => $adminEmail,
                'password' => $password,
                'type' => 'site_owner',
                'site_administration' => 'Yes',
                'homepage' => 'web',
                'timezone' => $timezone,
                'site_id' => $site_id,
                'unique_id' => $uniqueKey,
                // 'created_by' => 'system',
                'status' => 'A',
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createAdminUser) {
            DB::rollback();
        }

        $adminId = $createAdminUser->id;


         // Create Admin User
         $createSaUser = User::create(
            [
                'name' => 'sa',
                'description' => 'Site Support',
                'email' => $adminEmail,
                'password' => $password,
                'type' => 'site_support',
                'site_administration' => 'Yes',
                'homepage' => 'web',
                'timezone' => $timezone,
                'site_id' => $site_id,
                // 'created_by' => 'system',
                'unique_id' => $uniqueKey,
                'status' => 'A',
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createSaUser) {
            DB::rollback();
        }

        $saId = $createSaUser->id;


         // Give SA Ext stuff
         DB::table('user_ext')->insert([
            ['user_id' => $saId, 'whse_num' => '']
        ]);




        // Give Admin Ext stuff
        DB::table('user_ext')->insert([
            ['user_id' => $adminId, 'whse_num' => '']
        ]);

        $moduleSKU = DB::table('moduleskus')
            ->selectRaw('id, name')
            ->get();

        $arrGroup = array();

        // Check if plan code is Starter
        if (array_key_exists($planID, $arrFilterPlanCode)) {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->where('module_id', 1)
                ->get();
        } else {
            $group_moduleSKU = DB::table('group_modulesku')
                ->selectRaw('group_id')
                ->get();
        }

        foreach ($group_moduleSKU as $key => $value) {
            array_push($arrGroup, $value->group_id);
        }

        $group = DB::table('groups')
            ->selectRaw('id')
            ->whereIn('id', $arrGroup)
            ->get();

        $arr_user_groups_values = [];
        $arr_sa_user_groups_values = [];
        foreach ($group as $k => $value) {
            $grpId = $value->id;

            $arr_user_groups_values[] = [
                'user_id' => $adminId,
                'group_id' => $grpId,
                'created_date' => $now,
                'modified_date' => $now
            ];

            $arr_sa_user_groups_values[] = [
                'user_id' => $saId,
                'group_id' => $grpId,
                'created_date' => $now,
                'modified_date' => $now
            ];


            // DB::table('user_groups')->insert(
            //     [
            //         'user_id' => $adminId,
            //         'group_id' => $grpId,
            //         'created_date' => $now,
            //         'modified_date' => $now
            //     ]
            // );
        }

        if (count($arr_user_groups_values) > 0) {
            DB::table('user_groups')->insert($arr_user_groups_values);
        }

        if (count($arr_sa_user_groups_values) > 0) {
            DB::table('user_groups')->insert($arr_sa_user_groups_values);
        }

        if (array_key_exists($planID, $arrFilterPlanCode)) {
            //Create Labels
            $label_names = [
                'Inventory Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>',
                ],
                'CO Shipping Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "CO"},
                            {"size": 10, "style": "", "content": "%co_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                //                'Misc. Issue Label' => ['[
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Item_Item_Item"},
                //                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Desc"},
                //                            {"size": 10, "style": "", "content": "%item_desc%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Loc"},
                //                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //                            {"size": 1, "style": "", "content": "Lot"},
                //                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //                            {"size": 4, "style": "", "content": "%expiry_date%"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //                            {"size": 2, "style": "", "content": "Box"},
                //                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //                        ],
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //                        ]
                //                    ]',
                //                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item_Item_Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                //                ],
                'PO Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "PO"},
                            {"size": 10, "style": "", "content": "%po_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Vendor"},
                            {"size": 10, "style": "", "content": "%vend_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                 'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],



                // 'Pallet Label' => [
                //     '[
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                //         ],

                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                //         ],
                //     ]',
                //     '<div class="container">
                //     <div class="row">
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                //     </div>
                //     </div>',

                // ],

                // 'TO Shipping Label' => [
                //     '[

                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //         ],
                //         [
                //             {"size": 2, "style": "", "content": "Item"},
                //             {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //         ],
                //         [
                //             {"size": 2, "style": "", "content": "Desc"},
                //             {"size": 10, "style": "", "content": "%item_desc%"}
                //         ],
                //         [
                //             {"size": 2, "style": "", "content": "From Whse"},
                //             {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                //             {"size": 1, "style": "", "content": "To Whse"},
                //             {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                //         ],
                //         [
                //             {"size": 2, "style": "", "content": "Loc"},
                //             {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //             {"size": 1, "style": "", "content": "Lot"},
                //             {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //         ],
                //         [
                //             {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //             {"size": 4, "style": "", "content": "%expiry_date%"}
                //         ],
                //         [
                //             {"size": 2, "style": "", "content": "TO"},
                //             {"size": 10, "style": "", "content": "%trn_num%"}
                //         ],
                //         [
                //             {"size": 2, "style": "", "content": "TO Line"},
                //             {"size": 10, "style": "", "content": "%trn_line%"}
                //         ],
                //         [
                //             {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //             {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //             {"size": 2, "style": "", "content": "Box"},
                //             {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //         ]
                //     ]',
                //     '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-3" style="">Item</div><div class="col-xs-9" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-3" style="">Desc</div><div class="col-xs-9" style="">%item_desc%</div></div><div class="row"><div class="col-xs-3" style="">From Whse</div><div class="col-xs-3" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-3" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-3" style="">Loc</div><div class="col-xs-3" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-3" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br/><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-3" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style="">TO</div><div class="col-xs-9" style="">%trn_num%</div></div><div class="row"><div class="col-xs-3" style="">TO Line</div><div class="col-xs-9" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                // ],
            ];

            $arr_labels_values = [];
            foreach ($label_names as $label => $values) {
                $default_label = DefaultLabel::where('type', $label)->first();
                // dd($default_label);

                $arr_labels_values[] = [
                    'content_html' => $default_label ? $default_label->content_html : "",
                    'width' => $default_label ? $default_label->width : 100,
                    'height' => $default_label ? $default_label->height : 100,
                    'margin_left' => $default_label ? $default_label->margin_left : 0,
                    'margin_right' => $default_label ? $default_label->margin_right : 0,
                    'margin_top' => $default_label ? $default_label->margin_top : 0,
                    'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                    'label_name' => $label,
                    'type' => $label,
                    'raw_content' => $values[0],
                    'content' => $values[1],
                    'site_id' => $site_id,
                    'created_date' => $getnow,
                    'modified_date' => $getnow
                ];

                // DB::table('labels')->insert(
                //     [
                //         'content_html' => $default_label ? $default_label->content_html : "",
                //         'width' => $default_label ? $default_label->width : 100,
                //         'height' => $default_label ? $default_label->height : 100,
                //         'margin_left' => $default_label ? $default_label->margin_left : 0,
                //         'margin_right' => $default_label ? $default_label->margin_right : 0,
                //         'margin_top' => $default_label ? $default_label->margin_top : 0,
                //         'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                //         'label_name' => $label,
                //         'type' => $label,
                //         'raw_content' => $values[0],
                //         'content' => $values[1],
                //         'site_id' => $site_id,
                //         'created_date' => $now,
                //         'modified_date' => $now
                //     ]
                // );
            }

            if (count($arr_labels_values) > 0) {
                DB::table('labels')->insert($arr_labels_values);
            }

            $label_modules = [
                'MiscReceipt',
                'MiscIssue',
                'Putaway',
                'StockMove',
                'CustOrdShipping',
                'PoReceipt',
                'TranOrderShipping',
                'TransferOrderReceipt',
                'PickNShip',
                'Picklist',
                'CustomerReturn'
            ];
        } else {

            $label_names = [
                'Inventory Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>',
                ],
                'CO Shipping Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "CO"},
                            {"size": 10, "style": "", "content": "%co_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                //                'Misc. Issue Label' => ['[
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Item_Item_Item"},
                //                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Desc"},
                //                            {"size": 10, "style": "", "content": "%item_desc%"}
                //                        ],
                //                        [
                //                            {"size": 2, "style": "", "content": "Loc"},
                //                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                //                            {"size": 1, "style": "", "content": "Lot"},
                //                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                //                            {"size": 4, "style": "", "content": "%expiry_date%"}
                //                        ],
                //                        [
                //                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                //                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                //                            {"size": 2, "style": "", "content": "Box"},
                //                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                //                        ],
                //                        [
                //                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                //                        ]
                //                    ]',
                //                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item_Item_Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                //                ],
                'PO Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "PO"},
                            {"size": 10, "style": "", "content": "%po_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Vendor"},
                            {"size": 10, "style": "", "content": "%vend_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                 'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

                'Job Receipt Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Job"},
                            {"size": 6, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"},
                            {"size": 1, "style": "", "content": "Doc"},
                            {"size": 3, "style": "", "content": "<small>%document_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
                'Job Material Issue Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Job"},
                            {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

                // 'Pallet Label' => [
                //     '[
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                //         ],
                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                //         ],

                //         [
                //             {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                //         ],
                //     ]',
                //     '<div class="container">
                //     <div class="row">
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                //         <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                //     </div>
                //     </div>',

                // ],

            ];


            if ($planID != 7 && $planID != 4 && $planID != 1) {

                $label_names['TO Shipping Label'] = [
                    '[

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "From Whse"},
                            {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                            {"size": 1, "style": "", "content": "To Whse"},
                            {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO"},
                            {"size": 10, "style": "", "content": "%trn_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "TO Line"},
                            {"size": 10, "style": "", "content": "%trn_line%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-3" style="">Item</div><div class="col-xs-9" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-3" style="">Desc</div><div class="col-xs-9" style="">%item_desc%</div></div><div class="row"><div class="col-xs-3" style="">From Whse</div><div class="col-xs-3" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-3" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-3" style="">Loc</div><div class="col-xs-3" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-3" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br/><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-3" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-3" style="">TO</div><div class="col-xs-9" style="">%trn_num%</div></div><div class="row"><div class="col-xs-3" style="">TO Line</div><div class="col-xs-9" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ];

                $label_names['Pallet Label'] = [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Company %company_name%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "LPN : %lpn_num%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Creation Date : %creation_date%"}
                        ],

                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "Box : %boxnum% / %totalbox%"}

                        ],
                    ]',
                    '<div class="container">
                    <div class="row">
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Company %company_name%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">LPN : %lpn_num%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">%lpn_qrcode%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Creation Date : %creation_date%</div>
                        <div class="col-xs-12" style="text-align:center; font-weight:bold">Box : %boxnum% / %totalbox%</div>
                    </div>
                    </div>',
                ];
            }

            $arr_labels_values = [];
            foreach ($label_names as $label => $values) {
                $default_label = DefaultLabel::where('type', $label)->first();
                // dd($default_label);

                $arr_labels_values[] = [
                    'content_html' => $default_label ? $default_label->content_html : "",
                    'width' => $default_label ? $default_label->width : 100,
                    'height' => $default_label ? $default_label->height : 100,
                    'margin_left' => $default_label ? $default_label->margin_left : 0,
                    'margin_right' => $default_label ? $default_label->margin_right : 0,
                    'margin_top' => $default_label ? $default_label->margin_top : 0,
                    'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                    'label_name' => $label,
                    'type' => $label,

                    'raw_content' => $values[0],
                    'content' => $values[1],
                    'site_id' => $site_id,
                    'created_date' => $getnow,
                    'modified_date' => $getnow
                ];

                // DB::table('labels')->insert(
                //     [
                //         'content_html' => $default_label ? $default_label->content_html : "",
                //         'width' => $default_label ? $default_label->width : 100,
                //         'height' => $default_label ? $default_label->height : 100,
                //         'margin_left' => $default_label ? $default_label->margin_left : 0,
                //         'margin_right' => $default_label ? $default_label->margin_right : 0,
                //         'margin_top' => $default_label ? $default_label->margin_top : 0,
                //         'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                //         'label_name' => $label,
                //         'type' => $label,

                //         'raw_content' => $values[0],
                //         'content' => $values[1],
                //         'site_id' => $site_id,
                //         'created_date' => $now,
                //         'modified_date' => $now
                //     ]
                // );
            }

            if (count($arr_labels_values) > 0) {
                DB::table('labels')->insert($arr_labels_values);
            }

            $label_modules = [
                'MiscReceipt',
                'MiscIssue',
                'Putaway',
                'StockMove',
                'CustOrdShipping',
                'PoReceipt',
                'JobReceipt',
                'JobMaterialIssue',
                'TranOrderShipping',
                'TransferOrderReceipt',
                'PickNShip',
                'Picklist',
                'CustomerReturn'
            ];
        }



        //add objects to labels
        self::addLabelObjects($planID, $arrFilterPlanCode, $site_id, $now);

        return redirect()->back()->with('successmsg', 'Site ' . $site_id . ' Created!');
    }




    public static function addLabelObjectsAll()
    {
        // Delete and insert new label_modules data
        $modules_labels_map = self::$modules_labels;
        // DB::table('label_modules')->where('site_id', '')->delete();
        $labels = DB::table('labels')->get();
// dd($labels);
        // $labels = DB::table('labels')->where('site_id',"AXA_TEST")->get();
        foreach ($labels as $label) {
            $site_id = $label->site_id;
            $getnow = Carbon::now()->toDateTimeString();
            //$timezone = auth()->user()->timezone;
            // $siteSetting = SiteSetting::where('site_id', $site_id)->first();
            // dd($siteSetting);
            if (!isset($modules_labels_map[$label->type]))
                continue;
            // $timezone = $siteSetting->timezone;
            // $date = Timezone::convertFromUTC($getnow, $timezone, 'Y-m-d');
            // $time = Timezone::convertFromUTC($getnow, $timezone, 'H:i:s');
            // $now = $date . ' ' . $time;
            $now= Carbon::now()->format('Y-m-d H:i:s');
            // DB::table('label_modules')->where('label_id', $label->id)->delete();

            $modules_for_label = $modules_labels_map[$label->type];

            foreach ($modules_for_label as  $moduleName) {
                $moduleAlready = DB::table('label_modules')->where('site_id', $site_id)->where("label_id", $label->id)->where('modulename', $moduleName)->count();

                if (!$moduleAlready) {
                    $record =    DB::table('label_modules')->insert([
                        'label_id' => $label->id,
                        'modulename' => $moduleName,
                        'created_date' => $now,
                        'modified_date' => $now,
                        'site_id' => $site_id
                    ]);
                    // dd($record);
                }
            }
        }


        return;
    }
    public static function addLabelObjects($planID, $arrFilterPlanCode, $site_id, $now)
    {
        // Delete and insert new label_modules data
        $modules_labels_map = self::$modules_labels;

        DB::table('label_modules')->where('site_id', $site_id)->delete();
        $labels = DB::table('labels')->where('site_id', $site_id)->get();

        foreach ($modules_labels_map as $labelName => $moduleArray) {
            $label_Obj = $labels->firstWhere('label_name', $labelName);
            // dd($label_id);
            if ($label_Obj) {
                $label_id = $label_Obj->id;
                foreach ($moduleArray as  $moduleName) {

                    DB::table('label_modules')->insert([
                        'label_id' => $label_id,
                        'modulename' => $moduleName,
                        'created_date' => $now,
                        'modified_date' => $now,
                        'site_id' => $site_id
                    ]);
                }
            }
        }
        return;
        if (array_key_exists($planID, $arrFilterPlanCode)) {

            DB::table('label_modules')->insert([
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id ?? NULL, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id ?? NULL, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],

                ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id ?? NULL, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                // ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id ?? NULL, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
            ]);
            //added after issue 1, 48
            $label_modules = [
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id ?? NULL, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id ?? NULL, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ];
            DB::table('label_modules')->insert($label_modules);
        } else {
            DB::table('label_modules')->insert([
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id ?? NULL, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id ?? NULL, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Job Receipt Label')->id ?? NULL, 'modulename' => 'JobReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Job Material Issue Label')->id ?? NULL, 'modulename' => 'JobMaterialIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id ?? NULL, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                // ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id ?? NULL, 'modulename' => 'TranOrderShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                // ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id ?? NULL, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
            ]);
            if ($planID != 7 && $planID != 4 && $planID != 1) {
                DB::table('label_modules')->insert([
                    ['label_id' => $labels->firstWhere('label_name', 'Pallet Label')->id ?? NULL, 'modulename' => 'Pallet Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
                ]);
            }

            //added after issue 1, 48
            $label_modules = [
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id ?? NULL, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id ?? NULL, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
                ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id ?? NULL, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ];
            DB::table('label_modules')->insert($label_modules);
        }
    }

    public function execute(Request $request)
    {

        //$now = Carbon::now()->toDateTimeString();


        $timezone =  $request['timezone'];
        $now = Carbon::now()->toDateTimeString();
        //$timezone = auth()->user()->timezone;
        $date = Timezone::convertFromUTC($now, $timezone, 'Y-m-d');
        $time = Timezone::convertFromUTC($now, $timezone, 'H:i:s');
        $now = $date . ' ' . $time;
        //Check info here
        $site_id = $request['site_id'];
        $site_name = $request['site_name'];

        DB::beginTransaction();

        //Create Site
        $createSite = DB::table('site_settings')->insertOrIgnore(
            [
                'site_name' => $site_name,
                'site_id' => $site_id,
                //'admin_email' => $request['admin_email'],
                'timezone' => $request['timezone'],
                //'label_folder' => md5($site_id . 'label_folder' . $site_id),
                //'export_folder' => md5($site_id . 'export_folder' . $site_id),
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createSite) {
            DB::rollback();
        }

        //Create Licenses
        $createLicense = DB::connection('mysql2')->table('license_maint')->insertOrIgnore(
            [
                'lic_id' => md5(uniqid(rand(), true)),
                'lic_status' => 1,
                'valid_to' => '2025-12-31',
                'cust_id' => $site_id . 'OWNER',
                'site_id' => $site_id,
                'max_inv_lic' => $request['max_inv_lic'],
                'max_prod_lic' => $request['max_prod_lic'],
                'max_api_lic' => $request['max_api_lic'],
                'max_user' => $request['max_inv_lic'] + $request['max_prod_lic'] + $request['max_api_lic'],
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createLicense) {
            DB::rollback();
        }

        //Create Admin User
        $createAdminUser = User::create(
            [
                'name' => $request['admin_name'],
                'description' => 'Admin User',
                'email' => $request['admin_email'],
                'password' => $request['admin_password'],
                'homepage' => 'web',
                'timezone' => $request['timezone'],
                'site_id' => $site_id,
                'created_by' => 'system',
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        if (!$createAdminUser) {
            DB::rollback();
        }

        $adminId = $createAdminUser->id;

        // Give Admin Ext stuff

        DB::table('user_ext')->insert([
            ['user_id' => $adminId, 'whse_num' => '']
        ]);

        // Give Admin Special Permissions

        DB::table('user_groups')->insert([
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'ITEM_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'LOC_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'JOB_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'JOB_ATTACHMENT_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ZONE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_LOT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            // ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_PICKER')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_COUNT_GROUP')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_PICK_LIST_CO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_PICK_LIST_TO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_PICK_LIST_JO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ALLOCATION_CO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ALLOCATION_TO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ALLOCATION_JO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_BILL_OF_MATERIAL')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'WAREHOUSE_PICK_LIST')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'WIP_JOB_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'STOCK_MOVE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MISC_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MISC_ISSUE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'PO_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'PO_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'PUTAWAY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'TO_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'CO_PICKNSHIP')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'CO_SHIPPING')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'CO_PICK')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'CO_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'TO_SHIP')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'JOBMATL_ISSUE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'JOBMATL_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'JOB_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'JOB_LABOUR')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MACHINE_RUN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'WIP_MOVE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'HISTORY_JOB')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'HISTORY_MACHINE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'SYSADMIN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            // ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_MASTER')->first()->id, 'created_date'=>$now, 'modified_date'=>$now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'BARCODE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_PO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_CO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_TO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_JOB')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_WAREHOUSE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_LOCATION')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ITEM')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ITEMLOC')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ITEMLOTLOC')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_CUSTOMER')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_VENDOR')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_MACHINE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_TASK')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_UOM')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_UOMCONV')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_REASONCODE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_PRODUCTCODE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_EMPLOYEE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_WORKCENTER')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'HISTORY_OVERRIDE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            // ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_HISTORY')->first()->id, 'created_date'=>$now, 'modified_date'=>$now],
            // ['user_id' => $adminId, 'group_id' => Group::where('code', 'REPORT')->first()->id, 'created_date'=>$now, 'modified_date'=>$now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'INV_COUNT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_ITEM_WAREHOUSE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $adminId, 'group_id' => Group::where('code', 'MAINTENANCE_INVCOUNT')->first()->id, 'created_date' => $now, 'modified_date' => $now]
        ]);

        //Create Tparm
        //Create Users

        $createUser = User::create(
            [
                'name' => $request['user_name'],
                'description' => 'Web User',
                'email' => $request['user_email'],
                'password' => $request['user_password'],
                'homepage' => 'mobile',
                'timezone' => $request['timezone'],
                'site_id' => $site_id,
                'created_date' => $now,
                'modified_date' => $now
            ]
        );

        //Grant User Permissions

        $userId = $createUser->id;

        DB::table('user_ext')->insert([
            ['user_id' => $userId, 'whse_num' => '']
        ]);

        DB::table('user_groups')->insert([
            ['user_id' => $userId, 'group_id' => Group::where('code', 'ITEM_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'LOC_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'JOB_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'JOB_ATTACHMENT_INQUIRY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_ZONE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_LOT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            // ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_PICKER')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_COUNT_GROUP')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_PICK_LIST_CO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_PICK_LIST_TO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_PICK_LIST_JO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_ALLOCATION_CO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_ALLOCATION_TO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_ALLOCATION_JO')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MAINTENANCE_BILL_OF_MATERIAL')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'WAREHOUSE_PICK_LIST')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'WIP_JOB_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'STOCK_MOVE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MISC_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MISC_ISSUE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'PO_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'PO_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'PUTAWAY')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'TO_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'CO_PICKNSHIP')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'CO_SHIPPING')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'CO_PICK')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'CO_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'TO_SHIP')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'JOBMATL_ISSUE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'JOBMATL_RETURN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'JOB_RECEIPT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'JOB_LABOUR')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'MACHINE_RUN')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'WIP_MOVE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'HISTORY_JOB')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'HISTORY_MACHINE')->first()->id, 'created_date' => $now, 'modified_date' => $now],
            ['user_id' => $userId, 'group_id' => Group::where('code', 'INV_COUNT')->first()->id, 'created_date' => $now, 'modified_date' => $now],
        ]);

        $this->seedData('PHIDemoMaster', $site_id);

        DB::commit();

        return redirect()->back()->with('successmsg', 'Site ' . $site_id . ' Created!');
    }

    public function reset()
    {
        $now = Carbon::now()->toDateTimeString();

        //Check info here
        $site_id = auth()->user()->site_id;
        DB::beginTransaction();

        $this->clear($site_id);
        $this->seedResetData('PHIDemoData', $site_id);

        DB::commit();

        Alert::success('Success', 'You reset the demo data for ' . auth()->user()->site_id . '', 'success');
        return redirect()->back();
    }

    private function seedResetData($from_site_id, $site_id)
    {
    }

    private function seedData($from_site_id, $site_id)
    {
        $now = Carbon::now()->toDateTimeString();
        //Create Labels
        $label_names = [
            'Inventory Label' => [
                '[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]',
                '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
            ],
            'CO Shipping Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "CO"},
                        {"size": 10, "style": "", "content": "%co_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Cust"},
                        {"size": 10, "style": "", "content": "%cust_name%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'PO Receipt Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "PO"},
                        {"size": 10, "style": "", "content": "%po_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Vendor"},
                        {"size": 10, "style": "", "content": "%vend_name%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'Job Receipt Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Job"},
                        {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'Job Material Issue Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Job"},
                        {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'TO Shipping Label' => [
                '[

                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "From Whse"},
                        {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                        {"size": 1, "style": "", "content": "To Whse"},
                        {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "TO"},
                        {"size": 10, "style": "", "content": "%trn_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "TO Line"},
                        {"size": 10, "style": "", "content": "%trn_line%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]',
                '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">From Whse</div><div class="col-xs-6" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-1" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-2" style="">TO</div><div class="col-xs-10" style="">%trn_num%</div></div><div class="row"><div class="col-xs-2" style="">TO Line</div><div class="col-xs-10" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
            ],
            'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

        ];

        foreach ($label_names as $label => $values) {
            $default_label = DefaultLabel::where('type', $label)->first();
            // dd($default_label);

            DB::table('labels')->insert(
                [
                    'content_html' => $default_label ? $default_label->content_html : "",
                    'width' => $default_label ? $default_label->width : 100,
                    'height' => $default_label ? $default_label->height : 100,
                    'margin_left' => $default_label ? $default_label->margin_left : 0,
                    'margin_right' => $default_label ? $default_label->margin_right : 0,
                    'margin_top' => $default_label ? $default_label->margin_top : 0,
                    'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                    'label_name' => $label,
                    'type' => $label,
                    'raw_content' => $values[0],
                    'content' => $values[1],
                    'site_id' => $site_id,
                    'created_date' => $now,
                    'modified_date' => $now
                ]
            );
        }

        $label_modules = [
            'MiscReceipt',
            'MiscReceipt',
            'MiscIssue',
            'Putaway',
            'StockMove',
            'CustOrdShipping',
            'PoReceipt',
            'JobReceipt',
            'JobMaterialIssue',
            'CustomerReturn',
        ];

        $labels = DB::table('labels')->where('site_id', $site_id)->get();

        //delete and insert new label_modules data
        DB::table('label_modules')->where('site_id', $site_id)->delete();

        DB::table('label_modules')->insert([
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Job Receipt Label')->id, 'modulename' => 'JobReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Job Material Issue Label')->id, 'modulename' => 'JobMaterialIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TOShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id]
        ]);
        //added after issue 1, 48
        $label_modules = [
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
             ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        ];
        DB::table('label_modules')->insert($label_modules);

        // $warehouses = $this->getWarehouses($site_id);
        // // Delete and insert new warehouses data
        // foreach ($warehouses as $warehouse) {
        //     $warehousesCreated = DB::table('warehouses')->insert([
        //         'whse_num' => $warehouse['whse_num'],
        //         'whse_name' => $warehouse['whse_name'],
        //         'whse_status' => $warehouse['whse_status'],
        //         'whse_address' => $warehouse['whse_address'],
        //         'site_id' => $site_id
        //     ]);
        // }
        // if (!$warehousesCreated) DB::rollback();
        // // Get Locations
        // $locations = $this->getLocations($site_id);
        // foreach ($locations as $location) {
        //     $locationsCreated = DB::table('locs')->insert([
        //         'whse_num' => $location['whse_num'],
        //         'loc_num' => $location['loc_num'],
        //         'loc_name' => $location['loc_name'],
        //         'loc_status' => $location['loc_status'],
        //         'loc_type' => $location['loc_type'],
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$locationsCreated) DB::rollback();
        // // Get Product Codes
        // $product_codes = $this->getProductCodes($site_id);
        // foreach ($product_codes as $code) {
        //     $productsCreated = DB::table('product_codes')->insert([
        //         'product_code' => $code['product_code'],
        //         'product_desc' => $code['product_desc'],
        //         'product_status' => $code['product_status'],
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$productsCreated) DB::rollback();
        // // Delete and insert new items data
        // DB::table('items')->where('site_id', $site_id)->delete();
        // $items = $this->getItems($from_site_id);
        // foreach ($items as $item) {
        //     $itemsCreated = DB::table('items')->insert([
        //         'item_num' => $item->item_num,
        //         'item_desc' => $item->item_desc,
        //         'uom' => $item->uom,
        //         'product_code' => $item->product_code,
        //         'lot_tracked' => $item->lot_tracked,
        //         'unit_weight' => $item->unit_weight,
        //         'item_status' => $item->item_status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$itemsCreated) DB::rollback();
        // // Item Locations
        // $itemLocs = $this->getItemLocations($from_site_id);
        // foreach ($itemLocs as $itemLoc) {
        //         $itemLocsCreated = DB::table('item_locs')->insert([
        //             'whse_num' => $itemLoc->whse_num,
        //             'loc_num' => $itemLoc->loc_num,
        //             'item_num' => $itemLoc->item_num,
        //             'qty_on_hand' => $itemLoc->qty_on_hand,
        //             'uom' => $itemLoc->uom,
        //             'qty_on_rsvd' => $itemLoc->qty_on_rsvd,
        //             'site_id' => $site_id,
        //             'created_date'=>$now,
        //             'modified_date'=>$now
        //         ]);
        // }
        // if (!$itemLocsCreated) DB::rollback();
        // // Lot locations
        // $lot_locations = [
        //     ['20190622101', 'MAIN', 'L001', 'I011', 100.00, 'EA'],
        //     ['20190622101', 'MAIN', 'L002', 'I011', 100.00, 'EA'],
        //     ['XID2935835', 'MAIN', 'L001', 'I011', 100.00, 'EA']
        // ];
        // foreach ($lot_locations as $lot_location) {
        //     DB::table('lot_locs')->insert([
        //         ['whse_num' => $lot_location[1], 'loc_num' => $lot_location[2], 'item_num' => $lot_location[3],
        //          'lot_num' => $lot_location[0], 'uom' => $lot_location[5], 'qty_on_hand' => $lot_location[4],
        //          'qty_on_rsvd' => '0.00', 'site_id' => $site_id, 'created_date' => $now, 'modified_date' => $now]
        //     ]);
        // }
        // $uoms = [
        //     'EA' => ['Each', 0.1],
        // ];
        // foreach ($uoms as $uom => $uom_desc) {
        //     $UOMsCreated = DB::table('uoms')->insert([
        //         ['uom' => $uom, 'uom_desc' => $uom_desc[0], 'rounding' => $uom_desc[1], 'site_id' => $site_id, 'created_date' => $now, 'modified_date' => $now]
        //     ]);
        // }
        // if (!$UOMsCreated) DB::rollback();
        // // Create Reason Codes
        // $reasonCodes = $this->getReasonCodes($from_site_id);
        // foreach ($reasonCodes as $code) {
        //     $reasonsCreated = DB::table('reason_codes')->insert([
        //         'reason_class' => $code->reason_class,
        //         'reason_num' => $code->reason_num,
        //         'reason_desc' => $code->reason_desc,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$reasonsCreated) DB::rollback();
        // // Work Centers
        // $workCenters = $this->getWorkCenters($from_site_id);
        // foreach ($workCenters as $workCenter) {
        //     $workCentersCreate = DB::table('wcs')->insert([
        //         'wc_num' => $workCenter->wc_num,
        //         'wc_desc' => $workCenter->wc_desc,
        //         'wc_status' => $workCenter->wc_status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$workCentersCreate) DB::rollback();
        // // Employees
        // $employees = $this->getEmployees($from_site_id);
        // foreach ($employees as $employee) {
        //     $employeesCreate = DB::table('employees')->insert([
        //         'emp_num' => $employee->emp_num,
        //         'emp_name' => $employee->emp_name,
        //         'emp_status' => $employee->emp_status,
        //         'shift' => $employee->shift,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$employeesCreate) DB::rollback();
        // // Tasks
        // $createdTasks = DB::table('tasks')->insert([
        //     ['task_name' => 'MT1', 'task_desc'=> 'Meeting 1', 'site_id'=> $site_id, 'created_date'=>$now, 'modified_date'=>$now],
        //     ['task_name' => 'QC', 'task_desc'=> 'Quality Ctrl', 'site_id'=> $site_id, 'created_date'=>$now, 'modified_date'=>$now],
        //     ['task_name' => 'TBRK', 'task_desc'=> 'Toilet Break', 'site_id'=> $site_id, 'created_date'=>$now, 'modified_date'=>$now],
        //     ['task_name' => 'LBRK', 'task_desc'=> 'Lunch Break', 'site_id'=> $site_id, 'created_date'=>$now, 'modified_date'=>$now],
        //     ['task_name' => '5S', 'task_desc'=> '5S Tasks', 'site_id'=> $site_id, 'created_date'=>$now, 'modified_date'=>$now],
        //     ['task_name' => 'KAIZEN', 'task_desc'=> 'Kaizen Activity', 'site_id'=> $site_id, 'created_date'=>$now, 'modified_date'=>$now],
        // ]);
        // if (!$createdTasks) DB::rollback();
        // // Machines
        // $machinesCreated = DB::table('machines')->insert([
        //     ['res_id' => '16779055', 'res_desc'=> 'Pipe Bending Machine', 'res_status' => 1, 'site_id'=> $site_id, 'created_date'=>$now, 'modifIed_date'=>$now],
        //     ['res_id' => '3242528002', 'res_desc'=> 'Molding machine', 'res_status' => 1, 'site_id'=> $site_id, 'created_date'=>$now, 'modifIed_date'=>$now],
        //     ['res_id' => 'BR-2300', 'res_desc'=> 'Brother LSM 2300', 'res_status' => 1, 'site_id'=> $site_id, 'created_date'=>$now, 'modifIed_date'=>$now],
        //     ['res_id' => 'RY-0990', 'res_desc'=> 'Raytech B02911', 'res_status' => 1, 'site_id'=> $site_id, 'created_date'=>$now, 'modifIed_date'=>$now]
        // ]);
        // if (!$machinesCreated) DB::rollback();
        // // Customers
        // $customers = $this->getCustomers($from_site_id);
        // foreach ($customers as $customer) {
        //     $customersCreated = DB::table('customers')->insert([
        //         'cust_num' => $customer->cust_num,
        //         'cust_name' => $customer->cust_name,
        //         'cust_status' => $customer->cust_status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$customersCreated) DB::rollback();
        // // Vendors
        // $vendors = $this->getVendors($from_site_id);
        // foreach ($vendors as $vendor) {
        //     $vendorsCreated = DB::table('vendors')->insert([
        //         'vend_num' => $vendor->vend_num,
        //         'vend_name' => $vendor->vend_name,
        //         'vend_status' => $vendor->vend_status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$vendorsCreated) DB::rollback();
        // // Transactions
        // // PO Item
        // $poItems = $this->getPOItems($from_site_id);
        // foreach ($poItems as $poItem) {
        //     $poItemsCreated = DB::table('po_items')->insert([
        //         'po_num' => $poItem->po_num,
        //         'po_line' => $poItem->po_line,
        //         'po_rel' => $poItem->po_rel,
        //         'vend_do' => $poItem->vend_do,
        //         'qty_ordered' => $poItem->qty_ordered,
        //         'qty_ordered_conv' => $poItem->qty_ordered_conv,
        //         'qty_received' => $poItem->qty_received,
        //         'qty_picked' => $poItem->qty_picked,
        //         'qty_returned' => $poItem->qty_returned,
        //         'item_num' => $poItem->item_num,
        //         'item_desc' => $poItem->item_desc,
        //         'vend_num' => $poItem->vend_num,
        //         'vend_name' => $poItem->vend_name,
        //         'notes' => $poItem->notes,
        //         'whse_num' => $poItem->whse_num,
        //         'uom' => $poItem->uom,
        //         'due_date'=>$now,
        //         'rel_status' => $poItem->rel_status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$poItemsCreated) DB::rollback();
        // // CO Items
        // $coItems = $this->getCOItems($from_site_id);
        // foreach ($coItems as $coItem) {
        //     $coItemsCreated = DB::table('coitems')->insert([
        //         'co_num' => $coItem->co_num,
        //         'co_line' => $coItem->co_line,
        //         'co_rel' => $coItem->co_rel,
        //         'qty_released' => $coItem->qty_released,
        //         'qty_released_conv' => $coItem->qty_released_conv,
        //         'cust_num' => $coItem->cust_num,
        //         'add_num' => $coItem->add_num,
        //         'due_date' => $coItem->due_date,
        //         'do_num' => $coItem->do_num,
        //         'qty_ordered' => $coItem->qty_ordered,
        //         'qty_ordered_conv' => $coItem->qty_ordered_conv,
        //         'qty_shipped' => $coItem->qty_shipped,
        //         'qty_returned' => $coItem->qty_returned,
        //         'item_num' => $coItem->item_num,
        //         'item_desc' => $coItem->item_desc,
        //         'whse_num' => $coItem->whse_num,
        //         'uom' => $coItem->uom,
        //         'rel_status' => $coItem->rel_status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$coItemsCreated) DB::rollback();
        // // Transfer Orders
        // $transferOrders = $this->getTransferOrders($from_site_id);
        // foreach ($transferOrders as $transferOrder) {
        //     $transferOrderCreated = DB::table('transfer_orders')->insert([
        //         'trn_num' => $transferOrder->trn_num,
        //         'from_whse' => $transferOrder->from_whse,
        //         'to_whse' => $transferOrder->to_whse,
        //         'from_site' => $transferOrder->from_site,
        //         'to_site' => $transferOrder->to_site,
        //         'trn_loc' => $transferOrder->trn_loc,
        //         'driver_name' => $transferOrder->driver_name,
        //         'schedule_receive_date' => $transferOrder->schedule_receive_date,
        //         'schedule_ship_date' => $transferOrder->schedule_ship_date,
        //         'pickup_date' => $transferOrder->pickup_date,
        //         'driver_contact' => $transferOrder->driver_contact,
        //         'vehicle_num' => $transferOrder->vehicle_num,
        //         'status' => $transferOrder->status,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$transferOrderCreated) DB::rollback();
        // // Transfer Lines
        // $transferLines = $this->getTransferLines($from_site_id);
        // foreach ($transferLines as $transferLine) {
        //     $transferLineCreated = DB::table('transfer_lines')->insert([
        //         'trn_num' => $transferLine->trn_num,
        //         'trn_line' => $transferLine->trn_line,
        //         'from_whse' => $transferLine->from_whse,
        //         'to_whse' => $transferLine->to_whse,
        //         'item_num' => $transferLine->item_num,
        //         'qty_required' => $transferLine->qty_required,
        //         'qty_shipped' => $transferLine->qty_shipped,
        //         'qty_loss' => $transferLine->qty_loss,
        //         'qty_received' => $transferLine->qty_received,
        //         'uom' => $transferLine->uom,
        //         'line_stat' => $transferLine->line_stat,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$transferLineCreated) DB::rollback();
        // // Jobs
        // $jobs = $this->getJobs($from_site_id);
        // foreach ($jobs as $job) {
        //     $jobCreated = DB::table('jobs')->insert([
        //         'job_num' => $job->job_num,
        //         'whse_num' => $job->whse_num,
        //         'uom' => $job->uom,
        //         'job_date' => $job->job_date,
        //         'start_date_plan' => $job->start_date_plan,
        //         'end_date_plan' => $job->end_date_plan,
        //         'cust_num' => $job->cust_num,
        //         'item_num' => $job->item_num,
        //         'qty_released' => $job->qty_released,
        //         'qty_completed' => $job->qty_completed,
        //         'qty_scrapped' => $job->qty_scrapped,
        //         'job_status' => $job->job_status,
        //         'notes' => $job->notes,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // if (!$jobCreated) DB::rollback();
        // // Job Routes
        // $jobRoutes = $this->getJobRoutes($from_site_id);
        // foreach ($jobRoutes as $jobRoute) {
        //     $jobRoutesCreated = DB::table('job_routes')->insert([
        //         'job_num' => $jobRoute->job_num,
        //         'oper_num' => $jobRoute->oper_num,
        //         'oper_status' => $jobRoute->oper_status,
        //         'qty_moved' => $jobRoute->qty_moved,
        //         'qty_completed' => $jobRoute->qty_completed,
        //         'qty_received' => $jobRoute->qty_received,
        //         'qty_scrapped' => $jobRoute->qty_scrapped,
        //         'notes' => $jobRoute->notes,
        //         'wc_num' => $jobRoute->wc_num,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //         ]);
        //     }
        // if (!$jobRoutesCreated) DB::rollback();
        // // Job Materials
        // $jobMaterials = $this->getJobMaterials($from_site_id);
        // foreach ($jobMaterials as $jobMaterial) {
        //     $jobMaterialsCreated = DB::table('job_matls')->insert([
        //         'job_num' => $jobMaterial->job_num,
        //         'whse_num' => $jobMaterial->whse_num,
        //         'oper_num' => $jobMaterial->oper_num,
        //         'sequence' => $jobMaterial->sequence,
        //         'matl_item' => $jobMaterial->matl_item,
        //         'uom' => $jobMaterial->uom,
        //         'matl_desc' => $jobMaterial->matl_desc,
        //         'scrap_factor' => $jobMaterial->scrap_factor,
        //         'qty_required' => $jobMaterial->qty_required,
        //         'qty_required_conv' => $jobMaterial->qty_required_conv,
        //         'qty_issued' => $jobMaterial->qty_issued,
        //         'qty_per' => $jobMaterial->qty_per,
        //         'site_id' => $site_id,
        //         'created_date'=>$now,
        //         'modified_date'=>$now
        //     ]);
        // }
        // // // Inventory Counts
        // $countBatchTable = DB::table('batchs')->insert([
        //     ['batch_name' => 'Count Batch 1', 'batch_desc'=> 'Monthly Count', 'status' => 'O', 'site_id'=>$site_id, 'created_by' => 'system', 'created_date'=>$now, 'modified_date'=>$now]
        // ]);
        // $countBatchTable2 = DB::table('batchs')->insert([
        //     ['batch_name' => 'Count Batch 2', 'batch_desc'=> 'Yearly Count', 'status' => 'O', 'site_id'=>$site_id, 'created_by' => 'system', 'created_date'=>$now, 'modified_date'=>$now]
        // ]);
        // $batch1 = DB::table('batchs')->select('id')->where('site_id', $site_id)->first();
        // $countSheetsTable1 = DB::table('count_sheets')->insert([
        //     ['count_batch_id' => $batch1->id, 'count_sheet_num' => 1, 'status' => 'P', 'released_date'=>$now,'site_id'=>$site_id,  'created_by' => 'system', 'created_date'=>$now, 'modified_date'=>$now]
        // ]);
        // $countSheetsTable2 = DB::table('count_sheets')->insert([
        //     ['count_batch_id' => $batch1->id, 'count_sheet_num' => 2, 'status' => 'P', 'released_date'=>$now,'site_id'=>$site_id,  'created_by' => 'system', 'created_date'=>$now, 'modified_date'=>$now]
        // ]);
        // $sheet1 = DB::table('count_sheets')->select('id')->where('site_id', $site_id)->first();
        // $sheet2 = DB::table('count_sheets')->select('id')->where('site_id', $site_id)->skip(1)->first();
        // $countSheetLinesTable1 = DB::table('count_sheet_lines')->insert([
        //     ['count_sheet_id' => $sheet1->id, 'item_num' => 'I001', 'whse_num' => 'MAIN', 'loc_num'=>'L002', 'status'=>'U', 'uom' => 'EA',
        //         'site_id'=>$site_id,  'created_by' => 'system', 'created_date'=>$now, 'modified_date'=>$now]
        // ]);
        // DB::table('count_sheet_lines')->insert([
        //     ['count_sheet_id' => $sheet2->id, 'item_num' => 'I011', 'whse_num' => 'MAIN', 'loc_num'=>'L002', 'lot_num'=>'20190622101' , 'uom' => 'EA',
        //         'site_id'=>$site_id, 'created_date'=>$now, 'modified_date'=>$now]
        // ]);
    }

    public function do_migrate_label_without_production()
    {
        $now = Carbon::now()->toDateTimeString();
        $siteID = $request->siteID;
        $site_id = $siteID;
        //Create Labels
        $label_names = [
            'Inventory Label' => [
                '[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]',
                '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
            ],
            'CO Shipping Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "CO"},
                        {"size": 10, "style": "", "content": "%co_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Cust"},
                        {"size": 10, "style": "", "content": "%cust_name%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'PO Receipt Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "PO"},
                        {"size": 10, "style": "", "content": "%po_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Vendor"},
                        {"size": 10, "style": "", "content": "%vend_name%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'TO Shipping Label' => [
                '[

                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "From Whse"},
                        {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                        {"size": 1, "style": "", "content": "To Whse"},
                        {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "TO"},
                        {"size": 10, "style": "", "content": "%trn_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "TO Line"},
                        {"size": 10, "style": "", "content": "%trn_line%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]',
                '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">From Whse</div><div class="col-xs-6" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-1" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-2" style="">TO</div><div class="col-xs-10" style="">%trn_num%</div></div><div class="row"><div class="col-xs-2" style="">TO Line</div><div class="col-xs-10" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
            ],

             'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],
        ];
        $labelchecks = DB::table('labels')->where('site_id', $site_id)->count();
        if ($labelchecks > 0) {
            DB::table('labels')->where('site_id', $site_id)->delete();
        }
        foreach ($label_names as $label => $values) {
            $default_label = DefaultLabel::where('type', $label)->first();
            // dd($default_label);

            DB::table('labels')->insert(
                [
                    'content_html' => $default_label ? $default_label->content_html : "",
                    'width' => $default_label ? $default_label->width : 100,
                    'height' => $default_label ? $default_label->height : 100,
                    'margin_left' => $default_label ? $default_label->margin_left : 0,
                    'margin_right' => $default_label ? $default_label->margin_right : 0,
                    'margin_top' => $default_label ? $default_label->margin_top : 0,
                    'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                    'label_name' => $label,
                    'type' => $label,
                    'raw_content' => $values[0],
                    'content' => $values[1],
                    'site_id' => $site_id,
                    'created_date' => $now,
                    'modified_date' => $now
                ]
            );
        }

        $label_modules = [
            'MiscReceipt',
            'MiscReceipt',
            'MiscIssue',
            'Putaway',
            'StockMove',
            'CustOrdShipping',
            'PoReceipt',
            'CustomerReturn',
        ];

        $labels = DB::table('labels')->where('site_id', $site_id)->get();

        //delete and insert new label_modules data
        DB::table('label_modules')->where('site_id', $site_id)->delete();

        DB::table('label_modules')->insert([
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TOShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
             ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        ]);
        //added after issue 1, 48
        $label_modules = [
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        ];
        DB::table('label_modules')->insert($label_modules);

        Alert::success('Success', 'Your label data have installed sucessfully with Production Label.', 'success');
        return redirect()->back();
    }

    public function do_migrate_label(Request $request)
    {
        $now = Carbon::now()->toDateTimeString();
        $siteID = $request->siteID;
        //dd($siteID);
        $site_id = $siteID;
        //Create Labels
        $default_labels = DefaultLabel::get();
        $label_names = [
            'Inventory Label' => [
                '[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]',
                '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
            ],
            'CO Shipping Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "CO"},
                        {"size": 10, "style": "", "content": "%co_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Cust"},
                        {"size": 10, "style": "", "content": "%cust_name%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">CO</div><div class="col-xs-10" style="">%co_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'PO Receipt Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "PO"},
                        {"size": 10, "style": "", "content": "%po_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Vendor"},
                        {"size": 10, "style": "", "content": "%vend_name%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">PO</div><div class="col-xs-10" style="">%po_num%</div></div><div class="row"><div class="col-xs-2" style="">Vendor</div><div class="col-xs-10" style="">%vend_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'Job Receipt Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Job"},
                        {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'Job Material Issue Label' => ['[
                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Loc"},
                        {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                        {"size": 1, "style": "", "content": "Lot"},
                        {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Job"},
                        {"size": 10, "style": "", "content": "%job_qrcode% <small>%job_num%</small>"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]', '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-2" style="">Job</div><div class="col-xs-10" style="">%job_qrcode% <small>%job_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'],
            'TO Shipping Label' => [
                '[

                    [
                        {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Item"},
                        {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "Desc"},
                        {"size": 10, "style": "", "content": "%item_desc%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "From Whse"},
                        {"size": 6, "style": "", "content": "%from_whse_qrcode%<br /><small>%from_whse%</small>"},
                        {"size": 1, "style": "", "content": "To Whse"},
                        {"size": 3, "style": "", "content": "%to_whse_qrcode%<br /><small>%to_whse%</small>"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "TO"},
                        {"size": 10, "style": "", "content": "%trn_num%"}
                    ],
                    [
                        {"size": 2, "style": "", "content": "TO Line"},
                        {"size": 10, "style": "", "content": "%trn_line%"}
                    ],
                    [
                        {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                        {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                        {"size": 2, "style": "", "content": "Box"},
                        {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                    ],
                    [
                        {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                    ]
                ]',
                '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">From Whse</div><div class="col-xs-6" style="">%from_whse_qrcode%<br /><small>%from_whse%</small></div><div class="col-xs-1" style="">To Whse</div><div class="col-xs-3" style="">%to_whse_qrcode%<br /><small>%to_whse%</small></div></div><div class="row"><div class="col-xs-2" style="">TO</div><div class="col-xs-10" style="">%trn_num%</div></div><div class="row"><div class="col-xs-2" style="">TO Line</div><div class="col-xs-10" style="">%trn_line%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
            ],
             'Customer Return Label' => [
                    '[
                        [
                            {"size": 12, "style": "text-align:center; font-weight:bold", "content": "%label_name% - %site_name%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Item"},
                            {"size": 10, "style": "", "content": "%item_num% %item_qrcode%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Desc"},
                            {"size": 10, "style": "", "content": "%item_desc%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Loc"},
                            {"size": 6, "style": "", "content": "%loc_qrcode%<br /><small>%loc_num%</small>"},
                            {"size": 1, "style": "", "content": "Lot"},
                            {"size": 3, "style": "", "content": "%lot_qrcode%<br /><small>%lot_num%</small>"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Expiry Date</small>"},
                            {"size": 4, "style": "", "content": "%expiry_date%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Return Num"},
                            {"size": 10, "style": "", "content": "%return_num%"}
                        ],
                        [
                            {"size": 2, "style": "", "content": "Cust"},
                            {"size": 10, "style": "", "content": "%cust_name%"}
                        ],
                        [
                            {"size": 3, "style": "", "content": "<small>Qty in Box</small>"},
                            {"size": 4, "style": "", "content": "%qtyinbox% %uom%"},
                            {"size": 2, "style": "", "content": "Box"},
                            {"size": 3, "style": "", "content": "%boxnum% / %totalbox%"}
                        ],
                        [
                            {"size": 12, "style": "text-align:center; font-size:80%", "content": "%trans_date%"}
                        ]
                    ]',
                    '<div class="container"><div class="row"><div class="col-xs-12" style="text-align:center; font-weight:bold">%label_name% - %site_name%</div></div><div class="row"><div class="col-xs-2" style="">Item</div><div class="col-xs-10" style="">%item_num% %item_qrcode%</div></div><div class="row"><div class="col-xs-2" style="">Desc</div><div class="col-xs-10" style="">%item_desc%</div></div><div class="row"><div class="col-xs-2" style="">Loc</div><div class="col-xs-6" style="">%loc_qrcode%<br /><small>%loc_num%</small></div><div class="col-xs-1" style="">Lot</div><div class="col-xs-3" style="">%lot_qrcode%<br /><small>%lot_num%</small></div></div><div class="row"><div class="col-xs-3" style=""><small>Expiry Date</small></div><div class="col-xs-4" style="">%expiry_date%</div></div><div class="row"><div class="col-xs-2" style="">Return Num</div><div class="col-xs-10" style="">%return_num%</div></div><div class="row"><div class="col-xs-2" style="">Customer</div><div class="col-xs-10" style="">%cust_name%</div></div><div class="row"><div class="col-xs-3" style=""><small>Qty in Box</small></div><div class="col-xs-4" style="">%qtyinbox% %uom%</div><div class="col-xs-2" style="">Box</div><div class="col-xs-3" style="">%boxnum% / %totalbox%</div></div><div class="row"><div class="col-xs-12" style="text-align:center; font-size:80%">%trans_date%</div></div></div>'
                ],

        ];
        $labelchecks = DB::table('labels')->where('site_id', $site_id)->count();
        if ($labelchecks > 0) {
            DB::table('labels')->where('site_id', $site_id)->delete();
        }
        foreach ($label_names as $label => $values) {
            $default_label = DefaultLabel::where('type', $label)->first();
            // dd($default_label);

            DB::table('labels')->insert(
                [
                    'label_name' => $label,
                    'type' => $label,
                    'raw_content' => $values[0],
                    'content' => $values[1],
                    'site_id' => $site_id,
                    'created_date' => $now,
                    'modified_date' => $now,
                    'content_html' => $default_label ? $default_label->content_html : "",
                    'width' => $default_label ? $default_label->width : 100,
                    'height' => $default_label ? $default_label->height : 100,
                    'margin_left' => $default_label ? $default_label->margin_left : 0,
                    'margin_right' => $default_label ? $default_label->margin_right : 0,
                    'margin_top' => $default_label ? $default_label->margin_top : 0,
                    'margin_bottom' => $default_label ? $default_label->margin_bottom : 0,
                ]
            );
        }

        $label_modules = [
            'MiscReceipt',
            'MiscReceipt',
            'MiscIssue',
            'Putaway',
            'StockMove',
            'CustOrdShipping',
            'PoReceipt',
            'JobReceipt',
            'JobMaterialIssue',
            'CustomerReturn',
        ];

        $labels = DB::table('labels')->where('site_id', $site_id)->get();

        //delete and insert new label_modules data
        DB::table('label_modules')->where('site_id', $site_id)->delete();

        DB::table('label_modules')->insert([
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Inventory Label', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'MiscIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'Putaway', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'StockMove', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'CustOrdShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'PO Receipt Label')->id, 'modulename' => 'PoReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Job Receipt Label')->id, 'modulename' => 'JobReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Job Material Issue Label')->id, 'modulename' => 'JobMaterialIssue', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'TO Shipping Label')->id, 'modulename' => 'TOShipping', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        ]);

        //added after issue 1, 48
        $label_modules = [
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'CustOrdPicking', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickList', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'JobMaterialReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'TransferOrderReceipt', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            //            ['label_id' => $labels->firstWhere('label_name', 'Inventory Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'CO Shipping Label')->id, 'modulename' => 'PickNShip', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
            ['label_id' => $labels->firstWhere('label_name', 'Customer Return Label')->id, 'modulename' => 'CustomerReturn', 'created_date' => $now, 'modified_date' => $now, 'site_id' => $site_id],
        ];
        DB::table('label_modules')->insert($label_modules);
        Alert::success('Success', 'Your label data have installed sucessfully.', 'success');
        return redirect()->back();
    }

    private function clear($site_id)
    {
        DB::table('tparm_sites')->where('site_id', $site_id)->delete();
        DB::table('labels')->where('site_id', $site_id)->delete();

        //Inventory
        DB::table('warehouses')->where('site_id', $site_id)->delete();
        DB::table('locs')->where('site_id', $site_id)->delete();
        DB::table('items')->where('site_id', $site_id)->delete();
        DB::table('item_locs')->where('site_id', $site_id)->delete();
        DB::table('lot_locs')->where('site_id', $site_id)->delete();
        DB::table('uoms')->where('site_id', $site_id)->delete();
        DB::table('product_codes')->where('site_id', $site_id)->delete();
        DB::table('reason_codes')->where('site_id', $site_id)->delete();

        //PO
        DB::table('po_items')->where('site_id', $site_id)->delete();
        DB::table('vendors')->where('site_id', $site_id)->delete();
        //Co Related
        DB::table('coitems')->where('site_id', $site_id)->delete();
        DB::table('stage_locs')->where('site_id', $site_id)->delete();
        DB::table('customers')->where('site_id', $site_id)->delete();
        //TO
        DB::table('transfer_orders')->where('site_id', $site_id)->delete();
        DB::table('transfer_lines')->where('site_id', $site_id)->delete();

        //Jobs
        DB::table('jobs')->where('site_id', $site_id)->delete();
        DB::table('job_routes')->where('site_id', $site_id)->delete();
        DB::table('wcs')->where('site_id', $site_id)->delete();
        DB::table('job_matls')->where('site_id', $site_id)->delete();
        DB::table('employees')->where('site_id', $site_id)->delete();
        DB::table('machines')->where('site_id', $site_id)->delete();
        DB::table('tasks')->where('site_id', $site_id)->delete();
        DB::table('start_labour')->where('site_id', $site_id)->delete();
        DB::table('start_machine')->where('site_id', $site_id)->delete();

        //Inventory Count
        DB::table('batchs')->where('site_id', $site_id)->delete();
        DB::table('count_sheets')->where('site_id', $site_id)->delete();
        DB::table('count_sheet_lines')->where('site_id', $site_id)->delete();
        DB::table('counters')->where('site_id', $site_id)->delete();

        //History
        DB::table('matl_trans')->where('site_id', $site_id)->delete();
        DB::table('machine_trans')->where('site_id', $site_id)->delete();
        DB::table('job_trans')->where('site_id', $site_id)->delete();
        DB::table('overridehistories')->where('site_id', $site_id)->delete();
    }

    public function delete(Request $request)
    {

        $site_id = $request->site_id;

        DB::beginTransaction();

        $this->clear($site_id);
        //Site Settings, Tparm, Users, Licenses
        DB::table('users')->where('site_id', $site_id)->delete();
        DB::table('site_settings')->where('site_id', $site_id)->delete();
        DB::connection('mysql2')->table('license_maint')->where('site_id', $site_id)->delete();
        DB::commit();

        return redirect()->back()->with('successmsg', 'Site ' . $site_id . ' Deleted!');
    }

    public function update(Request $request)
    {

        $now = Carbon::now()->toDateTimeString();
        $site_id = auth()->user()->site_id;

        DB::beginTransaction();
        $this->clear('PHIDemoMaster');
        $this->seedData('PHIDemoData', 'PHIDemoMaster');
        DB::commit();

        return redirect()->back()->with('successmsg', 'Saved PHIDemoData to PHIDemoMaster');
    }

    private function getWarehouses($site_id)
    {
        $warehouses = DB::table('warehouses')->where('site_id', $site_id)->get();

        if ($warehouses->isNotEmpty()) {
            return $warehouses;
        } else
            return ([
                ["whse_num" => "MAIN", "whse_name" => "MAIN Warehouse", "whse_status" => 1, "whse_address" => "ABC Street", "site_id" => $site_id],
                ["whse_num" => "DIST", "whse_name" => "DIST Warehouse", "whse_status" => 1, "whse_address" => "DEF Street", "site_id" => $site_id],
            ]
            );
    }

    private function getLocations($site_id)
    {
        $locations = DB::table('locs')->where('site_id', $site_id)->get();

        if ($locations->isNotEmpty()) {
            return $locations;
        } else
            return [
                ["whse_num" => "MAIN", "loc_num" => "INSP", "loc_name" => "Incoming Inspection", "loc_status" => 1, "loc_type" => "S", "site_id" => $site_id],
                ["whse_num" => "MAIN", "loc_num" => "L001", "loc_name" => "Rack A", "loc_status" => 1, "loc_type" => "S", "site_id" => $site_id],
                ["whse_num" => "MAIN", "loc_num" => "L002", "loc_name" => "Rack B", "loc_status" => 1, "loc_type" => "S", "site_id" => $site_id],
                ["whse_num" => "MAIN", "loc_num" => "L003", "loc_name" => "Rack C", "loc_status" => 1, "loc_type" => "S", "site_id" => $site_id],
                ["whse_num" => "MAIN", "loc_num" => "L004", "loc_name" => "Rack D", "loc_status" => 1, "loc_type" => "S", "site_id" => $site_id],
                ["whse_num" => "MAIN", "loc_num" => "TRANSIT", "loc_name" => "Transit Location", "loc_status" => 1, "loc_type" => "T", "site_id" => $site_id],
                ["whse_num" => "MAIN", "loc_num" => "PACKING", "loc_name" => "Packing 1", "loc_status" => 1, "loc_type" => "S", "site_id" => $site_id],
            ];
    }

    private function getItems($from_site_id)
    {
        $items = DB::table('items')->where('site_id', $from_site_id)->get();
        if ($items->isNotEmpty()) {
            return $items;
        } else
            return [
                ['item_num' => 'I001', 'item_desc' => 'E1 Motor 21W 220V 50Hz', 'uom' => 'EA', 'product_code' => 'Type A', 'unit_weight' => 50.00, 'lot_tracked' => 0, 'item_status' => 1],
                ['item_num' => 'I002', 'item_desc' => 'E2 Motor 21W 220V 50Hz', 'uom' => 'EA', 'product_code' => 'Type A', 'unit_weight' => 23.00, 'lot_tracked' => 0, 'item_status' => 1],
                ['item_num' => 'I011', 'item_desc' => 'Aluminum A2212', 'uom' => 'EA', 'product_code' => 'RMMSA', 'unit_weight' => 50.00, 'lot_tracked' => 1, 'item_status' => 1],
                ['item_num' => 'K002', 'item_desc' => 'K22 Motor 21W 110V 50Hz', 'uom' => 'EA', 'product_code' => 'FG-MG', 'unit_weight' => 20.00, 'lot_tracked' => 0, 'item_status' => 1],
                ['item_num' => 'K011', 'item_desc' => 'K120 Motor 21W 220V 50Hz', 'uom' => 'EA', 'product_code' => 'FG-MG', 'unit_weight' => 21.20, 'lot_tracked' => 0, 'item_status' => 1],
                ['item_num' => 'K023', 'item_desc' => 'K120 Motor 25W 220V 50Hz', 'uom' => 'EA', 'product_code' => 'FG-MG', 'unit_weight' => 22.10, 'lot_tracked' => 0, 'item_status' => 1]
            ];
    }

    private function getItemLocations($from_site_id)
    {
        $itemLocs = DB::table('item_locs')->where('site_id', $from_site_id)->get();
        if ($itemLocs->isNotEmpty()) {
            return $itemLocs;
        } else
            return
                [
                    ['whse_num' => 'MAIN', 'loc_num' => 'INSP', 'item_num' => 'I001', 'qty_on_hand' => 0.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 1],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L001', 'item_num' => 'I001', 'qty_on_hand' => 14.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 2],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L001', 'item_num' => 'I002', 'qty_on_hand' => 0.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 1],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L001', 'item_num' => 'I011', 'qty_on_hand' => 200.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 1],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L002', 'item_num' => 'I001', 'qty_on_hand' => 93.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 3],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L002', 'item_num' => 'I002', 'qty_on_hand' => 22.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 2],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L002', 'item_num' => 'I011', 'qty_on_hand' => 100.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 2],
                    ['whse_num' => 'MAIN', 'loc_num' => 'L002', 'item_num' => 'K002', 'qty_on_hand' => 11.00, 'uom' => 'EA', 'qty_on_rsvd' => '0.00', 'rank' => 1]
                ];
    }

    private function getProductCodes($site_id)
    {
        $productCodes = DB::table('product_codes')->where('site_id', $site_id)->get();
        if ($productCodes->isNotEmpty()) {
            return $productCodes;
        } else
            return [
                ['product_code' => "FG-MG", 'product_desc' => "Megatech P100", 'product_status' => 1],
                ['product_code' => "RMACC", 'product_desc' => "Accessories", 'product_status' => 1],
                ['product_code' => "RMMSA", 'product_desc' => "Metal Sheets", 'product_status' => 1],
                ['product_code' => "RMSUB", 'product_desc' => "Sub components Electrical", 'product_status' => 1],
                ['product_code' => "Type A", 'product_desc' => "RM Type A", 'product_status' => 1]
            ];
    }

    private function getReasonCodes($from_site_id)
    {
        $reasonCodes = DB::table('reason_codes')->where('site_id', $from_site_id)->get();
        if ($reasonCodes->isNotEmpty()) {
            return $reasonCodes;
        } else
            return [
                ['reason_num' => 'R002', 'reason_class' => 'MiscIssue', 'reason_desc' => 'Misc Issue'],
                ['reason_num' => 'RNDUsage', 'reason_class' => 'MiscIssue', 'reason_desc' => 'R&D Usage'],
                ['reason_num' => 'Samples', 'reason_class' => 'MiscIssue', 'reason_desc' => 'Issue as Samples'],
                ['reason_num' => 'SCRP', 'reason_class' => 'MiscIssue', 'reason_desc' => 'Scrap'],
                ['reason_num' => 'PRODRTN', 'reason_class' => 'MiscReceipt', 'reason_desc' => 'Production Returns'],
                ['reason_num' => 'R001', 'reason_class' => 'MiscReceipt', 'reason_desc' => 'Misc Receipt'],
                ['reason_num' => 'DMG', 'reason_class' => 'TOLoss', 'reason_desc' => 'Item damaged'],
                ['reason_num' => 'STOLEN', 'reason_class' => 'TOLoss', 'reason_desc' => 'Theft of goods'],
                ['reason_num' => 'ProcessIss', 'reason_class' => 'JobScrap', 'reason_desc' => 'Processing Failure'],
                ['reason_num' => 'NC', 'reason_class' => 'POReturn', 'reason_desc' => 'Non-conformance'],
                ['reason_num' => 'WRGITEM', 'reason_class' => 'POReturn', 'reason_desc' => 'Incorrect item delivered'],
                ['reason_num' => 'DMG', 'reason_class' => 'POReturn', 'reason_desc' => 'Item Damaged'],
                ['reason_num' => 'WRGITEM', 'reason_class' => 'COReturn', 'reason_desc' => 'Wrong item'],
                ['reason_num' => 'EXCESS', 'reason_class' => 'COReturn', 'reason_desc' => 'Excess stock'],
                ['reason_num' => 'FOUND', 'reason_class' => 'MiscReceipt', 'reason_desc' => 'Item found after reported lost'],
                ['reason_num' => 'MMR', 'reason_class' => 'MiscReceipt', 'reason_desc' => 'Miscellaneous Receipt'],
                ['reason_num' => 'MMI', 'reason_class' => 'MiscIssue', 'reason_desc' => 'Miscellaneous Issue'],
                ['reason_num' => 'DMG', 'reason_class' => 'JobScrap', 'reason_desc' => 'Damaged by Employee']
            ];
    }

    private function getCustomers($from_site_id)
    {
        $customers = DB::table('customers')->where('site_id', $from_site_id)->get();
        if ($customers->isNotEmpty()) {
            return $customers;
        } else
            return [
                ['cust_num' => 'CUST1', 'cust_name' => 'SISTECH Corp', 'cust_status' => 1],
                ['cust_num' => 'CUST2', 'cust_name' => 'Corals Pte Ltd', 'cust_status' => 1],
                ['cust_num' => 'MZ001', 'cust_name' => 'Mazik Corp', 'cust_status' => 1],
                ['cust_num' => 'PR002', 'cust_name' => 'Paragon Pte Ltd', 'cust_status' => 1],
            ];
    }

    private function getEmployees($from_site_id)
    {
        $customers = DB::table('employees')->where('site_id', $from_site_id)->get();
        if ($customers->isNotEmpty()) {
            return $customers;
        } else
            return [
                ['emp_num' => 'E0001', 'emp_name' => 'Ailee', 'emp_status' => 1, 'shift' => 1],
                ['emp_num' => 'E0002', 'emp_name' => 'Jason', 'emp_status' => 1, 'shift' => 1],
                ['emp_num' => 'E0003', 'emp_name' => 'Steve', 'emp_status' => 1, 'shift' => 1],
            ];
    }

    private function getWorkCenters($from_site_id)
    {
        $workCenters = DB::table('wcs')->where('site_id', $from_site_id)->get();
        if ($workCenters->isNotEmpty()) {
            return $workCenters;
        } else
            return [
                ['wc_num' => 'Bending', 'wc_desc' => 'Bending', 'wc_status' => 1],
                ['wc_num' => 'COATING', 'wc_desc' => 'Coating Treatment', 'wc_status' => 1],
                ['wc_num' => 'Cutting', 'wc_desc' => 'MS Cutting', 'wc_status' => 1],
                ['wc_num' => 'Stamping', 'wc_desc' => 'Stamping', 'wc_status' => 1],
                ['wc_num' => 'WC01', 'wc_desc' => 'Assembly', 'wc_status' => 1]
            ];
    }

    private function getVendors($from_site_id)
    {
        $vendors = DB::table('vendors')->where('site_id', $from_site_id)->get();
        if ($vendors->isNotEmpty()) {
            return $vendors;
        } else
            return [
                ['vend_num' => 'MPH', 'vend_name' => 'MPH Group Malaysia', 'vend_status' => 1],
                ['vend_num' => 'VM', 'vend_name' => 'VM Holdings', 'vend_status' => 1],
                ['vend_num' => 'V001', 'vend_name' => 'Ventura Corp', 'vend_status' => 1]
            ];
    }

    private function getPOItems($from_site_id)
    {
        $poItems = DB::table('po_items')->where('site_id', $from_site_id)->get();
        if ($poItems->isNotEmpty()) {
            return $poItems;
        } else
            return [
                [
                    'po_num' => 'P001', 'po_line' => '1', 'po_rel' => '1', 'vend_do' => 'VDO1', 'qty_ordered' => '30', 'qty_ordered_conv' => '30', 'qty_received' => 0,
                    'qty_picked' => 0, 'qty_returned' => 0, 'item_num' => 'I001', 'item_desc' => 'E1 Motor 21W 220V 50Hz', 'vend_num' => 'V001', 'vend_name' => 'Ventura Corp',
                    'notes' => '', 'whse_num' => 'MAIN', 'uom' => 'EA', 'due_date' => '30/06/2020', 'rel_status' => 'O'
                ],
                [
                    'po_num' => 'P001', 'po_line' => '1', 'po_rel' => '2', 'vend_do' => 'VDO1', 'qty_ordered' => '30', 'qty_ordered_conv' => '30', 'qty_received' => 0,
                    'qty_picked' => 0, 'qty_returned' => 0, 'item_num' => 'I011', 'item_desc' => 'Aluminum A2212', 'vend_num' => 'V001', 'vend_name' => 'Ventura Corp',
                    'notes' => '', 'whse_num' => 'MAIN', 'uom' => 'EA', 'due_date' => '30/06/2020', 'rel_status' => 'O'
                ],
                [
                    'po_num' => 'P002', 'po_line' => '1', 'po_rel' => '2', 'vend_do' => 'VDO1', 'qty_ordered' => '30', 'qty_ordered_conv' => '30', 'qty_received' => 0,
                    'qty_picked' => 0, 'qty_returned' => 0, 'item_num' => 'I011', 'item_desc' => 'Aluminum A2212', 'vend_num' => 'V001', 'vend_name' => 'Ventura Corp',
                    'notes' => '', 'whse_num' => 'MAIN', 'uom' => 'EA', 'due_date' => '30/06/2020', 'rel_status' => 'O'
                ]
            ];
    }

    private function getCOItems($from_site_id)
    {
        $coItems = DB::table('coitems')->where('site_id', $from_site_id)->get();
        if ($coItems->isNotEmpty()) {
            return $coItems;
        } else
            return [
                [
                    'co_num' => 'CO00001', 'co_line' => '1', 'co_rel' => '0', 'qty_released' => 50, 'qty_released_conv' => 0, 'cust_num' => 'CUST1', 'add_num' => 'A001', 'due_date' => '30/06/2020',
                    'do_num' => '', 'qty_ordered' => 70, 'qty_ordered_conv' => 0, 'qty_shipped' => 0, 'qty_returned' => 0, 'item_num' => 'I001', 'item_desc' => 'E1 Motor 21W 220V 50Hz',
                    'whse_num' => 'MAIN', 'uom' => 'EA', 'rel_status' => 'O'
                ],
                [
                    'co_num' => 'CO00001', 'co_line' => '2', 'co_rel' => '0', 'qty_released' => 50, 'qty_released_conv' => 0, 'cust_num' => 'CUST1', 'add_num' => 'A001', 'due_date' => '30/06/2020',
                    'do_num' => '', 'qty_ordered' => 70, 'qty_ordered_conv' => 0, 'qty_shipped' => 0, 'qty_returned' => 0, 'item_num' => 'I001', 'item_desc' => 'E1 Motor 21W 220V 50Hz',
                    'whse_num' => 'MAIN', 'uom' => 'EA', 'rel_status' => 'O'
                ]
            ];
    }

    private function getTransferOrders($from_site_id)
    {
        $transferOrders = DB::table('transfer_orders')->where('site_id', $from_site_id)->get();
        if ($transferOrders->isNotEmpty()) {
            return $transferOrders;
        } else
            return [
                [
                    'trn_num' => 'TO01', 'from_whse' => 'MAIN', 'to_whse' => 'DIST', 'from_site' => '', 'to_site' => '', 'trn_loc' => 'TRANSIT', 'driver_name' => '',
                    'schedule_receive_date' => '', 'schedule_ship_date' => '', 'pickup_date' => '', 'driver_contact' => '', 'vehicle_num' => '', 'status' => 'O'
                ]
            ];
    }

    private function getTransferLines($from_site_id)
    {
        $transferLines = DB::table('transfer_lines')->where('site_id', $from_site_id)->get();
        if ($transferLines->isNotEmpty()) {
            return $transferLines;
        } else
            return [
                [
                    'trn_num' => 'TO01', 'trn_line' => '1', 'from_whse' => 'MAIN', 'to_whse' => 'DIST', 'item_num' => 'I011', 'qty_required' => 50,
                    'qty_shipped' => 25, 'qty_loss' => 0.00, 'qty_received' => 0, 'uom' => 'EA', 'line_stat' => 'C'
                ]
            ];
    }

    private function getJobs($from_site_id)
    {
        $jobs = DB::table('jobs')->where('site_id', $from_site_id)->get();
        if ($jobs->isNotEmpty()) {
            return $jobs;
        } else
            return [
                [
                    'job_num' => 'J0001', 'whse_num' => 'MAIN', 'uom' => 'EA', 'job_date' => '3/6/2019', 'start_date_plan' => '3/6/2019', 'end_date_plan' => '30/6/2019', 'cust_num' => 'CUST1',
                    'item_num' => 'I001', 'qty_released' => 50, 'qty_completed' => 0.00, 'qty_scrapped' => 0, 'job_status' => 'O', 'notes' => ''
                ],
                [
                    'job_num' => 'J0002', 'whse_num' => 'MAIN', 'uom' => 'EA', 'job_date' => '3/6/2019', 'start_date_plan' => '3/6/2019', 'end_date_plan' => '30/6/2019', 'cust_num' => 'CUST2',
                    'item_num' => 'K002', 'qty_released' => 50, 'qty_completed' => 0.00, 'qty_scrapped' => 0, 'job_status' => 'O', 'notes' => ''
                ]
            ];
    }

    private function getJobRoutes($from_site_id)
    {
        $jobRoutes = DB::table('job_routes')->where('site_id', $from_site_id)->get();
        if ($jobRoutes->isNotEmpty()) {
            return $jobRoutes;
        } else
            return [
                ['job_num' => 'J0001', 'oper_num' => '10', 'oper_status' => 1, 'qty_moved' => 0.00, 'qty_completed' => 0.00, 'qty_received' => 0.00, 'qty_scrapped' => 0.00, 'notes' => '', 'wc_num' => 'ASSY'],
                ['job_num' => 'J0001', 'oper_num' => '20', 'oper_status' => 1, 'qty_moved' => 0.00, 'qty_completed' => 0.00, 'qty_received' => 0.00, 'qty_scrapped' => 0.00, 'notes' => '', 'wc_num' => 'CUTTING'],
                ['job_num' => 'J0001', 'oper_num' => '30', 'oper_status' => 1, 'qty_moved' => 0.00, 'qty_completed' => 0.00, 'qty_received' => 0.00, 'qty_scrapped' => 0.00, 'notes' => '', 'wc_num' => 'BENDING'],
                ['job_num' => 'J0002', 'oper_num' => '10', 'oper_status' => 1, 'qty_moved' => 0.00, 'qty_completed' => 0.00, 'qty_received' => 0.00, 'qty_scrapped' => 0.00, 'notes' => '', 'wc_num' => 'ASSY'],
                ['job_num' => 'J0002', 'oper_num' => '20', 'oper_status' => 1, 'qty_moved' => 0.00, 'qty_completed' => 0.00, 'qty_received' => 0.00, 'qty_scrapped' => 0.00, 'notes' => '', 'wc_num' => 'STAMPING'],
                ['job_num' => 'J0002', 'oper_num' => '30', 'oper_status' => 1, 'qty_moved' => 0.00, 'qty_completed' => 0.00, 'qty_received' => 0.00, 'qty_scrapped' => 0.00, 'notes' => '', 'wc_num' => 'COATING']
            ];
    }

    private function getJobMaterials($from_site_id)
    {
        $jobMatls = DB::table('job_matls')->where('site_id', $from_site_id)->get();
        if ($jobMatls->isNotEmpty()) {
            return $jobMatls;
        } else
            return [
                [
                    'job_num' => 'J0001', 'whse_num' => 'MAIN', 'oper_num' => '10', 'sequence' => '1', 'matl_item' => 'I011', 'uom' => 'EA', 'matl_desc' => 'Aluminum A2212', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0001', 'whse_num' => 'MAIN', 'oper_num' => '10', 'sequence' => '2', 'matl_item' => 'K002', 'uom' => 'EA', 'matl_desc' => 'K22 Motor 21W 110V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0001', 'whse_num' => 'MAIN', 'oper_num' => '20', 'sequence' => '1', 'matl_item' => 'I002', 'uom' => 'EA', 'matl_desc' => 'E2 Motor 21W 220V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0001', 'whse_num' => 'MAIN', 'oper_num' => '30', 'sequence' => '1', 'matl_item' => 'K011', 'uom' => 'EA', 'matl_desc' => 'K120 Motor 21W 220V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0001', 'whse_num' => 'MAIN', 'oper_num' => '30', 'sequence' => '2', 'matl_item' => 'K023', 'uom' => 'EA', 'matl_desc' => 'K120 Motor 25W 220V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0002', 'whse_num' => 'MAIN', 'oper_num' => '10', 'sequence' => '1', 'matl_item' => 'I001', 'uom' => 'EA', 'matl_desc' => 'E1 Motor 21W 220V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0002', 'whse_num' => 'MAIN', 'oper_num' => '20', 'sequence' => '1', 'matl_item' => 'I002', 'uom' => 'EA', 'matl_desc' => 'E2 Motor 21W 220V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
                [
                    'job_num' => 'J0002', 'whse_num' => 'MAIN', 'oper_num' => '30', 'sequence' => '1', 'matl_item' => 'K011', 'uom' => 'EA', 'matl_desc' => 'K120 Motor 21W 220V 50Hz', 'scrap_factor' => '0.001',
                    'qty_required' => 50, 'qty_required_conv' => '', 'qty_issued' => 10.00, 'qty_per' => 1.00
                ],
            ];
    }

    public function openAccount()
    {
        $site_id = rand();
        $site_name = rand(); // Site Name
        $adminUsername = "test"; // Username
        $adminEmail = "<EMAIL>";    // Email
        $adminPassword = "test"; // auto generate
        // $zoho_customer_id = $customer_id;
        // $plan_code = $subscription->plan_code;
        // $max_user = $subscription->quantity;
        // $status = 1;
    }

    // Migrate New features
    public function migrate_plan()
    {
        // New Plan : Dapat Plan Id first and update feature_plan
        $arrPlanCode = ['AX-MT-STR-A', 'AX-MT-PRO-A', 'AX-MT-ENT-A'];
        // Add into New feature plan
        $plans = DB::table('plans')->get();
        $arrPlanId = [1, 2, 3];
        $arrPlanCodelookupId = [];
        $featurePlans = DB::table('feature_plan')->whereIn('plan_id', $arrPlanId)->get();
        $countfeaturePlans = DB::table('feature_plan')->whereIn('plan_id', $arrPlanId)->count();

        if ($countfeaturePlans == 0) {
            $arrSetforfeature = array('1' => array(1, 9, 3, 4, 5, 10, 11, 8), '2' => array(1, 2, 19, 4, 12, 14, 15, 20), '3' => array(1, 2, 19, 18, 13, 16, 17, 21));

            foreach ($arrSetforfeature as $plan_id => $featureId) {
                foreach ($featureId as $k => $featureId_id) {
                    DB::table('feature_plan')->updateOrInsert(
                        [
                            'plan_id' => $plan_id,
                            'feature_id' => $featureId_id,
                        ],
                        [
                            'created_date' => now()->toDateTimeString(),
                            'modified_date' => now()->toDateTimeString(),
                        ]
                    );
                }
            }


            // exit;
        }



        foreach ($plans as $key => $data) {
            //$data->plan_code;
            $arrPlanCodeDB[$data->id] = $data->plan_code;
            $arrPlanCodelookupId[$data->plan_code] = $data->id;
        }
        $arrnewPlan = [];
        $arrNewPlanCombine = [];
        foreach ($featurePlans as $keynew => $datanew) {
            $arrnewPlan[$arrPlanCodeDB[$datanew->plan_id]][$datanew->id] = $datanew->feature_id;
        }

        foreach ($arrPlanCode as $newObject) {
            foreach ($arrnewPlan as $key => $data) {
                if ($newObject == "AX-MT-STR-A" && $key == "AX-MT-STR-M") {
                    $arrNewPlanCombine[$newObject] = $arrnewPlan['AX-MT-STR-M'];
                }
                if ($newObject == "AX-MT-PRO-A" && $key == "AX-MT-PRO-M") {
                    $arrNewPlanCombine[$newObject] = $arrnewPlan["AX-MT-PRO-M"];
                }
                if ($newObject == "AX-MT-ENT-A" && $key == "AX-MT-ENT-M") {
                    $arrNewPlanCombine[$newObject] = $arrnewPlan["AX-MT-ENT-M"];
                }
            }
        }
        //dd($arrnewPlan,$arrPlanCode,$arrnewPlan,$arrPlanCodeDB,$arrNewPlanCombine);
        foreach ($arrNewPlanCombine as $p => $r) {
            $plan_id = $arrPlanCodelookupId[$p];
            foreach ($r as $key => $featureId) {
                DB::table('feature_plan')->updateOrInsert(
                    [
                        'plan_id' => $plan_id,
                        'feature_id' => $featureId,
                    ],
                    [
                        'created_date' => now()->toDateTimeString(),
                        'modified_date' => now()->toDateTimeString(),
                    ]
                );
            }
        }


        Alert::success('Success', 'Your new features have installed sucessfully', 'success');
        return redirect()->back();
    }

    // Migrate New Object
    public function login_as(Request $request)
    {

        $site_id = $request->custom_site_id;
        $user = DB::table('users')->where('type', 'site_owner')->where('site_id', $site_id)->first();
        // dd($user);
        if ($user) {
            $cookie = cookie('logged_in_with', auth()->user()->id);
            auth()->loginUsingId($user->id);
            return redirect(route('editsite'))->withCookie($cookie);
        }
        return redirect()->back();
    }


    function login_back(Request $request)
    {
        $loginBackId = $request->cookie('logged_in_with');
        $cookie = \Illuminate\Support\Facades\Cookie::forget('logged_in_with');
        auth()->loginUsingId($loginBackId);
        return redirect(route('super.index'))->withCookie($cookie);
    }
    public function migrate_object(Request $request)
    {
        $siteID = $request->siteID;
        $plans = DB::table('plans')->get();
        $arrPlanId = [2, 3, 5, 6];
        $arrPlanCodelookupId = [];
        $featurePlans = DB::table('feature_plan')->whereIn('plan_id', $arrPlanId)->get();
        foreach ($plans as $key => $data) {
            //$data->plan_code;
            $arrPlanCodeDB[$data->id] = $data->plan_code;
            $arrPlanCodelookupId[$data->plan_code] = $data->id;
        }
        $arrSkip = ['AX-MT-STR-M' => $arrPlanCodelookupId['AX-MT-STR-M'], 'AX-MT-STR-A' => $arrPlanCodelookupId['AX-MT-STR-A']];

        // dd($arrPlanCodeDB,$arrPlanCodelookupId,$arrSkip);
        $allGroup = Group::all();
        // From DB
        $arrGrp = [];
        foreach ($allGroup as $grpkey) {
            $arrGrp[$grpkey->category][$grpkey->id] = $grpkey->name;
            if ($grpkey->category == "All") {
                $arrGrp['Inventory'][$grpkey->id] = $grpkey->name;
                $arrGrp['Production'][$grpkey->id] = $grpkey->name;
            }
        }
        $rest_apis = [
            // Warehouse
            'MAINTENANCE_WAREHOUSE',
            // Zone
            'MAINTENANCE_ZONE',
            // Location
            'MAINTENANCE_LOCATION',
            // Lot
            'MAINTENANCE_LOT',
            // Item
            'MAINTENANCE_ITEM',
            // Item Warehouse
            'MAINTENANCE_ITEM_WAREHOUSE',
            // Product Code
            'MAINTENANCE_PRODUCTCODE',
            // Reason Code
            'MAINTENANCE_REASONCODE',
            // Work Center
            'MAINTENANCE_WORKCENTER',
            // UOM
            'MAINTENANCE_UOM',
            // Indirect Task
            'MAINTENANCE_TASK',
            // Customer
            'MAINTENANCE_CUSTOMER',
            // Vendor
            'MAINTENANCE_VENDOR',
            // Item Location
            'MAINTENANCE_ITEMLOC',
            // Item Lot Location
            'MAINTENANCE_ITEMLOTLOC',
            // Employee
            'MAINTENANCE_EMPLOYEE',
            // Machine
            'MAINTENANCE_MACHINE',
            // Purchase order
            'MAINTENANCE_PO',
            // Customer Order
            'MAINTENANCE_CO',
            // Transfer Order
            'MAINTENANCE_TO',
            // Job Order
            'MAINTENANCE_JOB',
            // Job Routes
            'MAINTENANCE_JOBROUTE',
            // Job Materials
            'MAINTENANCE_JOBMATL',
            // Inventory Count
            'MAINTENANCE_INVCOUNT',
            // Labor & Machine
            'MAINTENANCE_LABORMACHINE',
            // Material Transactions
            'HISTORY_MATERIAL',
            // Job Transactions
            'HISTORY_JOB',
            // Machine Transactions
            'HISTORY_MACHINE',
            // Custom Fields
            'SYSADMIN',
        ];
        $moduleskus = DB::table('moduleskus')->get();
        $rest_apis_ids = DB::table('groups')->whereIn('code', $rest_apis)->pluck('id')->toArray();

        DB::beginTransaction();
        try {
            // DB::statement('SET FOREIGN_KEY_CHECKS=0;');
            // DB::table('moduleskus')->truncate();
            // DB::statement('SET FOREIGN_KEY_CHECKS=1;');
            // Update
            foreach ($moduleskus as $modulesku) {
                // Inventory
                if ($modulesku->name == "Inventory") {
                    foreach ($arrGrp['Inventory'] as $inventories_id => $inventoryname) {
                        DB::table('group_modulesku')->updateOrInsert(
                            [
                                'group_id' => $inventories_id,
                                'module_id' => $modulesku->id,
                            ],
                            [
                                'created_date' => now()->toDateTimeString(),
                                'modified_date' => now()->toDateTimeString(),
                            ]
                        );
                    }
                }
                // Update Prodution
                else if ($modulesku->name == "Production") {
                    foreach ($arrGrp['Production'] as $productions_id => $productionname) {
                        DB::table('group_modulesku')->updateOrInsert(
                            [
                                'group_id' => $productions_id,
                                'module_id' => $modulesku->id,
                            ],
                            [
                                'created_date' => now()->toDateTimeString(),
                                'modified_date' => now()->toDateTimeString(),
                            ]
                        );
                    }
                }
                // Update Rest API
                else if ($modulesku->name == "Rest Api") {
                    foreach ($rest_apis_ids as $rest_apis_id) {
                        DB::table('group_modulesku')->updateOrInsert(
                            [
                                'group_id' => $rest_apis_id,
                                'module_id' => $modulesku->id,
                            ],
                            [
                                'created_date' => now()->toDateTimeString(),
                                'modified_date' => now()->toDateTimeString(),
                            ]
                        );
                    }
                }
            }

            // Update Admin's user foreach site
            $arrUserID = DB::table('users')->where('site_administration', 'Yes')->where('site_id', $siteID)->select('id', 'site_id')->get();
            $planID = DB::table('site_settings')->select('site_id', 'plan_id')->get();

            $userPlan = [];
            $arrPlanSite = [];
            foreach ($planID as $key => $data) {
                $arrPlanSite[$data->site_id] = $data->plan_id;
            }
            foreach ($arrUserID as $kUser => $datauser) {
                $userPlan[$datauser->id] = $arrPlanSite[$datauser->site_id];
            }


            // Check User Base On grp
            foreach ($userPlan as $userId => $userplanid) {
                if (array_key_exists($arrPlanCodeDB[$userplanid], $arrSkip)) {

                    $group_moduleSKU[$userId] = DB::table('group_modulesku')
                        ->selectRaw('group_id')
                        ->where('module_id', 1)
                        ->groupby('group_id')
                        ->get();

                    // echo "skipped";
                } else {
                    $group_moduleSKU[$userId] = DB::table('group_modulesku')
                        ->selectRaw('group_id')
                        ->groupby('group_id')
                        ->get();

                    // echo "No skipped";
                }
            }

            // dd($userPlan,$arrPlanCodeDB[1],$arrSkip);
            foreach ($group_moduleSKU as $userid => $grpz) {

                DB::table('user_groups')->where('user_id', $userid)->delete();

                foreach ($grpz as $p => $r) {
                    $grpId = $r->group_id;

                    DB::table('user_groups')->updateOrInsert(
                        [
                            'user_id' => $userid,
                            'group_id' => $grpId,
                        ],
                        [
                            'created_date' => now()->toDateTimeString(),
                            'modified_date' => now()->toDateTimeString(),
                        ]
                    );
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }

        Alert::success('Success', 'Your new objects have installed sucessfully', 'success');
        return redirect()->back();
    }

    function convert_max_user(Request $request)
    {


        $result = DB::table('site_settings')->where('site_id', $request->siteID)->first();
        $convertmax_user = customCrypt($result->max_user);
        if (!is_numeric($result->max_user)) {

            Alert::error('Warning', 'Already Enypted.');
            return \App::make('redirect')->back();
        } else {
            DB::table('site_settings')->updateOrInsert(
                [

                    'site_id' => $request->siteID,
                ],
                [
                    'max_user' => $convertmax_user,
                    'created_date' => now()->toDateTimeString(),
                    'modified_date' => now()->toDateTimeString(),
                ]
            );

            Alert::success('Success', 'Your max_user have converted sucessfully', 'success');
            return redirect()->back();
        }
    }

    function convert_plan_id(Request $request)
    {

        $result = DB::table('site_settings')->where('site_id', $request->siteID)->first();

        $convertplan_id = customCrypt($result->plan_id);
        $convertmax_user = customCrypt($result->max_user);

        //    dd($convertplan_id);

        if (!is_numeric($result->plan_id)) {

            Alert::error('Warning', 'Already Enypted.');
            return \App::make('redirect')->back();
        } else {

            // dd($convertplan_id,$request->siteID);
            $test = DB::table('site_settings')->updateOrInsert(
                [

                    'site_id' => $request->siteID,
                ],
                [
                    'plan_id' => $convertplan_id,
                    'max_user' => $convertmax_user,
                    'created_date' => now()->toDateTimeString(),
                    'modified_date' => now()->toDateTimeString(),
                ]
            );


            Alert::success('Success', 'Your plan_id have converted sucessfully', 'success');
            return redirect()->back();
        }
    }

    // Site_Setting : zoho_customer_id , plan_code :: Free, max_user,created_user , plan_id , Status [ Each user login need to check ] ,
    // User
    // Create 1 more Table called Object_Categories 1: Inventory , 2: Production , 3: All  , 4: Rest Api >> Alter Table groups Ob_cate_id
    // Upgrade how?
    // Email to user with password
    // All data from Zoho [ diffre database ] > drive to [ icapt  database ]
}
